{"ast": null, "code": "import { isPlainObject } from \"./objects.js\";\nexport function omitDeep(value, key) {\n  return __omitDeep(value, key);\n}\nfunction __omitDeep(value, key, known) {\n  if (known === void 0) {\n    known = new Map();\n  }\n  if (known.has(value)) {\n    return known.get(value);\n  }\n  var modified = false;\n  if (Array.isArray(value)) {\n    var array_1 = [];\n    known.set(value, array_1);\n    value.forEach(function (value, index) {\n      var result = __omitDeep(value, key, known);\n      modified || (modified = result !== value);\n      array_1[index] = result;\n    });\n    if (modified) {\n      return array_1;\n    }\n  } else if (isPlainObject(value)) {\n    var obj_1 = Object.create(Object.getPrototypeOf(value));\n    known.set(value, obj_1);\n    Object.keys(value).forEach(function (k) {\n      if (k === key) {\n        modified = true;\n        return;\n      }\n      var result = __omitDeep(value[k], key, known);\n      modified || (modified = result !== value[k]);\n      obj_1[k] = result;\n    });\n    if (modified) {\n      return obj_1;\n    }\n  }\n  return value;\n}\n//# sourceMappingURL=omitDeep.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}