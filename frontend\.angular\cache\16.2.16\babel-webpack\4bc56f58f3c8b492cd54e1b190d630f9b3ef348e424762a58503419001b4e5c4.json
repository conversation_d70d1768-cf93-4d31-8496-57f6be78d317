{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isSaturday} function options.\n */\n\n/**\n * @name isSaturday\n * @category Weekday Helpers\n * @summary Is the given date Saturday?\n *\n * @description\n * Is the given date Saturday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Saturday\n *\n * @example\n * // Is 27 September 2014 Saturday?\n * const result = isSaturday(new Date(2014, 8, 27))\n * //=> true\n */\nexport function isSaturday(date, options) {\n  return toDate(date, options?.in).getDay() === 6;\n}\n\n// Fallback for modularized imports:\nexport default isSaturday;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}