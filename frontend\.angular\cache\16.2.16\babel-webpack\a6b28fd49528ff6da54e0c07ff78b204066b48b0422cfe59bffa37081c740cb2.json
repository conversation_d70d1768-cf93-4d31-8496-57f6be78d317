{"ast": null, "code": "import _asyncToGenerator from \"c:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/logger.service\";\nimport * as i2 from \"@angular/common\";\nfunction VoiceRecorderComponent_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 15);\n  }\n  if (rf & 2) {\n    const i_r3 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", 5 + ctx_r2.Math.abs(ctx_r2.Math.sin(i_r3 / 3) * 15), \"px\")(\"animation-delay\", i_r3 * 60, \"ms\");\n  }\n}\nconst _c0 = function () {\n  return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15];\n};\nfunction VoiceRecorderComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5);\n    i0.ɵɵelement(3, \"div\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 7);\n    i0.ɵɵtemplate(5, VoiceRecorderComponent_div_1_div_5_Template, 1, 4, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 9);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 10)(9, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function VoiceRecorderComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.stopRecording());\n    });\n    i0.ɵɵelement(10, \"i\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function VoiceRecorderComponent_div_1_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.cancelRecording());\n    });\n    i0.ɵɵelement(12, \"i\", 14);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(2, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.formattedTime);\n  }\n}\nfunction VoiceRecorderComponent_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function VoiceRecorderComponent_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.startRecording());\n    });\n    i0.ɵɵelement(1, \"i\", 17);\n    i0.ɵɵelementEnd();\n  }\n}\nexport let VoiceRecorderComponent = /*#__PURE__*/(() => {\n  class VoiceRecorderComponent {\n    constructor(logger) {\n      this.logger = logger;\n      this.recordingComplete = new EventEmitter();\n      this.recordingCancelled = new EventEmitter();\n      this.maxDuration = 60; // Durée maximale en secondes\n      this.isRecording = false;\n      this.recordingTime = 0;\n      this.mediaRecorder = null;\n      this.audioChunks = [];\n      this.audioStream = null;\n      // Exposer Math pour l'utiliser dans le template\n      this.Math = Math;\n    }\n    ngOnInit() {}\n    ngOnDestroy() {\n      this.stopRecording();\n      this.stopMediaTracks();\n    }\n    /**\n     * Démarre l'enregistrement vocal\n     */\n    startRecording() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this.audioChunks = [];\n          _this.recordingTime = 0;\n          // Demander l'accès au microphone\n          _this.audioStream = yield navigator.mediaDevices.getUserMedia({\n            audio: true\n          });\n          // Créer un MediaRecorder\n          _this.mediaRecorder = new MediaRecorder(_this.audioStream);\n          // Configurer les gestionnaires d'événements\n          _this.mediaRecorder.ondataavailable = event => {\n            if (event.data.size > 0) {\n              _this.audioChunks.push(event.data);\n            }\n          };\n          _this.mediaRecorder.onstop = () => {\n            // Créer un blob à partir des chunks audio\n            const audioBlob = new Blob(_this.audioChunks, {\n              type: 'audio/webm'\n            });\n            _this.recordingComplete.emit(audioBlob);\n            _this.stopMediaTracks();\n          };\n          // Démarrer l'enregistrement\n          _this.mediaRecorder.start();\n          _this.isRecording = true;\n          // Démarrer le timer\n          _this.startTimer();\n          // Arrêter automatiquement après la durée maximale\n          setTimeout(() => {\n            if (_this.isRecording) {\n              _this.stopRecording();\n            }\n          }, _this.maxDuration * 1000);\n        } catch (error) {\n          _this.logger.error('Error starting voice recording:', error);\n          _this.isRecording = false;\n        }\n      })();\n    }\n    /**\n     * Arrête l'enregistrement vocal\n     */\n    stopRecording() {\n      if (this.mediaRecorder && this.isRecording) {\n        this.mediaRecorder.stop();\n        this.isRecording = false;\n        this.stopTimer();\n      }\n    }\n    /**\n     * Annule l'enregistrement vocal\n     */\n    cancelRecording() {\n      this.stopRecording();\n      this.stopMediaTracks();\n      this.recordingCancelled.emit();\n    }\n    /**\n     * Démarre le timer pour afficher la durée d'enregistrement\n     */\n    startTimer() {\n      this.stopTimer();\n      this.timerInterval = setInterval(() => {\n        this.recordingTime++;\n        // Arrêter si on atteint la durée maximale\n        if (this.recordingTime >= this.maxDuration) {\n          this.stopRecording();\n        }\n      }, 1000);\n    }\n    /**\n     * Arrête le timer\n     */\n    stopTimer() {\n      if (this.timerInterval) {\n        clearInterval(this.timerInterval);\n        this.timerInterval = null;\n      }\n    }\n    /**\n     * Arrête les pistes média pour libérer le microphone\n     */\n    stopMediaTracks() {\n      if (this.audioStream) {\n        this.audioStream.getTracks().forEach(track => track.stop());\n        this.audioStream = null;\n      }\n    }\n    /**\n     * Formate le temps d'enregistrement en MM:SS\n     */\n    get formattedTime() {\n      const minutes = Math.floor(this.recordingTime / 60);\n      const seconds = this.recordingTime % 60;\n      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    }\n    static {\n      this.ɵfac = function VoiceRecorderComponent_Factory(t) {\n        return new (t || VoiceRecorderComponent)(i0.ɵɵdirectiveInject(i1.LoggerService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: VoiceRecorderComponent,\n        selectors: [[\"app-voice-recorder\"]],\n        inputs: {\n          maxDuration: \"maxDuration\"\n        },\n        outputs: {\n          recordingComplete: \"recordingComplete\",\n          recordingCancelled: \"recordingCancelled\"\n        },\n        decls: 3,\n        vars: 2,\n        consts: [[1, \"whatsapp-voice-recorder\"], [\"class\", \"whatsapp-voice-container\", 4, \"ngIf\"], [\"class\", \"whatsapp-voice-start-button\", 3, \"click\", 4, \"ngIf\"], [1, \"whatsapp-voice-container\"], [1, \"whatsapp-voice-info\"], [1, \"whatsapp-recording-indicator\"], [1, \"whatsapp-recording-dot\"], [1, \"whatsapp-waveform\"], [\"class\", \"whatsapp-waveform-bar\", 3, \"height\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"whatsapp-recording-time\"], [1, \"whatsapp-voice-controls\"], [1, \"whatsapp-voice-stop-button\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"whatsapp-voice-cancel-button\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [1, \"whatsapp-waveform-bar\"], [1, \"whatsapp-voice-start-button\", 3, \"click\"], [1, \"fas\", \"fa-microphone\"]],\n        template: function VoiceRecorderComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, VoiceRecorderComponent_div_1_Template, 13, 3, \"div\", 1);\n            i0.ɵɵtemplate(2, VoiceRecorderComponent_button_2_Template, 2, 0, \"button\", 2);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isRecording);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isRecording);\n          }\n        },\n        dependencies: [i2.NgForOf, i2.NgIf],\n        styles: [\".voice-recorder[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;padding:10px;width:100%}.voice-recorder[_ngcontent-%COMP%]   .recording-status[_ngcontent-%COMP%]{display:flex;align-items:center;border-radius:var(--border-radius-full);padding:8px 15px;transition:all var(--transition-medium);background-color:#00f7ff0d;border:1px solid rgba(0,247,255,.1);width:100%;position:relative;overflow:hidden}.voice-recorder[_ngcontent-%COMP%]   .recording-status[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:1px;background:linear-gradient(90deg,transparent,var(--accent-color),transparent);opacity:.3}.voice-recorder[_ngcontent-%COMP%]   .recording-status.active[_ngcontent-%COMP%]{background-color:#ff35471a;border:1px solid rgba(255,53,71,.3);box-shadow:0 0 15px #ff35471a;animation:_ngcontent-%COMP%_glow-pulse 2s infinite}.voice-recorder[_ngcontent-%COMP%]   .recording-status.active[_ngcontent-%COMP%]:before{background:linear-gradient(90deg,transparent,#ff3547,transparent)}@keyframes _ngcontent-%COMP%_glow-pulse{0%{box-shadow:0 0 5px #ff35471a}50%{box-shadow:0 0 15px #ff35474d}to{box-shadow:0 0 5px #ff35471a}}.voice-recorder[_ngcontent-%COMP%]   .recording-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;margin-right:15px;background-color:#ff35471a;padding:5px 10px;border-radius:var(--border-radius-full);-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px)}.voice-recorder[_ngcontent-%COMP%]   .recording-indicator[_ngcontent-%COMP%]   .recording-dot[_ngcontent-%COMP%]{width:12px;height:12px;background:linear-gradient(135deg,#ff3547,#ff5252);border-radius:50%;margin-right:8px;position:relative;animation:_ngcontent-%COMP%_pulse 1.5s infinite}.voice-recorder[_ngcontent-%COMP%]   .recording-indicator[_ngcontent-%COMP%]   .recording-dot[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:18px;height:18px;background:radial-gradient(circle,rgba(255,53,71,.5) 0%,transparent 70%);border-radius:50%;animation:_ngcontent-%COMP%_pulse-glow 1.5s infinite}@keyframes _ngcontent-%COMP%_pulse-glow{0%{opacity:.3;transform:translate(-50%,-50%) scale(1)}50%{opacity:.6;transform:translate(-50%,-50%) scale(1.5)}to{opacity:.3;transform:translate(-50%,-50%) scale(1)}}.voice-recorder[_ngcontent-%COMP%]   .recording-indicator[_ngcontent-%COMP%]   .recording-time[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#ff3547;letter-spacing:.5px}.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]{display:flex;align-items:center;margin-left:auto}.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-record[_ngcontent-%COMP%]{width:45px;height:45px;border-radius:50%;background:linear-gradient(135deg,var(--accent-color),var(--secondary-color));color:var(--text-light);border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all var(--transition-medium);position:relative;overflow:hidden;box-shadow:0 0 15px #00f7ff4d}.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-record[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:radial-gradient(circle at center,rgba(255,255,255,.2) 0%,transparent 70%);opacity:0;transition:opacity var(--transition-fast)}.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-record[_ngcontent-%COMP%]:hover:before{opacity:1}.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-record[_ngcontent-%COMP%]:hover{transform:translateY(-3px);box-shadow:0 0 20px #00f7ff80}.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-record.recording[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff3547,#ff5252);box-shadow:0 0 15px #ff35474d}.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-record.recording[_ngcontent-%COMP%]:hover{box-shadow:0 0 20px #ff354780}.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-record[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:18px;position:relative;z-index:2}.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]{width:35px;height:35px;border-radius:50%;background-color:#ff35471a;color:#ff3547;border:1px solid rgba(255,53,71,.3);display:flex;align-items:center;justify-content:center;cursor:pointer;margin-left:12px;transition:all var(--transition-medium);position:relative;overflow:hidden}.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:radial-gradient(circle at center,rgba(255,255,255,.1) 0%,transparent 70%);opacity:0;transition:opacity var(--transition-fast)}.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]:hover:before{opacity:1}.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]:hover{background-color:#ff3547;color:#fff;transform:translateY(-3px);box-shadow:0 0 15px #ff35474d;border-color:transparent}.voice-recorder[_ngcontent-%COMP%]   .recording-controls[_ngcontent-%COMP%]   .btn-cancel[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px;position:relative;z-index:2}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1;transform:scale(1)}50%{opacity:.7;transform:scale(1.2)}to{opacity:1;transform:scale(1)}}\", \"\\n\\n  .whatsapp-voice-recorder[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    width: 100%;\\n    position: relative;\\n  }\\n\\n  .whatsapp-voice-container[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    width: 100%;\\n    background-color: #f0f2f5;\\n    border-radius: 24px;\\n    padding: 8px 12px;\\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .dark[_nghost-%COMP%]   .whatsapp-voice-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-voice-container[_ngcontent-%COMP%] {\\n    background-color: #2a2a2a;\\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\\n  }\\n\\n  .whatsapp-voice-info[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    flex: 1;\\n  }\\n\\n  .whatsapp-recording-indicator[_ngcontent-%COMP%] {\\n    position: relative;\\n    margin-right: 12px;\\n  }\\n\\n  .whatsapp-recording-dot[_ngcontent-%COMP%] {\\n    width: 10px;\\n    height: 10px;\\n    background-color: #ff3b30;\\n    border-radius: 50%;\\n    animation: _ngcontent-%COMP%_whatsapp-pulse 1.5s infinite;\\n  }\\n\\n  .whatsapp-waveform[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    height: 32px;\\n    margin-right: 12px;\\n    gap: 2px;\\n  }\\n\\n  .whatsapp-waveform-bar[_ngcontent-%COMP%] {\\n    width: 3px;\\n    background-color: #25d366;\\n    border-radius: 1.5px;\\n    animation: _ngcontent-%COMP%_whatsapp-wave 1.5s ease-in-out infinite;\\n  }\\n\\n  .dark[_nghost-%COMP%]   .whatsapp-waveform-bar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-waveform-bar[_ngcontent-%COMP%] {\\n    background-color: #00c853;\\n  }\\n\\n  .whatsapp-recording-time[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    font-weight: 500;\\n    color: #333;\\n  }\\n\\n  .dark[_nghost-%COMP%]   .whatsapp-recording-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-recording-time[_ngcontent-%COMP%] {\\n    color: #e0e0e0;\\n  }\\n\\n  .whatsapp-voice-controls[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    gap: 8px;\\n  }\\n\\n  .whatsapp-voice-stop-button[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n    border-radius: 50%;\\n    background-color: #25d366;\\n    color: white;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    border: none;\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n  }\\n\\n  .whatsapp-voice-stop-button[_ngcontent-%COMP%]:hover {\\n    background-color: #128c7e;\\n  }\\n\\n  .whatsapp-voice-cancel-button[_ngcontent-%COMP%] {\\n    width: 36px;\\n    height: 36px;\\n    border-radius: 50%;\\n    background-color: #f0f2f5;\\n    color: #888;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    border: none;\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n  }\\n\\n  .dark[_nghost-%COMP%]   .whatsapp-voice-cancel-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-voice-cancel-button[_ngcontent-%COMP%] {\\n    background-color: #3a3a3a;\\n    color: #ccc;\\n  }\\n\\n  .whatsapp-voice-cancel-button[_ngcontent-%COMP%]:hover {\\n    background-color: #e0e0e0;\\n    color: #666;\\n  }\\n\\n  .dark[_nghost-%COMP%]   .whatsapp-voice-cancel-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .whatsapp-voice-cancel-button[_ngcontent-%COMP%]:hover {\\n    background-color: #444;\\n    color: #fff;\\n  }\\n\\n  .whatsapp-voice-start-button[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    border-radius: 50%;\\n    background-color: #25d366;\\n    color: white;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    border: none;\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n  }\\n\\n  .whatsapp-voice-start-button[_ngcontent-%COMP%]:hover {\\n    background-color: #128c7e;\\n  }\\n\\n  @keyframes _ngcontent-%COMP%_whatsapp-pulse {\\n    0% {\\n      opacity: 1;\\n      transform: scale(1);\\n    }\\n    50% {\\n      opacity: 0.5;\\n      transform: scale(1.2);\\n    }\\n    100% {\\n      opacity: 1;\\n      transform: scale(1);\\n    }\\n  }\\n\\n  @keyframes _ngcontent-%COMP%_whatsapp-wave {\\n    0%,\\n    100% {\\n      transform: scaleY(0.3);\\n    }\\n    50% {\\n      transform: scaleY(1);\\n    }\\n  }\"]\n      });\n    }\n  }\n  return VoiceRecorderComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}