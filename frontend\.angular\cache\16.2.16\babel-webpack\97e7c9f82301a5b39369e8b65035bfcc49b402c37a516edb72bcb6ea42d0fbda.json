{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, Inject, QueryList, Directive, Input, InjectionToken, Optional, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getFocusedElementPierceShadowDom, normalizePassiveListenerOptions, _getEventTarget, _getShadowRoot } from '@angular/cdk/platform';\nimport { Subject, Subscription, BehaviorSubject, of } from 'rxjs';\nimport { hasModifierKey, A, Z, ZERO, NINE, PAGE_DOWN, PAGE_UP, END, HOME, LEFT_ARROW, RIGHT_ARROW, UP_ARROW, DOWN_ARROW, TAB, ALT, CONTROL, MAC_META, META, SHIFT } from '@angular/cdk/keycodes';\nimport { tap, debounceTime, filter, map, take, skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceElement } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport { BreakpointObserver } from '@angular/cdk/layout';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  if (ids.some(existingId => existingId.trim() == id.trim())) {\n    return;\n  }\n  ids.push(id.trim());\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  const filteredIds = ids.filter(val => val != id.trim());\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  return (el.getAttribute(attr) || '').match(/\\S+/g) || [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nlet AriaDescriber = /*#__PURE__*/(() => {\n  class AriaDescriber {\n    constructor(_document,\n    /**\n     * @deprecated To be turned into a required parameter.\n     * @breaking-change 14.0.0\n     */\n    _platform) {\n      this._platform = _platform;\n      /** Map of all registered message elements that have been placed into the document. */\n      this._messageRegistry = new Map();\n      /** Container for all registered messages. */\n      this._messagesContainer = null;\n      /** Unique ID for the service. */\n      this._id = `${nextId++}`;\n      this._document = _document;\n      this._id = inject(APP_ID) + '-' + nextId++;\n    }\n    describe(hostElement, message, role) {\n      if (!this._canBeDescribed(hostElement, message)) {\n        return;\n      }\n      const key = getKey(message, role);\n      if (typeof message !== 'string') {\n        // We need to ensure that the element has an ID.\n        setMessageId(message, this._id);\n        this._messageRegistry.set(key, {\n          messageElement: message,\n          referenceCount: 0\n        });\n      } else if (!this._messageRegistry.has(key)) {\n        this._createMessageElement(message, role);\n      }\n      if (!this._isElementDescribedByMessage(hostElement, key)) {\n        this._addMessageReference(hostElement, key);\n      }\n    }\n    removeDescription(hostElement, message, role) {\n      if (!message || !this._isElementNode(hostElement)) {\n        return;\n      }\n      const key = getKey(message, role);\n      if (this._isElementDescribedByMessage(hostElement, key)) {\n        this._removeMessageReference(hostElement, key);\n      }\n      // If the message is a string, it means that it's one that we created for the\n      // consumer so we can remove it safely, otherwise we should leave it in place.\n      if (typeof message === 'string') {\n        const registeredMessage = this._messageRegistry.get(key);\n        if (registeredMessage && registeredMessage.referenceCount === 0) {\n          this._deleteMessageElement(key);\n        }\n      }\n      if (this._messagesContainer?.childNodes.length === 0) {\n        this._messagesContainer.remove();\n        this._messagesContainer = null;\n      }\n    }\n    /** Unregisters all created message elements and removes the message container. */\n    ngOnDestroy() {\n      const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n      for (let i = 0; i < describedElements.length; i++) {\n        this._removeCdkDescribedByReferenceIds(describedElements[i]);\n        describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n      }\n      this._messagesContainer?.remove();\n      this._messagesContainer = null;\n      this._messageRegistry.clear();\n    }\n    /**\n     * Creates a new element in the visually hidden message container element with the message\n     * as its content and adds it to the message registry.\n     */\n    _createMessageElement(message, role) {\n      const messageElement = this._document.createElement('div');\n      setMessageId(messageElement, this._id);\n      messageElement.textContent = message;\n      if (role) {\n        messageElement.setAttribute('role', role);\n      }\n      this._createMessagesContainer();\n      this._messagesContainer.appendChild(messageElement);\n      this._messageRegistry.set(getKey(message, role), {\n        messageElement,\n        referenceCount: 0\n      });\n    }\n    /** Deletes the message element from the global messages container. */\n    _deleteMessageElement(key) {\n      this._messageRegistry.get(key)?.messageElement?.remove();\n      this._messageRegistry.delete(key);\n    }\n    /** Creates the global container for all aria-describedby messages. */\n    _createMessagesContainer() {\n      if (this._messagesContainer) {\n        return;\n      }\n      const containerClassName = 'cdk-describedby-message-container';\n      const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n      for (let i = 0; i < serverContainers.length; i++) {\n        // When going from the server to the client, we may end up in a situation where there's\n        // already a container on the page, but we don't have a reference to it. Clear the\n        // old container so we don't get duplicates. Doing this, instead of emptying the previous\n        // container, should be slightly faster.\n        serverContainers[i].remove();\n      }\n      const messagesContainer = this._document.createElement('div');\n      // We add `visibility: hidden` in order to prevent text in this container from\n      // being searchable by the browser's Ctrl + F functionality.\n      // Screen-readers will still read the description for elements with aria-describedby even\n      // when the description element is not visible.\n      messagesContainer.style.visibility = 'hidden';\n      // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n      // the description element doesn't impact page layout.\n      messagesContainer.classList.add(containerClassName);\n      messagesContainer.classList.add('cdk-visually-hidden');\n      // @breaking-change 14.0.0 Remove null check for `_platform`.\n      if (this._platform && !this._platform.isBrowser) {\n        messagesContainer.setAttribute('platform', 'server');\n      }\n      this._document.body.appendChild(messagesContainer);\n      this._messagesContainer = messagesContainer;\n    }\n    /** Removes all cdk-describedby messages that are hosted through the element. */\n    _removeCdkDescribedByReferenceIds(element) {\n      // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n      const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n      element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n    }\n    /**\n     * Adds a message reference to the element using aria-describedby and increments the registered\n     * message's reference count.\n     */\n    _addMessageReference(element, key) {\n      const registeredMessage = this._messageRegistry.get(key);\n      // Add the aria-describedby reference and set the\n      // describedby_host attribute to mark the element.\n      addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n      element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n      registeredMessage.referenceCount++;\n    }\n    /**\n     * Removes a message reference from the element using aria-describedby\n     * and decrements the registered message's reference count.\n     */\n    _removeMessageReference(element, key) {\n      const registeredMessage = this._messageRegistry.get(key);\n      registeredMessage.referenceCount--;\n      removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n      element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    /** Returns true if the element has been described by the provided message ID. */\n    _isElementDescribedByMessage(element, key) {\n      const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n      const registeredMessage = this._messageRegistry.get(key);\n      const messageId = registeredMessage && registeredMessage.messageElement.id;\n      return !!messageId && referenceIds.indexOf(messageId) != -1;\n    }\n    /** Determines whether a message can be described on a particular element. */\n    _canBeDescribed(element, message) {\n      if (!this._isElementNode(element)) {\n        return false;\n      }\n      if (message && typeof message === 'object') {\n        // We'd have to make some assumptions about the description element's text, if the consumer\n        // passed in an element. Assume that if an element is passed in, the consumer has verified\n        // that it can be used as a description.\n        return true;\n      }\n      const trimmedMessage = message == null ? '' : `${message}`.trim();\n      const ariaLabel = element.getAttribute('aria-label');\n      // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n      // element, because screen readers will end up reading out the same text twice in a row.\n      return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n    }\n    /** Checks whether a node is an Element node. */\n    _isElementNode(element) {\n      return element.nodeType === this._document.ELEMENT_NODE;\n    }\n    static {\n      this.ɵfac = function AriaDescriber_Factory(t) {\n        return new (t || AriaDescriber)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.Platform));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: AriaDescriber,\n        factory: AriaDescriber.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AriaDescriber;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n  }\n}\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n  constructor(_items) {\n    this._items = _items;\n    this._activeItemIndex = -1;\n    this._activeItem = null;\n    this._wrap = false;\n    this._letterKeyStream = new Subject();\n    this._typeaheadSubscription = Subscription.EMPTY;\n    this._vertical = true;\n    this._allowedModifierKeys = [];\n    this._homeAndEnd = false;\n    this._pageUpAndDown = {\n      enabled: false,\n      delta: 10\n    };\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager. By default, disabled items are skipped.\n     */\n    this._skipPredicateFn = item => item.disabled;\n    // Buffer for the letters that the user has pressed when the typeahead option is turned on.\n    this._pressedLetters = [];\n    /**\n     * Stream that emits any time the TAB key is pressed, so components can react\n     * when focus is shifted off of the list.\n     */\n    this.tabOut = new Subject();\n    /** Stream that emits whenever the active item of the list manager changes. */\n    this.change = new Subject();\n    // We allow for the items to be an array because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (_items instanceof QueryList) {\n      this._itemChangesSubscription = _items.changes.subscribe(newItems => {\n        if (this._activeItem) {\n          const itemArray = newItems.toArray();\n          const newIndex = itemArray.indexOf(this._activeItem);\n          if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n            this._activeItemIndex = newIndex;\n          }\n        }\n      });\n    }\n  }\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n  skipPredicate(predicate) {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n  /**\n   * Configures wrapping mode, which determines whether the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   * @param shouldWrap Whether the list should wrap when reaching the end.\n   */\n  withWrap(shouldWrap = true) {\n    this._wrap = shouldWrap;\n    return this;\n  }\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n  withVerticalOrientation(enabled = true) {\n    this._vertical = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n  withHorizontalOrientation(direction) {\n    this._horizontal = direction;\n    return this;\n  }\n  /**\n   * Modifier keys which are allowed to be held down and whose default actions will be prevented\n   * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n   */\n  withAllowedModifierKeys(keys) {\n    this._allowedModifierKeys = keys;\n    return this;\n  }\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n  withTypeAhead(debounceInterval = 200) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && this._items.length && this._items.some(item => typeof item.getLabel !== 'function')) {\n      throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n    this._typeaheadSubscription.unsubscribe();\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._typeaheadSubscription = this._letterKeyStream.pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(debounceInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join(''))).subscribe(inputString => {\n      const items = this._getItemsArray();\n      // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n      for (let i = 1; i < items.length + 1; i++) {\n        const index = (this._activeItemIndex + i) % items.length;\n        const item = items[index];\n        if (!this._skipPredicateFn(item) && item.getLabel().toUpperCase().trim().indexOf(inputString) === 0) {\n          this.setActiveItem(index);\n          break;\n        }\n      }\n      this._pressedLetters = [];\n    });\n    return this;\n  }\n  /** Cancels the current typeahead sequence. */\n  cancelTypeahead() {\n    this._pressedLetters = [];\n    return this;\n  }\n  /**\n   * Configures the key manager to activate the first and last items\n   * respectively when the Home or End key is pressed.\n   * @param enabled Whether pressing the Home or End key activates the first/last item.\n   */\n  withHomeAndEnd(enabled = true) {\n    this._homeAndEnd = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n   * respectively when the Page-Up or Page-Down key is pressed.\n   * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n   * @param delta Whether pressing the Home or End key activates the first/last item.\n   */\n  withPageUpDown(enabled = true, delta = 10) {\n    this._pageUpAndDown = {\n      enabled,\n      delta\n    };\n    return this;\n  }\n  setActiveItem(item) {\n    const previousActiveItem = this._activeItem;\n    this.updateActiveItem(item);\n    if (this._activeItem !== previousActiveItem) {\n      this.change.next(this._activeItemIndex);\n    }\n  }\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n  onKeydown(event) {\n    const keyCode = event.keyCode;\n    const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n    const isModifierAllowed = modifiers.every(modifier => {\n      return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n    });\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n      case DOWN_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case UP_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case RIGHT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case LEFT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case HOME:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setFirstItemActive();\n          break;\n        } else {\n          return;\n        }\n      case END:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setLastItemActive();\n          break;\n        } else {\n          return;\n        }\n      case PAGE_UP:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex - this._pageUpAndDown.delta;\n          this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n          break;\n        } else {\n          return;\n        }\n      case PAGE_DOWN:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex + this._pageUpAndDown.delta;\n          const itemsLength = this._getItemsArray().length;\n          this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n          break;\n        } else {\n          return;\n        }\n      default:\n        if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n          // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n          // otherwise fall back to resolving alphanumeric characters via the keyCode.\n          if (event.key && event.key.length === 1) {\n            this._letterKeyStream.next(event.key.toLocaleUpperCase());\n          } else if (keyCode >= A && keyCode <= Z || keyCode >= ZERO && keyCode <= NINE) {\n            this._letterKeyStream.next(String.fromCharCode(keyCode));\n          }\n        }\n        // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n        return;\n    }\n    this._pressedLetters = [];\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  get activeItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The active item. */\n  get activeItem() {\n    return this._activeItem;\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return this._pressedLetters.length > 0;\n  }\n  /** Sets the active item to the first enabled item in the list. */\n  setFirstItemActive() {\n    this._setActiveItemByIndex(0, 1);\n  }\n  /** Sets the active item to the last enabled item in the list. */\n  setLastItemActive() {\n    this._setActiveItemByIndex(this._items.length - 1, -1);\n  }\n  /** Sets the active item to the next enabled item in the list. */\n  setNextItemActive() {\n    this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n  /** Sets the active item to a previous enabled item in the list. */\n  setPreviousItemActive() {\n    this._activeItemIndex < 0 && this._wrap ? this.setLastItemActive() : this._setActiveItemByDelta(-1);\n  }\n  updateActiveItem(item) {\n    const itemArray = this._getItemsArray();\n    const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n    const activeItem = itemArray[index];\n    // Explicitly check for `null` and `undefined` because other falsy values are valid.\n    this._activeItem = activeItem == null ? null : activeItem;\n    this._activeItemIndex = index;\n  }\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._itemChangesSubscription?.unsubscribe();\n    this._letterKeyStream.complete();\n    this.tabOut.complete();\n    this.change.complete();\n    this._pressedLetters = [];\n  }\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n  _setActiveItemByDelta(delta) {\n    this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n  }\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n  _setActiveInWrapMode(delta) {\n    const items = this._getItemsArray();\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n      const item = items[index];\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n  _setActiveInDefaultMode(delta) {\n    this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n  }\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n  _setActiveItemByIndex(index, fallbackDelta) {\n    const items = this._getItemsArray();\n    if (!items[index]) {\n      return;\n    }\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n      if (!items[index]) {\n        return;\n      }\n    }\n    this.setActiveItem(index);\n  }\n  /** Returns the items as an array. */\n  _getItemsArray() {\n    return this._items instanceof QueryList ? this._items.toArray() : this._items;\n  }\n}\nclass ActiveDescendantKeyManager extends ListKeyManager {\n  setActiveItem(index) {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n    super.setActiveItem(index);\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n}\nclass FocusKeyManager extends ListKeyManager {\n  constructor() {\n    super(...arguments);\n    this._origin = 'program';\n  }\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n  setFocusOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  setActiveItem(item) {\n    super.setActiveItem(item);\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n}\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n  constructor() {\n    /**\n     * Whether to count an element as focusable even if it is not currently visible.\n     */\n    this.ignoreVisibility = false;\n  }\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nlet InteractivityChecker = /*#__PURE__*/(() => {\n  class InteractivityChecker {\n    constructor(_platform) {\n      this._platform = _platform;\n    }\n    /**\n     * Gets whether an element is disabled.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is disabled.\n     */\n    isDisabled(element) {\n      // This does not capture some cases, such as a non-form control with a disabled attribute or\n      // a form control inside of a disabled form, but should capture the most common cases.\n      return element.hasAttribute('disabled');\n    }\n    /**\n     * Gets whether an element is visible for the purposes of interactivity.\n     *\n     * This will capture states like `display: none` and `visibility: hidden`, but not things like\n     * being clipped by an `overflow: hidden` parent or being outside the viewport.\n     *\n     * @returns Whether the element is visible.\n     */\n    isVisible(element) {\n      return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n    }\n    /**\n     * Gets whether an element can be reached via Tab key.\n     * Assumes that the element has already been checked with isFocusable.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is tabbable.\n     */\n    isTabbable(element) {\n      // Nothing is tabbable on the server 😎\n      if (!this._platform.isBrowser) {\n        return false;\n      }\n      const frameElement = getFrameElement(getWindow(element));\n      if (frameElement) {\n        // Frame elements inherit their tabindex onto all child elements.\n        if (getTabIndexValue(frameElement) === -1) {\n          return false;\n        }\n        // Browsers disable tabbing to an element inside of an invisible frame.\n        if (!this.isVisible(frameElement)) {\n          return false;\n        }\n      }\n      let nodeName = element.nodeName.toLowerCase();\n      let tabIndexValue = getTabIndexValue(element);\n      if (element.hasAttribute('contenteditable')) {\n        return tabIndexValue !== -1;\n      }\n      if (nodeName === 'iframe' || nodeName === 'object') {\n        // The frame or object's content may be tabbable depending on the content, but it's\n        // not possibly to reliably detect the content of the frames. We always consider such\n        // elements as non-tabbable.\n        return false;\n      }\n      // In iOS, the browser only considers some specific elements as tabbable.\n      if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n        return false;\n      }\n      if (nodeName === 'audio') {\n        // Audio elements without controls enabled are never tabbable, regardless\n        // of the tabindex attribute explicitly being set.\n        if (!element.hasAttribute('controls')) {\n          return false;\n        }\n        // Audio elements with controls are by default tabbable unless the\n        // tabindex attribute is set to `-1` explicitly.\n        return tabIndexValue !== -1;\n      }\n      if (nodeName === 'video') {\n        // For all video elements, if the tabindex attribute is set to `-1`, the video\n        // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n        // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n        // tabindex attribute is the source of truth here.\n        if (tabIndexValue === -1) {\n          return false;\n        }\n        // If the tabindex is explicitly set, and not `-1` (as per check before), the\n        // video element is always tabbable (regardless of whether it has controls or not).\n        if (tabIndexValue !== null) {\n          return true;\n        }\n        // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n        // has controls enabled. Firefox is special as videos are always tabbable regardless\n        // of whether there are controls or not.\n        return this._platform.FIREFOX || element.hasAttribute('controls');\n      }\n      return element.tabIndex >= 0;\n    }\n    /**\n     * Gets whether an element can be focused by the user.\n     *\n     * @param element Element to be checked.\n     * @param config The config object with options to customize this method's behavior\n     * @returns Whether the element is focusable.\n     */\n    isFocusable(element, config) {\n      // Perform checks in order of left to most expensive.\n      // Again, naive approach that does not capture many edge cases and browser quirks.\n      return isPotentiallyFocusable(element) && !this.isDisabled(element) && (config?.ignoreVisibility || this.isVisible(element));\n    }\n    static {\n      this.ɵfac = function InteractivityChecker_Factory(t) {\n        return new (t || InteractivityChecker)(i0.ɵɵinject(i1.Platform));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: InteractivityChecker,\n        factory: InteractivityChecker.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return InteractivityChecker;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n *\n * @deprecated Use `ConfigurableFocusTrap` instead.\n * @breaking-change 11.0.0\n */\nclass FocusTrap {\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false) {\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._hasAttached = false;\n    // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n    this.startAnchorListener = () => this.focusLastTabbableElement();\n    this.endAnchorListener = () => this.focusFirstTabbableElement();\n    this._enabled = true;\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n      this._hasAttached = true;\n    }\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusInitialElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusFirstTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusLastTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n        }\n      }\n    }\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n  focusInitialElement(options) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      }\n      // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._checker.isFocusable(redirectToElement)) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n        focusableChild?.focus(options);\n        return !!focusableChild;\n      }\n      redirectToElement.focus(options);\n      return true;\n    }\n    return this.focusFirstTabbableElement(options);\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusFirstTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('start');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusLastTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('end');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    const children = root.children;\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    // Iterate in reverse DOM order.\n    const children = root.children;\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Creates an anchor element. */\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n  _executeOnStable(fn) {\n    if (this._ngZone.isStable) {\n      fn();\n    } else {\n      this._ngZone.onStable.pipe(take(1)).subscribe(fn);\n    }\n  }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n * @deprecated Use `ConfigurableFocusTrapFactory` instead.\n * @breaking-change 11.0.0\n */\nlet FocusTrapFactory = /*#__PURE__*/(() => {\n  class FocusTrapFactory {\n    constructor(_checker, _ngZone, _document) {\n      this._checker = _checker;\n      this._ngZone = _ngZone;\n      this._document = _document;\n    }\n    /**\n     * Creates a focus-trapped region around the given element.\n     * @param element The element around which focus will be trapped.\n     * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n     *     manually by the user.\n     * @returns The created focus trap instance.\n     */\n    create(element, deferCaptureElements = false) {\n      return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements);\n    }\n    static {\n      this.ɵfac = function FocusTrapFactory_Factory(t) {\n        return new (t || FocusTrapFactory)(i0.ɵɵinject(InteractivityChecker), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: FocusTrapFactory,\n        factory: FocusTrapFactory.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return FocusTrapFactory;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Directive for trapping focus within a region. */\nlet CdkTrapFocus = /*#__PURE__*/(() => {\n  class CdkTrapFocus {\n    /** Whether the focus trap is active. */\n    get enabled() {\n      return this.focusTrap.enabled;\n    }\n    set enabled(value) {\n      this.focusTrap.enabled = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the directive should automatically move focus into the trapped region upon\n     * initialization and return focus to the previous activeElement upon destruction.\n     */\n    get autoCapture() {\n      return this._autoCapture;\n    }\n    set autoCapture(value) {\n      this._autoCapture = coerceBooleanProperty(value);\n    }\n    constructor(_elementRef, _focusTrapFactory,\n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 13.0.0\n     */\n    _document) {\n      this._elementRef = _elementRef;\n      this._focusTrapFactory = _focusTrapFactory;\n      /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n      this._previouslyFocusedElement = null;\n      this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n    ngOnDestroy() {\n      this.focusTrap.destroy();\n      // If we stored a previously focused element when using autoCapture, return focus to that\n      // element now that the trapped region is being destroyed.\n      if (this._previouslyFocusedElement) {\n        this._previouslyFocusedElement.focus();\n        this._previouslyFocusedElement = null;\n      }\n    }\n    ngAfterContentInit() {\n      this.focusTrap.attachAnchors();\n      if (this.autoCapture) {\n        this._captureFocus();\n      }\n    }\n    ngDoCheck() {\n      if (!this.focusTrap.hasAttached()) {\n        this.focusTrap.attachAnchors();\n      }\n    }\n    ngOnChanges(changes) {\n      const autoCaptureChange = changes['autoCapture'];\n      if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && this.focusTrap.hasAttached()) {\n        this._captureFocus();\n      }\n    }\n    _captureFocus() {\n      this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n      this.focusTrap.focusInitialElementWhenReady();\n    }\n    static {\n      this.ɵfac = function CdkTrapFocus_Factory(t) {\n        return new (t || CdkTrapFocus)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkTrapFocus,\n        selectors: [[\"\", \"cdkTrapFocus\", \"\"]],\n        inputs: {\n          enabled: [\"cdkTrapFocus\", \"enabled\"],\n          autoCapture: [\"cdkTrapFocusAutoCapture\", \"autoCapture\"]\n        },\n        exportAs: [\"cdkTrapFocus\"],\n        features: [i0.ɵɵNgOnChangesFeature]\n      });\n    }\n  }\n  return CdkTrapFocus;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n  /** Whether the FocusTrap is enabled. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config) {\n    super(_element, _checker, _ngZone, _document, config.defer);\n    this._focusTrapManager = _focusTrapManager;\n    this._inertStrategy = _inertStrategy;\n    this._focusTrapManager.register(this);\n  }\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n  destroy() {\n    this._focusTrapManager.deregister(this);\n    super.destroy();\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n    this.toggleAnchors(true);\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n    this.toggleAnchors(false);\n  }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = /*#__PURE__*/new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n  constructor() {\n    /** Focus event handler. */\n    this._listener = null;\n  }\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n  preventFocus(focusTrap) {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener, true);\n    }\n    this._listener = e => this._trapFocus(focusTrap, e);\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener, true);\n    });\n  }\n  /** Removes the event listener added in preventFocus. */\n  allowFocus(focusTrap) {\n    if (!this._listener) {\n      return;\n    }\n    focusTrap._document.removeEventListener('focus', this._listener, true);\n    this._listener = null;\n  }\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n  _trapFocus(focusTrap, event) {\n    const target = event.target;\n    const focusTrapRoot = focusTrap._element;\n    // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n    if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n}\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nlet FocusTrapManager = /*#__PURE__*/(() => {\n  class FocusTrapManager {\n    constructor() {\n      // A stack of the FocusTraps on the page. Only the FocusTrap at the\n      // top of the stack is active.\n      this._focusTrapStack = [];\n    }\n    /**\n     * Disables the FocusTrap at the top of the stack, and then pushes\n     * the new FocusTrap onto the stack.\n     */\n    register(focusTrap) {\n      // Dedupe focusTraps that register multiple times.\n      this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n      let stack = this._focusTrapStack;\n      if (stack.length) {\n        stack[stack.length - 1]._disable();\n      }\n      stack.push(focusTrap);\n      focusTrap._enable();\n    }\n    /**\n     * Removes the FocusTrap from the stack, and activates the\n     * FocusTrap that is the new top of the stack.\n     */\n    deregister(focusTrap) {\n      focusTrap._disable();\n      const stack = this._focusTrapStack;\n      const i = stack.indexOf(focusTrap);\n      if (i !== -1) {\n        stack.splice(i, 1);\n        if (stack.length) {\n          stack[stack.length - 1]._enable();\n        }\n      }\n    }\n    static {\n      this.ɵfac = function FocusTrapManager_Factory(t) {\n        return new (t || FocusTrapManager)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: FocusTrapManager,\n        factory: FocusTrapManager.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return FocusTrapManager;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nlet ConfigurableFocusTrapFactory = /*#__PURE__*/(() => {\n  class ConfigurableFocusTrapFactory {\n    constructor(_checker, _ngZone, _focusTrapManager, _document, _inertStrategy) {\n      this._checker = _checker;\n      this._ngZone = _ngZone;\n      this._focusTrapManager = _focusTrapManager;\n      this._document = _document;\n      // TODO split up the strategies into different modules, similar to DateAdapter.\n      this._inertStrategy = _inertStrategy || new EventListenerFocusTrapInertStrategy();\n    }\n    create(element, config = {\n      defer: false\n    }) {\n      let configObject;\n      if (typeof config === 'boolean') {\n        configObject = {\n          defer: config\n        };\n      } else {\n        configObject = config;\n      }\n      return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject);\n    }\n    static {\n      this.ɵfac = function ConfigurableFocusTrapFactory_Factory(t) {\n        return new (t || ConfigurableFocusTrapFactory)(i0.ɵɵinject(InteractivityChecker), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(FocusTrapManager), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(FOCUS_TRAP_INERT_STRATEGY, 8));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: ConfigurableFocusTrapFactory,\n        factory: ConfigurableFocusTrapFactory.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ConfigurableFocusTrapFactory;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n  // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n  // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n  // `event.detail` is zero depending on the browser:\n  // - `event.buttons` works on Firefox, but fails on Chrome.\n  // - `detail` works on Chrome, but fails on Firefox.\n  return event.buttons === 0 || event.detail === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n  const touch = event.touches && event.touches[0] || event.changedTouches && event.changedTouches[0];\n  // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n  // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n  // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n  // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n  return !!touch && touch.identifier === -1 && (touch.radiusX == null || touch.radiusX === 1) && (touch.radiusY == null || touch.radiusY === 1);\n}\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = /*#__PURE__*/new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT]\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nlet InputModalityDetector = /*#__PURE__*/(() => {\n  class InputModalityDetector {\n    /** The most recently detected input modality. */\n    get mostRecentModality() {\n      return this._modality.value;\n    }\n    constructor(_platform, ngZone, document, options) {\n      this._platform = _platform;\n      /**\n       * The most recently detected input modality event target. Is null if no input modality has been\n       * detected or if the associated event target is null for some unknown reason.\n       */\n      this._mostRecentTarget = null;\n      /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n      this._modality = new BehaviorSubject(null);\n      /**\n       * The timestamp of the last touch input modality. Used to determine whether mousedown events\n       * should be attributed to mouse or touch.\n       */\n      this._lastTouchMs = 0;\n      /**\n       * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n       * bound.\n       */\n      this._onKeydown = event => {\n        // If this is one of the keys we should ignore, then ignore it and don't update the input\n        // modality to keyboard.\n        if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n          return;\n        }\n        this._modality.next('keyboard');\n        this._mostRecentTarget = _getEventTarget(event);\n      };\n      /**\n       * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n       * gets bound.\n       */\n      this._onMousedown = event => {\n        // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n        // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n        // after the previous touch event.\n        if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n          return;\n        }\n        // Fake mousedown events are fired by some screen readers when controls are activated by the\n        // screen reader. Attribute them to keyboard input modality.\n        this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n        this._mostRecentTarget = _getEventTarget(event);\n      };\n      /**\n       * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n       * gets bound.\n       */\n      this._onTouchstart = event => {\n        // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n        // events are fired. Again, attribute to keyboard input modality.\n        if (isFakeTouchstartFromScreenReader(event)) {\n          this._modality.next('keyboard');\n          return;\n        }\n        // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n        // triggered via mouse vs touch.\n        this._lastTouchMs = Date.now();\n        this._modality.next('touch');\n        this._mostRecentTarget = _getEventTarget(event);\n      };\n      this._options = {\n        ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n        ...options\n      };\n      // Skip the first emission as it's null.\n      this.modalityDetected = this._modality.pipe(skip(1));\n      this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n      // If we're not in a browser, this service should do nothing, as there's no relevant input\n      // modality to detect.\n      if (_platform.isBrowser) {\n        ngZone.runOutsideAngular(() => {\n          document.addEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n          document.addEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n          document.addEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n        });\n      }\n    }\n    ngOnDestroy() {\n      this._modality.complete();\n      if (this._platform.isBrowser) {\n        document.removeEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n        document.removeEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n        document.removeEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n      }\n    }\n    static {\n      this.ɵfac = function InputModalityDetector_Factory(t) {\n        return new (t || InputModalityDetector)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(INPUT_MODALITY_DETECTOR_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: InputModalityDetector,\n        factory: InputModalityDetector.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return InputModalityDetector;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = /*#__PURE__*/new InjectionToken('liveAnnouncerElement', {\n  providedIn: 'root',\n  factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY\n});\n/** @docs-private */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n  return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\nlet uniqueIds = 0;\nlet LiveAnnouncer = /*#__PURE__*/(() => {\n  class LiveAnnouncer {\n    constructor(elementToken, _ngZone, _document, _defaultOptions) {\n      this._ngZone = _ngZone;\n      this._defaultOptions = _defaultOptions;\n      // We inject the live element and document as `any` because the constructor signature cannot\n      // reference browser globals (HTMLElement, Document) on non-browser environments, since having\n      // a class decorator causes TypeScript to preserve the constructor signature types.\n      this._document = _document;\n      this._liveElement = elementToken || this._createLiveElement();\n    }\n    announce(message, ...args) {\n      const defaultOptions = this._defaultOptions;\n      let politeness;\n      let duration;\n      if (args.length === 1 && typeof args[0] === 'number') {\n        duration = args[0];\n      } else {\n        [politeness, duration] = args;\n      }\n      this.clear();\n      clearTimeout(this._previousTimeout);\n      if (!politeness) {\n        politeness = defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n      }\n      if (duration == null && defaultOptions) {\n        duration = defaultOptions.duration;\n      }\n      // TODO: ensure changing the politeness works on all environments we support.\n      this._liveElement.setAttribute('aria-live', politeness);\n      if (this._liveElement.id) {\n        this._exposeAnnouncerToModals(this._liveElement.id);\n      }\n      // This 100ms timeout is necessary for some browser + screen-reader combinations:\n      // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n      // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n      //   second time without clearing and then using a non-zero delay.\n      // (using JAWS 17 at time of this writing).\n      return this._ngZone.runOutsideAngular(() => {\n        if (!this._currentPromise) {\n          this._currentPromise = new Promise(resolve => this._currentResolve = resolve);\n        }\n        clearTimeout(this._previousTimeout);\n        this._previousTimeout = setTimeout(() => {\n          this._liveElement.textContent = message;\n          if (typeof duration === 'number') {\n            this._previousTimeout = setTimeout(() => this.clear(), duration);\n          }\n          this._currentResolve();\n          this._currentPromise = this._currentResolve = undefined;\n        }, 100);\n        return this._currentPromise;\n      });\n    }\n    /**\n     * Clears the current text from the announcer element. Can be used to prevent\n     * screen readers from reading the text out again while the user is going\n     * through the page landmarks.\n     */\n    clear() {\n      if (this._liveElement) {\n        this._liveElement.textContent = '';\n      }\n    }\n    ngOnDestroy() {\n      clearTimeout(this._previousTimeout);\n      this._liveElement?.remove();\n      this._liveElement = null;\n      this._currentResolve?.();\n      this._currentPromise = this._currentResolve = undefined;\n    }\n    _createLiveElement() {\n      const elementClass = 'cdk-live-announcer-element';\n      const previousElements = this._document.getElementsByClassName(elementClass);\n      const liveEl = this._document.createElement('div');\n      // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n      for (let i = 0; i < previousElements.length; i++) {\n        previousElements[i].remove();\n      }\n      liveEl.classList.add(elementClass);\n      liveEl.classList.add('cdk-visually-hidden');\n      liveEl.setAttribute('aria-atomic', 'true');\n      liveEl.setAttribute('aria-live', 'polite');\n      liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n      this._document.body.appendChild(liveEl);\n      return liveEl;\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live announcer element if there is an\n     * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live announcer element.\n     */\n    _exposeAnnouncerToModals(id) {\n      // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n      // the `SnakBarContainer` and other usages.\n      //\n      // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n      // section of the DOM we need to look through. This should cover all the cases we support, but\n      // the selector can be expanded if it turns out to be too narrow.\n      const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n      for (let i = 0; i < modals.length; i++) {\n        const modal = modals[i];\n        const ariaOwns = modal.getAttribute('aria-owns');\n        if (!ariaOwns) {\n          modal.setAttribute('aria-owns', id);\n        } else if (ariaOwns.indexOf(id) === -1) {\n          modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n        }\n      }\n    }\n    static {\n      this.ɵfac = function LiveAnnouncer_Factory(t) {\n        return new (t || LiveAnnouncer)(i0.ɵɵinject(LIVE_ANNOUNCER_ELEMENT_TOKEN, 8), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: LiveAnnouncer,\n        factory: LiveAnnouncer.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return LiveAnnouncer;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nlet CdkAriaLive = /*#__PURE__*/(() => {\n  class CdkAriaLive {\n    /** The aria-live politeness level to use when announcing messages. */\n    get politeness() {\n      return this._politeness;\n    }\n    set politeness(value) {\n      this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n      if (this._politeness === 'off') {\n        if (this._subscription) {\n          this._subscription.unsubscribe();\n          this._subscription = null;\n        }\n      } else if (!this._subscription) {\n        this._subscription = this._ngZone.runOutsideAngular(() => {\n          return this._contentObserver.observe(this._elementRef).subscribe(() => {\n            // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n            const elementText = this._elementRef.nativeElement.textContent;\n            // The `MutationObserver` fires also for attribute\n            // changes which we don't want to announce.\n            if (elementText !== this._previousAnnouncedText) {\n              this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n              this._previousAnnouncedText = elementText;\n            }\n          });\n        });\n      }\n    }\n    constructor(_elementRef, _liveAnnouncer, _contentObserver, _ngZone) {\n      this._elementRef = _elementRef;\n      this._liveAnnouncer = _liveAnnouncer;\n      this._contentObserver = _contentObserver;\n      this._ngZone = _ngZone;\n      this._politeness = 'polite';\n    }\n    ngOnDestroy() {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n      }\n    }\n    static {\n      this.ɵfac = function CdkAriaLive_Factory(t) {\n        return new (t || CdkAriaLive)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(LiveAnnouncer), i0.ɵɵdirectiveInject(i1$1.ContentObserver), i0.ɵɵdirectiveInject(i0.NgZone));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkAriaLive,\n        selectors: [[\"\", \"cdkAriaLive\", \"\"]],\n        inputs: {\n          politeness: [\"cdkAriaLive\", \"politeness\"],\n          duration: [\"cdkAriaLiveDuration\", \"duration\"]\n        },\n        exportAs: [\"cdkAriaLive\"]\n      });\n    }\n  }\n  return CdkAriaLive;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = /*#__PURE__*/new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nlet FocusMonitor = /*#__PURE__*/(() => {\n  class FocusMonitor {\n    constructor(_ngZone, _platform, _inputModalityDetector, /** @breaking-change 11.0.0 make document required */\n    document, options) {\n      this._ngZone = _ngZone;\n      this._platform = _platform;\n      this._inputModalityDetector = _inputModalityDetector;\n      /** The focus origin that the next focus event is a result of. */\n      this._origin = null;\n      /** Whether the window has just been focused. */\n      this._windowFocused = false;\n      /**\n       * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n       * focus events to touch interactions requires special logic.\n       */\n      this._originFromTouchInteraction = false;\n      /** Map of elements being monitored to their info. */\n      this._elementInfo = new Map();\n      /** The number of elements currently being monitored. */\n      this._monitoredElementCount = 0;\n      /**\n       * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n       * as well as the number of monitored elements that they contain. We have to treat focus/blur\n       * handlers differently from the rest of the events, because the browser won't emit events\n       * to the document when focus moves inside of a shadow root.\n       */\n      this._rootNodeFocusListenerCount = new Map();\n      /**\n       * Event listener for `focus` events on the window.\n       * Needs to be an arrow function in order to preserve the context when it gets bound.\n       */\n      this._windowFocusListener = () => {\n        // Make a note of when the window regains focus, so we can\n        // restore the origin info for the focused element.\n        this._windowFocused = true;\n        this._windowFocusTimeoutId = window.setTimeout(() => this._windowFocused = false);\n      };\n      /** Subject for stopping our InputModalityDetector subscription. */\n      this._stopInputModalityDetector = new Subject();\n      /**\n       * Event listener for `focus` and 'blur' events on the document.\n       * Needs to be an arrow function in order to preserve the context when it gets bound.\n       */\n      this._rootNodeFocusAndBlurListener = event => {\n        const target = _getEventTarget(event);\n        // We need to walk up the ancestor chain in order to support `checkChildren`.\n        for (let element = target; element; element = element.parentElement) {\n          if (event.type === 'focus') {\n            this._onFocus(event, element);\n          } else {\n            this._onBlur(event, element);\n          }\n        }\n      };\n      this._document = document;\n      this._detectionMode = options?.detectionMode || 0 /* FocusMonitorDetectionMode.IMMEDIATE */;\n    }\n\n    monitor(element, checkChildren = false) {\n      const nativeElement = coerceElement(element);\n      // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n      if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n        // Note: we don't want the observable to emit at all so we don't pass any parameters.\n        return of();\n      }\n      // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n      // the shadow root, rather than the `document`, because the browser won't emit focus events\n      // to the `document`, if focus is moving within the same shadow root.\n      const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n      const cachedInfo = this._elementInfo.get(nativeElement);\n      // Check if we're already monitoring this element.\n      if (cachedInfo) {\n        if (checkChildren) {\n          // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n          // observers into ones that behave as if `checkChildren` was turned on. We need a more\n          // robust solution.\n          cachedInfo.checkChildren = true;\n        }\n        return cachedInfo.subject;\n      }\n      // Create monitored element info.\n      const info = {\n        checkChildren: checkChildren,\n        subject: new Subject(),\n        rootNode\n      };\n      this._elementInfo.set(nativeElement, info);\n      this._registerGlobalListeners(info);\n      return info.subject;\n    }\n    stopMonitoring(element) {\n      const nativeElement = coerceElement(element);\n      const elementInfo = this._elementInfo.get(nativeElement);\n      if (elementInfo) {\n        elementInfo.subject.complete();\n        this._setClasses(nativeElement);\n        this._elementInfo.delete(nativeElement);\n        this._removeGlobalListeners(elementInfo);\n      }\n    }\n    focusVia(element, origin, options) {\n      const nativeElement = coerceElement(element);\n      const focusedElement = this._getDocument().activeElement;\n      // If the element is focused already, calling `focus` again won't trigger the event listener\n      // which means that the focus classes won't be updated. If that's the case, update the classes\n      // directly without waiting for an event.\n      if (nativeElement === focusedElement) {\n        this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n      } else {\n        this._setOrigin(origin);\n        // `focus` isn't available on the server\n        if (typeof nativeElement.focus === 'function') {\n          nativeElement.focus(options);\n        }\n      }\n    }\n    ngOnDestroy() {\n      this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    /** Access injected document if available or fallback to global document reference */\n    _getDocument() {\n      return this._document || document;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n      const doc = this._getDocument();\n      return doc.defaultView || window;\n    }\n    _getFocusOrigin(focusEventTarget) {\n      if (this._origin) {\n        // If the origin was realized via a touch interaction, we need to perform additional checks\n        // to determine whether the focus origin should be attributed to touch or program.\n        if (this._originFromTouchInteraction) {\n          return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n        } else {\n          return this._origin;\n        }\n      }\n      // If the window has just regained focus, we can restore the most recent origin from before the\n      // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n      // focus. This typically means one of two things happened:\n      //\n      // 1) The element was programmatically focused, or\n      // 2) The element was focused via screen reader navigation (which generally doesn't fire\n      //    events).\n      //\n      // Because we can't distinguish between these two cases, we default to setting `program`.\n      if (this._windowFocused && this._lastFocusOrigin) {\n        return this._lastFocusOrigin;\n      }\n      // If the interaction is coming from an input label, we consider it a mouse interactions.\n      // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n      // our detection, because all our assumptions are for `mousedown`. We need to handle this\n      // special case, because it's very common for checkboxes and radio buttons.\n      if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n        return 'mouse';\n      }\n      return 'program';\n    }\n    /**\n     * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n     * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n     * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n     * event was directly caused by the touch interaction or (2) the focus event was caused by a\n     * subsequent programmatic focus call triggered by the touch interaction.\n     * @param focusEventTarget The target of the focus event under examination.\n     */\n    _shouldBeAttributedToTouch(focusEventTarget) {\n      // Please note that this check is not perfect. Consider the following edge case:\n      //\n      // <div #parent tabindex=\"0\">\n      //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n      // </div>\n      //\n      // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n      // #child, #parent is programmatically focused. This code will attribute the focus to touch\n      // instead of program. This is a relatively minor edge-case that can be worked around by using\n      // focusVia(parent, 'program') to focus #parent.\n      return this._detectionMode === 1 /* FocusMonitorDetectionMode.EVENTUAL */ || !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget);\n    }\n    /**\n     * Sets the focus classes on the element based on the given focus origin.\n     * @param element The element to update the classes on.\n     * @param origin The focus origin.\n     */\n    _setClasses(element, origin) {\n      element.classList.toggle('cdk-focused', !!origin);\n      element.classList.toggle('cdk-touch-focused', origin === 'touch');\n      element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n      element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n      element.classList.toggle('cdk-program-focused', origin === 'program');\n    }\n    /**\n     * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n     * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n     * the origin being set.\n     * @param origin The origin to set.\n     * @param isFromInteraction Whether we are setting the origin from an interaction event.\n     */\n    _setOrigin(origin, isFromInteraction = false) {\n      this._ngZone.runOutsideAngular(() => {\n        this._origin = origin;\n        this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n        // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n        // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n        // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n        // a touch event because when a touch event is fired, the associated focus event isn't yet in\n        // the event queue. Before doing so, clear any pending timeouts.\n        if (this._detectionMode === 0 /* FocusMonitorDetectionMode.IMMEDIATE */) {\n          clearTimeout(this._originTimeoutId);\n          const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n          this._originTimeoutId = setTimeout(() => this._origin = null, ms);\n        }\n      });\n    }\n    /**\n     * Handles focus events on a registered element.\n     * @param event The focus event.\n     * @param element The monitored element.\n     */\n    _onFocus(event, element) {\n      // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n      // focus event affecting the monitored element. If we want to use the origin of the first event\n      // instead we should check for the cdk-focused class here and return if the element already has\n      // it. (This only matters for elements that have includesChildren = true).\n      // If we are not counting child-element-focus as focused, make sure that the event target is the\n      // monitored element itself.\n      const elementInfo = this._elementInfo.get(element);\n      const focusEventTarget = _getEventTarget(event);\n      if (!elementInfo || !elementInfo.checkChildren && element !== focusEventTarget) {\n        return;\n      }\n      this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n    }\n    /**\n     * Handles blur events on a registered element.\n     * @param event The blur event.\n     * @param element The monitored element.\n     */\n    _onBlur(event, element) {\n      // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n      // order to focus another child of the monitored element.\n      const elementInfo = this._elementInfo.get(element);\n      if (!elementInfo || elementInfo.checkChildren && event.relatedTarget instanceof Node && element.contains(event.relatedTarget)) {\n        return;\n      }\n      this._setClasses(element);\n      this._emitOrigin(elementInfo, null);\n    }\n    _emitOrigin(info, origin) {\n      if (info.subject.observers.length) {\n        this._ngZone.run(() => info.subject.next(origin));\n      }\n    }\n    _registerGlobalListeners(elementInfo) {\n      if (!this._platform.isBrowser) {\n        return;\n      }\n      const rootNode = elementInfo.rootNode;\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n      if (!rootNodeFocusListeners) {\n        this._ngZone.runOutsideAngular(() => {\n          rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n          rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        });\n      }\n      this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n      // Register global listeners when first element is monitored.\n      if (++this._monitoredElementCount === 1) {\n        // Note: we listen to events in the capture phase so we\n        // can detect them even if the user stops propagation.\n        this._ngZone.runOutsideAngular(() => {\n          const window = this._getWindow();\n          window.addEventListener('focus', this._windowFocusListener);\n        });\n        // The InputModalityDetector is also just a collection of global listeners.\n        this._inputModalityDetector.modalityDetected.pipe(takeUntil(this._stopInputModalityDetector)).subscribe(modality => {\n          this._setOrigin(modality, true /* isFromInteraction */);\n        });\n      }\n    }\n\n    _removeGlobalListeners(elementInfo) {\n      const rootNode = elementInfo.rootNode;\n      if (this._rootNodeFocusListenerCount.has(rootNode)) {\n        const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n        if (rootNodeFocusListeners > 1) {\n          this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n        } else {\n          rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n          rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n          this._rootNodeFocusListenerCount.delete(rootNode);\n        }\n      }\n      // Unregister global listeners when last element is unmonitored.\n      if (! --this._monitoredElementCount) {\n        const window = this._getWindow();\n        window.removeEventListener('focus', this._windowFocusListener);\n        // Equivalently, stop our InputModalityDetector subscription.\n        this._stopInputModalityDetector.next();\n        // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n        clearTimeout(this._windowFocusTimeoutId);\n        clearTimeout(this._originTimeoutId);\n      }\n    }\n    /** Updates all the state on an element once its focus origin has changed. */\n    _originChanged(element, origin, elementInfo) {\n      this._setClasses(element, origin);\n      this._emitOrigin(elementInfo, origin);\n      this._lastFocusOrigin = origin;\n    }\n    /**\n     * Collects the `MonitoredElementInfo` of a particular element and\n     * all of its ancestors that have enabled `checkChildren`.\n     * @param element Element from which to start the search.\n     */\n    _getClosestElementsInfo(element) {\n      const results = [];\n      this._elementInfo.forEach((info, currentElement) => {\n        if (currentElement === element || info.checkChildren && currentElement.contains(element)) {\n          results.push([currentElement, info]);\n        }\n      });\n      return results;\n    }\n    /**\n     * Returns whether an interaction is likely to have come from the user clicking the `label` of\n     * an `input` or `textarea` in order to focus it.\n     * @param focusEventTarget Target currently receiving focus.\n     */\n    _isLastInteractionFromInputLabel(focusEventTarget) {\n      const {\n        _mostRecentTarget: mostRecentTarget,\n        mostRecentModality\n      } = this._inputModalityDetector;\n      // If the last interaction used the mouse on an element contained by one of the labels\n      // of an `input`/`textarea` that is currently focused, it is very likely that the\n      // user redirected focus using the label.\n      if (mostRecentModality !== 'mouse' || !mostRecentTarget || mostRecentTarget === focusEventTarget || focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA' || focusEventTarget.disabled) {\n        return false;\n      }\n      const labels = focusEventTarget.labels;\n      if (labels) {\n        for (let i = 0; i < labels.length; i++) {\n          if (labels[i].contains(mostRecentTarget)) {\n            return true;\n          }\n        }\n      }\n      return false;\n    }\n    static {\n      this.ɵfac = function FocusMonitor_Factory(t) {\n        return new (t || FocusMonitor)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.Platform), i0.ɵɵinject(InputModalityDetector), i0.ɵɵinject(DOCUMENT, 8), i0.ɵɵinject(FOCUS_MONITOR_DEFAULT_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: FocusMonitor,\n        factory: FocusMonitor.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return FocusMonitor;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nlet CdkMonitorFocus = /*#__PURE__*/(() => {\n  class CdkMonitorFocus {\n    constructor(_elementRef, _focusMonitor) {\n      this._elementRef = _elementRef;\n      this._focusMonitor = _focusMonitor;\n      this._focusOrigin = null;\n      this.cdkFocusChange = new EventEmitter();\n    }\n    get focusOrigin() {\n      return this._focusOrigin;\n    }\n    ngAfterViewInit() {\n      const element = this._elementRef.nativeElement;\n      this._monitorSubscription = this._focusMonitor.monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus')).subscribe(origin => {\n        this._focusOrigin = origin;\n        this.cdkFocusChange.emit(origin);\n      });\n    }\n    ngOnDestroy() {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n      if (this._monitorSubscription) {\n        this._monitorSubscription.unsubscribe();\n      }\n    }\n    static {\n      this.ɵfac = function CdkMonitorFocus_Factory(t) {\n        return new (t || CdkMonitorFocus)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(FocusMonitor));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkMonitorFocus,\n        selectors: [[\"\", \"cdkMonitorElementFocus\", \"\"], [\"\", \"cdkMonitorSubtreeFocus\", \"\"]],\n        outputs: {\n          cdkFocusChange: \"cdkFocusChange\"\n        },\n        exportAs: [\"cdkMonitorFocus\"]\n      });\n    }\n  }\n  return CdkMonitorFocus;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nlet HighContrastModeDetector = /*#__PURE__*/(() => {\n  class HighContrastModeDetector {\n    constructor(_platform, document) {\n      this._platform = _platform;\n      this._document = document;\n      this._breakpointSubscription = inject(BreakpointObserver).observe('(forced-colors: active)').subscribe(() => {\n        if (this._hasCheckedHighContrastMode) {\n          this._hasCheckedHighContrastMode = false;\n          this._applyBodyHighContrastModeCssClasses();\n        }\n      });\n    }\n    /** Gets the current high-contrast-mode for the page. */\n    getHighContrastMode() {\n      if (!this._platform.isBrowser) {\n        return 0 /* HighContrastMode.NONE */;\n      }\n      // Create a test element with an arbitrary background-color that is neither black nor\n      // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n      // appending the test element to the DOM does not affect layout by absolutely positioning it\n      const testElement = this._document.createElement('div');\n      testElement.style.backgroundColor = 'rgb(1,2,3)';\n      testElement.style.position = 'absolute';\n      this._document.body.appendChild(testElement);\n      // Get the computed style for the background color, collapsing spaces to normalize between\n      // browsers. Once we get this color, we no longer need the test element. Access the `window`\n      // via the document so we can fake it in tests. Note that we have extra null checks, because\n      // this logic will likely run during app bootstrap and throwing can break the entire app.\n      const documentWindow = this._document.defaultView || window;\n      const computedStyle = documentWindow && documentWindow.getComputedStyle ? documentWindow.getComputedStyle(testElement) : null;\n      const computedColor = (computedStyle && computedStyle.backgroundColor || '').replace(/ /g, '');\n      testElement.remove();\n      switch (computedColor) {\n        // Pre Windows 11 dark theme.\n        case 'rgb(0,0,0)':\n        // Windows 11 dark themes.\n        case 'rgb(45,50,54)':\n        case 'rgb(32,32,32)':\n          return 2 /* HighContrastMode.WHITE_ON_BLACK */;\n        // Pre Windows 11 light theme.\n        case 'rgb(255,255,255)':\n        // Windows 11 light theme.\n        case 'rgb(255,250,239)':\n          return 1 /* HighContrastMode.BLACK_ON_WHITE */;\n      }\n\n      return 0 /* HighContrastMode.NONE */;\n    }\n\n    ngOnDestroy() {\n      this._breakpointSubscription.unsubscribe();\n    }\n    /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n    _applyBodyHighContrastModeCssClasses() {\n      if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n        const bodyClasses = this._document.body.classList;\n        bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n        this._hasCheckedHighContrastMode = true;\n        const mode = this.getHighContrastMode();\n        if (mode === 1 /* HighContrastMode.BLACK_ON_WHITE */) {\n          bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n        } else if (mode === 2 /* HighContrastMode.WHITE_ON_BLACK */) {\n          bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n        }\n      }\n    }\n    static {\n      this.ɵfac = function HighContrastModeDetector_Factory(t) {\n        return new (t || HighContrastModeDetector)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(DOCUMENT));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: HighContrastModeDetector,\n        factory: HighContrastModeDetector.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return HighContrastModeDetector;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet A11yModule = /*#__PURE__*/(() => {\n  class A11yModule {\n    constructor(highContrastModeDetector) {\n      highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n    }\n    static {\n      this.ɵfac = function A11yModule_Factory(t) {\n        return new (t || A11yModule)(i0.ɵɵinject(HighContrastModeDetector));\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: A11yModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [ObserversModule]\n      });\n    }\n  }\n  return A11yModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { A11yModule, ActiveDescendantKeyManager, AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, CdkAriaLive, CdkMonitorFocus, CdkTrapFocus, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_MONITOR_DEFAULT_OPTIONS, FOCUS_TRAP_INERT_STRATEGY, FocusKeyManager, FocusMonitor, FocusTrap, FocusTrapFactory, HighContrastModeDetector, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, INPUT_MODALITY_DETECTOR_OPTIONS, InputModalityDetector, InteractivityChecker, IsFocusableConfig, LIVE_ANNOUNCER_DEFAULT_OPTIONS, LIVE_ANNOUNCER_ELEMENT_TOKEN, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, ListKeyManager, LiveAnnouncer, MESSAGES_CONTAINER_ID, addAriaReferencedId, getAriaReferenceIds, isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader, removeAriaReferencedId };\n//# sourceMappingURL=a11y.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}