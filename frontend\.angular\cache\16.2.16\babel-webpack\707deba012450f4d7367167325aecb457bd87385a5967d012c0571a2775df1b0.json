{"ast": null, "code": "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/async.ts\n */\nexport default function asyncIterator(source) {\n  var _a;\n  var iterator = source[Symbol.asyncIterator]();\n  return _a = {\n    next: function () {\n      return iterator.next();\n    }\n  }, _a[Symbol.asyncIterator] = function () {\n    return this;\n  }, _a;\n}\n//# sourceMappingURL=async.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}