{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/planning.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction PlanningListComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningListComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction PlanningListComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 10);\n    i0.ɵɵelement(2, \"path\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"h3\", 12);\n    i0.ɵɵtext(4, \"Aucun planning disponible\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PlanningListComponent_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\")(3, \"h3\", 17)(4, \"a\", 18);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 19);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function PlanningListComponent_div_9_div_1_Template_button_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const planning_r5 = restoredCtx.$implicit;\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.deletePlanning(planning_r5._id));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 21);\n    i0.ɵɵelement(10, \"path\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"div\", 23);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 24);\n    i0.ɵɵelement(13, \"path\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(17, \"div\", 26)(18, \"span\", 27);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"a\", 28);\n    i0.ɵɵlistener(\"click\", function PlanningListComponent_div_9_div_1_Template_a_click_20_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const planning_r5 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.GotoDetail(planning_r5.id));\n    });\n    i0.ɵɵtext(21, \" Voir d\\u00E9tails \\u2192 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const planning_r5 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", planning_r5.titre, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(planning_r5.description || \"Aucune description\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(15, 5, planning_r5.dateDebut, \"mediumDate\"), \" - \", i0.ɵɵpipeBind2(16, 8, planning_r5.dateFin, \"mediumDate\"), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", (planning_r5.reunions == null ? null : planning_r5.reunions.length) || 0, \" r\\u00E9union(s) \");\n  }\n}\nfunction PlanningListComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, PlanningListComponent_div_9_div_1_Template, 22, 11, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.plannings);\n  }\n}\nexport let PlanningListComponent = /*#__PURE__*/(() => {\n  class PlanningListComponent {\n    constructor(planningService, authService, router, route) {\n      this.planningService = planningService;\n      this.authService = authService;\n      this.router = router;\n      this.route = route;\n      this.plannings = [];\n      this.loading = true;\n      this.error = null;\n    }\n    ngOnInit() {\n      this.loadPlannings();\n    }\n    loadPlannings() {\n      const userId = this.authService.getCurrentUserId();\n      if (!userId) {\n        this.error = 'Utilisateur non connecté';\n        this.loading = false;\n        return;\n      }\n      this.planningService.getPlanningsByUser(userId).subscribe({\n        next: response => {\n          if (response.success) {\n            this.plannings = response.plannings;\n            console.log(this.plannings);\n          } else {\n            this.error = 'Erreur lors du chargement';\n          }\n          this.loading = false;\n        },\n        error: err => {\n          this.error = err.error?.message || 'Erreur serveur';\n          this.loading = false;\n          console.error('Erreur:', err);\n        }\n      });\n    }\n    deletePlanning(id) {\n      if (confirm('Supprimer ce planning ?')) {\n        this.planningService.deletePlanning(id).subscribe({\n          next: () => {\n            this.plannings = this.plannings.filter(p => p._id !== id);\n          },\n          error: err => {\n            this.error = err.error?.message || 'Erreur lors de la suppression';\n          }\n        });\n      }\n    }\n    GotoDetail(id) {\n      if (id) {\n        this.router.navigate([id], {\n          relativeTo: this.route\n        });\n      }\n    }\n    static {\n      this.ɵfac = function PlanningListComponent_Factory(t) {\n        return new (t || PlanningListComponent)(i0.ɵɵdirectiveInject(i1.PlanningService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PlanningListComponent,\n        selectors: [[\"app-planning-list\"]],\n        decls: 10,\n        vars: 4,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-6\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\"], [\"routerLink\", \"/plannings/nouveau\", 1, \"px-4\", \"py-2\", \"bg-purple-600\", \"text-white\", \"rounded-md\", \"hover:bg-purple-700\", \"transition-colors\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"grid gap-4 md:grid-cols-2 lg:grid-cols-3\", 4, \"ngIf\"], [1, \"text-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-b-2\", \"border-purple-600\", \"mx-auto\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"mx-auto\", \"h-12\", \"w-12\", \"text-gray-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [1, \"mt-2\", \"text-lg\", \"font-medium\", \"text-gray-900\"], [1, \"grid\", \"gap-4\", \"md:grid-cols-2\", \"lg:grid-cols-3\"], [\"class\", \"bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-4\", \"hover:shadow-lg\", \"transition-shadow\"], [1, \"flex\", \"justify-between\", \"items-start\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\"], [1, \"hover:text-purple-600\"], [1, \"text-sm\", \"text-gray-600\", \"mt-1\"], [1, \"text-red-500\", \"hover:text-red-700\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"mt-3\", \"flex\", \"items-center\", \"text-sm\", \"text-gray-500\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"mt-4\", \"pt-3\", \"border-t\", \"border-gray-100\", \"flex\", \"justify-between\", \"items-center\"], [1, \"text-sm\", \"text-gray-500\"], [1, \"text-sm\", \"text-purple-600\", \"hover:text-purple-800\", 3, \"click\"]],\n        template: function PlanningListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n            i0.ɵɵtext(3, \"Mes Plannings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"a\", 3);\n            i0.ɵɵtext(5, \" Nouveau Planning \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(6, PlanningListComponent_div_6_Template, 2, 0, \"div\", 4);\n            i0.ɵɵtemplate(7, PlanningListComponent_div_7_Template, 2, 1, \"div\", 5);\n            i0.ɵɵtemplate(8, PlanningListComponent_div_8_Template, 5, 0, \"div\", 4);\n            i0.ɵɵtemplate(9, PlanningListComponent_div_9_Template, 2, 1, \"div\", 6);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.plannings.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.plannings.length > 0);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i3.RouterLink, i4.DatePipe]\n      });\n    }\n  }\n  return PlanningListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}