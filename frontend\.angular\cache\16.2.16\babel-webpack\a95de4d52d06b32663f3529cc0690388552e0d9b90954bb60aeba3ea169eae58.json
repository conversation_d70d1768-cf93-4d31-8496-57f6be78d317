{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { OperationBatcher } from \"./batching.js\";\nexport { OperationBatcher } from \"./batching.js\";\nvar BatchLink = /** @class */function (_super) {\n  __extends(BatchLink, _super);\n  function BatchLink(fetchParams) {\n    var _this = _super.call(this) || this;\n    var _a = fetchParams || {},\n      batchDebounce = _a.batchDebounce,\n      _b = _a.batchInterval,\n      batchInterval = _b === void 0 ? 10 : _b,\n      _c = _a.batchMax,\n      batchMax = _c === void 0 ? 0 : _c,\n      _d = _a.batchHandler,\n      batchHandler = _d === void 0 ? function () {\n        return null;\n      } : _d,\n      _e = _a.batchKey,\n      batchKey = _e === void 0 ? function () {\n        return \"\";\n      } : _e;\n    _this.batcher = new OperationBatcher({\n      batchDebounce: batchDebounce,\n      batchInterval: batchInterval,\n      batchMax: batchMax,\n      batchHandler: batch<PERSON><PERSON><PERSON>,\n      batchKey: batchKey\n    });\n    //make this link terminating\n    if (fetchParams.batchHandler.length <= 1) {\n      _this.request = function (operation) {\n        return _this.batcher.enqueueRequest({\n          operation: operation\n        });\n      };\n    }\n    return _this;\n  }\n  BatchLink.prototype.request = function (operation, forward) {\n    return this.batcher.enqueueRequest({\n      operation: operation,\n      forward: forward\n    });\n  };\n  return BatchLink;\n}(ApolloLink);\nexport { BatchLink };\n//# sourceMappingURL=batchLink.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}