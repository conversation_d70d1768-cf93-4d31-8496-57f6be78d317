{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/projets.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"src/app/services/rendus.service\";\nimport * as i4 from \"src/app/services/file.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/router\";\nfunction ProjectListComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"div\", 23)(3, \"div\")(4, \"p\", 24);\n    i0.ɵɵtext(5, \"Total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 25);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 26);\n    i0.ɵɵtext(9, \"Projets\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 27);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 28);\n    i0.ɵɵelement(12, \"path\", 29);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"div\", 22)(14, \"div\", 23)(15, \"div\")(16, \"p\", 30);\n    i0.ɵɵtext(17, \"Rendus\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 31);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 26);\n    i0.ɵɵtext(21, \"Compl\\u00E9t\\u00E9s\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 32);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(23, \"svg\", 33);\n    i0.ɵɵelement(24, \"path\", 34);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(25, \"div\", 22)(26, \"div\", 23)(27, \"div\")(28, \"p\", 35);\n    i0.ɵɵtext(29, \"En attente\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\", 36);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\", 26);\n    i0.ɵɵtext(33, \"\\u00C0 rendre\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 37);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(35, \"svg\", 38);\n    i0.ɵɵelement(36, \"path\", 39);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(37, \"div\", 22)(38, \"div\", 23)(39, \"div\")(40, \"p\", 24);\n    i0.ɵɵtext(41, \"Taux\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"p\", 25);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\", 26);\n    i0.ɵɵtext(45, \"R\\u00E9ussite\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 27);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(47, \"svg\", 28);\n    i0.ɵɵelement(48, \"path\", 40);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.getTotalProjects());\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.getRendusCount());\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r0.getPendingCount());\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getSuccessRate(), \"%\");\n  }\n}\nfunction ProjectListComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"h3\", 43);\n    i0.ɵɵtext(3, \"Progression globale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 44);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45);\n    i0.ɵɵelement(7, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 47)(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getSuccessRate(), \"% compl\\u00E9t\\u00E9\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getSuccessRate(), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getRendusCount(), \" projets rendus\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getPendingCount(), \" en attente\");\n  }\n}\nfunction ProjectListComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"div\", 50)(3, \"div\", 51);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectListComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 54);\n    i0.ɵɵelement(3, \"path\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(4, \"div\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\", 56);\n    i0.ɵɵtext(6, \" Aucun projet disponible \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 57);\n    i0.ɵɵtext(8, \" Vos missions appara\\u00EEtront ici \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectListComponent_div_33_div_1_div_25_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 89)(2, \"div\", 90);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 91);\n    i0.ɵɵelement(4, \"path\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(5, \"div\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 94);\n    i0.ɵɵtext(7, \"Document\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"a\", 95);\n    i0.ɵɵelement(9, \"div\", 96)(10, \"div\", 97);\n    i0.ɵɵelementStart(11, \"span\", 98);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 99);\n    i0.ɵɵelement(13, \"path\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" T\\u00E9l\\u00E9charger \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"href\", ctx_r10.getFileUrl(file_r11), i0.ɵɵsanitizeUrl);\n    i0.ɵɵattribute(\"download\", ctx_r10.getFileName(file_r11));\n  }\n}\nfunction ProjectListComponent_div_33_div_1_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"h4\", 85);\n    i0.ɵɵtext(2, \" Fichiers \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 86);\n    i0.ɵɵtemplate(4, ProjectListComponent_div_33_div_1_div_25_div_4_Template, 15, 2, \"div\", 87);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const projet_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", projet_r6.fichiers);\n  }\n}\nfunction ProjectListComponent_div_33_div_1_ng_container_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 101)(2, \"div\", 79);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 102);\n    i0.ɵɵelement(4, \"path\", 103);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(5, \"div\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"Rendu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/projects/submit\", a1];\n};\nfunction ProjectListComponent_div_33_div_1_ng_container_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 105);\n    i0.ɵɵelement(2, \"div\", 106)(3, \"div\", 107);\n    i0.ɵɵelementStart(4, \"span\", 108);\n    i0.ɵɵtext(5, \" Rendre \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const projet_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, projet_r6._id));\n  }\n}\nconst _c1 = function (a1) {\n  return [\"/projects/detail\", a1];\n};\nfunction ProjectListComponent_div_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61);\n    i0.ɵɵelement(2, \"div\", 62)(3, \"div\", 63);\n    i0.ɵɵelementStart(4, \"div\", 64)(5, \"div\", 65)(6, \"span\", 66);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"h3\", 67);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 68)(11, \"span\", 69);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 70);\n    i0.ɵɵelement(13, \"path\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(15, \"span\", 72);\n    i0.ɵɵtext(16, \"\\u2022\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 69);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(18, \"svg\", 70);\n    i0.ɵɵelement(19, \"path\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(22, \"div\", 74)(23, \"p\", 75);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, ProjectListComponent_div_33_div_1_div_25_Template, 5, 1, \"div\", 76);\n    i0.ɵɵelementStart(26, \"div\", 77)(27, \"a\", 78)(28, \"div\", 79);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(29, \"svg\", 80);\n    i0.ɵɵelement(30, \"path\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(31, \"div\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\");\n    i0.ɵɵtext(33, \"D\\u00E9tails\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(34, ProjectListComponent_div_33_div_1_ng_container_34_Template, 8, 0, \"ng-container\", 83);\n    i0.ɵɵtemplate(35, ProjectListComponent_div_33_div_1_ng_container_35_Template, 6, 3, \"ng-container\", 83);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const projet_r6 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", ctx_r5.getStatusClass(projet_r6));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getStatusText(projet_r6), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", projet_r6.titre, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", projet_r6.groupe || \"Tous\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(21, 10, projet_r6.dateLimite, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", projet_r6.description || \"Aucune description\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", projet_r6.fichiers && projet_r6.fichiers.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(13, _c1, projet_r6._id));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isRendu(projet_r6._id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.isRendu(projet_r6._id));\n  }\n}\nfunction ProjectListComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵtemplate(1, ProjectListComponent_div_33_div_1_Template, 36, 15, \"div\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.projets);\n  }\n}\n// Composant pour afficher la liste des projets\nexport let ProjectListComponent = /*#__PURE__*/(() => {\n  class ProjectListComponent {\n    constructor(projetService, authService, rendusService, fileService) {\n      this.projetService = projetService;\n      this.authService = authService;\n      this.rendusService = rendusService;\n      this.fileService = fileService;\n      this.projets = [];\n      this.rendusMap = new Map();\n      this.isLoading = true;\n      this.userGroup = '';\n    }\n    ngOnInit() {\n      // On garde cette ligne pour une utilisation future\n      this.userGroup = this.authService.getCurrentUser()?.groupe || '';\n      this.loadProjets();\n    }\n    loadProjets() {\n      this.isLoading = true;\n      this.projetService.getProjets().subscribe({\n        next: projets => {\n          // Afficher tous les projets sans filtrage\n          this.projets = projets;\n          this.isLoading = false;\n          // Vérifier quels projets ont déjà été rendus par l'étudiant\n          this.projets.forEach(projet => {\n            if (projet._id) {\n              this.checkRenduStatus(projet._id);\n            }\n          });\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des projets', error);\n          this.isLoading = false;\n        }\n      });\n    }\n    checkRenduStatus(projetId) {\n      const etudiantId = this.authService.getCurrentUserId();\n      if (!etudiantId) return;\n      this.rendusService.checkRenduExists(projetId, etudiantId).subscribe({\n        next: exists => {\n          this.rendusMap.set(projetId, exists);\n        },\n        error: error => {\n          console.error(`Erreur lors de la vérification du rendu pour le projet ${projetId}`, error);\n        }\n      });\n    }\n    getFileUrl(filePath) {\n      if (!filePath) return '';\n      // Extraire uniquement le nom du fichier\n      let fileName = filePath;\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        fileName = parts[parts.length - 1];\n      }\n      // Utiliser la route qui pointe vers le bon emplacement\n      return `${environment.urlBackend}projets/telecharger/${fileName}`;\n    }\n    getFileName(filePath) {\n      if (!filePath) return 'fichier';\n      // Extraire uniquement le nom du fichier\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        return parts[parts.length - 1];\n      }\n      return filePath;\n    }\n    // Méthode pour vérifier si un projet a été rendu\n    isRendu(projetId) {\n      return projetId ? this.rendusMap.get(projetId) === true : false;\n    }\n    // Méthodes pour les statistiques\n    getTotalProjects() {\n      return this.projets.length;\n    }\n    getRendusCount() {\n      return this.projets.filter(projet => projet._id && this.isRendu(projet._id)).length;\n    }\n    getPendingCount() {\n      return this.projets.filter(projet => projet._id && !this.isRendu(projet._id)).length;\n    }\n    getSuccessRate() {\n      if (this.projets.length === 0) return 0;\n      return Math.round(this.getRendusCount() / this.projets.length * 100);\n    }\n    // Méthode pour obtenir les projets urgents (date limite dans moins de 7 jours)\n    getUrgentProjects() {\n      const now = new Date();\n      const oneWeekFromNow = new Date();\n      oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);\n      return this.projets.filter(projet => {\n        if (!projet.dateLimite || this.isRendu(projet._id)) return false;\n        const deadline = new Date(projet.dateLimite);\n        return deadline >= now && deadline <= oneWeekFromNow;\n      });\n    }\n    // Méthode pour obtenir les projets expirés\n    getExpiredProjects() {\n      const now = new Date();\n      return this.projets.filter(projet => {\n        if (!projet.dateLimite || this.isRendu(projet._id)) return false;\n        const deadline = new Date(projet.dateLimite);\n        return deadline < now;\n      });\n    }\n    // Méthode pour obtenir le statut d'un projet\n    getProjectStatus(projet) {\n      if (this.isRendu(projet._id)) return 'completed';\n      if (!projet.dateLimite) return 'active';\n      const now = new Date();\n      const deadline = new Date(projet.dateLimite);\n      if (deadline < now) return 'expired';\n      const oneWeekFromNow = new Date();\n      oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);\n      if (deadline <= oneWeekFromNow) return 'urgent';\n      return 'active';\n    }\n    // Méthode pour obtenir la classe CSS du statut\n    getStatusClass(projet) {\n      const status = this.getProjectStatus(projet);\n      switch (status) {\n        case 'completed':\n          return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400';\n        case 'urgent':\n          return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-400';\n        case 'expired':\n          return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400';\n        default:\n          return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400';\n      }\n    }\n    // Méthode pour obtenir le texte du statut\n    getStatusText(projet) {\n      const status = this.getProjectStatus(projet);\n      switch (status) {\n        case 'completed':\n          return 'Rendu';\n        case 'urgent':\n          return 'Urgent';\n        case 'expired':\n          return 'Expiré';\n        default:\n          return 'Actif';\n      }\n    }\n    static {\n      this.ɵfac = function ProjectListComponent_Factory(t) {\n        return new (t || ProjectListComponent)(i0.ɵɵdirectiveInject(i1.ProjetService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.RendusService), i0.ɵɵdirectiveInject(i4.FileService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectListComponent,\n        selectors: [[\"app-project-list\"]],\n        decls: 34,\n        vars: 5,\n        consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"p-4\", \"md:p-6\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"max-w-6xl\", \"mx-auto\", \"relative\", \"z-10\"], [1, \"mb-8\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:justify-between\", \"lg:items-center\", \"mb-6\"], [1, \"text-3xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\", \"md:text-base\", \"mt-1\"], [1, \"h-12\", \"w-12\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"shadow-lg\", \"relative\", \"group\", \"overflow-hidden\", \"mt-4\", \"lg:mt-0\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-full\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"relative\", \"z-10\", \"group-hover:scale-110\", \"transition-transform\", \"duration-300\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [\"class\", \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8\", 4, \"ngIf\"], [\"class\", \"bg-white/80 dark:bg-[#1e1e1e]/80 backdrop-blur-sm rounded-xl p-6 border border-[#edf1f4]/50 dark:border-[#2a2a2a] shadow-md mb-8\", 4, \"ngIf\"], [\"class\", \"flex justify-center my-12\", 4, \"ngIf\"], [\"class\", \"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] p-8 text-center backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a]\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-2\", \"md:grid-cols-4\", \"gap-4\", \"mb-8\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-xl\", \"p-4\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"duration-300\", \"group\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-xs\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"p-3\", \"rounded-xl\", \"group-hover:scale-110\", \"transition-transform\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-xs\", \"font-medium\", \"text-green-600\", \"dark:text-green-400\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-green-700\", \"dark:text-green-400\"], [1, \"bg-green-100\", \"dark:bg-green-900/30\", \"p-3\", \"rounded-xl\", \"group-hover:scale-110\", \"transition-transform\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-green-600\", \"dark:text-green-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-xs\", \"font-medium\", \"text-orange-600\", \"dark:text-orange-400\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-orange-700\", \"dark:text-orange-400\"], [1, \"bg-orange-100\", \"dark:bg-orange-900/30\", \"p-3\", \"rounded-xl\", \"group-hover:scale-110\", \"transition-transform\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-orange-600\", \"dark:text-orange-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-xl\", \"p-6\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"shadow-md\", \"mb-8\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-3\"], [1, \"text-lg\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"w-full\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"rounded-full\", \"h-3\"], [1, \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"h-3\", \"rounded-full\", \"transition-all\", \"duration-500\"], [1, \"flex\", \"justify-between\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"flex\", \"justify-center\", \"my-12\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"p-8\", \"text-center\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"w-24\", \"h-24\", \"mx-auto\", \"mb-6\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"relative\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-12\", \"h-12\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"relative\", \"z-10\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-xl\", \"font-medium\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-3\", \"gap-6\"], [\"class\", \"bg-white dark:bg-[#1e1e1e] rounded-xl shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] overflow-hidden hover:shadow-lg dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)] transition-all duration-300 hover:-translate-y-1 backdrop-blur-sm border border-[#edf1f4]/50 dark:border-[#2a2a2a] group\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"overflow-hidden\", \"hover:shadow-lg\", \"dark:hover:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"transition-all\", \"duration-300\", \"hover:-translate-y-1\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"group\"], [1, \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"p-5\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"relative\"], [1, \"absolute\", \"top-3\", \"right-3\"], [1, \"text-xs\", \"px-2\", \"py-1\", \"rounded-full\", \"font-medium\", \"backdrop-blur-sm\", 3, \"ngClass\"], [1, \"text-lg\", \"font-bold\", \"pr-16\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"group-hover:scale-[1.01]\", \"transition-transform\", \"duration-300\", \"origin-left\"], [1, \"flex\", \"items-center\", \"mt-2\", \"text-xs\", \"space-x-2\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"px-2\", \"py-0.5\", \"rounded-full\", \"backdrop-blur-sm\", \"flex\", \"items-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"p-5\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-4\", \"line-clamp-3\"], [\"class\", \"mb-4\", 4, \"ngIf\"], [1, \"flex\", \"justify-between\", \"items-center\", \"pt-3\", \"border-t\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"text-sm\", \"font-medium\", \"flex\", \"items-center\", \"transition-colors\", \"relative\", \"group/details\", 3, \"routerLink\"], [1, \"relative\", \"mr-1\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"relative\", \"z-10\", \"group-hover/details:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover/details:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [4, \"ngIf\"], [1, \"mb-4\"], [1, \"text-xs\", \"font-semibold\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"uppercase\", \"tracking-wider\", \"mb-2\"], [1, \"space-y-2\"], [\"class\", \"flex items-center justify-between bg-[#edf1f4]/70 dark:bg-[#2a2a2a]/70 rounded-lg p-2.5 backdrop-blur-sm group/file hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"bg-[#edf1f4]/70\", \"dark:bg-[#2a2a2a]/70\", \"rounded-lg\", \"p-2.5\", \"backdrop-blur-sm\", \"group/file\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"transition-colors\"], [1, \"flex\", \"items-center\", \"truncate\"], [1, \"relative\", \"mr-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"relative\", \"z-10\", \"group-hover/file:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover/file:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"truncate\"], [\"download\", \"\", 1, \"relative\", \"overflow-hidden\", \"group/download\", 3, \"href\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/download:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover/download:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-white\", \"text-xs\", \"px-3\", \"py-1\", \"rounded-lg\", \"transition-all\", \"z-10\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\", \"mr-1\", \"group-hover/download:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"bg-gradient-to-r\", \"from-green-100\", \"to-green-50\", \"dark:from-green-900/30\", \"dark:to-green-800/30\", \"text-green-800\", \"dark:text-green-400\", \"text-xs\", \"px-3\", \"py-1.5\", \"rounded-full\", \"flex\", \"items-center\", \"shadow-sm\", \"backdrop-blur-sm\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\", 1, \"w-3\", \"h-3\", \"relative\", \"z-10\"], [\"fill-rule\", \"evenodd\", \"d\", \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\", \"clip-rule\", \"evenodd\"], [1, \"absolute\", \"inset-0\", \"bg-green-500/20\", \"blur-md\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"relative\", \"overflow-hidden\", \"group/submit\", 3, \"routerLink\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover/submit:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover/submit:opacity-100\", \"blur-md\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"text-white\", \"text-sm\", \"font-medium\", \"px-3\", \"py-1.5\", \"rounded-lg\", \"transition-all\", \"z-10\"]],\n        template: function ProjectListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"div\", 9)(20, \"div\")(21, \"h1\", 10);\n            i0.ɵɵtext(22, \" Mes Projets \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"p\", 11);\n            i0.ɵɵtext(24, \" G\\u00E9rez vos missions acad\\u00E9miques et suivez vos rendus \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"div\", 12);\n            i0.ɵɵelement(26, \"div\", 13);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(27, \"svg\", 14);\n            i0.ɵɵelement(28, \"path\", 15);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(29, ProjectListComponent_div_29_Template, 49, 4, \"div\", 16);\n            i0.ɵɵtemplate(30, ProjectListComponent_div_30_Template, 13, 5, \"div\", 17);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(31, ProjectListComponent_div_31_Template, 4, 0, \"div\", 18);\n            i0.ɵɵtemplate(32, ProjectListComponent_div_32_Template, 9, 0, \"div\", 19);\n            i0.ɵɵtemplate(33, ProjectListComponent_div_33_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(29);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.projets.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.projets.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i6.RouterLink, i5.DatePipe],\n        styles: [\".badge[_ngcontent-%COMP%]{display:inline-flex;align-items:center;padding:.25rem .5rem;border-radius:9999px;font-size:.75rem;font-weight:500}.badge-group[_ngcontent-%COMP%], .badge-deadline[_ngcontent-%COMP%]{background-color:#fff3;color:#fff}.line-clamp-3[_ngcontent-%COMP%]{display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden}\"]\n      });\n    }\n  }\n  return ProjectListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}