{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { print } from '../../language/printer.mjs';\nimport { isCompositeType } from '../../type/definition.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\n/**\n * Fragments on composite type\n *\n * Fragments use a type condition to determine if they apply, since fragments\n * can only be spread into a composite type (object, interface, or union), the\n * type condition must also be a composite type.\n *\n * See https://spec.graphql.org/draft/#sec-Fragments-On-Composite-Types\n */\nexport function FragmentsOnCompositeTypesRule(context) {\n  return {\n    InlineFragment(node) {\n      const typeCondition = node.typeCondition;\n      if (typeCondition) {\n        const type = typeFromAST(context.getSchema(), typeCondition);\n        if (type && !isCompositeType(type)) {\n          const typeStr = print(typeCondition);\n          context.reportError(new GraphQLError(`Fragment cannot condition on non composite type \"${typeStr}\".`, {\n            nodes: typeCondition\n          }));\n        }\n      }\n    },\n    FragmentDefinition(node) {\n      const type = typeFromAST(context.getSchema(), node.typeCondition);\n      if (type && !isCompositeType(type)) {\n        const typeStr = print(node.typeCondition);\n        context.reportError(new GraphQLError(`Fragment \"${node.name.value}\" cannot condition on non composite type \"${typeStr}\".`, {\n          nodes: node.typeCondition\n        }));\n      }\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}