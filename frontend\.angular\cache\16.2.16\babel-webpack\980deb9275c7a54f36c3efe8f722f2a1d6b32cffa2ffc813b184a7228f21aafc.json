{"ast": null, "code": "/* Core */\nexport { ApolloClient, mergeOptions } from \"./ApolloClient.js\";\nexport { ObservableQuery } from \"./ObservableQuery.js\";\nexport { NetworkStatus, isNetworkRequestSettled } from \"./networkStatus.js\";\nexport { isApolloError, ApolloError } from \"../errors/index.js\";\nexport { Cache, ApolloCache, InMemoryCache, MissingFieldError, defaultDataIdFromObject, makeVar } from \"../cache/index.js\";\n/* Link */\nexport * from \"../link/core/index.js\";\nexport * from \"../link/http/index.js\";\nexport { fromError, toPromise, fromPromise, throwServerError } from \"../link/utils/index.js\";\nexport { DocumentTransform, Observable, isReference, makeReference } from \"../utilities/index.js\";\n/* Supporting */\n// The verbosity of invariant.{log,warn,error} can be controlled globally\n// (for anyone using the same ts-invariant package) by passing \"log\",\n// \"warn\", \"error\", or \"silent\" to setVerbosity (\"log\" is the default).\n// Note that all invariant.* logging is hidden in production.\nimport { setVerbosity } from \"ts-invariant\";\nexport { setVerbosity as setLogVerbosity };\nsetVerbosity(globalThis.__DEV__ !== false ? \"log\" : \"silent\");\n// Note that importing `gql` by itself, then destructuring\n// additional properties separately before exporting, is intentional.\n// Due to the way the `graphql-tag` library is setup, certain bundlers\n// can't find the properties added to the exported `gql` function without\n// additional guidance (e.g. Rollup - see\n// https://rollupjs.org/guide/en/#error-name-is-not-exported-by-module).\n// Instead of having people that are using bundlers with `@apollo/client` add\n// extra bundler config to help `graphql-tag` exports be found (which would be\n// awkward since they aren't importing `graphql-tag` themselves), this\n// workaround of pulling the extra properties off the `gql` function,\n// then re-exporting them separately, helps keeps bundlers happy without any\n// additional config changes.\nexport { gql, resetCaches, disableFragmentWarnings, enableExperimentalFragmentVariables, disableExperimentalFragmentVariables } from \"graphql-tag\";\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}