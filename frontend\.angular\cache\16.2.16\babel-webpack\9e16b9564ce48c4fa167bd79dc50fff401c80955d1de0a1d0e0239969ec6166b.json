{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setMinutes} function options.\n */\n\n/**\n * @name setMinutes\n * @category Minute Helpers\n * @summary Set the minutes to the given date.\n *\n * @description\n * Set the minutes to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, returned from the context function, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param minutes - The minutes of the new date\n * @param options - An object with options\n *\n * @returns The new date with the minutes set\n *\n * @example\n * // Set 45 minutes to 1 September 2014 11:30:40:\n * const result = setMinutes(new Date(2014, 8, 1, 11, 30, 40), 45)\n * //=> Mon Sep 01 2014 11:45:40\n */\nexport function setMinutes(date, minutes, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setMinutes(minutes);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default setMinutes;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}