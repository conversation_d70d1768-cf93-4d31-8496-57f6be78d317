{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { keyMap } from '../jsutils/keyMap.mjs';\nimport { print } from '../language/printer.mjs';\nimport { isEnumType, isInputObjectType, isInterfaceType, isListType, isNamedType, isNonNullType, isObjectType, isRequiredArgument, isRequiredInputField, isScalarType, isUnionType } from '../type/definition.mjs';\nimport { isSpecifiedScalarType } from '../type/scalars.mjs';\nimport { astFromValue } from './astFromValue.mjs';\nimport { sortValueNode } from './sortValueNode.mjs';\nvar BreakingChangeType = /*#__PURE__*/function (BreakingChangeType) {\n  BreakingChangeType['TYPE_REMOVED'] = 'TYPE_REMOVED';\n  BreakingChangeType['TYPE_CHANGED_KIND'] = 'TYPE_CHANGED_KIND';\n  BreakingChangeType['TYPE_REMOVED_FROM_UNION'] = 'TYPE_REMOVED_FROM_UNION';\n  BreakingChangeType['VALUE_REMOVED_FROM_ENUM'] = 'VALUE_REMOVED_FROM_ENUM';\n  BreakingChangeType['REQUIRED_INPUT_FIELD_ADDED'] = 'REQUIRED_INPUT_FIELD_ADDED';\n  BreakingChangeType['IMPLEMENTED_INTERFACE_REMOVED'] = 'IMPLEMENTED_INTERFACE_REMOVED';\n  BreakingChangeType['FIELD_REMOVED'] = 'FIELD_REMOVED';\n  BreakingChangeType['FIELD_CHANGED_KIND'] = 'FIELD_CHANGED_KIND';\n  BreakingChangeType['REQUIRED_ARG_ADDED'] = 'REQUIRED_ARG_ADDED';\n  BreakingChangeType['ARG_REMOVED'] = 'ARG_REMOVED';\n  BreakingChangeType['ARG_CHANGED_KIND'] = 'ARG_CHANGED_KIND';\n  BreakingChangeType['DIRECTIVE_REMOVED'] = 'DIRECTIVE_REMOVED';\n  BreakingChangeType['DIRECTIVE_ARG_REMOVED'] = 'DIRECTIVE_ARG_REMOVED';\n  BreakingChangeType['REQUIRED_DIRECTIVE_ARG_ADDED'] = 'REQUIRED_DIRECTIVE_ARG_ADDED';\n  BreakingChangeType['DIRECTIVE_REPEATABLE_REMOVED'] = 'DIRECTIVE_REPEATABLE_REMOVED';\n  BreakingChangeType['DIRECTIVE_LOCATION_REMOVED'] = 'DIRECTIVE_LOCATION_REMOVED';\n  return BreakingChangeType;\n}(BreakingChangeType || {});\nexport { BreakingChangeType };\nvar DangerousChangeType = /*#__PURE__*/function (DangerousChangeType) {\n  DangerousChangeType['VALUE_ADDED_TO_ENUM'] = 'VALUE_ADDED_TO_ENUM';\n  DangerousChangeType['TYPE_ADDED_TO_UNION'] = 'TYPE_ADDED_TO_UNION';\n  DangerousChangeType['OPTIONAL_INPUT_FIELD_ADDED'] = 'OPTIONAL_INPUT_FIELD_ADDED';\n  DangerousChangeType['OPTIONAL_ARG_ADDED'] = 'OPTIONAL_ARG_ADDED';\n  DangerousChangeType['IMPLEMENTED_INTERFACE_ADDED'] = 'IMPLEMENTED_INTERFACE_ADDED';\n  DangerousChangeType['ARG_DEFAULT_VALUE_CHANGE'] = 'ARG_DEFAULT_VALUE_CHANGE';\n  return DangerousChangeType;\n}(DangerousChangeType || {});\nexport { DangerousChangeType };\n\n/**\n * Given two schemas, returns an Array containing descriptions of all the types\n * of breaking changes covered by the other functions down below.\n */\nexport function findBreakingChanges(oldSchema, newSchema) {\n  // @ts-expect-error\n  return findSchemaChanges(oldSchema, newSchema).filter(change => change.type in BreakingChangeType);\n}\n/**\n * Given two schemas, returns an Array containing descriptions of all the types\n * of potentially dangerous changes covered by the other functions down below.\n */\n\nexport function findDangerousChanges(oldSchema, newSchema) {\n  // @ts-expect-error\n  return findSchemaChanges(oldSchema, newSchema).filter(change => change.type in DangerousChangeType);\n}\nfunction findSchemaChanges(oldSchema, newSchema) {\n  return [...findTypeChanges(oldSchema, newSchema), ...findDirectiveChanges(oldSchema, newSchema)];\n}\nfunction findDirectiveChanges(oldSchema, newSchema) {\n  const schemaChanges = [];\n  const directivesDiff = diff(oldSchema.getDirectives(), newSchema.getDirectives());\n  for (const oldDirective of directivesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.DIRECTIVE_REMOVED,\n      description: `${oldDirective.name} was removed.`\n    });\n  }\n  for (const [oldDirective, newDirective] of directivesDiff.persisted) {\n    const argsDiff = diff(oldDirective.args, newDirective.args);\n    for (const newArg of argsDiff.added) {\n      if (isRequiredArgument(newArg)) {\n        schemaChanges.push({\n          type: BreakingChangeType.REQUIRED_DIRECTIVE_ARG_ADDED,\n          description: `A required arg ${newArg.name} on directive ${oldDirective.name} was added.`\n        });\n      }\n    }\n    for (const oldArg of argsDiff.removed) {\n      schemaChanges.push({\n        type: BreakingChangeType.DIRECTIVE_ARG_REMOVED,\n        description: `${oldArg.name} was removed from ${oldDirective.name}.`\n      });\n    }\n    if (oldDirective.isRepeatable && !newDirective.isRepeatable) {\n      schemaChanges.push({\n        type: BreakingChangeType.DIRECTIVE_REPEATABLE_REMOVED,\n        description: `Repeatable flag was removed from ${oldDirective.name}.`\n      });\n    }\n    for (const location of oldDirective.locations) {\n      if (!newDirective.locations.includes(location)) {\n        schemaChanges.push({\n          type: BreakingChangeType.DIRECTIVE_LOCATION_REMOVED,\n          description: `${location} was removed from ${oldDirective.name}.`\n        });\n      }\n    }\n  }\n  return schemaChanges;\n}\nfunction findTypeChanges(oldSchema, newSchema) {\n  const schemaChanges = [];\n  const typesDiff = diff(Object.values(oldSchema.getTypeMap()), Object.values(newSchema.getTypeMap()));\n  for (const oldType of typesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.TYPE_REMOVED,\n      description: isSpecifiedScalarType(oldType) ? `Standard scalar ${oldType.name} was removed because it is not referenced anymore.` : `${oldType.name} was removed.`\n    });\n  }\n  for (const [oldType, newType] of typesDiff.persisted) {\n    if (isEnumType(oldType) && isEnumType(newType)) {\n      schemaChanges.push(...findEnumTypeChanges(oldType, newType));\n    } else if (isUnionType(oldType) && isUnionType(newType)) {\n      schemaChanges.push(...findUnionTypeChanges(oldType, newType));\n    } else if (isInputObjectType(oldType) && isInputObjectType(newType)) {\n      schemaChanges.push(...findInputObjectTypeChanges(oldType, newType));\n    } else if (isObjectType(oldType) && isObjectType(newType)) {\n      schemaChanges.push(...findFieldChanges(oldType, newType), ...findImplementedInterfacesChanges(oldType, newType));\n    } else if (isInterfaceType(oldType) && isInterfaceType(newType)) {\n      schemaChanges.push(...findFieldChanges(oldType, newType), ...findImplementedInterfacesChanges(oldType, newType));\n    } else if (oldType.constructor !== newType.constructor) {\n      schemaChanges.push({\n        type: BreakingChangeType.TYPE_CHANGED_KIND,\n        description: `${oldType.name} changed from ` + `${typeKindName(oldType)} to ${typeKindName(newType)}.`\n      });\n    }\n  }\n  return schemaChanges;\n}\nfunction findInputObjectTypeChanges(oldType, newType) {\n  const schemaChanges = [];\n  const fieldsDiff = diff(Object.values(oldType.getFields()), Object.values(newType.getFields()));\n  for (const newField of fieldsDiff.added) {\n    if (isRequiredInputField(newField)) {\n      schemaChanges.push({\n        type: BreakingChangeType.REQUIRED_INPUT_FIELD_ADDED,\n        description: `A required field ${newField.name} on input type ${oldType.name} was added.`\n      });\n    } else {\n      schemaChanges.push({\n        type: DangerousChangeType.OPTIONAL_INPUT_FIELD_ADDED,\n        description: `An optional field ${newField.name} on input type ${oldType.name} was added.`\n      });\n    }\n  }\n  for (const oldField of fieldsDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.FIELD_REMOVED,\n      description: `${oldType.name}.${oldField.name} was removed.`\n    });\n  }\n  for (const [oldField, newField] of fieldsDiff.persisted) {\n    const isSafe = isChangeSafeForInputObjectFieldOrFieldArg(oldField.type, newField.type);\n    if (!isSafe) {\n      schemaChanges.push({\n        type: BreakingChangeType.FIELD_CHANGED_KIND,\n        description: `${oldType.name}.${oldField.name} changed type from ` + `${String(oldField.type)} to ${String(newField.type)}.`\n      });\n    }\n  }\n  return schemaChanges;\n}\nfunction findUnionTypeChanges(oldType, newType) {\n  const schemaChanges = [];\n  const possibleTypesDiff = diff(oldType.getTypes(), newType.getTypes());\n  for (const newPossibleType of possibleTypesDiff.added) {\n    schemaChanges.push({\n      type: DangerousChangeType.TYPE_ADDED_TO_UNION,\n      description: `${newPossibleType.name} was added to union type ${oldType.name}.`\n    });\n  }\n  for (const oldPossibleType of possibleTypesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.TYPE_REMOVED_FROM_UNION,\n      description: `${oldPossibleType.name} was removed from union type ${oldType.name}.`\n    });\n  }\n  return schemaChanges;\n}\nfunction findEnumTypeChanges(oldType, newType) {\n  const schemaChanges = [];\n  const valuesDiff = diff(oldType.getValues(), newType.getValues());\n  for (const newValue of valuesDiff.added) {\n    schemaChanges.push({\n      type: DangerousChangeType.VALUE_ADDED_TO_ENUM,\n      description: `${newValue.name} was added to enum type ${oldType.name}.`\n    });\n  }\n  for (const oldValue of valuesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.VALUE_REMOVED_FROM_ENUM,\n      description: `${oldValue.name} was removed from enum type ${oldType.name}.`\n    });\n  }\n  return schemaChanges;\n}\nfunction findImplementedInterfacesChanges(oldType, newType) {\n  const schemaChanges = [];\n  const interfacesDiff = diff(oldType.getInterfaces(), newType.getInterfaces());\n  for (const newInterface of interfacesDiff.added) {\n    schemaChanges.push({\n      type: DangerousChangeType.IMPLEMENTED_INTERFACE_ADDED,\n      description: `${newInterface.name} added to interfaces implemented by ${oldType.name}.`\n    });\n  }\n  for (const oldInterface of interfacesDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.IMPLEMENTED_INTERFACE_REMOVED,\n      description: `${oldType.name} no longer implements interface ${oldInterface.name}.`\n    });\n  }\n  return schemaChanges;\n}\nfunction findFieldChanges(oldType, newType) {\n  const schemaChanges = [];\n  const fieldsDiff = diff(Object.values(oldType.getFields()), Object.values(newType.getFields()));\n  for (const oldField of fieldsDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.FIELD_REMOVED,\n      description: `${oldType.name}.${oldField.name} was removed.`\n    });\n  }\n  for (const [oldField, newField] of fieldsDiff.persisted) {\n    schemaChanges.push(...findArgChanges(oldType, oldField, newField));\n    const isSafe = isChangeSafeForObjectOrInterfaceField(oldField.type, newField.type);\n    if (!isSafe) {\n      schemaChanges.push({\n        type: BreakingChangeType.FIELD_CHANGED_KIND,\n        description: `${oldType.name}.${oldField.name} changed type from ` + `${String(oldField.type)} to ${String(newField.type)}.`\n      });\n    }\n  }\n  return schemaChanges;\n}\nfunction findArgChanges(oldType, oldField, newField) {\n  const schemaChanges = [];\n  const argsDiff = diff(oldField.args, newField.args);\n  for (const oldArg of argsDiff.removed) {\n    schemaChanges.push({\n      type: BreakingChangeType.ARG_REMOVED,\n      description: `${oldType.name}.${oldField.name} arg ${oldArg.name} was removed.`\n    });\n  }\n  for (const [oldArg, newArg] of argsDiff.persisted) {\n    const isSafe = isChangeSafeForInputObjectFieldOrFieldArg(oldArg.type, newArg.type);\n    if (!isSafe) {\n      schemaChanges.push({\n        type: BreakingChangeType.ARG_CHANGED_KIND,\n        description: `${oldType.name}.${oldField.name} arg ${oldArg.name} has changed type from ` + `${String(oldArg.type)} to ${String(newArg.type)}.`\n      });\n    } else if (oldArg.defaultValue !== undefined) {\n      if (newArg.defaultValue === undefined) {\n        schemaChanges.push({\n          type: DangerousChangeType.ARG_DEFAULT_VALUE_CHANGE,\n          description: `${oldType.name}.${oldField.name} arg ${oldArg.name} defaultValue was removed.`\n        });\n      } else {\n        // Since we looking only for client's observable changes we should\n        // compare default values in the same representation as they are\n        // represented inside introspection.\n        const oldValueStr = stringifyValue(oldArg.defaultValue, oldArg.type);\n        const newValueStr = stringifyValue(newArg.defaultValue, newArg.type);\n        if (oldValueStr !== newValueStr) {\n          schemaChanges.push({\n            type: DangerousChangeType.ARG_DEFAULT_VALUE_CHANGE,\n            description: `${oldType.name}.${oldField.name} arg ${oldArg.name} has changed defaultValue from ${oldValueStr} to ${newValueStr}.`\n          });\n        }\n      }\n    }\n  }\n  for (const newArg of argsDiff.added) {\n    if (isRequiredArgument(newArg)) {\n      schemaChanges.push({\n        type: BreakingChangeType.REQUIRED_ARG_ADDED,\n        description: `A required arg ${newArg.name} on ${oldType.name}.${oldField.name} was added.`\n      });\n    } else {\n      schemaChanges.push({\n        type: DangerousChangeType.OPTIONAL_ARG_ADDED,\n        description: `An optional arg ${newArg.name} on ${oldType.name}.${oldField.name} was added.`\n      });\n    }\n  }\n  return schemaChanges;\n}\nfunction isChangeSafeForObjectOrInterfaceField(oldType, newType) {\n  if (isListType(oldType)) {\n    return (\n      // if they're both lists, make sure the underlying types are compatible\n      isListType(newType) && isChangeSafeForObjectOrInterfaceField(oldType.ofType, newType.ofType) ||\n      // moving from nullable to non-null of the same underlying type is safe\n      isNonNullType(newType) && isChangeSafeForObjectOrInterfaceField(oldType, newType.ofType)\n    );\n  }\n  if (isNonNullType(oldType)) {\n    // if they're both non-null, make sure the underlying types are compatible\n    return isNonNullType(newType) && isChangeSafeForObjectOrInterfaceField(oldType.ofType, newType.ofType);\n  }\n  return (\n    // if they're both named types, see if their names are equivalent\n    isNamedType(newType) && oldType.name === newType.name ||\n    // moving from nullable to non-null of the same underlying type is safe\n    isNonNullType(newType) && isChangeSafeForObjectOrInterfaceField(oldType, newType.ofType)\n  );\n}\nfunction isChangeSafeForInputObjectFieldOrFieldArg(oldType, newType) {\n  if (isListType(oldType)) {\n    // if they're both lists, make sure the underlying types are compatible\n    return isListType(newType) && isChangeSafeForInputObjectFieldOrFieldArg(oldType.ofType, newType.ofType);\n  }\n  if (isNonNullType(oldType)) {\n    return (\n      // if they're both non-null, make sure the underlying types are\n      // compatible\n      isNonNullType(newType) && isChangeSafeForInputObjectFieldOrFieldArg(oldType.ofType, newType.ofType) ||\n      // moving from non-null to nullable of the same underlying type is safe\n      !isNonNullType(newType) && isChangeSafeForInputObjectFieldOrFieldArg(oldType.ofType, newType)\n    );\n  } // if they're both named types, see if their names are equivalent\n\n  return isNamedType(newType) && oldType.name === newType.name;\n}\nfunction typeKindName(type) {\n  if (isScalarType(type)) {\n    return 'a Scalar type';\n  }\n  if (isObjectType(type)) {\n    return 'an Object type';\n  }\n  if (isInterfaceType(type)) {\n    return 'an Interface type';\n  }\n  if (isUnionType(type)) {\n    return 'a Union type';\n  }\n  if (isEnumType(type)) {\n    return 'an Enum type';\n  }\n  if (isInputObjectType(type)) {\n    return 'an Input type';\n  }\n  /* c8 ignore next 3 */\n  // Not reachable, all possible types have been considered.\n\n  false || invariant(false, 'Unexpected type: ' + inspect(type));\n}\nfunction stringifyValue(value, type) {\n  const ast = astFromValue(value, type);\n  ast != null || invariant(false);\n  return print(sortValueNode(ast));\n}\nfunction diff(oldArray, newArray) {\n  const added = [];\n  const removed = [];\n  const persisted = [];\n  const oldMap = keyMap(oldArray, ({\n    name\n  }) => name);\n  const newMap = keyMap(newArray, ({\n    name\n  }) => name);\n  for (const oldItem of oldArray) {\n    const newItem = newMap[oldItem.name];\n    if (newItem === undefined) {\n      removed.push(oldItem);\n    } else {\n      persisted.push([oldItem, newItem]);\n    }\n  }\n  for (const newItem of newArray) {\n    if (oldMap[newItem.name] === undefined) {\n      added.push(newItem);\n    }\n  }\n  return {\n    added,\n    persisted,\n    removed\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}