{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Known fragment names\n *\n * A GraphQL document is only valid if all `...Fragment` fragment spreads refer\n * to fragments defined in the same document.\n *\n * See https://spec.graphql.org/draft/#sec-Fragment-spread-target-defined\n */\nexport function KnownFragmentNamesRule(context) {\n  return {\n    FragmentSpread(node) {\n      const fragmentName = node.name.value;\n      const fragment = context.getFragment(fragmentName);\n      if (!fragment) {\n        context.reportError(new GraphQLError(`Unknown fragment \"${fragmentName}\".`, {\n          nodes: node.name\n        }));\n      }\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}