{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let ReunionSchedulerComponent = /*#__PURE__*/(() => {\n  class ReunionSchedulerComponent {\n    static {\n      this.ɵfac = function ReunionSchedulerComponent_Factory(t) {\n        return new (t || ReunionSchedulerComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ReunionSchedulerComponent,\n        selectors: [[\"app-reunion-scheduler\"]],\n        decls: 2,\n        vars: 0,\n        template: function ReunionSchedulerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p\");\n            i0.ɵɵtext(1, \"reunion-scheduler works!\");\n            i0.ɵɵelementEnd();\n          }\n        }\n      });\n    }\n  }\n  return ReunionSchedulerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}