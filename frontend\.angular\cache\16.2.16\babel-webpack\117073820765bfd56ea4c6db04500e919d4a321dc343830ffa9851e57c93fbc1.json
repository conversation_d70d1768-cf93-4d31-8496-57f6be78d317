{"ast": null, "code": "import { groupBy } from '../../jsutils/groupBy.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique argument names\n *\n * A GraphQL field or directive is only valid if all supplied arguments are\n * uniquely named.\n *\n * See https://spec.graphql.org/draft/#sec-Argument-Names\n */\nexport function UniqueArgumentNamesRule(context) {\n  return {\n    Field: checkArgUniqueness,\n    Directive: checkArgUniqueness\n  };\n  function checkArgUniqueness(parentNode) {\n    var _parentNode$arguments;\n\n    // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n    const argumentNodes = (_parentNode$arguments = parentNode.arguments) !== null && _parentNode$arguments !== void 0 ? _parentNode$arguments : [];\n    const seenArgs = groupBy(argumentNodes, arg => arg.name.value);\n    for (const [argName, argNodes] of seenArgs) {\n      if (argNodes.length > 1) {\n        context.reportError(new GraphQLError(`There can be only one argument named \"${argName}\".`, {\n          nodes: argNodes.map(node => node.name)\n        }));\n      }\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}