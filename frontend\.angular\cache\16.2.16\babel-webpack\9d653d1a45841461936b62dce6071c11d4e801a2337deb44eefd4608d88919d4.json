{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"src/app/services/rendus.service\";\nimport * as i4 from \"src/app/services/authuser.service\";\nimport * as i5 from \"@angular/common\";\nfunction ProjectDetailComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31);\n    i0.ɵɵelement(2, \"div\", 32)(3, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectDetailComponent_div_38_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79)(2, \"div\", 80)(3, \"div\", 81)(4, \"div\", 82);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 39);\n    i0.ɵɵelement(6, \"path\", 83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"div\", 84)(8, \"p\", 85);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 86);\n    i0.ɵɵtext(11, \"Document de projet\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"a\", 87)(13, \"div\", 19);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(14, \"svg\", 88);\n    i0.ɵɵelement(15, \"path\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"T\\u00E9l\\u00E9charger\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const file_r7 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getFileName(file_r7), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r6.getFileUrl(file_r7), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProjectDetailComponent_div_38_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtemplate(1, ProjectDetailComponent_div_38_div_21_div_1_Template, 18, 2, \"div\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.projet.fichiers);\n  }\n}\nfunction ProjectDetailComponent_div_38_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"div\", 91)(2, \"div\", 92);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 93);\n    i0.ɵɵelement(4, \"path\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"p\", 25);\n    i0.ɵɵtext(6, \"Aucun fichier joint \\u00E0 ce projet\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectDetailComponent_div_38_ng_container_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 94)(2, \"div\", 26)(3, \"div\", 66);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 67);\n    i0.ɵɵelement(5, \"path\", 95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"div\")(7, \"p\", 96);\n    i0.ɵɵtext(8, \"Projet soumis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 97);\n    i0.ɵɵtext(10, \"Votre rendu a \\u00E9t\\u00E9 enregistr\\u00E9 avec succ\\u00E8s\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/projects/submit\", a1];\n};\nfunction ProjectDetailComponent_div_38_ng_container_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 98)(2, \"div\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 74);\n    i0.ɵɵelement(4, \"path\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"Soumettre mon projet\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, ctx_r5.projetId));\n  }\n}\nfunction ProjectDetailComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 36)(3, \"div\", 37)(4, \"div\", 38);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 39);\n    i0.ɵɵelement(6, \"path\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"h3\", 41);\n    i0.ɵɵtext(8, \"Description du projet\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 42)(10, \"p\", 43);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 36)(13, \"div\", 37)(14, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(15, \"svg\", 45);\n    i0.ɵɵelement(16, \"path\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(17, \"h3\", 41);\n    i0.ɵɵtext(18, \" Ressources du projet \");\n    i0.ɵɵelementStart(19, \"span\", 47);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(21, ProjectDetailComponent_div_38_div_21_Template, 2, 1, \"div\", 48);\n    i0.ɵɵtemplate(22, ProjectDetailComponent_div_38_div_22_Template, 7, 0, \"div\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 50)(24, \"div\", 51)(25, \"div\", 52)(26, \"div\", 26)(27, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 54);\n    i0.ɵɵelement(29, \"path\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(30, \"div\")(31, \"h3\", 56);\n    i0.ɵɵtext(32, \"Informations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 57);\n    i0.ɵɵtext(34, \"D\\u00E9tails du projet\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(35, \"div\", 58)(36, \"div\", 59)(37, \"div\", 26)(38, \"div\", 44);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(39, \"svg\", 60);\n    i0.ɵɵelement(40, \"path\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(41, \"div\")(42, \"p\", 61);\n    i0.ɵɵtext(43, \"Date limite\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\", 62);\n    i0.ɵɵtext(45);\n    i0.ɵɵpipe(46, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(47, \"div\", 59)(48, \"div\", 26)(49, \"div\", 63);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(50, \"svg\", 64);\n    i0.ɵɵelement(51, \"path\", 65);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(52, \"div\")(53, \"p\", 61);\n    i0.ɵɵtext(54, \"Temps restant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"p\", 62);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(57, \"div\", 59)(58, \"div\", 26)(59, \"div\", 38);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(60, \"svg\", 20);\n    i0.ɵɵelement(61, \"path\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(62, \"div\")(63, \"p\", 61);\n    i0.ɵɵtext(64, \"Groupe cible\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"p\", 62);\n    i0.ɵɵtext(66);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(67, \"div\", 36)(68, \"div\", 37)(69, \"div\", 66);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(70, \"svg\", 67);\n    i0.ɵɵelement(71, \"path\", 68)(72, \"path\", 69);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(73, \"h3\", 41);\n    i0.ɵɵtext(74, \"Actions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(75, \"div\", 70);\n    i0.ɵɵtemplate(76, ProjectDetailComponent_div_38_ng_container_76_Template, 11, 0, \"ng-container\", 71);\n    i0.ɵɵtemplate(77, ProjectDetailComponent_div_38_ng_container_77_Template, 7, 3, \"ng-container\", 71);\n    i0.ɵɵelementStart(78, \"a\", 72)(79, \"div\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(80, \"svg\", 74);\n    i0.ɵɵelement(81, \"path\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(82, \"span\");\n    i0.ɵɵtext(83, \"Retour aux projets\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r1.projet == null ? null : ctx_r1.projet.description) || \"Aucune description fournie pour ce projet.\", \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate2(\" (\", (ctx_r1.projet == null ? null : ctx_r1.projet.fichiers == null ? null : ctx_r1.projet.fichiers.length) || 0, \" fichier\", ((ctx_r1.projet == null ? null : ctx_r1.projet.fichiers == null ? null : ctx_r1.projet.fichiers.length) || 0) > 1 ? \"s\" : \"\", \") \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.projet == null ? null : ctx_r1.projet.fichiers == null ? null : ctx_r1.projet.fichiers.length) > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.projet == null ? null : ctx_r1.projet.fichiers) || ctx_r1.projet.fichiers.length === 0);\n    i0.ɵɵadvance(23);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(46, 10, ctx_r1.projet == null ? null : ctx_r1.projet.dateLimite, \"dd/MM/yyyy\" || \"Non d\\u00E9finie\"));\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getRemainingDays(), \" jours\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate((ctx_r1.projet == null ? null : ctx_r1.projet.groupe) || \"Tous les groupes\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasSubmitted);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasSubmitted);\n  }\n}\n// Composant pour afficher les détails d'un projet\nexport let ProjectDetailComponent = /*#__PURE__*/(() => {\n  class ProjectDetailComponent {\n    constructor(route, router, projetService, rendusService, authService) {\n      this.route = route;\n      this.router = router;\n      this.projetService = projetService;\n      this.rendusService = rendusService;\n      this.authService = authService;\n      this.projetId = '';\n      this.isLoading = true;\n      this.hasSubmitted = false;\n    }\n    ngOnInit() {\n      this.projetId = this.route.snapshot.paramMap.get('id') || '';\n      this.loadProjetDetails();\n      this.checkRenduStatus();\n    }\n    loadProjetDetails() {\n      this.isLoading = true;\n      this.projetService.getProjetById(this.projetId).subscribe({\n        next: projet => {\n          this.projet = projet;\n          this.isLoading = false;\n        },\n        error: err => {\n          console.error('Erreur lors du chargement du projet', err);\n          this.isLoading = false;\n          this.router.navigate(['/projects']);\n        }\n      });\n    }\n    checkRenduStatus() {\n      const etudiantId = this.authService.getCurrentUserId();\n      if (etudiantId) {\n        this.rendusService.checkRenduExists(this.projetId, etudiantId).subscribe({\n          next: exists => {\n            console.log(exists);\n            this.hasSubmitted = exists;\n          },\n          error: err => {\n            console.error('Erreur lors de la vérification du rendu', err);\n          }\n        });\n      }\n    }\n    getFileUrl(filePath) {\n      // Extraire uniquement le nom du fichier\n      let fileName = filePath;\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        fileName = parts[parts.length - 1];\n      }\n      // Utiliser l'endpoint API spécifique pour le téléchargement\n      return `http://localhost:3000/api/projets/download/${fileName}`;\n    }\n    getFileName(filePath) {\n      if (!filePath) return 'Fichier';\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        return parts[parts.length - 1];\n      }\n      return filePath;\n    }\n    getProjectStatus() {\n      if (this.hasSubmitted) return 'completed';\n      if (!this.projet?.dateLimite) return 'active';\n      const now = new Date();\n      const deadline = new Date(this.projet.dateLimite);\n      if (deadline < now) return 'expired';\n      const oneWeekFromNow = new Date();\n      oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);\n      if (deadline <= oneWeekFromNow) return 'urgent';\n      return 'active';\n    }\n    getStatusClass() {\n      const status = this.getProjectStatus();\n      switch (status) {\n        case 'completed':\n          return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 border border-green-200 dark:border-green-800/30';\n        case 'urgent':\n          return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-400 border border-orange-200 dark:border-orange-800/30';\n        case 'expired':\n          return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400 border border-red-200 dark:border-red-800/30';\n        default:\n          return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 border border-blue-200 dark:border-blue-800/30';\n      }\n    }\n    getStatusText() {\n      const status = this.getProjectStatus();\n      switch (status) {\n        case 'completed':\n          return 'Projet soumis';\n        case 'urgent':\n          return 'Urgent';\n        case 'expired':\n          return 'Expiré';\n        default:\n          return 'Actif';\n      }\n    }\n    getRemainingDays() {\n      if (!this.projet?.dateLimite) return 0;\n      const now = new Date();\n      const deadline = new Date(this.projet.dateLimite);\n      const diffTime = deadline.getTime() - now.getTime();\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n      return Math.max(0, diffDays);\n    }\n    static {\n      this.ɵfac = function ProjectDetailComponent_Factory(t) {\n        return new (t || ProjectDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.RendusService), i0.ɵɵdirectiveInject(i4.AuthuserService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectDetailComponent,\n        selectors: [[\"app-project-detail\"]],\n        decls: 39,\n        vars: 11,\n        consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\", \"relative\", \"z-10\"], [1, \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-4\"], [\"routerLink\", \"/projects\", 1, \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mb-6\", \"lg:mb-0\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-3xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mt-2\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-orange-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"px-4\", \"py-2\", \"rounded-xl\", \"text-sm\", \"font-medium\", 3, \"ngClass\"], [\"class\", \"flex justify-center my-12\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 lg:grid-cols-3 gap-8\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-12\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\", \"gap-8\"], [1, \"lg:col-span-2\", \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-4\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h7\"], [1, \"text-lg\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [1, \"prose\", \"prose-gray\", \"dark:prose-invert\", \"max-w-none\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"leading-relaxed\"], [1, \"bg-orange-100\", \"dark:bg-orange-900/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-orange-600\", \"dark:text-orange-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-sm\", \"font-normal\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"ml-2\"], [\"class\", \"grid grid-cols-1 sm:grid-cols-2 gap-4\", 4, \"ngIf\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [1, \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"p-6\", \"text-white\"], [1, \"bg-white/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-lg\", \"font-semibold\"], [1, \"text-sm\", \"text-white/80\"], [1, \"p-6\", \"space-y-4\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-3\", \"bg-[#edf1f4]/50\", \"dark:bg-[#2a2a2a]/50\", \"rounded-xl\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-orange-600\", \"dark:text-orange-400\"], [1, \"text-xs\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"uppercase\", \"tracking-wider\"], [1, \"text-sm\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [1, \"bg-blue-100\", \"dark:bg-blue-900/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-blue-600\", \"dark:text-blue-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"], [1, \"bg-green-100\", \"dark:bg-green-900/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-green-600\", \"dark:text-green-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"space-y-3\"], [4, \"ngIf\"], [\"routerLink\", \"/projects\", 1, \"block\", \"w-full\", \"px-6\", \"py-3\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"rounded-xl\", \"transition-all\", \"duration-200\", \"font-medium\", \"text-center\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10 19l-7-7m0 0l7-7m-7 7h18\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-4\"], [\"class\", \"group\", 4, \"ngFor\", \"ngForOf\"], [1, \"group\"], [1, \"bg-[#edf1f4]/70\", \"dark:bg-[#2a2a2a]/70\", \"rounded-xl\", \"p-4\", \"border\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"hover:border-[#4f5fad]\", \"dark:hover:border-[#6d78c9]\", \"transition-all\", \"duration-200\", \"hover:shadow-md\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"flex-1\", \"min-w-0\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"p-2\", \"rounded-lg\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"], [1, \"flex-1\", \"min-w-0\"], [1, \"text-sm\", \"font-medium\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"truncate\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"download\", \"\", 1, \"ml-3\", \"px-3\", \"py-2\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:bg-[#4f5fad]\", \"hover:text-white\", \"dark:hover:bg-[#6d78c9]\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"text-xs\", \"font-medium\", \"group-hover:scale-105\", 3, \"href\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"text-center\", \"py-8\"], [1, \"bg-[#edf1f4]/70\", \"dark:bg-[#2a2a2a]/70\", \"rounded-xl\", \"p-6\"], [1, \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"p-3\", \"rounded-lg\", \"inline-flex\", \"items-center\", \"justify-center\", \"mb-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"p-4\", \"bg-green-50\", \"dark:bg-green-900/20\", \"border\", \"border-green-200\", \"dark:border-green-800/30\", \"rounded-xl\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"font-semibold\", \"text-green-800\", \"dark:text-green-400\"], [1, \"text-xs\", \"text-green-600\", \"dark:text-green-500\"], [1, \"block\", \"w-full\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", \"text-center\", 3, \"routerLink\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"]],\n        template: function ProjectDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"nav\", 6)(7, \"a\", 7);\n            i0.ɵɵtext(8, \"Mes Projets\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(9, \"svg\", 8);\n            i0.ɵɵelement(10, \"path\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(11, \"span\", 10);\n            i0.ɵɵtext(12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"div\", 13)(16, \"div\", 14);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(17, \"svg\", 15);\n            i0.ɵɵelement(18, \"path\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(19, \"div\")(20, \"h1\", 17);\n            i0.ɵɵtext(21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"div\", 18)(23, \"div\", 19);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(24, \"svg\", 20);\n            i0.ɵɵelement(25, \"path\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(26, \"span\", 22);\n            i0.ɵɵtext(27);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"div\", 19);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(29, \"svg\", 23);\n            i0.ɵɵelement(30, \"path\", 24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(31, \"span\", 25);\n            i0.ɵɵtext(32);\n            i0.ɵɵpipe(33, \"date\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(34, \"div\", 26)(35, \"span\", 27);\n            i0.ɵɵtext(36);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(37, ProjectDetailComponent_div_37_Template, 4, 0, \"div\", 28);\n            i0.ɵɵtemplate(38, ProjectDetailComponent_div_38_Template, 84, 13, \"div\", 29);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.titre) || \"D\\u00E9tails du projet\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.projet == null ? null : ctx.projet.titre) || \"Chargement...\", \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.groupe) || \"Tous les groupes\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(33, 8, ctx.projet == null ? null : ctx.projet.dateLimite, \"dd/MM/yyyy\" || \"Pas de date limite\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngClass\", ctx.getStatusClass());\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getStatusText(), \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.RouterLink, i5.DatePipe],\n        styles: [\".project-container[_ngcontent-%COMP%]{padding:1.5rem;background-color:#fff;border-radius:.5rem;box-shadow:0 2px 4px #0000001a}.project-header[_ngcontent-%COMP%]{margin-bottom:1.5rem;border-bottom:1px solid #e5e7eb;padding-bottom:1rem}.project-description[_ngcontent-%COMP%]{margin-bottom:1.5rem}.project-meta[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:1rem;margin-bottom:1.5rem}.project-meta-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}\"]\n      });\n    }\n  }\n  return ProjectDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}