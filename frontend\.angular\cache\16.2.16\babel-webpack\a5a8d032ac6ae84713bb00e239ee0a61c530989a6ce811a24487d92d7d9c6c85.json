{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let ThemeService = /*#__PURE__*/(() => {\n  class ThemeService {\n    constructor() {\n      this.darkMode = new BehaviorSubject(false);\n      this.darkMode$ = this.darkMode.asObservable();\n      // Check if user has a theme preference in localStorage\n      const savedTheme = localStorage.getItem('darkMode');\n      if (savedTheme) {\n        this.darkMode.next(savedTheme === 'true');\n        this.applyTheme(savedTheme === 'true');\n      } else {\n        // Check if user prefers dark mode at OS level\n        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n        this.darkMode.next(prefersDark);\n        this.applyTheme(prefersDark);\n      }\n    }\n    toggleDarkMode() {\n      const newValue = !this.darkMode.value;\n      this.darkMode.next(newValue);\n      localStorage.setItem('darkMode', String(newValue));\n      this.applyTheme(newValue);\n    }\n    applyTheme(isDark) {\n      if (isDark) {\n        document.documentElement.classList.add('dark');\n        console.log('Dark mode enabled');\n      } else {\n        document.documentElement.classList.remove('dark');\n        console.log('Dark mode disabled');\n      }\n    }\n    static {\n      this.ɵfac = function ThemeService_Factory(t) {\n        return new (t || ThemeService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ThemeService,\n        factory: ThemeService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ThemeService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}