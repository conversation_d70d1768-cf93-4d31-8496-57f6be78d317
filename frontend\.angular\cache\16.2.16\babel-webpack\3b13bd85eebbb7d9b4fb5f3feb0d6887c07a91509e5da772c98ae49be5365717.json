{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isFuture\n * @category Common Helpers\n * @summary Is the given date in the future?\n * @pure false\n *\n * @description\n * Is the given date in the future?\n *\n * @param date - The date to check\n *\n * @returns The date is in the future\n *\n * @example\n * // If today is 6 October 2014, is 31 December 2014 in the future?\n * const result = isFuture(new Date(2014, 11, 31))\n * //=> true\n */\nexport function isFuture(date) {\n  return +toDate(date) > Date.now();\n}\n\n// Fallback for modularized imports:\nexport default isFuture;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}