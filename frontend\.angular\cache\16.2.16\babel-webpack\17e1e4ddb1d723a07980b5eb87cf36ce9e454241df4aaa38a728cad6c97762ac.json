{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { compact } from \"./compact.js\";\nexport function mergeOptions(defaults, options) {\n  return compact(defaults, options, options.variables && {\n    variables: compact(__assign(__assign({}, defaults && defaults.variables), options.variables))\n  });\n}\n//# sourceMappingURL=mergeOptions.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}