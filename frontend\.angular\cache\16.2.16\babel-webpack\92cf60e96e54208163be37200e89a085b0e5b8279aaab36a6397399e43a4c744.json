{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link interval} function options.\n */\n\n/**\n * The {@link interval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the start argument,\n * then the end interval date. If a context function is passed, it uses the context\n * function return type.\n */\n\n/**\n * @name interval\n * @category Interval Helpers\n * @summary Creates an interval object and validates its values.\n *\n * @description\n * Creates a normalized interval object and validates its values. If the interval is invalid, an exception is thrown.\n *\n * @typeParam StartDate - Start date type.\n * @typeParam EndDate - End date type.\n * @typeParam Options - Options type.\n *\n * @param start - The start of the interval.\n * @param end - The end of the interval.\n * @param options - The options object.\n *\n * @throws `Start date is invalid` when `start` is invalid.\n * @throws `End date is invalid` when `end` is invalid.\n * @throws `End date must be after start date` when end is before `start` and `options.assertPositive` is true.\n *\n * @returns The normalized and validated interval object.\n */\nexport function interval(start, end, options) {\n  const [_start, _end] = normalizeDates(options?.in, start, end);\n  if (isNaN(+_start)) throw new TypeError(\"Start date is invalid\");\n  if (isNaN(+_end)) throw new TypeError(\"End date is invalid\");\n  if (options?.assertPositive && +_start > +_end) throw new TypeError(\"End date must be after start date\");\n  return {\n    start: _start,\n    end: _end\n  };\n}\n\n// Fallback for modularized imports:\nexport default interval;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}