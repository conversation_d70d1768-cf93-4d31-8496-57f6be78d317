{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { keyValMap } from '../jsutils/keyValMap.mjs';\nimport { naturalCompare } from '../jsutils/naturalCompare.mjs';\nimport { GraphQLEnumType, GraphQLInputObjectType, GraphQLInterfaceType, GraphQLList, GraphQLNonNull, GraphQLObjectType, GraphQLUnionType, isEnumType, isInputObjectType, isInterfaceType, isListType, isNonNullType, isObjectType, isScalarType, isUnionType } from '../type/definition.mjs';\nimport { GraphQLDirective } from '../type/directives.mjs';\nimport { isIntrospectionType } from '../type/introspection.mjs';\nimport { GraphQLSchema } from '../type/schema.mjs';\n/**\n * Sort GraphQLSchema.\n *\n * This function returns a sorted copy of the given GraphQLSchema.\n */\n\nexport function lexicographicSortSchema(schema) {\n  const schemaConfig = schema.toConfig();\n  const typeMap = keyValMap(sortByName(schemaConfig.types), type => type.name, sortNamedType);\n  return new GraphQLSchema({\n    ...schemaConfig,\n    types: Object.values(typeMap),\n    directives: sortByName(schemaConfig.directives).map(sortDirective),\n    query: replaceMaybeType(schemaConfig.query),\n    mutation: replaceMaybeType(schemaConfig.mutation),\n    subscription: replaceMaybeType(schemaConfig.subscription)\n  });\n  function replaceType(type) {\n    if (isListType(type)) {\n      // @ts-expect-error\n      return new GraphQLList(replaceType(type.ofType));\n    } else if (isNonNullType(type)) {\n      // @ts-expect-error\n      return new GraphQLNonNull(replaceType(type.ofType));\n    } // @ts-expect-error FIXME: TS Conversion\n\n    return replaceNamedType(type);\n  }\n  function replaceNamedType(type) {\n    return typeMap[type.name];\n  }\n  function replaceMaybeType(maybeType) {\n    return maybeType && replaceNamedType(maybeType);\n  }\n  function sortDirective(directive) {\n    const config = directive.toConfig();\n    return new GraphQLDirective({\n      ...config,\n      locations: sortBy(config.locations, x => x),\n      args: sortArgs(config.args)\n    });\n  }\n  function sortArgs(args) {\n    return sortObjMap(args, arg => ({\n      ...arg,\n      type: replaceType(arg.type)\n    }));\n  }\n  function sortFields(fieldsMap) {\n    return sortObjMap(fieldsMap, field => ({\n      ...field,\n      type: replaceType(field.type),\n      args: field.args && sortArgs(field.args)\n    }));\n  }\n  function sortInputFields(fieldsMap) {\n    return sortObjMap(fieldsMap, field => ({\n      ...field,\n      type: replaceType(field.type)\n    }));\n  }\n  function sortTypes(array) {\n    return sortByName(array).map(replaceNamedType);\n  }\n  function sortNamedType(type) {\n    if (isScalarType(type) || isIntrospectionType(type)) {\n      return type;\n    }\n    if (isObjectType(type)) {\n      const config = type.toConfig();\n      return new GraphQLObjectType({\n        ...config,\n        interfaces: () => sortTypes(config.interfaces),\n        fields: () => sortFields(config.fields)\n      });\n    }\n    if (isInterfaceType(type)) {\n      const config = type.toConfig();\n      return new GraphQLInterfaceType({\n        ...config,\n        interfaces: () => sortTypes(config.interfaces),\n        fields: () => sortFields(config.fields)\n      });\n    }\n    if (isUnionType(type)) {\n      const config = type.toConfig();\n      return new GraphQLUnionType({\n        ...config,\n        types: () => sortTypes(config.types)\n      });\n    }\n    if (isEnumType(type)) {\n      const config = type.toConfig();\n      return new GraphQLEnumType({\n        ...config,\n        values: sortObjMap(config.values, value => value)\n      });\n    }\n    if (isInputObjectType(type)) {\n      const config = type.toConfig();\n      return new GraphQLInputObjectType({\n        ...config,\n        fields: () => sortInputFields(config.fields)\n      });\n    }\n    /* c8 ignore next 3 */\n    // Not reachable, all possible types have been considered.\n\n    false || invariant(false, 'Unexpected type: ' + inspect(type));\n  }\n}\nfunction sortObjMap(map, sortValueFn) {\n  const sortedMap = Object.create(null);\n  for (const key of Object.keys(map).sort(naturalCompare)) {\n    sortedMap[key] = sortValueFn(map[key]);\n  }\n  return sortedMap;\n}\nfunction sortByName(array) {\n  return sortBy(array, obj => obj.name);\n}\nfunction sortBy(array, mapToKey) {\n  return array.slice().sort((obj1, obj2) => {\n    const key1 = mapToKey(obj1);\n    const key2 = mapToKey(obj2);\n    return naturalCompare(key1, key2);\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}