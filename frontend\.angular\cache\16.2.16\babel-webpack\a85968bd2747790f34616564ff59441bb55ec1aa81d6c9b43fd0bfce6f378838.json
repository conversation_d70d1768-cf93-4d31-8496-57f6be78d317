{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/message.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/logger.service\";\nimport * as i4 from \"@angular/common\";\nexport let MessageLayoutComponent = /*#__PURE__*/(() => {\n  class MessageLayoutComponent {\n    constructor(MessageService, route, logger) {\n      this.MessageService = MessageService;\n      this.route = route;\n      this.logger = logger;\n      this._sidebarVisible = new BehaviorSubject(true);\n      this.sidebarVisible$ = this._sidebarVisible.asObservable();\n      this.subscriptions = [];\n      this.context = 'messages';\n    }\n    ngOnInit() {\n      // Détermine le contexte (messages ou notifications)\n      this.context = this.route.snapshot.data['context'] || 'messages';\n      if (this.context === 'messages') {\n        // S'abonner aux changements de conversation active\n        this.subscriptions.push(this.MessageService.activeConversation$.subscribe(conversationId => {\n          // Ne s'abonner aux messages que si une conversation est sélectionnée\n          if (conversationId) {\n            this.conversationId = conversationId;\n            // Désabonner de l'ancienne souscription si elle existe\n            this.subscriptions.forEach(sub => sub.unsubscribe());\n            this.subscriptions = [];\n            // S'abonner aux nouveaux messages pour cette conversation\n            this.subscriptions.push(this.MessageService.subscribeToNewMessages(conversationId).subscribe({\n              next: message => {\n                // Gestion des nouveaux messages\n              },\n              error: err => this.logger.error('MessageLayout', 'Error in message subscription', err)\n            }));\n          }\n        }));\n      }\n      // Ajoutez ici la logique spécifique aux notifications si nécessaire\n    }\n\n    toggleSidebar() {\n      this._sidebarVisible.next(!this._sidebarVisible.value);\n    }\n    hideSidebar() {\n      this._sidebarVisible.next(false);\n    }\n    showSidebar() {\n      this._sidebarVisible.next(true);\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    static {\n      this.ɵfac = function MessageLayoutComponent_Factory(t) {\n        return new (t || MessageLayoutComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.LoggerService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: MessageLayoutComponent,\n        selectors: [[\"app-message-layout\"]],\n        decls: 4,\n        vars: 4,\n        consts: [[1, \"layout-container\", \"futuristic-layout\"], [1, \"main-content\", \"futuristic-main-content\"]],\n        template: function MessageLayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵpipe(2, \"async\");\n            i0.ɵɵelement(3, \"router-outlet\");\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"sidebar-hidden\", !i0.ɵɵpipeBind1(2, 2, ctx.sidebarVisible$));\n          }\n        },\n        dependencies: [i2.RouterOutlet, i4.AsyncPipe],\n        styles: [\".layout-container[_ngcontent-%COMP%]{display:flex;height:100vh;width:100%;overflow:hidden}.main-content[_ngcontent-%COMP%]{flex-grow:1;transition:margin-left .3s ease;overflow:hidden}.sidebar-hidden[_ngcontent-%COMP%]{margin-left:0}.futuristic-layout[_ngcontent-%COMP%]{background-color:var(--dark-bg);color:var(--text-light)}.futuristic-main-content[_ngcontent-%COMP%]{background-color:var(--dark-bg);position:relative}.futuristic-layout[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background-image:linear-gradient(rgba(0,247,255,.03) 1px,transparent 1px),linear-gradient(90deg,rgba(0,247,255,.03) 1px,transparent 1px);background-size:20px 20px;pointer-events:none;z-index:0}\"]\n      });\n    }\n  }\n  return MessageLayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}