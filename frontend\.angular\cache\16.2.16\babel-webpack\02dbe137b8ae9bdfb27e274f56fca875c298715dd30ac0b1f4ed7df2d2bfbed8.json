{"ast": null, "code": "import { catchError, finalize, takeUntil } from 'rxjs/operators';\nimport { Subject, of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/rendus.service\";\nimport * as i2 from \"../../../../services/evaluation.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction EvaluationsListComponent_div_25_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_25_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.clearSearch());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 31);\n    i0.ɵɵelement(3, \"path\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EvaluationsListComponent_div_25_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r7.filteredEvaluations.length, \" r\\u00E9sultat(s) trouv\\u00E9(s) pour \\\"\", ctx_r7.searchTerm, \"\\\" \");\n  }\n}\nfunction EvaluationsListComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"div\", 23);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 24);\n    i0.ɵɵelement(4, \"path\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"input\", 26);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_25_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.searchTerm = $event);\n    })(\"input\", function EvaluationsListComponent_div_25_Template_input_input_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onSearchChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, EvaluationsListComponent_div_25_div_6_Template, 4, 0, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, EvaluationsListComponent_div_25_div_7_Template, 2, 2, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.searchTerm);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.searchTerm);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.searchTerm);\n  }\n}\nfunction EvaluationsListComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"div\", 35)(3, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 37);\n    i0.ɵɵtext(5, \"Chargement des \\u00E9valuations...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EvaluationsListComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 3)(2, \"div\", 39);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 40);\n    i0.ɵɵelement(4, \"path\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"p\", 42);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_27_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.loadEvaluations());\n    });\n    i0.ɵɵtext(8, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.error);\n  }\n}\nfunction EvaluationsListComponent_div_28_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groupe_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", groupe_r17);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(groupe_r17);\n  }\n}\nfunction EvaluationsListComponent_div_28_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const projet_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", projet_r18._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(projet_r18.titre);\n  }\n}\nfunction EvaluationsListComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45)(2, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 47);\n    i0.ɵɵelement(4, \"path\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"h2\", 49);\n    i0.ɵɵtext(6, \"Filtres et recherche\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 50)(8, \"div\", 51)(9, \"label\", 52)(10, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 54);\n    i0.ɵɵelement(12, \"path\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Filtrer par groupe\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"select\", 56);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_28_Template_select_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.filterGroupe = $event);\n    })(\"change\", function EvaluationsListComponent_div_28_Template_select_change_15_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.applyFilters());\n    });\n    i0.ɵɵelementStart(16, \"option\", 57);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, EvaluationsListComponent_div_28_option_18_Template, 2, 2, \"option\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 51)(20, \"label\", 52)(21, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(22, \"svg\", 54);\n    i0.ɵɵelement(23, \"path\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25, \"Filtrer par projet\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"select\", 56);\n    i0.ɵɵlistener(\"ngModelChange\", function EvaluationsListComponent_div_28_Template_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.filterProjet = $event);\n    })(\"change\", function EvaluationsListComponent_div_28_Template_select_change_26_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.applyFilters());\n    });\n    i0.ɵɵelementStart(27, \"option\", 57);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, EvaluationsListComponent_div_28_option_29_Template, 2, 2, \"option\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 51)(31, \"label\", 52)(32, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(33, \"svg\", 54);\n    i0.ɵɵelement(34, \"path\", 60)(35, \"path\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(36, \"span\");\n    i0.ɵɵtext(37, \"Actions rapides\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"div\", 62)(39, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_28_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.refreshList());\n    });\n    i0.ɵɵelementStart(40, \"div\", 64);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(41, \"svg\", 65);\n    i0.ɵɵelement(42, \"path\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(43, \"span\");\n    i0.ɵɵtext(44, \"Actualiser\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.filterGroupe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Tous les groupes (\", ctx_r3.groupes.length, \")\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.groupes);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.filterProjet);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Tous les projets (\", ctx_r3.projets.length, \")\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.projets);\n  }\n}\nfunction EvaluationsListComponent_div_29_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71)(2, \"div\", 4)(3, \"div\", 22)(4, \"div\", 72);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 74);\n    i0.ɵɵelement(8, \"path\", 75);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"div\", 76)(10, \"h3\", 77);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 78);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 79)(15, \"div\", 80);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(16, \"svg\", 54);\n    i0.ɵɵelement(17, \"path\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(18, \"span\", 81);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 80);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(21, \"svg\", 54);\n    i0.ɵɵelement(22, \"path\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(23, \"span\", 81);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(25, \"div\", 82)(26, \"div\", 53)(27, \"div\", 83);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 84);\n    i0.ɵɵelement(29, \"path\", 85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(30, \"div\")(31, \"p\", 86);\n    i0.ɵɵtext(32, \"\\u00C9valu\\u00E9e le\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 87);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 53)(36, \"div\", 88);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(37, \"svg\", 65);\n    i0.ɵɵelement(38, \"path\", 89);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(39, \"div\")(40, \"p\", 86);\n    i0.ɵɵtext(41, \"Score total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"span\", 90);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"div\", 91)(45, \"p\", 92);\n    i0.ɵɵtext(46, \"D\\u00E9tail des scores\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 93)(48, \"div\", 94)(49, \"span\");\n    i0.ɵɵtext(50, \"Structure:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\", 42);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 94)(54, \"span\");\n    i0.ɵɵtext(55, \"Pratiques:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"span\", 42);\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 94)(59, \"span\");\n    i0.ɵɵtext(60, \"Fonctionnalit\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"span\", 42);\n    i0.ɵɵtext(62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 94)(64, \"span\");\n    i0.ɵɵtext(65, \"Originalit\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"span\", 42);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(68, \"div\", 95)(69, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_29_div_1_Template_button_click_69_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const evaluation_r26 = restoredCtx.$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.viewEvaluationDetails(evaluation_r26.rendu));\n    });\n    i0.ɵɵelementStart(70, \"div\", 64);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(71, \"svg\", 97);\n    i0.ɵɵelement(72, \"path\", 61)(73, \"path\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(74, \"span\");\n    i0.ɵɵtext(75, \"Voir d\\u00E9tails\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(76, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_29_div_1_Template_button_click_76_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const evaluation_r26 = restoredCtx.$implicit;\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.editEvaluation(evaluation_r26.rendu));\n    });\n    i0.ɵɵelementStart(77, \"div\", 64);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(78, \"svg\", 97);\n    i0.ɵɵelement(79, \"path\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(80, \"span\");\n    i0.ɵɵtext(81, \"Modifier\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(82, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_29_div_1_Template_button_click_82_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const evaluation_r26 = restoredCtx.$implicit;\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.deleteEvaluation(evaluation_r26._id));\n    });\n    i0.ɵɵelementStart(83, \"div\", 64);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(84, \"svg\", 97);\n    i0.ɵɵelement(85, \"path\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(86, \"span\");\n    i0.ɵɵtext(87, \"Supprimer\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const evaluation_r26 = ctx.$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.getStudentInitials(evaluation_r26.etudiant), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.getStudentName(evaluation_r26.etudiant), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((evaluation_r26.etudiant == null ? null : evaluation_r26.etudiant.email) || \"Email non disponible\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.getStudentGroup(evaluation_r26.etudiant), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.getProjectTitle(evaluation_r26), \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r25.formatDate(evaluation_r26.dateEvaluation));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r25.getScoreIconClass(ctx_r25.getScoreTotal(evaluation_r26)));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", ctx_r25.getScoreColorClass(ctx_r25.getScoreTotal(evaluation_r26)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r25.getScoreTotal(evaluation_r26), \"/20 \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(evaluation_r26.scores.structure || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(evaluation_r26.scores.pratiques || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(evaluation_r26.scores.fonctionnalite || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(evaluation_r26.scores.originalite || 0);\n  }\n}\nfunction EvaluationsListComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtemplate(1, EvaluationsListComponent_div_29_div_1_Template, 88, 13, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.filteredEvaluations);\n  }\n}\nfunction EvaluationsListComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"div\", 104)(2, \"div\", 105);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 106);\n    i0.ɵɵelement(4, \"path\", 107);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"h3\", 108);\n    i0.ɵɵtext(6, \"Aucune \\u00E9valuation trouv\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 109);\n    i0.ɵɵtext(8, \"Aucune \\u00E9valuation ne correspond \\u00E0 vos crit\\u00E8res de filtrage actuels.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function EvaluationsListComponent_div_30_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.resetFilters());\n    });\n    i0.ɵɵelementStart(10, \"div\", 64);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 65);\n    i0.ɵɵelement(12, \"path\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"R\\u00E9initialiser les filtres\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nexport let EvaluationsListComponent = /*#__PURE__*/(() => {\n  class EvaluationsListComponent {\n    constructor(rendusService, evaluationService, router) {\n      this.rendusService = rendusService;\n      this.evaluationService = evaluationService;\n      this.router = router;\n      this.evaluations = [];\n      this.filteredEvaluations = [];\n      this.isLoading = true;\n      this.error = '';\n      this.searchTerm = '';\n      this.filterGroupe = '';\n      this.filterProjet = '';\n      this.groupes = [];\n      this.projets = [];\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      this.loadEvaluations();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    loadEvaluations() {\n      this.isLoading = true;\n      this.error = '';\n      console.log('Début du chargement des évaluations...');\n      this.evaluationService.getAllEvaluations().pipe(takeUntil(this.destroy$), catchError(err => {\n        console.error('Erreur lors du chargement des évaluations:', err);\n        this.error = 'Impossible de charger les évaluations. Veuillez réessayer plus tard.';\n        this.isLoading = false;\n        return of([]);\n      }), finalize(() => {\n        console.log('Finalisation du chargement des évaluations');\n        this.isLoading = false;\n      })).subscribe({\n        next: evaluations => {\n          console.log('Évaluations reçues:', evaluations);\n          if (!Array.isArray(evaluations)) {\n            console.error('Les données reçues ne sont pas un tableau:', evaluations);\n            this.error = 'Format de données incorrect. Veuillez réessayer plus tard.';\n            return;\n          }\n          // Vérifier et compléter les données manquantes\n          this.evaluations = evaluations.map(evaluation => {\n            const evalWithDetails = evaluation;\n            // Vérifier si les détails du projet sont disponibles\n            if (!evalWithDetails.projetDetails || !evalWithDetails.projetDetails.titre) {\n              console.warn('Détails du projet manquants pour l\\'évaluation:', evalWithDetails._id);\n              // Si le rendu contient des détails de projet, les utiliser\n              if (evalWithDetails.renduDetails && evalWithDetails.renduDetails.projet) {\n                evalWithDetails.projetDetails = evalWithDetails.renduDetails.projet;\n              }\n            }\n            return evalWithDetails;\n          });\n          this.extractGroupesAndProjets();\n          this.applyFilters();\n        }\n      });\n    }\n    extractGroupesAndProjets() {\n      // Extraire les groupes uniques\n      const groupesSet = new Set();\n      this.evaluations.forEach(evaluation => {\n        if (evaluation.etudiant) {\n          const groupeName = this.getStudentGroup(evaluation.etudiant);\n          if (groupeName && groupeName !== 'Non spécifié') {\n            groupesSet.add(groupeName);\n          }\n        }\n      });\n      this.groupes = Array.from(groupesSet).sort();\n      // Extraire les projets uniques\n      const projetsMap = new Map();\n      this.evaluations.forEach(evaluation => {\n        if (evaluation.projetDetails && evaluation.projetDetails._id) {\n          projetsMap.set(evaluation.projetDetails._id, evaluation.projetDetails);\n        }\n      });\n      this.projets = Array.from(projetsMap.values());\n    }\n    applyFilters() {\n      let results = this.evaluations;\n      // Filtre par terme de recherche\n      if (this.searchTerm.trim() !== '') {\n        const term = this.searchTerm.toLowerCase().trim();\n        results = results.filter(evaluation => {\n          if (!evaluation.etudiant) return false;\n          const studentName = this.getStudentName(evaluation.etudiant).toLowerCase();\n          const email = (evaluation.etudiant.email || '').toLowerCase();\n          const projectTitle = this.getProjectTitle(evaluation).toLowerCase();\n          const groupName = this.getStudentGroup(evaluation.etudiant).toLowerCase();\n          return studentName.includes(term) || email.includes(term) || projectTitle.includes(term) || groupName.includes(term);\n        });\n      }\n      // Filtre par groupe\n      if (this.filterGroupe) {\n        results = results.filter(evaluation => {\n          const groupeName = this.getStudentGroup(evaluation.etudiant);\n          return groupeName === this.filterGroupe;\n        });\n      }\n      // Filtre par projet\n      if (this.filterProjet) {\n        results = results.filter(evaluation => evaluation.projetDetails?._id === this.filterProjet);\n      }\n      this.filteredEvaluations = results;\n    }\n    onSearchChange() {\n      this.applyFilters();\n    }\n    clearSearch() {\n      this.searchTerm = '';\n      this.applyFilters();\n    }\n    refreshList() {\n      console.log('Actualisation de la liste des évaluations...');\n      // Réinitialiser tous les filtres\n      this.searchTerm = '';\n      this.filterGroupe = '';\n      this.filterProjet = '';\n      // Recharger complètement les données depuis le serveur\n      this.loadEvaluations();\n      console.log('Liste actualisée et filtres réinitialisés');\n    }\n    resetFilters() {\n      console.log('Réinitialisation des filtres...');\n      // Réinitialiser tous les filtres\n      this.searchTerm = '';\n      this.filterGroupe = '';\n      this.filterProjet = '';\n      // Appliquer les filtres (qui vont maintenant montrer toutes les évaluations)\n      this.applyFilters();\n      console.log('Filtres réinitialisés');\n    }\n    editEvaluation(renduId) {\n      this.router.navigate(['/admin/projects/edit-evaluation', renduId]);\n    }\n    viewEvaluationDetails(renduId) {\n      this.router.navigate(['/admin/projects/evaluation-details', renduId]);\n    }\n    getScoreTotal(evaluation) {\n      if (!evaluation.scores) return 0;\n      const scores = evaluation.scores;\n      return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n    }\n    getScoreClass(score) {\n      if (score >= 16) return 'text-green-600 bg-green-100';\n      if (score >= 12) return 'text-blue-600 bg-blue-100';\n      if (score >= 8) return 'text-yellow-600 bg-yellow-100';\n      return 'text-red-600 bg-red-100';\n    }\n    formatDate(date) {\n      if (!date) return 'Non disponible';\n      return new Date(date).toLocaleDateString();\n    }\n    // Nouvelles méthodes pour le design moderne\n    getStudentInitials(etudiant) {\n      if (!etudiant) return '??';\n      // Priorité 1: firstName + lastName (si lastName existe et n'est pas vide)\n      const firstName = etudiant.firstName || '';\n      const lastName = etudiant.lastName || '';\n      if (firstName && lastName && lastName.trim()) {\n        return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();\n      }\n      // Priorité 2: fullName (diviser en mots)\n      const fullName = etudiant.fullName || etudiant.name || etudiant.username || '';\n      if (fullName && fullName.trim()) {\n        const parts = fullName.trim().split(' ');\n        if (parts.length >= 2) {\n          return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();\n        } else {\n          // Si un seul mot, prendre les 2 premières lettres\n          return fullName.substring(0, 2).toUpperCase();\n        }\n      }\n      // Priorité 3: firstName seul (prendre les 2 premières lettres)\n      if (firstName && firstName.trim()) {\n        return firstName.substring(0, 2).toUpperCase();\n      }\n      return '??';\n    }\n    getStudentName(etudiant) {\n      if (!etudiant) return 'Utilisateur inconnu';\n      // Priorité 1: firstName + lastName (si lastName existe et n'est pas vide)\n      const firstName = etudiant.firstName || '';\n      const lastName = etudiant.lastName || '';\n      if (firstName && lastName && lastName.trim()) {\n        return `${firstName} ${lastName}`.trim();\n      }\n      // Priorité 2: fullName\n      const fullName = etudiant.fullName || etudiant.name || etudiant.username || '';\n      if (fullName && fullName.trim()) {\n        return fullName.trim();\n      }\n      // Priorité 3: firstName seul\n      if (firstName && firstName.trim()) {\n        return firstName.trim();\n      }\n      // Priorité 4: email comme fallback\n      if (etudiant.email) {\n        return etudiant.email;\n      }\n      return 'Utilisateur inconnu';\n    }\n    getStudentGroup(etudiant) {\n      if (!etudiant) return 'Non spécifié';\n      // Debug: afficher les données de l'étudiant\n      console.log('Données étudiant pour groupe:', {\n        email: etudiant.email,\n        group: etudiant.group,\n        groupe: etudiant.groupe,\n        groupName: etudiant.groupName,\n        department: etudiant.department,\n        allData: etudiant\n      });\n      // Si group est un objet (référence populée avec le modèle Group)\n      if (etudiant.group && typeof etudiant.group === 'object' && etudiant.group.name) {\n        console.log(`Groupe objet trouvé pour ${etudiant.email}: ${etudiant.group.name}`);\n        return etudiant.group.name;\n      }\n      // Si group est une chaîne directe (valeur ajoutée manuellement)\n      if (etudiant.group && typeof etudiant.group === 'string' && etudiant.group.trim()) {\n        console.log(`Groupe string trouvé pour ${etudiant.email}: ${etudiant.group}`);\n        return etudiant.group.trim();\n      }\n      // Fallback vers d'autres champs possibles\n      if (etudiant.groupe && typeof etudiant.groupe === 'string' && etudiant.groupe.trim()) {\n        console.log(`Groupe (ancien champ) trouvé pour ${etudiant.email}: ${etudiant.groupe}`);\n        return etudiant.groupe.trim();\n      }\n      if (etudiant.groupName && typeof etudiant.groupName === 'string' && etudiant.groupName.trim()) {\n        console.log(`GroupName trouvé pour ${etudiant.email}: ${etudiant.groupName}`);\n        return etudiant.groupName.trim();\n      }\n      if (etudiant.department && typeof etudiant.department === 'string' && etudiant.department.trim()) {\n        console.log(`Department trouvé pour ${etudiant.email}: ${etudiant.department}`);\n        return etudiant.department.trim();\n      }\n      console.log(`Aucun groupe trouvé pour ${etudiant.email}`);\n      return 'Non spécifié';\n    }\n    getProjectTitle(evaluation) {\n      return evaluation.projetDetails?.titre || evaluation.renduDetails?.projet?.titre || 'Projet inconnu';\n    }\n    getAverageScore() {\n      if (this.evaluations.length === 0) return '0';\n      const totalScore = this.evaluations.reduce((sum, evaluation) => {\n        return sum + this.getScoreTotal(evaluation);\n      }, 0);\n      const average = totalScore / this.evaluations.length;\n      return average.toFixed(1);\n    }\n    getScoreIconClass(score) {\n      if (score >= 16) return 'bg-success/10 dark:bg-dark-accent-secondary/10 text-success dark:text-dark-accent-secondary';\n      if (score >= 12) return 'bg-info/10 dark:bg-dark-accent-primary/10 text-info dark:text-dark-accent-primary';\n      if (score >= 8) return 'bg-warning/10 dark:bg-warning/20 text-warning dark:text-warning';\n      return 'bg-danger/10 dark:bg-danger-dark/20 text-danger dark:text-danger-dark';\n    }\n    getScoreColorClass(score) {\n      if (score >= 16) return 'text-success dark:text-dark-accent-secondary';\n      if (score >= 12) return 'text-info dark:text-dark-accent-primary';\n      if (score >= 8) return 'text-warning dark:text-warning';\n      return 'text-danger dark:text-danger-dark';\n    }\n    // Méthode pour supprimer une évaluation\n    deleteEvaluation(evaluationId) {\n      if (!confirm('Êtes-vous sûr de vouloir supprimer cette évaluation ? Cette action est irréversible.')) {\n        return;\n      }\n      this.evaluationService.deleteEvaluation(evaluationId).subscribe({\n        next: () => {\n          alert('Évaluation supprimée avec succès !');\n          this.loadEvaluations(); // Recharger la liste\n        },\n\n        error: err => {\n          console.error('Erreur lors de la suppression:', err);\n          alert('Erreur lors de la suppression de l\\'évaluation.');\n        }\n      });\n    }\n    static {\n      this.ɵfac = function EvaluationsListComponent_Factory(t) {\n        return new (t || EvaluationsListComponent)(i0.ɵɵdirectiveInject(i1.RendusService), i0.ɵɵdirectiveInject(i2.EvaluationService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EvaluationsListComponent,\n        selectors: [[\"app-evaluations-list\"]],\n        decls: 31,\n        vars: 8,\n        consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-dark-bg-primary\", \"transition-colors\", \"duration-300\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-2xl\", \"p-8\", \"mb-8\", \"shadow-xl\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"bg-white/20\", \"dark:bg-black/20\", \"p-3\", \"rounded-xl\", \"backdrop-blur-sm\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 01-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\"], [1, \"text-white/80\"], [1, \"hidden\", \"md:flex\", \"items-center\", \"space-x-4\", \"text-white/80\"], [1, \"text-center\"], [1, \"text-2xl\", \"font-bold\"], [1, \"text-sm\"], [1, \"w-px\", \"h-12\", \"bg-white/20\"], [\"class\", \"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-4 mb-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\", 4, \"ngIf\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-6 mb-6 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 mb-8 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50\", 4, \"ngIf\"], [\"class\", \"space-y-6\", 4, \"ngIf\"], [\"class\", \"text-center py-16\", 4, \"ngIf\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-4\", \"mb-6\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"relative\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-400\", \"dark:text-dark-text-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"], [\"type\", \"text\", \"placeholder\", \"Rechercher par nom, email, projet ou groupe...\", 1, \"block\", \"w-full\", \"pl-10\", \"pr-3\", \"py-3\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"leading-5\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-gray-500\", \"dark:placeholder-dark-text-secondary\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"class\", \"absolute inset-y-0 right-0 pr-3 flex items-center\", 4, \"ngIf\"], [\"class\", \"mt-2 text-sm text-text dark:text-dark-text-secondary\", 4, \"ngIf\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"pr-3\", \"flex\", \"items-center\"], [1, \"text-gray-400\", \"hover:text-gray-600\", \"dark:text-dark-text-secondary\", \"dark:hover:text-dark-text-primary\", \"transition-colors\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"mt-2\", \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-primary/30\", \"dark:border-dark-accent-primary/30\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-transparent\", \"border-t-primary\", \"dark:border-t-dark-accent-primary\", \"absolute\", \"top-0\", \"left-0\"], [1, \"mt-4\", \"text-text\", \"dark:text-dark-text-secondary\", \"animate-pulse\"], [1, \"bg-danger/10\", \"dark:bg-danger-dark/20\", \"border\", \"border-danger/30\", \"dark:border-danger-dark/40\", \"text-danger\", \"dark:text-danger-dark\", \"rounded-xl\", \"p-6\", \"mb-6\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-danger\", \"dark:text-danger-dark\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"font-medium\"], [1, \"px-4\", \"py-2\", \"bg-danger/20\", \"dark:bg-danger-dark/20\", \"text-danger\", \"dark:text-danger-dark\", \"rounded-lg\", \"hover:bg-danger/30\", \"dark:hover:bg-danger-dark/30\", \"transition-colors\", \"font-medium\", 3, \"click\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"mb-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-6\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [1, \"space-y-2\"], [1, \"block\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"flex\", \"flex-col\", \"space-y-2\"], [1, \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"], [3, \"value\"], [1, \"space-y-6\"], [\"class\", \"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"hover:shadow-xl\", \"transition-all\", \"duration-300\", \"group\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\", \"space-y-4\", \"lg:space-y-0\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-lg\", \"font-bold\", \"shadow-lg\"], [1, \"absolute\", \"-bottom-1\", \"-right-1\", \"w-6\", \"h-6\", \"bg-gradient-to-r\", \"from-success\", \"to-success-dark\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"flex-1\"], [1, \"text-lg\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mt-2\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"sm:items-center\", \"space-y-3\", \"sm:space-y-0\", \"sm:space-x-6\"], [1, \"bg-info/10\", \"dark:bg-dark-accent-primary/10\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-info\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L16 7\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"p-2\", \"rounded-lg\", 3, \"ngClass\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"], [1, \"text-lg\", \"font-bold\", 3, \"ngClass\"], [1, \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-lg\", \"p-3\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\", \"mb-1\"], [1, \"grid\", \"grid-cols-2\", \"gap-1\", \"text-xs\"], [1, \"flex\", \"justify-between\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-2\"], [1, \"group/btn\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-info\", \"to-primary\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"group-hover/btn:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"], [1, \"group/btn\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary-dark\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"group/btn\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-danger\", \"to-danger-dark\", \"dark:from-danger-dark\", \"dark:to-danger\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"text-center\", \"py-16\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-12\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"max-w-md\", \"mx-auto\"], [1, \"bg-gradient-to-br\", \"from-primary/10\", \"to-secondary/10\", \"dark:from-dark-accent-primary/20\", \"dark:to-dark-accent-secondary/20\", \"rounded-2xl\", \"p-6\", \"mb-6\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-16\", \"w-16\", \"mx-auto\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 01-2 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-2\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [1, \"px-6\", \"py-2\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"]],\n        template: function EvaluationsListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(6, \"svg\", 6);\n            i0.ɵɵelement(7, \"path\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(8, \"div\")(9, \"h1\", 8);\n            i0.ɵɵtext(10, \"Liste des \\u00C9valuations\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"p\", 9);\n            i0.ɵɵtext(12, \"Gestion et suivi des \\u00E9valuations de projets\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 10)(14, \"div\", 11)(15, \"div\", 12);\n            i0.ɵɵtext(16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 13);\n            i0.ɵɵtext(18, \"Total\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(19, \"div\", 14);\n            i0.ɵɵelementStart(20, \"div\", 11)(21, \"div\", 12);\n            i0.ɵɵtext(22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"div\", 13);\n            i0.ɵɵtext(24, \"Moyenne\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(25, EvaluationsListComponent_div_25_Template, 8, 3, \"div\", 15);\n            i0.ɵɵtemplate(26, EvaluationsListComponent_div_26_Template, 6, 0, \"div\", 16);\n            i0.ɵɵtemplate(27, EvaluationsListComponent_div_27_Template, 9, 1, \"div\", 17);\n            i0.ɵɵtemplate(28, EvaluationsListComponent_div_28_Template, 45, 6, \"div\", 18);\n            i0.ɵɵtemplate(29, EvaluationsListComponent_div_29_Template, 2, 1, \"div\", 19);\n            i0.ɵɵtemplate(30, EvaluationsListComponent_div_30_Template, 15, 0, \"div\", 20);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(16);\n            i0.ɵɵtextInterpolate(ctx.evaluations.length);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.getAverageScore());\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.filteredEvaluations.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.filteredEvaluations.length === 0);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n        styles: [\".loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:2rem 0}.error-message[_ngcontent-%COMP%]{color:#dc3545;margin-top:.25rem}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_scaleIn{0%{opacity:0;transform:scale(.9)}to{opacity:1;transform:scale(1)}}.glass-card[_ngcontent-%COMP%]{backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2)}.dark[_ngcontent-%COMP%]   .glass-card[_ngcontent-%COMP%]{border:1px solid rgba(255,255,255,.1)}.evaluation-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out;transition:all .3s cubic-bezier(.4,0,.2,1)}.evaluation-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 20px 40px #0000001a}.dark[_ngcontent-%COMP%]   .evaluation-card[_ngcontent-%COMP%]:hover{box-shadow:0 20px 40px #0000004d}.btn-modern[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1)}.btn-modern[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.btn-modern[_ngcontent-%COMP%]:hover:before{left:100%}.avatar-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad 0%,#7826b5 100%);transition:all .3s ease}.dark[_ngcontent-%COMP%]   .avatar-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00f7ff 0%,#9d4edd 100%)}.avatar-gradient[_ngcontent-%COMP%]:hover{transform:scale(1.05);box-shadow:0 8px 25px #4f5fad4d}.dark[_ngcontent-%COMP%]   .avatar-gradient[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #00f7ff4d}.score-badge[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_scaleIn .4s ease-out;transition:all .2s ease}.score-badge[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.filter-select[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}.filter-select[_ngcontent-%COMP%]:focus{transform:translateY(-2px);box-shadow:0 8px 25px #4f5fad26}.dark[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]:focus{box-shadow:0 8px 25px #00f7ff26}.header-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad 0%,#7826b5 100%);animation:_ngcontent-%COMP%_slideInRight .8s ease-out}.dark[_ngcontent-%COMP%]   .header-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00f7ff 0%,#9d4edd 100%)}@media (max-width: 768px){.evaluation-card[_ngcontent-%COMP%]{margin-bottom:1rem}.btn-modern[_ngcontent-%COMP%]{width:100%;justify-content:center}.filter-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1rem}}.icon-hover[_ngcontent-%COMP%]{transition:transform .2s ease}.icon-hover[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(5deg)}.tooltip[_ngcontent-%COMP%]{position:relative}.tooltip[_ngcontent-%COMP%]:after{content:attr(data-tooltip);position:absolute;bottom:100%;left:50%;transform:translate(-50%);background:rgba(0,0,0,.8);color:#fff;padding:.5rem;border-radius:.375rem;font-size:.75rem;white-space:nowrap;opacity:0;pointer-events:none;transition:opacity .3s;z-index:1000}.tooltip[_ngcontent-%COMP%]:hover:after{opacity:1}.loading-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.5}}.empty-state[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .8s ease-out}.focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #4f5fad;outline-offset:2px}.dark[_ngcontent-%COMP%]   .focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #00f7ff}.delete-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#dc2626 0%,#b91c1c 100%);transition:all .3s ease}.delete-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#b91c1c 0%,#991b1b 100%);transform:scale(1.05);box-shadow:0 8px 25px #dc26264d}.dark[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ef4444 0%,#dc2626 100%)}.dark[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#dc2626 0%,#b91c1c 100%);box-shadow:0 8px 25px #ef44444d}\"]\n      });\n    }\n  }\n  return EvaluationsListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}