{"ast": null, "code": "import { isNonNullObject } from \"./objects.js\";\nexport function deepFreeze(value) {\n  var workSet = new Set([value]);\n  workSet.forEach(function (obj) {\n    if (isNonNullObject(obj) && shallowFreeze(obj) === obj) {\n      Object.getOwnPropertyNames(obj).forEach(function (name) {\n        if (isNonNullObject(obj[name])) workSet.add(obj[name]);\n      });\n    }\n  });\n  return value;\n}\nfunction shallowFreeze(obj) {\n  if (globalThis.__DEV__ !== false && !Object.isFrozen(obj)) {\n    try {\n      Object.freeze(obj);\n    } catch (e) {\n      // Some types like Uint8Array and Node.js's Buffer cannot be frozen, but\n      // they all throw a TypeError when you try, so we re-throw any exceptions\n      // that are not TypeErrors, since that would be unexpected.\n      if (e instanceof TypeError) return null;\n      throw e;\n    }\n  }\n  return obj;\n}\nexport function maybeDeepFreeze(obj) {\n  if (globalThis.__DEV__ !== false) {\n    deepFreeze(obj);\n  }\n  return obj;\n}\n//# sourceMappingURL=maybeDeepFreeze.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}