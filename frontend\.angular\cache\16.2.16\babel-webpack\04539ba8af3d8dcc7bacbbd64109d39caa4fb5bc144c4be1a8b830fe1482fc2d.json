{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/reunion.service\";\nimport * as i3 from \"src/app/services/planning.service\";\nimport * as i4 from \"@app/services/data.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"src/app/services/authuser.service\";\nimport * as i7 from \"@angular/common\";\nfunction ReunionFormComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error.message || \"Une erreur est survenue\", \" \");\n  }\n}\nfunction ReunionFormComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Le titre est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" La date est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" L'heure de d\\u00E9but est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" L'heure de fin est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_option_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const planning_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", planning_r8.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(planning_r8.titre);\n  }\n}\nfunction ReunionFormComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵtext(1, \" Le planning est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_ng_container_52_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r11._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(user_r11.username);\n  }\n}\nfunction ReunionFormComponent_ng_container_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ReunionFormComponent_ng_container_52_option_1_Template, 2, 2, \"option\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const users_r9 = ctx.ngIf;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", users_r9);\n  }\n}\nexport let ReunionFormComponent = /*#__PURE__*/(() => {\n  class ReunionFormComponent {\n    constructor(fb, reunionService, planningService, userService, route, router, authService) {\n      this.fb = fb;\n      this.reunionService = reunionService;\n      this.planningService = planningService;\n      this.userService = userService;\n      this.route = route;\n      this.router = router;\n      this.authService = authService;\n      this.plannings = [];\n      this.loading = true;\n      this.isSubmitting = false;\n      this.error = null;\n      this.isEditMode = false;\n      this.currentReunionId = null;\n      this.reunionForm = this.fb.group({\n        titre: ['', Validators.required],\n        description: [''],\n        date: ['', Validators.required],\n        heureDebut: ['', Validators.required],\n        heureFin: ['', Validators.required],\n        lieu: [''],\n        lienVisio: [''],\n        planning: ['', Validators.required],\n        participants: [[]]\n      });\n      this.users$ = this.userService.getAllUsers();\n    }\n    ngOnInit() {\n      this.loadPlannings();\n      this.checkEditMode();\n    }\n    checkEditMode() {\n      const reunionId = this.route.snapshot.paramMap.get('id');\n      if (reunionId) {\n        this.isEditMode = true;\n        this.currentReunionId = reunionId;\n        this.loadReunion(reunionId);\n      }\n    }\n    loadPlannings() {\n      const userId = this.authService.getCurrentUserId();\n      if (!userId) return;\n      this.planningService.getPlanningsByUser(userId).subscribe({\n        next: response => {\n          this.plannings = response.plannings || [];\n        },\n        error: err => {\n          this.error = err;\n        }\n      });\n    }\n    loadReunion(id) {\n      this.reunionService.getReunionById(id).subscribe({\n        next: reunion => {\n          this.reunionForm.patchValue({\n            titre: reunion.titre,\n            description: reunion.description,\n            dateDebut: this.formatDateForInput(reunion.dateDebut),\n            dateFin: this.formatDateForInput(reunion.dateFin),\n            lieu: reunion.lieu,\n            lienVisio: reunion.lienVisio,\n            planningId: reunion.planningId,\n            participants: reunion.participants\n          });\n          this.loading = false;\n        },\n        error: err => {\n          this.error = err;\n          this.loading = false;\n        }\n      });\n    }\n    formatDateForInput(date) {\n      return new Date(date).toISOString().slice(0, 16); // yyyy-MM-ddTHH:mm\n    }\n\n    onSubmit() {\n      if (this.reunionForm.invalid) return;\n      this.isSubmitting = true;\n      const formValue = this.reunionForm.value;\n      const date = formValue.date; // already in yyyy-MM-dd format from input[type=date]\n      const heureDebut = formValue.heureDebut; // already in HH:mm format from input[type=time]\n      const heureFin = formValue.heureFin;\n      const reunionData = {\n        titre: formValue.titre,\n        description: formValue.description,\n        date: date,\n        heureDebut: heureDebut,\n        heureFin: heureFin,\n        lieu: formValue.lieu,\n        lienVisio: formValue.lienVisio,\n        planning: formValue.planning,\n        participants: formValue.participants || []\n      };\n      console.log(reunionData);\n      this.reunionService.createReunion(reunionData).subscribe({\n        next: res => {\n          this.router.navigate(['/reunions', res?.id]);\n          this.isSubmitting = false;\n        },\n        error: err => {\n          this.error = err;\n          this.isSubmitting = false;\n        }\n      });\n    }\n    goReunion() {\n      this.router.navigate(['/reunions']);\n    }\n    static {\n      this.ɵfac = function ReunionFormComponent_Factory(t) {\n        return new (t || ReunionFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ReunionService), i0.ɵɵdirectiveInject(i3.PlanningService), i0.ɵɵdirectiveInject(i4.DataService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.AuthuserService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ReunionFormComponent,\n        selectors: [[\"app-reunion-form\"]],\n        decls: 59,\n        vars: 14,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"max-w-3xl\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\", \"mb-6\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-6\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-6\"], [\"for\", \"titre\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"titre\", \"type\", \"text\", \"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"class\", \"text-red-500 text-sm mt-1\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"3\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [\"for\", \"date\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"date\", \"type\", \"date\", \"formControlName\", \"date\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"heureDebut\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"heureDebut\", \"type\", \"time\", \"formControlName\", \"heureDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"heureFin\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"heureFin\", \"type\", \"time\", \"formControlName\", \"heureFin\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"for\", \"lieu\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"lieu\", \"type\", \"text\", \"formControlName\", \"lieu\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"lienVisio\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"lienVisio\", \"type\", \"url\", \"formControlName\", \"lienVisio\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"planning\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"planning\", \"formControlName\", \"planning\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [4, \"ngIf\"], [1, \"mt-6\", \"flex\", \"justify-end\", \"space-x-3\"], [\"type\", \"button\", 1, \"px-4\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"hover:bg-gray-50\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-4\", \"py-2\", \"rounded-md\", \"text-sm\", \"font-medium\", \"text-white\", \"bg-purple-600\", \"hover:bg-purple-700\", \"disabled:opacity-50\", 3, \"disabled\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"text-red-500\", \"text-sm\", \"mt-1\"], [3, \"value\"]],\n        template: function ReunionFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"h1\", 1);\n            i0.ɵɵtext(2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"form\", 2);\n            i0.ɵɵlistener(\"ngSubmit\", function ReunionFormComponent_Template_form_ngSubmit_3_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtemplate(4, ReunionFormComponent_div_4_Template, 2, 1, \"div\", 3);\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\")(7, \"label\", 5);\n            i0.ɵɵtext(8, \"Titre *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(9, \"input\", 6);\n            i0.ɵɵtemplate(10, ReunionFormComponent_div_10_Template, 2, 0, \"div\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"div\")(12, \"label\", 8);\n            i0.ɵɵtext(13, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(14, \"textarea\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"div\", 10)(16, \"div\")(17, \"label\", 11);\n            i0.ɵɵtext(18, \"Date *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(19, \"input\", 12);\n            i0.ɵɵtemplate(20, ReunionFormComponent_div_20_Template, 2, 0, \"div\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"div\")(22, \"label\", 13);\n            i0.ɵɵtext(23, \"Heure de d\\u00E9but *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(24, \"input\", 14);\n            i0.ɵɵtemplate(25, ReunionFormComponent_div_25_Template, 2, 0, \"div\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\")(27, \"label\", 15);\n            i0.ɵɵtext(28, \"Heure de fin *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(29, \"input\", 16);\n            i0.ɵɵtemplate(30, ReunionFormComponent_div_30_Template, 2, 0, \"div\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 17)(32, \"div\")(33, \"label\", 18);\n            i0.ɵɵtext(34, \"Lieu\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(35, \"input\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"div\")(37, \"label\", 20);\n            i0.ɵɵtext(38, \"Lien Visio\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(39, \"input\", 21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\")(41, \"label\", 22);\n            i0.ɵɵtext(42, \"Planning *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"select\", 23)(44, \"option\", 24);\n            i0.ɵɵtext(45, \"S\\u00E9lectionnez un planning\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(46, ReunionFormComponent_option_46_Template, 2, 2, \"option\", 25);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(47, ReunionFormComponent_div_47_Template, 2, 0, \"div\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"div\")(49, \"label\", 26);\n            i0.ɵɵtext(50, \"Participants\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"select\", 27);\n            i0.ɵɵtemplate(52, ReunionFormComponent_ng_container_52_Template, 2, 1, \"ng-container\", 28);\n            i0.ɵɵpipe(53, \"async\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(54, \"div\", 29)(55, \"button\", 30);\n            i0.ɵɵlistener(\"click\", function ReunionFormComponent_Template_button_click_55_listener() {\n              return ctx.goReunion();\n            });\n            i0.ɵɵtext(56, \" Annuler \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"button\", 31);\n            i0.ɵɵtext(58);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_3_0;\n            let tmp_4_0;\n            let tmp_5_0;\n            let tmp_6_0;\n            let tmp_8_0;\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifier la R\\u00E9union\" : \"Nouvelle R\\u00E9union\", \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.reunionForm);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_5_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_6_0.touched));\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngForOf\", ctx.plannings);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.reunionForm.get(\"planning\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.reunionForm.get(\"planning\")) == null ? null : tmp_8_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(53, 12, ctx.users$));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"disabled\", ctx.reunionForm.invalid || ctx.isSubmitting);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"Enregistrement...\" : \"Enregistrer\", \" \");\n          }\n        },\n        dependencies: [i7.NgForOf, i7.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.SelectMultipleControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.AsyncPipe]\n      });\n    }\n  }\n  return ReunionFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}