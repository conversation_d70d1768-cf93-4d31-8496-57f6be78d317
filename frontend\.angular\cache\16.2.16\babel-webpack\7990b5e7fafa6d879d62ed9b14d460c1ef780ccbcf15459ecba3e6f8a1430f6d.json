{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@auth0/angular-jwt\";\nexport let AuthadminService = /*#__PURE__*/(() => {\n  class AuthadminService {\n    constructor(http, jwtHelper) {\n      this.http = http;\n      this.jwtHelper = jwtHelper;\n    }\n    // login\n    login(body) {\n      return this.http.post(`${environment.urlBackend}auth/login`, body);\n    }\n    saveDataProfil(token) {\n      localStorage.setItem('token', token);\n    }\n    getUser() {\n      let token = localStorage.getItem('token');\n      let decodedToken = this.jwtHelper.decodeToken(token);\n      return decodedToken;\n    }\n    loggedIn() {\n      let token = localStorage.getItem('token');\n      if (!token) {\n        return false;\n      }\n      if (this.jwtHelper.decodeToken(token).role !== 'admin') {\n        return false;\n      }\n      if (this.jwtHelper.isTokenExpired(token)) {\n        return false;\n      }\n      return true;\n    }\n    clearAuthData() {\n      localStorage.removeItem('token');\n    }\n    static {\n      this.ɵfac = function AuthadminService_Factory(t) {\n        return new (t || AuthadminService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.JwtHelperService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthadminService,\n        factory: AuthadminService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthadminService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}