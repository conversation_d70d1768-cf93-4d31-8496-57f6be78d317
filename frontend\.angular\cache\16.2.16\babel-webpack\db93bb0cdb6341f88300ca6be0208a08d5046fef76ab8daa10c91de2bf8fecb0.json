{"ast": null, "code": "import { newInvariantError } from \"../../utilities/globals/index.js\";\nexport function validateOperation(operation) {\n  var OPERATION_FIELDS = [\"query\", \"operationName\", \"variables\", \"extensions\", \"context\"];\n  for (var _i = 0, _a = Object.keys(operation); _i < _a.length; _i++) {\n    var key = _a[_i];\n    if (OPERATION_FIELDS.indexOf(key) < 0) {\n      throw newInvariantError(46, key);\n    }\n  }\n  return operation;\n}\n//# sourceMappingURL=validateOperation.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}