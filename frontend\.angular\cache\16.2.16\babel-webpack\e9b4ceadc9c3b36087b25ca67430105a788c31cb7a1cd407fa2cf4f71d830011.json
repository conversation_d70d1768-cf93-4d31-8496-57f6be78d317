{"ast": null, "code": "/**\n * Returns true if the provided object implements the AsyncIterator protocol via\n * implementing a `Symbol.asyncIterator` method.\n */\nexport function isAsyncIterable(maybeAsyncIterable) {\n  return typeof (maybeAsyncIterable === null || maybeAsyncIterable === void 0 ? void 0 : maybeAsyncIterable[Symbol.asyncIterator]) === 'function';\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}