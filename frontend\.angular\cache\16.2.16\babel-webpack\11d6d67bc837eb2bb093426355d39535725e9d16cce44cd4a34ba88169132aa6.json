{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ProjetService = /*#__PURE__*/(() => {\n  class ProjetService {\n    constructor(http) {\n      this.http = http;\n      // Correction de l'URL pour éviter la duplication de /api\n      this.apiUrl = `${environment.urlBackend}projets`;\n    }\n    getHeaders() {\n      const token = localStorage.getItem('token');\n      return new HttpHeaders({\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      });\n    }\n    getProjets() {\n      console.log('Appel API pour récupérer les projets:', this.apiUrl);\n      return this.http.get(this.apiUrl, {\n        headers: this.getHeaders()\n      }).pipe(tap(projets => console.log('Projets récupérés:', projets)), catchError(error => {\n        console.error('Erreur lors de la récupération des projets:', error);\n        return throwError(() => error);\n      }));\n    }\n    getProjetById(id) {\n      return this.http.get(`${this.apiUrl}/${id}`, {\n        headers: this.getHeaders()\n      }).pipe(catchError(error => throwError(() => error)));\n    }\n    addProjet(formData) {\n      // Pour les requêtes multipart/form-data, ne pas définir Content-Type\n      const headers = new HttpHeaders({\n        'Authorization': `Bearer ${localStorage.getItem('token')}`\n      });\n      return this.http.post(`${this.apiUrl}/create`, formData, {\n        headers\n      }).pipe(tap(response => console.log('Projet ajouté:', response)), catchError(error => {\n        console.error('Erreur lors de l\\'ajout du projet:', error);\n        return throwError(() => error);\n      }));\n    }\n    updateProjet(id, projet) {\n      return this.http.put(`${this.apiUrl}/update/${id}`, projet, {\n        headers: this.getHeaders()\n      }).pipe(catchError(error => throwError(() => error)));\n    }\n    deleteProjet(id) {\n      // Assurez-vous que l'URL est correcte\n      return this.http.delete(`${this.apiUrl}/delete/${id}`, {\n        headers: this.getHeaders()\n      }).pipe(tap(response => console.log('Projet supprimé:', response)), catchError(error => {\n        console.error('Erreur lors de la suppression du projet:', error);\n        return throwError(() => error);\n      }));\n    }\n    uploadFile(file) {\n      const formData = new FormData();\n      formData.append('file', file);\n      // Utiliser les headers sans Content-Type pour permettre au navigateur de définir le boundary correct\n      const headers = new HttpHeaders({\n        'Authorization': `Bearer ${localStorage.getItem('token')}`\n      });\n      return this.http.post(`${this.apiUrl}/uploads`, formData, {\n        headers\n      }).pipe(catchError(error => throwError(() => error)));\n    }\n    static {\n      this.ɵfac = function ProjetService_Factory(t) {\n        return new (t || ProjetService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ProjetService,\n        factory: ProjetService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ProjetService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}