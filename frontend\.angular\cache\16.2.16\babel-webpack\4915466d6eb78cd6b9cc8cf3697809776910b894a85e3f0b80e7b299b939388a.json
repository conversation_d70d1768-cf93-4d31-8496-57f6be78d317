{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction LoginComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelement(1, \"i\", 39);\n    i0.ɵɵtext(2, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelement(1, \"i\", 39);\n    i0.ɵɵtext(2, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"div\", 42);\n    i0.ɵɵelement(3, \"i\", 43)(4, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 45)(6, \"p\", 46);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nfunction LoginComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 41)(2, \"div\", 48);\n    i0.ɵɵelement(3, \"i\", 49)(4, \"div\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 45)(6, \"p\", 51);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.message, \" \");\n  }\n}\nfunction LoginComponent_span_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵelement(1, \"i\", 53);\n    i0.ɵɵtext(2, \" Sign In \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 55);\n    i0.ɵɵelement(2, \"circle\", 56)(3, \"path\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Signing in... \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"shake-animation\": a0\n  };\n};\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(fb, authService, router) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.message = '';\n      this.error = '';\n      this.isLoading = false;\n      this.loginForm = this.fb.group({\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', Validators.required]\n      });\n    }\n    onSubmit() {\n      if (this.loginForm.invalid) return;\n      // Reset previous error messages\n      this.error = '';\n      this.message = '';\n      this.isLoading = true;\n      this.authService.login(this.loginForm.value).subscribe({\n        next: res => {\n          this.isLoading = false;\n          localStorage.setItem('token', res.token);\n          localStorage.setItem('user', JSON.stringify(res.user));\n          // Check if profile completion is needed\n          const user = res.user;\n          const needsProfileCompletion = user.isFirstLogin || !user.isProfileComplete || user.profileCompletionPercentage && user.profileCompletionPercentage < 80;\n          if (needsProfileCompletion) {\n            this.router.navigate(['/complete-profile']);\n          } else {\n            this.router.navigate(['/']);\n          }\n        },\n        error: err => {\n          this.isLoading = false;\n          // Handle authentication errors\n          if (err.status === 401) {\n            this.error = 'Email ou mot de passe incorrect. Veuillez réessayer.';\n          } else if (err.status === 403) {\n            this.error = \"Votre compte n'est pas vérifié. Veuillez vérifier votre email.\";\n          } else {\n            this.error = err.error?.message || 'Une erreur est survenue lors de la connexion. Veuillez réessayer.';\n          }\n        }\n      });\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(t) {\n        return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"app-login\"]],\n        decls: 63,\n        vars: 11,\n        consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"flex\", \"items-center\", \"justify-center\", \"p-4\", \"relative\", \"futuristic-layout\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"max-w-md\", \"relative\", \"z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"p-6\", \"text-center\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"p-6\"], [1, \"space-y-5\", 3, \"formGroup\", \"ngClass\", \"ngSubmit\"], [1, \"group\"], [\"for\", \"email\", 1, \"flex\", \"items-center\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-envelope\", \"mr-1.5\", \"text-xs\"], [1, \"relative\"], [\"id\", \"email\", \"type\", \"email\", \"formControlName\", \"email\", \"required\", \"\", \"placeholder\", \"<EMAIL>\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [\"class\", \"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1.5 flex items-center\", 4, \"ngIf\"], [\"for\", \"password\", 1, \"flex\", \"items-center\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-lock\", \"mr-1.5\", \"text-xs\"], [\"id\", \"password\", \"type\", \"password\", \"formControlName\", \"password\", \"required\", \"\", \"placeholder\", \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"w-full\", \"relative\", \"overflow-hidden\", \"group\", \"mt-6\", 3, \"disabled\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\", \"disabled:opacity-50\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\", \"disabled:opacity-0\"], [1, \"relative\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"font-medium\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"transition-all\", \"z-10\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [\"class\", \"flex items-center justify-center\", 4, \"ngIf\"], [1, \"text-center\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"space-y-2\", \"pt-4\"], [\"routerLink\", \"/forgot-password\", 1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"transition-colors\", \"font-medium\"], [\"routerLink\", \"/signup\", 1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"transition-colors\", \"font-medium\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"text-xs\", \"mt-1.5\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"flex-1\"], [1, \"text-xs\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"flex\", \"items-center\"], [1, \"fas\", \"fa-sign-in-alt\", \"mr-2\"], [1, \"flex\", \"items-center\", \"justify-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"mr-2\", \"h-4\", \"w-4\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n            i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n            i0.ɵɵelementStart(21, \"div\", 11)(22, \"h1\", 12);\n            i0.ɵɵtext(23, \" Welcome Back \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"p\", 13);\n            i0.ɵɵtext(25, \" Sign in to access your workspace \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 14)(27, \"form\", 15);\n            i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_27_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(28, \"div\", 16)(29, \"label\", 17);\n            i0.ɵɵelement(30, \"i\", 18);\n            i0.ɵɵtext(31, \" Email \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"div\", 19);\n            i0.ɵɵelement(33, \"input\", 20);\n            i0.ɵɵelementStart(34, \"div\", 21);\n            i0.ɵɵelement(35, \"div\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(36, LoginComponent_div_36_Template, 3, 0, \"div\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"div\", 16)(38, \"label\", 24);\n            i0.ɵɵelement(39, \"i\", 25);\n            i0.ɵɵtext(40, \" Password \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"div\", 19);\n            i0.ɵɵelement(42, \"input\", 26);\n            i0.ɵɵelementStart(43, \"div\", 21);\n            i0.ɵɵelement(44, \"div\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(45, LoginComponent_div_45_Template, 3, 0, \"div\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(46, LoginComponent_div_46_Template, 8, 1, \"div\", 27);\n            i0.ɵɵtemplate(47, LoginComponent_div_47_Template, 8, 1, \"div\", 28);\n            i0.ɵɵelementStart(48, \"button\", 29);\n            i0.ɵɵelement(49, \"div\", 30)(50, \"div\", 31);\n            i0.ɵɵelementStart(51, \"span\", 32);\n            i0.ɵɵtemplate(52, LoginComponent_span_52_Template, 3, 0, \"span\", 33);\n            i0.ɵɵtemplate(53, LoginComponent_span_53_Template, 5, 0, \"span\", 34);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(54, \"div\", 35)(55, \"div\");\n            i0.ɵɵtext(56, \" Forgot your password? \");\n            i0.ɵɵelementStart(57, \"a\", 36);\n            i0.ɵɵtext(58, \" Reset it \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(59, \"div\");\n            i0.ɵɵtext(60, \" Don't have an account? \");\n            i0.ɵɵelementStart(61, \"a\", 37);\n            i0.ɵɵtext(62, \" Sign up \");\n            i0.ɵɵelementEnd()()()()()()()();\n          }\n          if (rf & 2) {\n            let tmp_2_0;\n            let tmp_3_0;\n            i0.ɵɵadvance(27);\n            i0.ɵɵproperty(\"formGroup\", ctx.loginForm)(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx.error));\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.message);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink],\n        styles: [\"@keyframes _ngcontent-%COMP%_shake{0%,to{transform:translate(0)}10%,30%,50%,70%,90%{transform:translate(-5px)}20%,40%,60%,80%{transform:translate(5px)}}.shake-animation[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_shake .6s cubic-bezier(.36,.07,.19,.97) both}\"]\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}