{"ast": null, "code": "export function createFulfilledPromise(value) {\n  var promise = Promise.resolve(value);\n  promise.status = \"fulfilled\";\n  promise.value = value;\n  return promise;\n}\nexport function createRejectedPromise(reason) {\n  var promise = Promise.reject(reason);\n  // prevent potential edge cases leaking unhandled error rejections\n  promise.catch(function () {});\n  promise.status = \"rejected\";\n  promise.reason = reason;\n  return promise;\n}\nexport function isStatefulPromise(promise) {\n  return \"status\" in promise;\n}\nexport function wrapPromiseWithState(promise) {\n  if (isStatefulPromise(promise)) {\n    return promise;\n  }\n  var pendingPromise = promise;\n  pendingPromise.status = \"pending\";\n  pendingPromise.then(function (value) {\n    if (pendingPromise.status === \"pending\") {\n      var fulfilledPromise = pendingPromise;\n      fulfilledPromise.status = \"fulfilled\";\n      fulfilledPromise.value = value;\n    }\n  }, function (reason) {\n    if (pendingPromise.status === \"pending\") {\n      var rejectedPromise = pendingPromise;\n      rejectedPromise.status = \"rejected\";\n      rejectedPromise.reason = reason;\n    }\n  });\n  return promise;\n}\n//# sourceMappingURL=decoration.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}