{"ast": null, "code": "import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { isTypeDefinitionNode, isTypeSystemDefinitionNode, isTypeSystemExtensionNode } from '../../language/predicates.mjs';\nimport { introspectionTypes } from '../../type/introspection.mjs';\nimport { specifiedScalarTypes } from '../../type/scalars.mjs';\n\n/**\n * Known type names\n *\n * A GraphQL document is only valid if referenced types (specifically\n * variable definitions and fragment conditions) are defined by the type schema.\n *\n * See https://spec.graphql.org/draft/#sec-Fragment-Spread-Type-Existence\n */\nexport function KnownTypeNamesRule(context) {\n  const schema = context.getSchema();\n  const existingTypesMap = schema ? schema.getTypeMap() : Object.create(null);\n  const definedTypes = Object.create(null);\n  for (const def of context.getDocument().definitions) {\n    if (isTypeDefinitionNode(def)) {\n      definedTypes[def.name.value] = true;\n    }\n  }\n  const typeNames = [...Object.keys(existingTypesMap), ...Object.keys(definedTypes)];\n  return {\n    NamedType(node, _1, parent, _2, ancestors) {\n      const typeName = node.name.value;\n      if (!existingTypesMap[typeName] && !definedTypes[typeName]) {\n        var _ancestors$;\n        const definitionNode = (_ancestors$ = ancestors[2]) !== null && _ancestors$ !== void 0 ? _ancestors$ : parent;\n        const isSDL = definitionNode != null && isSDLNode(definitionNode);\n        if (isSDL && standardTypeNames.includes(typeName)) {\n          return;\n        }\n        const suggestedTypes = suggestionList(typeName, isSDL ? standardTypeNames.concat(typeNames) : typeNames);\n        context.reportError(new GraphQLError(`Unknown type \"${typeName}\".` + didYouMean(suggestedTypes), {\n          nodes: node\n        }));\n      }\n    }\n  };\n}\nconst standardTypeNames = [...specifiedScalarTypes, ...introspectionTypes].map(type => type.name);\nfunction isSDLNode(value) {\n  return 'kind' in value && (isTypeSystemDefinitionNode(value) || isTypeSystemExtensionNode(value));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}