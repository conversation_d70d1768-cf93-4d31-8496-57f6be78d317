{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { PlanningListComponent } from './planning-list/planning-list.component';\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\nimport { PlanningCalendarComponent } from './planning-calendar/planning-calendar.component';\nimport { PlanningEditComponent } from './planning-edit/planning-edit.component';\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: PlanningListComponent\n}, {\n  path: 'nouveau',\n  component: PlanningFormComponent\n}, {\n  path: 'calandarPlanning',\n  component: PlanningCalendarComponent\n}, {\n  path: 'edit/:id',\n  component: PlanningEditComponent\n}, {\n  path: ':id',\n  component: PlanningDetailComponent // <-- put this last\n}];\n\nexport let PlanningsRoutingModule = /*#__PURE__*/(() => {\n  class PlanningsRoutingModule {\n    static {\n      this.ɵfac = function PlanningsRoutingModule_Factory(t) {\n        return new (t || PlanningsRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: PlanningsRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return PlanningsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}