{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { isInputObjectType, isInterfaceType, isObjectType } from '../../type/definition.mjs';\n\n/**\n * Unique field definition names\n *\n * A GraphQL complex type is only valid if all its fields are uniquely named.\n */\nexport function UniqueFieldDefinitionNamesRule(context) {\n  const schema = context.getSchema();\n  const existingTypeMap = schema ? schema.getTypeMap() : Object.create(null);\n  const knownFieldNames = Object.create(null);\n  return {\n    InputObjectTypeDefinition: checkFieldUniqueness,\n    InputObjectTypeExtension: checkFieldUniqueness,\n    InterfaceTypeDefinition: checkFieldUniqueness,\n    InterfaceTypeExtension: checkFieldUniqueness,\n    ObjectTypeDefinition: checkFieldUniqueness,\n    ObjectTypeExtension: checkFieldUniqueness\n  };\n  function checkFieldUniqueness(node) {\n    var _node$fields;\n    const typeName = node.name.value;\n    if (!knownFieldNames[typeName]) {\n      knownFieldNames[typeName] = Object.create(null);\n    } // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n\n    const fieldNodes = (_node$fields = node.fields) !== null && _node$fields !== void 0 ? _node$fields : [];\n    const fieldNames = knownFieldNames[typeName];\n    for (const fieldDef of fieldNodes) {\n      const fieldName = fieldDef.name.value;\n      if (hasField(existingTypeMap[typeName], fieldName)) {\n        context.reportError(new GraphQLError(`Field \"${typeName}.${fieldName}\" already exists in the schema. It cannot also be defined in this type extension.`, {\n          nodes: fieldDef.name\n        }));\n      } else if (fieldNames[fieldName]) {\n        context.reportError(new GraphQLError(`Field \"${typeName}.${fieldName}\" can only be defined once.`, {\n          nodes: [fieldNames[fieldName], fieldDef.name]\n        }));\n      } else {\n        fieldNames[fieldName] = fieldDef.name;\n      }\n    }\n    return false;\n  }\n}\nfunction hasField(type, fieldName) {\n  if (isObjectType(type) || isInterfaceType(type) || isInputObjectType(type)) {\n    return type.getFields()[fieldName] != null;\n  }\n  return false;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}