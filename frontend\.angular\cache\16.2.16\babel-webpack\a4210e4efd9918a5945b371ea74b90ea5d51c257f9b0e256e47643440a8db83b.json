{"ast": null, "code": "import \"../utilities/globals/index.js\";\nexport { ApolloCache } from \"./core/cache.js\";\nexport { Cache } from \"./core/types/Cache.js\";\nexport { MissingFieldError } from \"./core/types/common.js\";\nexport { isReference, makeReference, canonicalStringify } from \"../utilities/index.js\";\nexport { EntityStore } from \"./inmemory/entityStore.js\";\nexport { fieldNameFromStoreName, defaultDataIdFromObject } from \"./inmemory/helpers.js\";\nexport { InMemoryCache } from \"./inmemory/inMemoryCache.js\";\nexport { makeVar, cacheSlot } from \"./inmemory/reactiveVars.js\";\nexport { Policies } from \"./inmemory/policies.js\";\nexport { createFragmentRegistry } from \"./inmemory/fragmentRegistry.js\";\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}