{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/reunion.service\";\nimport * as i4 from \"@app/services/data.service\";\nimport * as i5 from \"@app/services/planning.service\";\nimport * as i6 from \"@angular/common\";\nfunction ReunionEditComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error.message || \"Une erreur est survenue\", \" \");\n  }\n}\nfunction ReunionEditComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Le titre est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" La date est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" L'heure de d\\u00E9but est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" L'heure de fin est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_option_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const planning_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", planning_r8.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(planning_r8.titre);\n  }\n}\nfunction ReunionEditComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1, \" Le planning est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_ng_container_50_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r10._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(user_r10.username);\n  }\n}\nfunction ReunionEditComponent_ng_container_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ReunionEditComponent_ng_container_50_option_1_Template, 2, 2, \"option\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.users);\n  }\n}\nexport let ReunionEditComponent = /*#__PURE__*/(() => {\n  class ReunionEditComponent {\n    constructor(fb, route, router, reunionService, userService, planningService) {\n      this.fb = fb;\n      this.route = route;\n      this.router = router;\n      this.reunionService = reunionService;\n      this.userService = userService;\n      this.planningService = planningService;\n      this.error = null;\n      this.isSubmitting = false;\n      this.users = [];\n      this.plannings = [];\n    }\n    ngOnInit() {\n      this.reunionId = this.route.snapshot.paramMap.get('id');\n      this.initForm();\n      this.fetchUsers();\n      this.fetchPlannings();\n      this.loadReunion();\n    }\n    initForm() {\n      this.reunionForm = this.fb.group({\n        titre: ['', Validators.required],\n        description: [''],\n        date: ['', Validators.required],\n        heureDebut: ['', Validators.required],\n        heureFin: ['', Validators.required],\n        lieu: [''],\n        lienVisio: [''],\n        planning: ['', Validators.required],\n        participants: [[]]\n      });\n    }\n    fetchUsers() {\n      this.userService.getAllUsers().subscribe(users => {\n        this.users = users;\n      });\n    }\n    fetchPlannings() {\n      this.planningService.getAllPlannings().subscribe(plannings => {\n        this.plannings = plannings.plannings;\n      });\n    }\n    loadReunion() {\n      this.reunionService.getReunionById(this.reunionId).subscribe({\n        next: reunion => {\n          this.reunionForm.patchValue({\n            titre: reunion.reunion.titre,\n            description: reunion.reunion.description,\n            date: reunion.reunion.date?.split('T')[0],\n            heureDebut: reunion.reunion.heureDebut,\n            heureFin: reunion.reunion.heureFin,\n            lieu: reunion.reunion.lieu,\n            lienVisio: reunion.reunion.lienVisio,\n            planning: reunion.reunion.planning?.id,\n            participants: reunion.reunion.participants?.map(p => p._id)\n          });\n        },\n        error: err => {\n          this.error = err;\n        }\n      });\n    }\n    onSubmit() {\n      if (this.reunionForm.invalid) return;\n      this.isSubmitting = true;\n      const reunion = this.reunionForm.value;\n      this.reunionService.updateReunion(this.reunionId, reunion).subscribe({\n        next: () => {\n          this.isSubmitting = false;\n          this.router.navigate(['/reunions']);\n        },\n        error: err => {\n          this.error = err;\n          this.isSubmitting = false;\n        }\n      });\n    }\n    goReunion() {\n      this.router.navigate(['/reunions']);\n    }\n    static {\n      this.ɵfac = function ReunionEditComponent_Factory(t) {\n        return new (t || ReunionEditComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ReunionService), i0.ɵɵdirectiveInject(i4.DataService), i0.ɵɵdirectiveInject(i5.PlanningService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ReunionEditComponent,\n        selectors: [[\"app-reunion-edit\"]],\n        decls: 56,\n        vars: 11,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"max-w-3xl\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-6\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-6\"], [\"for\", \"titre\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"titre\", \"type\", \"text\", \"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"class\", \"text-red-500 text-sm mt-1\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"3\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [\"for\", \"date\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"date\", \"type\", \"date\", \"formControlName\", \"date\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"heureDebut\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"heureDebut\", \"type\", \"time\", \"formControlName\", \"heureDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"heureFin\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"heureFin\", \"type\", \"time\", \"formControlName\", \"heureFin\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"for\", \"lieu\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"lieu\", \"type\", \"text\", \"formControlName\", \"lieu\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"lienVisio\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"lienVisio\", \"type\", \"url\", \"formControlName\", \"lienVisio\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"for\", \"planning\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"planning\", \"formControlName\", \"planning\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-md\", \"border-gray-300\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [4, \"ngIf\"], [1, \"mt-6\", \"flex\", \"justify-end\", \"space-x-3\"], [\"type\", \"button\", 1, \"px-4\", \"py-2\", \"border\", \"border-gray-300\", \"rounded-md\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"hover:bg-gray-50\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-4\", \"py-2\", \"rounded-md\", \"text-sm\", \"font-medium\", \"text-white\", \"bg-purple-600\", \"hover:bg-purple-700\", \"disabled:opacity-50\", 3, \"disabled\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"text-red-500\", \"text-sm\", \"mt-1\"], [3, \"value\"]],\n        template: function ReunionEditComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"form\", 1);\n            i0.ɵɵlistener(\"ngSubmit\", function ReunionEditComponent_Template_form_ngSubmit_1_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtemplate(2, ReunionEditComponent_div_2_Template, 2, 1, \"div\", 2);\n            i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\")(5, \"label\", 4);\n            i0.ɵɵtext(6, \"Titre *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(7, \"input\", 5);\n            i0.ɵɵtemplate(8, ReunionEditComponent_div_8_Template, 2, 0, \"div\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\")(10, \"label\", 7);\n            i0.ɵɵtext(11, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(12, \"textarea\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\")(15, \"label\", 10);\n            i0.ɵɵtext(16, \"Date *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(17, \"input\", 11);\n            i0.ɵɵtemplate(18, ReunionEditComponent_div_18_Template, 2, 0, \"div\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"div\")(20, \"label\", 12);\n            i0.ɵɵtext(21, \"Heure de d\\u00E9but *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(22, \"input\", 13);\n            i0.ɵɵtemplate(23, ReunionEditComponent_div_23_Template, 2, 0, \"div\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"div\")(25, \"label\", 14);\n            i0.ɵɵtext(26, \"Heure de fin *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(27, \"input\", 15);\n            i0.ɵɵtemplate(28, ReunionEditComponent_div_28_Template, 2, 0, \"div\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(29, \"div\", 16)(30, \"div\")(31, \"label\", 17);\n            i0.ɵɵtext(32, \"Lieu\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(33, \"input\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"div\")(35, \"label\", 19);\n            i0.ɵɵtext(36, \"Lien Visio\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(37, \"input\", 20);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"div\")(39, \"label\", 21);\n            i0.ɵɵtext(40, \"Planning *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"select\", 22)(42, \"option\", 23);\n            i0.ɵɵtext(43, \"S\\u00E9lectionnez un planning\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(44, ReunionEditComponent_option_44_Template, 2, 2, \"option\", 24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(45, ReunionEditComponent_div_45_Template, 2, 0, \"div\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"div\")(47, \"label\", 25);\n            i0.ɵɵtext(48, \"Participants\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"select\", 26);\n            i0.ɵɵtemplate(50, ReunionEditComponent_ng_container_50_Template, 2, 1, \"ng-container\", 27);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(51, \"div\", 28)(52, \"button\", 29);\n            i0.ɵɵlistener(\"click\", function ReunionEditComponent_Template_button_click_52_listener() {\n              return ctx.goReunion();\n            });\n            i0.ɵɵtext(53, \" Annuler \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"button\", 30);\n            i0.ɵɵtext(55);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_4_0;\n            let tmp_5_0;\n            let tmp_7_0;\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.reunionForm);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_5_0.touched));\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngForOf\", ctx.plannings);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.reunionForm.get(\"planning\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.reunionForm.get(\"planning\")) == null ? null : tmp_7_0.touched));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.users);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"disabled\", ctx.reunionForm.invalid || ctx.isSubmitting);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"Enregistrement...\" : \"Enregistrer\", \" \");\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.SelectMultipleControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName]\n      });\n    }\n  }\n  return ReunionEditComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}