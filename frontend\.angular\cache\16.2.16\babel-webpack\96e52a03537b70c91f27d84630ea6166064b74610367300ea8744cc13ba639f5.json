{"ast": null, "code": "import { gql } from 'apollo-angular';\n// Définir les types GraphQL\nexport const typeDefs = gql`\n  enum MessageType {\n    TEXT\n    IMAGE\n    FILE\n    AUDIO\n    VIDEO\n    SYSTEM\n    VOICE_MESSAGE\n  }\n`;\n// Message Mutations\nexport const SEND_MESSAGE_MUTATION = gql`\n  mutation SendMessage(\n    $receiverId: ID!\n    $content: String\n    $file: Upload\n    $type: MessageType\n    $metadata: JSON\n  ) {\n    sendMessage(\n      receiverId: $receiverId\n      content: $content\n      file: $file\n      type: $type\n      metadata: $metadata\n    ) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      conversation {\n        id\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n    }\n  }\n`;\nexport const MARK_AS_READ_MUTATION = gql`\n  mutation MarkMessageAsRead($messageId: ID!) {\n    markMessageAsRead(messageId: $messageId) {\n      id\n      isRead\n      readAt\n    }\n  }\n`;\nexport const EDIT_MESSAGE_MUTATION = gql`\n  mutation EditMessage($messageId: ID!, $newContent: String!) {\n    editMessage(messageId: $messageId, newContent: $newContent) {\n      id\n      content\n      isEdited\n      updatedAt\n    }\n  }\n`;\nexport const DELETE_MESSAGE_MUTATION = gql`\n  mutation DeleteMessage($messageId: ID!) {\n    deleteMessage(messageId: $messageId) {\n      id\n      isDeleted\n      deletedAt\n    }\n  }\n`;\nexport const GET_MESSAGES_QUERY = gql`\n  query GetMessages(\n    $senderId: ID!\n    $receiverId: ID!\n    $conversationId: ID!\n    $page: Int\n    $limit: Int\n  ) {\n    getMessages(\n      senderId: $senderId\n      receiverId: $receiverId\n      conversationId: $conversationId\n      page: $page\n      limit: $limit\n    ) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n      }\n      replyTo {\n        id\n        content\n      }\n    }\n  }\n`;\n// Conversation Queries\nexport const GET_CONVERSATIONS_QUERY = gql`\n  query GetConversations {\n    getConversations {\n      id\n      participants {\n        id\n        username\n        image\n        isOnline\n      }\n      lastMessage {\n        id\n        content\n        timestamp\n        isRead\n        sender {\n          id\n          username\n        }\n      }\n      unreadCount\n      updatedAt\n    }\n  }\n`;\nexport const GET_CONVERSATION_QUERY = gql`\n  query GetConversation(\n    $conversationId: ID!\n    $limit: Int = 10\n    $offset: Int = 0\n  ) {\n    getConversation(conversationId: $conversationId) {\n      id\n      participants {\n        id\n        username\n        image\n        isOnline\n      }\n      messages(limit: $limit, offset: $offset) {\n        id\n        content\n        type\n        timestamp\n        isRead\n        sender {\n          id\n          username\n          image\n        }\n        receiver {\n          id\n          username\n          image\n        }\n        attachments {\n          url\n          type\n          duration\n        }\n        metadata\n        conversationId\n      }\n    }\n  }\n`;\n// User Queries\nexport const GET_USER_QUERY = gql`\n  query GetOneUser($id: ID!) {\n    getOneUser(id: $id) {\n      id\n      username\n      email\n      image\n      isOnline\n      lastActive\n    }\n  }\n`;\nexport const GET_ALL_USER_QUERY = gql`\n  query GetAllUsers(\n    $search: String\n    $page: Int\n    $limit: Int\n    $sortBy: String\n    $sortOrder: String\n    $isOnline: Boolean\n  ) {\n    getAllUsers(\n      search: $search\n      page: $page\n      limit: $limit\n      sortBy: $sortBy\n      sortOrder: $sortOrder\n      isOnline: $isOnline\n    ) {\n      users {\n        id\n        username\n        email\n        image\n        isOnline\n        lastActive\n      }\n      totalCount\n      totalPages\n      currentPage\n      hasNextPage\n      hasPreviousPage\n    }\n  }\n`;\n// Status Mutations\nexport const SET_USER_ONLINE_MUTATION = gql`\n  mutation SetUserOnline($userId: ID!) {\n    setUserOnline(userId: $userId) {\n      id\n      isOnline\n      lastActive\n    }\n  }\n`;\nexport const SET_USER_OFFLINE_MUTATION = gql`\n  mutation SetUserOffline($userId: ID!) {\n    setUserOffline(userId: $userId) {\n      id\n      isOnline\n      lastActive\n    }\n  }\n`;\n// Search Query\nexport const SEARCH_MESSAGES_QUERY = gql`\n  query SearchMessages($query: String!, $conversationId: ID) {\n    searchMessages(query: $query, conversationId: $conversationId) {\n      id\n      content\n      timestamp\n      sender {\n        id\n        username\n      }\n    }\n  }\n`;\n// Unread Messages Query\nexport const GET_UNREAD_MESSAGES_QUERY = gql`\n  query GetUnreadMessages($userId: ID!) {\n    getUnreadMessages(userId: $userId) {\n      id\n      content\n      timestamp\n      sender {\n        id\n        username\n        image\n      }\n      conversation {\n        id\n      }\n    }\n  }\n`;\n// Subscriptions\nexport const MESSAGE_SENT_SUBSCRIPTION = gql`\n  subscription MessageSent($conversationId: ID!) {\n    messageSent(conversationId: $conversationId) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      conversation {\n        id\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n    }\n  }\n`;\nexport const USER_STATUS_SUBSCRIPTION = gql`\n  subscription UserStatusChanged {\n    userStatusChanged {\n      id\n      username\n      isOnline\n      lastActive\n    }\n  }\n`;\nexport const CONVERSATION_UPDATED_SUBSCRIPTION = gql`\n  subscription ConversationUpdated($conversationId: ID!) {\n    conversationUpdated(conversationId: $conversationId) {\n      id\n      participants {\n        id\n        username\n        image\n        isOnline\n      }\n      lastMessage {\n        id\n        content\n        timestamp\n        isRead\n        sender {\n          id\n          username\n        }\n      }\n      unreadCount\n      updatedAt\n    }\n  }\n`;\n// Notification Queries\nexport const GET_NOTIFICATIONS_QUERY = gql`\n  query GetUserNotifications {\n    getUserNotifications {\n      id\n      type\n      content\n      timestamp\n      isRead\n      senderId {\n        id\n        username\n        image\n      }\n      message {\n        id\n        content\n      }\n      readAt\n      relatedEntity\n      metadata\n    }\n  }\n`;\nexport const GET_NOTIFICATIONS_ATTACHAMENTS = gql`\n  query GetNotificationAttachments($id: ID!) {\n    getNotificationAttachments(notificationId: $id) {\n      url\n      type\n      name\n      size\n    }\n  }\n`;\nexport const MARK_NOTIFICATION_READ_MUTATION = gql`\n  mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n    markNotificationsAsRead(notificationIds: $notificationIds) {\n      success\n      readCount\n      remainingCount\n    }\n  }\n`;\nexport const DELETE_NOTIFICATION_MUTATION = gql`\n  mutation DeleteNotification($notificationId: ID!) {\n    deleteNotification(notificationId: $notificationId) {\n      success\n      message\n    }\n  }\n`;\nexport const DELETE_MULTIPLE_NOTIFICATIONS_MUTATION = gql`\n  mutation DeleteMultipleNotifications($notificationIds: [ID!]!) {\n    deleteMultipleNotifications(notificationIds: $notificationIds) {\n      success\n      count\n      message\n    }\n  }\n`;\nexport const DELETE_ALL_NOTIFICATIONS_MUTATION = gql`\n  mutation DeleteAllNotifications {\n    deleteAllNotifications {\n      success\n      count\n      message\n    }\n  }\n`;\nexport const NOTIFICATION_SUBSCRIPTION = gql`\n  subscription NotificationReceived {\n    notificationReceived {\n      id\n      type\n      content\n      timestamp\n      isRead\n      senderId {\n        id\n        username\n        image\n      }\n      message {\n        id\n        content\n      }\n      readAt\n      relatedEntity\n      metadata\n    }\n  }\n`;\nexport const NOTIFICATIONS_READ_SUBSCRIPTION = gql`\n  subscription NotificationsRead {\n    notificationsRead\n  }\n`;\n// group Queries\nexport const GET_GROUP_QUERY = gql`\n  query GetGroup($id: ID!) {\n    getGroup(id: $id) {\n      id\n      name\n      photo\n      description\n      participants {\n        id\n        username\n        email\n        image\n        isOnline\n      }\n      admins {\n        id\n        username\n        email\n        image\n        isOnline\n      }\n      messageCount\n      createdAt\n      updatedAt\n    }\n  }\n`;\nexport const GET_USER_GROUPS_QUERY = gql`\n  query GetUserGroups($userId: ID!) {\n    getUserGroups(userId: $userId) {\n      id\n      name\n      photo\n      description\n      participants {\n        id\n        username\n        image\n        isOnline\n      }\n      admins {\n        id\n        username\n        image\n        isOnline\n      }\n      messageCount\n      createdAt\n      updatedAt\n    }\n  }\n`;\nexport const CREATE_GROUP_MUTATION = gql`\n  mutation CreateGroup(\n    $name: String!\n    $participantIds: [ID!]!\n    $photo: Upload\n    $description: String\n  ) {\n    createGroup(\n      name: $name\n      participantIds: $participantIds\n      photo: $photo\n      description: $description\n    ) {\n      id\n      name\n      photo\n      description\n      participants {\n        id\n        username\n        image\n      }\n      admins {\n        id\n        username\n        image\n      }\n    }\n  }\n`;\nexport const UPDATE_GROUP_MUTATION = gql`\n  mutation UpdateGroup($id: ID!, $input: UpdateGroupInput!) {\n    updateGroup(id: $id, input: $input) {\n      id\n      name\n      photo\n      description\n      participants {\n        id\n        username\n        image\n      }\n      admins {\n        id\n        username\n        image\n      }\n    }\n  }\n`;\n// Add to exports\nexport const TYPING_INDICATOR_SUBSCRIPTION = gql`\n  subscription TypingIndicator($conversationId: ID!) {\n    typingIndicator(conversationId: $conversationId) {\n      conversationId\n      userId\n      isTyping\n    }\n  }\n`;\nexport const START_TYPING_MUTATION = gql`\n  mutation StartTyping($input: TypingInput!) {\n    startTyping(input: $input)\n  }\n`;\nexport const STOP_TYPING_MUTATION = gql`\n  mutation StopTyping($input: TypingInput!) {\n    stopTyping(input: $input)\n  }\n`;\nexport const GET_CURRENT_USER_QUERY = gql`\n  query GetCurrentUser {\n    getCurrentUser {\n      id\n      username\n      email\n      image\n      isOnline\n      lastActive\n      createdAt\n      updatedAt\n    }\n  }\n`;\nexport const REACT_TO_MESSAGE_MUTATION = gql`\n  mutation ReactToMessage($messageId: ID!, $emoji: String!) {\n    reactToMessage(messageId: $messageId, emoji: $emoji) {\n      id\n      reactions {\n        userId\n        emoji\n        createdAt\n      }\n    }\n  }\n`;\nexport const FORWARD_MESSAGE_MUTATION = gql`\n  mutation ForwardMessage($messageId: ID!, $conversationIds: [ID!]!) {\n    forwardMessage(messageId: $messageId, conversationIds: $conversationIds) {\n      id\n      content\n      timestamp\n      sender {\n        id\n        username\n      }\n      conversation {\n        id\n      }\n    }\n  }\n`;\nexport const PIN_MESSAGE_MUTATION = gql`\n  mutation PinMessage($messageId: ID!, $conversationId: ID!) {\n    pinMessage(messageId: $messageId, conversationId: $conversationId) {\n      id\n      pinned\n      pinnedAt\n      pinnedBy {\n        id\n        username\n      }\n    }\n  }\n`;\nexport const CREATE_CONVERSATION_MUTATION = gql`\n  mutation CreateConversation($userId: ID!) {\n    createConversation(userId: $userId) {\n      id\n      participants {\n        id\n        username\n        image\n        isOnline\n      }\n      lastMessage {\n        id\n        content\n        timestamp\n      }\n      unreadCount\n      updatedAt\n    }\n  }\n`;\n// Call Queries\nexport const CALL_HISTORY_QUERY = gql`\n  query CallHistory(\n    $limit: Int\n    $offset: Int\n    $status: [CallStatus]\n    $type: [CallType]\n    $startDate: String\n    $endDate: String\n  ) {\n    callHistory(\n      limit: $limit\n      offset: $offset\n      status: $status\n      type: $type\n      startDate: $startDate\n      endDate: $endDate\n    ) {\n      id\n      caller {\n        id\n        username\n        image\n      }\n      recipient {\n        id\n        username\n        image\n      }\n      type\n      status\n      startTime\n      endTime\n      duration\n      conversationId\n    }\n  }\n`;\nexport const CALL_DETAILS_QUERY = gql`\n  query CallDetails($callId: ID!) {\n    callDetails(callId: $callId) {\n      id\n      caller {\n        id\n        username\n        image\n      }\n      recipient {\n        id\n        username\n        image\n      }\n      type\n      status\n      startTime\n      endTime\n      duration\n      conversationId\n      metadata\n    }\n  }\n`;\nexport const CALL_STATS_QUERY = gql`\n  query CallStats {\n    callStats {\n      totalCalls\n      totalDuration\n      missedCalls\n      callsByType {\n        type\n        count\n      }\n      averageCallDuration\n      mostCalledUser {\n        id\n        username\n        image\n      }\n    }\n  }\n`;\n// Call Mutations\nexport const INITIATE_CALL_MUTATION = gql`\n  mutation InitiateCall(\n    $recipientId: ID!\n    $callType: CallType!\n    $callId: String!\n    $offer: String!\n    $conversationId: ID\n    $options: CallOptions\n  ) {\n    initiateCall(\n      recipientId: $recipientId\n      callType: $callType\n      callId: $callId\n      offer: $offer\n      conversationId: $conversationId\n      options: $options\n    ) {\n      id\n      caller {\n        id\n        username\n        image\n      }\n      recipient {\n        id\n        username\n        image\n      }\n      type\n      status\n      startTime\n      conversationId\n    }\n  }\n`;\nexport const SEND_CALL_SIGNAL_MUTATION = gql`\n  mutation SendCallSignal(\n    $callId: ID!\n    $signalType: String!\n    $signalData: String!\n  ) {\n    sendCallSignal(\n      callId: $callId\n      signalType: $signalType\n      signalData: $signalData\n    ) {\n      success\n      message\n    }\n  }\n`;\nexport const ACCEPT_CALL_MUTATION = gql`\n  mutation AcceptCall($callId: ID!, $answer: String!) {\n    acceptCall(callId: $callId, answer: $answer) {\n      id\n      status\n    }\n  }\n`;\nexport const REJECT_CALL_MUTATION = gql`\n  mutation RejectCall($callId: ID!, $reason: String) {\n    rejectCall(callId: $callId, reason: $reason) {\n      id\n      status\n    }\n  }\n`;\nexport const END_CALL_MUTATION = gql`\n  mutation EndCall($callId: ID!, $feedback: CallFeedbackInput) {\n    endCall(callId: $callId, feedback: $feedback) {\n      id\n      status\n      endTime\n      duration\n    }\n  }\n`;\nexport const TOGGLE_CALL_MEDIA_MUTATION = gql`\n  mutation ToggleCallMedia($callId: ID!, $video: Boolean, $audio: Boolean) {\n    toggleCallMedia(callId: $callId, video: $video, audio: $audio) {\n      success\n      message\n    }\n  }\n`;\n// Call Subscriptions\nexport const CALL_SIGNAL_SUBSCRIPTION = gql`\n  subscription CallSignal($callId: ID) {\n    callSignal(callId: $callId) {\n      callId\n      senderId\n      type\n      data\n      timestamp\n    }\n  }\n`;\nexport const INCOMING_CALL_SUBSCRIPTION = gql`\n  subscription IncomingCall {\n    incomingCall {\n      id\n      caller {\n        id\n        username\n        image\n      }\n      type\n      conversationId\n      offer\n      timestamp\n    }\n  }\n`;\nexport const CALL_STATUS_CHANGED_SUBSCRIPTION = gql`\n  subscription CallStatusChanged($callId: ID) {\n    callStatusChanged(callId: $callId) {\n      id\n      status\n      endTime\n      duration\n    }\n  }\n`;\n// Requête pour récupérer les messages vocaux\nexport const GET_VOICE_MESSAGES_QUERY = gql`\n  query GetVoiceMessages {\n    getVoiceMessages {\n      id\n      caller {\n        id\n        username\n        image\n      }\n      recipient {\n        id\n        username\n        image\n      }\n      type\n      status\n      startTime\n      endTime\n      duration\n      conversationId\n      metadata\n    }\n  }\n`;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}