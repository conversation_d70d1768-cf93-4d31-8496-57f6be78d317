{"ast": null, "code": "import { print as origPrint } from \"graphql\";\nimport { AutoCleanedWeakCache, cacheSizes } from \"../caching/index.js\";\nimport { registerGlobalCache } from \"../caching/getMemoryInternals.js\";\nvar printCache;\nexport var print = Object.assign(function (ast) {\n  var result = printCache.get(ast);\n  if (!result) {\n    result = origPrint(ast);\n    printCache.set(ast, result);\n  }\n  return result;\n}, {\n  reset: function () {\n    printCache = new AutoCleanedWeakCache(cacheSizes.print || 2000 /* defaultCacheSizes.print */);\n  }\n});\n\nprint.reset();\nif (globalThis.__DEV__ !== false) {\n  registerGlobalCache(\"print\", function () {\n    return printCache ? printCache.size : 0;\n  });\n}\n//# sourceMappingURL=print.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}