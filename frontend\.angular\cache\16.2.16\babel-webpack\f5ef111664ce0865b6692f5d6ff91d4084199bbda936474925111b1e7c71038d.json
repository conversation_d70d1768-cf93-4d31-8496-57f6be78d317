{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { <PERSON><PERSON> } from \"@wry/trie\";\nimport { canUseWeakMap, canUseWeakSet, isNonNullObject as isObjectOrArray } from \"../../utilities/index.js\";\nimport { isArray } from \"./helpers.js\";\nfunction shallowCopy(value) {\n  if (isObjectOrArray(value)) {\n    return isArray(value) ? value.slice(0) : __assign({\n      __proto__: Object.getPrototypeOf(value)\n    }, value);\n  }\n  return value;\n}\n// When programmers talk about the \"canonical form\" of an object, they\n// usually have the following meaning in mind, which I've copied from\n// https://en.wiktionary.org/wiki/canonical_form:\n//\n// 1. A standard or normal presentation of a mathematical entity [or\n//    object]. A canonical form is an element of a set of representatives\n//    of equivalence classes of forms such that there is a function or\n//    procedure which projects every element of each equivalence class\n//    onto that one element, the canonical form of that equivalence\n//    class. The canonical form is expected to be simpler than the rest of\n//    the forms in some way.\n//\n// That's a long-winded way of saying any two objects that have the same\n// canonical form may be considered equivalent, even if they are !==,\n// which usually means the objects are structurally equivalent (deeply\n// equal), but don't necessarily use the same memory.\n//\n// Like a literary or musical canon, this ObjectCanon class represents a\n// collection of unique canonical items (JavaScript objects), with the\n// important property that canon.admit(a) === canon.admit(b) if a and b\n// are deeply equal to each other. In terms of the definition above, the\n// canon.admit method is the \"function or procedure which projects every\"\n// object \"onto that one element, the canonical form.\"\n//\n// In the worst case, the canonicalization process may involve looking at\n// every property in the provided object tree, so it takes the same order\n// of time as deep equality checking. Fortunately, already-canonicalized\n// objects are returned immediately from canon.admit, so the presence of\n// canonical subtrees tends to speed up canonicalization.\n//\n// Since consumers of canonical objects can check for deep equality in\n// constant time, canonicalizing cache results can massively improve the\n// performance of application code that skips re-rendering unchanged\n// results, such as \"pure\" UI components in a framework like React.\n//\n// Of course, since canonical objects may be shared widely between\n// unrelated consumers, it's important to think of them as immutable, even\n// though they are not actually frozen with Object.freeze in production,\n// due to the extra performance overhead that comes with frozen objects.\n//\n// Custom scalar objects whose internal class name is neither Array nor\n// Object can be included safely in the admitted tree, but they will not\n// be replaced with a canonical version (to put it another way, they are\n// assumed to be canonical already).\n//\n// If we ignore custom objects, no detection of cycles or repeated object\n// references is currently required by the StoreReader class, since\n// GraphQL result objects are JSON-serializable trees (and thus contain\n// neither cycles nor repeated subtrees), so we can avoid the complexity\n// of keeping track of objects we've already seen during the recursion of\n// the admit method.\n//\n// In the future, we may consider adding additional cases to the switch\n// statement to handle other common object types, such as \"[object Date]\"\n// objects, as needed.\nvar ObjectCanon = /** @class */function () {\n  function ObjectCanon() {\n    // Set of all canonical objects this ObjectCanon has admitted, allowing\n    // canon.admit to return previously-canonicalized objects immediately.\n    this.known = new (canUseWeakSet ? WeakSet : Set)();\n    // Efficient storage/lookup structure for canonical objects.\n    this.pool = new Trie(canUseWeakMap);\n    // Make the ObjectCanon assume this value has already been\n    // canonicalized.\n    this.passes = new WeakMap();\n    // Arrays that contain the same elements in a different order can share\n    // the same SortedKeysInfo object, to save memory.\n    this.keysByJSON = new Map();\n    // This has to come last because it depends on keysByJSON.\n    this.empty = this.admit({});\n  }\n  ObjectCanon.prototype.isKnown = function (value) {\n    return isObjectOrArray(value) && this.known.has(value);\n  };\n  ObjectCanon.prototype.pass = function (value) {\n    if (isObjectOrArray(value)) {\n      var copy = shallowCopy(value);\n      this.passes.set(copy, value);\n      return copy;\n    }\n    return value;\n  };\n  ObjectCanon.prototype.admit = function (value) {\n    var _this = this;\n    if (isObjectOrArray(value)) {\n      var original = this.passes.get(value);\n      if (original) return original;\n      var proto = Object.getPrototypeOf(value);\n      switch (proto) {\n        case Array.prototype:\n          {\n            if (this.known.has(value)) return value;\n            var array = value.map(this.admit, this);\n            // Arrays are looked up in the Trie using their recursively\n            // canonicalized elements, and the known version of the array is\n            // preserved as node.array.\n            var node = this.pool.lookupArray(array);\n            if (!node.array) {\n              this.known.add(node.array = array);\n              // Since canonical arrays may be shared widely between\n              // unrelated consumers, it's important to regard them as\n              // immutable, even if they are not frozen in production.\n              if (globalThis.__DEV__ !== false) {\n                Object.freeze(array);\n              }\n            }\n            return node.array;\n          }\n        case null:\n        case Object.prototype:\n          {\n            if (this.known.has(value)) return value;\n            var proto_1 = Object.getPrototypeOf(value);\n            var array_1 = [proto_1];\n            var keys = this.sortedKeys(value);\n            array_1.push(keys.json);\n            var firstValueIndex_1 = array_1.length;\n            keys.sorted.forEach(function (key) {\n              array_1.push(_this.admit(value[key]));\n            });\n            // Objects are looked up in the Trie by their prototype (which\n            // is *not* recursively canonicalized), followed by a JSON\n            // representation of their (sorted) keys, followed by the\n            // sequence of recursively canonicalized values corresponding to\n            // those keys. To keep the final results unambiguous with other\n            // sequences (such as arrays that just happen to contain [proto,\n            // keys.json, value1, value2, ...]), the known version of the\n            // object is stored as node.object.\n            var node = this.pool.lookupArray(array_1);\n            if (!node.object) {\n              var obj_1 = node.object = Object.create(proto_1);\n              this.known.add(obj_1);\n              keys.sorted.forEach(function (key, i) {\n                obj_1[key] = array_1[firstValueIndex_1 + i];\n              });\n              // Since canonical objects may be shared widely between\n              // unrelated consumers, it's important to regard them as\n              // immutable, even if they are not frozen in production.\n              if (globalThis.__DEV__ !== false) {\n                Object.freeze(obj_1);\n              }\n            }\n            return node.object;\n          }\n      }\n    }\n    return value;\n  };\n  // It's worthwhile to cache the sorting of arrays of strings, since the\n  // same initial unsorted arrays tend to be encountered many times.\n  // Fortunately, we can reuse the Trie machinery to look up the sorted\n  // arrays in linear time (which is faster than sorting large arrays).\n  ObjectCanon.prototype.sortedKeys = function (obj) {\n    var keys = Object.keys(obj);\n    var node = this.pool.lookupArray(keys);\n    if (!node.keys) {\n      keys.sort();\n      var json = JSON.stringify(keys);\n      if (!(node.keys = this.keysByJSON.get(json))) {\n        this.keysByJSON.set(json, node.keys = {\n          sorted: keys,\n          json: json\n        });\n      }\n    }\n    return node.keys;\n  };\n  return ObjectCanon;\n}();\nexport { ObjectCanon };\n//# sourceMappingURL=object-canon.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}