{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Inject, Input, Output, Injectable, LOCALE_ID, Pipe, Component, HostListener, InjectionToken, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { DOCUMENT, formatDate, CommonModule, I18nPluralPipe } from '@angular/common';\nimport { Subject, Observable, of, timer, BehaviorSubject, interval } from 'rxjs';\nimport { takeUntil, switchMap, startWith, switchMapTo, map } from 'rxjs/operators';\nimport { positionElements } from 'positioning';\nimport { validateEvents as validateEvents$1, getMonthView, getWeekViewHeader, getWeekView } from 'calendar-utils';\nexport { DAYS_OF_WEEK } from 'calendar-utils';\nimport * as i2 from 'angular-draggable-droppable';\nimport { DragAndDropModule } from 'angular-draggable-droppable';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i4 from 'angular-resizable-element';\nimport { ResizableModule } from 'angular-resizable-element';\nconst _c0 = function (a0) {\n  return {\n    action: a0\n  };\n};\nfunction CalendarEventActionsComponent_ng_template_0_span_0_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 5);\n    i0.ɵɵlistener(\"mwlClick\", function CalendarEventActionsComponent_ng_template_0_span_0_a_1_Template_a_mwlClick_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const action_r7 = restoredCtx.$implicit;\n      const event_r3 = i0.ɵɵnextContext(2).event;\n      return i0.ɵɵresetView(action_r7.onClick({\n        event: event_r3,\n        sourceEvent: $event\n      }));\n    })(\"mwlKeydownEnter\", function CalendarEventActionsComponent_ng_template_0_span_0_a_1_Template_a_mwlKeydownEnter_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const action_r7 = restoredCtx.$implicit;\n      const event_r3 = i0.ɵɵnextContext(2).event;\n      return i0.ɵɵresetView(action_r7.onClick({\n        event: event_r3,\n        sourceEvent: $event\n      }));\n    });\n    i0.ɵɵpipe(1, \"calendarA11y\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", action_r7.cssClass)(\"innerHtml\", action_r7.label, i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind2(1, 3, i0.ɵɵpureFunction1(6, _c0, action_r7), \"actionButtonLabel\"));\n  }\n}\nfunction CalendarEventActionsComponent_ng_template_0_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵtemplate(1, CalendarEventActionsComponent_ng_template_0_span_0_a_1_Template, 2, 8, \"a\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    const event_r3 = ctx_r13.event;\n    const trackByActionId_r4 = ctx_r13.trackByActionId;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", event_r3.actions)(\"ngForTrackBy\", trackByActionId_r4);\n  }\n}\nfunction CalendarEventActionsComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CalendarEventActionsComponent_ng_template_0_span_0_Template, 2, 2, \"span\", 2);\n  }\n  if (rf & 2) {\n    const event_r3 = ctx.event;\n    i0.ɵɵproperty(\"ngIf\", event_r3.actions);\n  }\n}\nfunction CalendarEventActionsComponent_ng_template_2_Template(rf, ctx) {}\nconst _c1 = function (a0, a1) {\n  return {\n    event: a0,\n    trackByActionId: a1\n  };\n};\nconst _c2 = function () {\n  return {};\n};\nfunction CalendarEventTitleComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 2);\n    i0.ɵɵpipe(1, \"calendarEventTitle\");\n    i0.ɵɵpipe(2, \"calendarA11y\");\n  }\n  if (rf & 2) {\n    const event_r3 = ctx.event;\n    const view_r4 = ctx.view;\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind3(1, 2, event_r3.title, view_r4, event_r3), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"aria-hidden\", i0.ɵɵpipeBind2(2, 6, i0.ɵɵpureFunction0(9, _c2), \"hideEventTitle\"));\n  }\n}\nfunction CalendarEventTitleComponent_ng_template_2_Template(rf, ctx) {}\nconst _c3 = function (a0, a1) {\n  return {\n    event: a0,\n    view: a1\n  };\n};\nfunction CalendarTooltipWindowComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵelement(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const contents_r3 = ctx.contents;\n    const placement_r4 = ctx.placement;\n    i0.ɵɵproperty(\"ngClass\", \"cal-tooltip-\" + placement_r4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHtml\", contents_r3, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction CalendarTooltipWindowComponent_ng_template_2_Template(rf, ctx) {}\nconst _c4 = function (a0, a1, a2) {\n  return {\n    contents: a0,\n    placement: a1,\n    event: a2\n  };\n};\nfunction CalendarMonthCellComponent_ng_template_0_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r3 = i0.ɵɵnextContext().day;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(day_r3.badgeTotal);\n  }\n}\nconst _c5 = function (a0) {\n  return {\n    backgroundColor: a0\n  };\n};\nconst _c6 = function (a0, a1) {\n  return {\n    event: a0,\n    draggedFrom: a1\n  };\n};\nconst _c7 = function (a0, a1) {\n  return {\n    x: a0,\n    y: a1\n  };\n};\nconst _c8 = function () {\n  return {\n    delay: 300,\n    delta: 30\n  };\n};\nfunction CalendarMonthCellComponent_ng_template_0_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵlistener(\"mouseenter\", function CalendarMonthCellComponent_ng_template_0_div_7_div_1_Template_div_mouseenter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r21);\n      const event_r19 = restoredCtx.$implicit;\n      const highlightDay_r7 = i0.ɵɵnextContext(2).highlightDay;\n      return i0.ɵɵresetView(highlightDay_r7.emit({\n        event: event_r19\n      }));\n    })(\"mouseleave\", function CalendarMonthCellComponent_ng_template_0_div_7_div_1_Template_div_mouseleave_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r21);\n      const event_r19 = restoredCtx.$implicit;\n      const unhighlightDay_r8 = i0.ɵɵnextContext(2).unhighlightDay;\n      return i0.ɵɵresetView(unhighlightDay_r8.emit({\n        event: event_r19\n      }));\n    })(\"mwlClick\", function CalendarMonthCellComponent_ng_template_0_div_7_div_1_Template_div_mwlClick_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r21);\n      const event_r19 = restoredCtx.$implicit;\n      const eventClicked_r9 = i0.ɵɵnextContext(2).eventClicked;\n      return i0.ɵɵresetView(eventClicked_r9.emit({\n        event: event_r19,\n        sourceEvent: $event\n      }));\n    });\n    i0.ɵɵpipe(1, \"calendarEventTitle\");\n    i0.ɵɵpipe(2, \"calendarA11y\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const event_r19 = ctx.$implicit;\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    const tooltipPlacement_r6 = ctx_r27.tooltipPlacement;\n    const tooltipTemplate_r10 = ctx_r27.tooltipTemplate;\n    const tooltipAppendToBody_r11 = ctx_r27.tooltipAppendToBody;\n    const tooltipDelay_r12 = ctx_r27.tooltipDelay;\n    const day_r3 = ctx_r27.day;\n    const validateDrag_r14 = ctx_r27.validateDrag;\n    i0.ɵɵclassProp(\"cal-draggable\", event_r19.draggable);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(22, _c5, event_r19.color == null ? null : event_r19.color.primary))(\"ngClass\", event_r19 == null ? null : event_r19.cssClass)(\"mwlCalendarTooltip\", i0.ɵɵpipeBind3(1, 15, event_r19.title, \"monthTooltip\", event_r19))(\"tooltipPlacement\", tooltipPlacement_r6)(\"tooltipEvent\", event_r19)(\"tooltipTemplate\", tooltipTemplate_r10)(\"tooltipAppendToBody\", tooltipAppendToBody_r11)(\"tooltipDelay\", tooltipDelay_r12)(\"dropData\", i0.ɵɵpureFunction2(24, _c6, event_r19, day_r3))(\"dragAxis\", i0.ɵɵpureFunction2(27, _c7, event_r19.draggable, event_r19.draggable))(\"validateDrag\", validateDrag_r14)(\"touchStartLongPress\", i0.ɵɵpureFunction0(30, _c8));\n    i0.ɵɵattribute(\"aria-hidden\", i0.ɵɵpipeBind2(2, 19, i0.ɵɵpureFunction0(31, _c2), \"hideMonthCellEvents\"));\n  }\n}\nfunction CalendarMonthCellComponent_ng_template_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, CalendarMonthCellComponent_ng_template_0_div_7_div_1_Template, 3, 32, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext();\n    const day_r3 = ctx_r28.day;\n    const trackByEventId_r13 = ctx_r28.trackByEventId;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", day_r3.events)(\"ngForTrackBy\", trackByEventId_r13);\n  }\n}\nconst _c9 = function (a0, a1) {\n  return {\n    day: a0,\n    locale: a1\n  };\n};\nfunction CalendarMonthCellComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵpipe(1, \"calendarA11y\");\n    i0.ɵɵelementStart(2, \"span\", 3);\n    i0.ɵɵtemplate(3, CalendarMonthCellComponent_ng_template_0_span_3_Template, 2, 1, \"span\", 4);\n    i0.ɵɵelementStart(4, \"span\", 5);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"calendarDate\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(7, CalendarMonthCellComponent_ng_template_0_div_7_Template, 2, 2, \"div\", 6);\n  }\n  if (rf & 2) {\n    const day_r3 = ctx.day;\n    const locale_r5 = ctx.locale;\n    i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind2(1, 4, i0.ɵɵpureFunction2(11, _c9, day_r3, locale_r5), \"monthCell\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", day_r3.badgeTotal > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(6, 7, day_r3.date, \"monthViewDayNumber\", locale_r5));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", day_r3.events.length > 0);\n  }\n}\nfunction CalendarMonthCellComponent_ng_template_2_Template(rf, ctx) {}\nconst _c10 = function (a0, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11) {\n  return {\n    day: a0,\n    openDay: a1,\n    locale: a2,\n    tooltipPlacement: a3,\n    highlightDay: a4,\n    unhighlightDay: a5,\n    eventClicked: a6,\n    tooltipTemplate: a7,\n    tooltipAppendToBody: a8,\n    tooltipDelay: a9,\n    trackByEventId: a10,\n    validateDrag: a11\n  };\n};\nconst _c11 = function (a0) {\n  return {\n    event: a0\n  };\n};\nconst _c12 = function (a0, a1) {\n  return {\n    event: a0,\n    locale: a1\n  };\n};\nfunction CalendarOpenDayEventsComponent_ng_template_0_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"span\", 8);\n    i0.ɵɵtext(2, \" \");\n    i0.ɵɵelementStart(3, \"mwl-calendar-event-title\", 9);\n    i0.ɵɵlistener(\"mwlClick\", function CalendarOpenDayEventsComponent_ng_template_0_div_0_div_5_Template_mwl_calendar_event_title_mwlClick_3_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const event_r10 = restoredCtx.$implicit;\n      const eventClicked_r4 = i0.ɵɵnextContext(2).eventClicked;\n      return i0.ɵɵresetView(eventClicked_r4.emit({\n        event: event_r10,\n        sourceEvent: $event\n      }));\n    })(\"mwlKeydownEnter\", function CalendarOpenDayEventsComponent_ng_template_0_div_0_div_5_Template_mwl_calendar_event_title_mwlKeydownEnter_3_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const event_r10 = restoredCtx.$implicit;\n      const eventClicked_r4 = i0.ɵɵnextContext(2).eventClicked;\n      return i0.ɵɵresetView(eventClicked_r4.emit({\n        event: event_r10,\n        sourceEvent: $event\n      }));\n    });\n    i0.ɵɵpipe(4, \"calendarA11y\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \");\n    i0.ɵɵelement(6, \"mwl-calendar-event-actions\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const event_r10 = ctx.$implicit;\n    const validateDrag_r7 = i0.ɵɵnextContext(2).validateDrag;\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"cal-draggable\", event_r10.draggable);\n    i0.ɵɵproperty(\"ngClass\", event_r10 == null ? null : event_r10.cssClass)(\"dropData\", i0.ɵɵpureFunction1(16, _c11, event_r10))(\"dragAxis\", i0.ɵɵpureFunction2(18, _c7, event_r10.draggable, event_r10.draggable))(\"validateDrag\", validateDrag_r7)(\"touchStartLongPress\", i0.ɵɵpureFunction0(21, _c8));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(22, _c5, event_r10.color == null ? null : event_r10.color.primary));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"event\", event_r10)(\"customTemplate\", ctx_r9.eventTitleTemplate);\n    i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind2(4, 13, i0.ɵɵpureFunction2(24, _c12, event_r10, ctx_r9.locale), \"eventDescription\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"event\", event_r10)(\"customTemplate\", ctx_r9.eventActionsTemplate);\n  }\n}\nconst _c13 = function (a0, a1) {\n  return {\n    date: a0,\n    locale: a1\n  };\n};\nfunction CalendarOpenDayEventsComponent_ng_template_0_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"span\", 4);\n    i0.ɵɵpipe(2, \"calendarA11y\");\n    i0.ɵɵelement(3, \"span\", 5);\n    i0.ɵɵpipe(4, \"calendarA11y\");\n    i0.ɵɵtemplate(5, CalendarOpenDayEventsComponent_ng_template_0_div_0_div_5_Template, 7, 27, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    const events_r3 = ctx_r17.events;\n    const trackByEventId_r6 = ctx_r17.trackByEventId;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@collapse\", undefined);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind2(2, 5, i0.ɵɵpureFunction2(11, _c13, ctx_r8.date, ctx_r8.locale), \"openDayEventsAlert\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind2(4, 8, i0.ɵɵpureFunction2(14, _c13, ctx_r8.date, ctx_r8.locale), \"openDayEventsLandmark\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", events_r3)(\"ngForTrackBy\", trackByEventId_r6);\n  }\n}\nfunction CalendarOpenDayEventsComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CalendarOpenDayEventsComponent_ng_template_0_div_0_Template, 6, 17, \"div\", 2);\n  }\n  if (rf & 2) {\n    const isOpen_r5 = ctx.isOpen;\n    i0.ɵɵproperty(\"ngIf\", isOpen_r5);\n  }\n}\nfunction CalendarOpenDayEventsComponent_ng_template_2_Template(rf, ctx) {}\nconst _c14 = function (a0, a1, a2, a3, a4) {\n  return {\n    events: a0,\n    eventClicked: a1,\n    isOpen: a2,\n    trackByEventId: a3,\n    validateDrag: a4\n  };\n};\nfunction CalendarMonthViewHeaderComponent_ng_template_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵlistener(\"click\", function CalendarMonthViewHeaderComponent_ng_template_0_div_1_Template_div_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const day_r7 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.columnHeaderClicked.emit({\n        isoDayNumber: day_r7.day,\n        sourceEvent: $event\n      }));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"calendarDate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r7 = ctx.$implicit;\n    const locale_r4 = i0.ɵɵnextContext().locale;\n    i0.ɵɵclassProp(\"cal-past\", day_r7.isPast)(\"cal-today\", day_r7.isToday)(\"cal-future\", day_r7.isFuture)(\"cal-weekend\", day_r7.isWeekend);\n    i0.ɵɵproperty(\"ngClass\", day_r7.cssClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(2, 10, day_r7.date, \"monthViewColumnHeader\", locale_r4), \" \");\n  }\n}\nfunction CalendarMonthViewHeaderComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, CalendarMonthViewHeaderComponent_ng_template_0_div_1_Template, 3, 14, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const days_r3 = ctx.days;\n    const trackByWeekDayHeaderDate_r5 = ctx.trackByWeekDayHeaderDate;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", days_r3)(\"ngForTrackBy\", trackByWeekDayHeaderDate_r5);\n  }\n}\nfunction CalendarMonthViewHeaderComponent_ng_template_2_Template(rf, ctx) {}\nconst _c15 = function (a0, a1, a2) {\n  return {\n    days: a0,\n    locale: a1,\n    trackByWeekDayHeaderDate: a2\n  };\n};\nfunction CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mwl-calendar-month-cell\", 7);\n    i0.ɵɵlistener(\"mwlClick\", function CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template_mwl_calendar_month_cell_mwlClick_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const day_r3 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.dayClicked.emit({\n        day: day_r3,\n        sourceEvent: $event\n      }));\n    })(\"mwlKeydownEnter\", function CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template_mwl_calendar_month_cell_mwlKeydownEnter_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const day_r3 = restoredCtx.$implicit;\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.dayClicked.emit({\n        day: day_r3,\n        sourceEvent: $event\n      }));\n    })(\"highlightDay\", function CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template_mwl_calendar_month_cell_highlightDay_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.toggleDayHighlight($event.event, true));\n    })(\"unhighlightDay\", function CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template_mwl_calendar_month_cell_unhighlightDay_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.toggleDayHighlight($event.event, false));\n    })(\"drop\", function CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template_mwl_calendar_month_cell_drop_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const day_r3 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.eventDropped(day_r3, $event.dropData.event, $event.dropData.draggedFrom));\n    })(\"eventClicked\", function CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template_mwl_calendar_month_cell_eventClicked_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.eventClicked.emit({\n        event: $event.event,\n        sourceEvent: $event.sourceEvent\n      }));\n    });\n    i0.ɵɵpipe(1, \"calendarA11y\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r3 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", day_r3 == null ? null : day_r3.cssClass)(\"day\", day_r3)(\"openDay\", ctx_r2.openDay)(\"locale\", ctx_r2.locale)(\"tooltipPlacement\", ctx_r2.tooltipPlacement)(\"tooltipAppendToBody\", ctx_r2.tooltipAppendToBody)(\"tooltipTemplate\", ctx_r2.tooltipTemplate)(\"tooltipDelay\", ctx_r2.tooltipDelay)(\"customTemplate\", ctx_r2.cellTemplate)(\"ngStyle\", i0.ɵɵpureFunction1(15, _c5, day_r3.backgroundColor))(\"clickListenerDisabled\", ctx_r2.dayClicked.observers.length === 0);\n    i0.ɵɵattribute(\"tabindex\", i0.ɵɵpipeBind2(1, 12, i0.ɵɵpureFunction0(17, _c2), \"monthCellTabIndex\"));\n  }\n}\nfunction CalendarMonthViewComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 4);\n    i0.ɵɵtemplate(2, CalendarMonthViewComponent_div_3_mwl_calendar_month_cell_2_Template, 2, 18, \"mwl-calendar-month-cell\", 5);\n    i0.ɵɵpipe(3, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mwl-calendar-open-day-events\", 6);\n    i0.ɵɵlistener(\"eventClicked\", function CalendarMonthViewComponent_div_3_Template_mwl_calendar_open_day_events_eventClicked_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.eventClicked.emit({\n        event: $event.event,\n        sourceEvent: $event.sourceEvent\n      }));\n    })(\"drop\", function CalendarMonthViewComponent_div_3_Template_mwl_calendar_open_day_events_drop_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.eventDropped(ctx_r13.openDay, $event.dropData.event, $event.dropData.draggedFrom));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const rowIndex_r1 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(3, 9, ctx_r0.view.days, rowIndex_r1, rowIndex_r1 + ctx_r0.view.totalDaysVisibleInWeek))(\"ngForTrackBy\", ctx_r0.trackByDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"locale\", ctx_r0.locale)(\"isOpen\", ctx_r0.openRowIndex === rowIndex_r1)(\"events\", ctx_r0.openDay == null ? null : ctx_r0.openDay.events)(\"date\", ctx_r0.openDay == null ? null : ctx_r0.openDay.date)(\"customTemplate\", ctx_r0.openDayEventsTemplate)(\"eventTitleTemplate\", ctx_r0.eventTitleTemplate)(\"eventActionsTemplate\", ctx_r0.eventActionsTemplate);\n  }\n}\nfunction CalendarWeekViewHeaderComponent_ng_template_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵlistener(\"mwlClick\", function CalendarWeekViewHeaderComponent_ng_template_0_div_1_Template_div_mwlClick_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const day_r10 = restoredCtx.$implicit;\n      const dayHeaderClicked_r5 = i0.ɵɵnextContext().dayHeaderClicked;\n      return i0.ɵɵresetView(dayHeaderClicked_r5.emit({\n        day: day_r10,\n        sourceEvent: $event\n      }));\n    })(\"drop\", function CalendarWeekViewHeaderComponent_ng_template_0_div_1_Template_div_drop_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const day_r10 = restoredCtx.$implicit;\n      const eventDropped_r6 = i0.ɵɵnextContext().eventDropped;\n      return i0.ɵɵresetView(eventDropped_r6.emit({\n        event: $event.dropData.event,\n        newStart: day_r10.date\n      }));\n    })(\"dragEnter\", function CalendarWeekViewHeaderComponent_ng_template_0_div_1_Template_div_dragEnter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const day_r10 = restoredCtx.$implicit;\n      const dragEnter_r8 = i0.ɵɵnextContext().dragEnter;\n      return i0.ɵɵresetView(dragEnter_r8.emit({\n        date: day_r10.date\n      }));\n    });\n    i0.ɵɵelementStart(1, \"b\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"calendarDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"br\");\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"calendarDate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r10 = ctx.$implicit;\n    const locale_r4 = i0.ɵɵnextContext().locale;\n    i0.ɵɵclassProp(\"cal-past\", day_r10.isPast)(\"cal-today\", day_r10.isToday)(\"cal-future\", day_r10.isFuture)(\"cal-weekend\", day_r10.isWeekend);\n    i0.ɵɵproperty(\"ngClass\", day_r10.cssClass);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(3, 11, day_r10.date, \"weekViewColumnHeader\", locale_r4));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(7, 15, day_r10.date, \"weekViewColumnSubHeader\", locale_r4));\n  }\n}\nfunction CalendarWeekViewHeaderComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, CalendarWeekViewHeaderComponent_ng_template_0_div_1_Template, 8, 19, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const days_r3 = ctx.days;\n    const trackByWeekDayHeaderDate_r7 = ctx.trackByWeekDayHeaderDate;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", days_r3)(\"ngForTrackBy\", trackByWeekDayHeaderDate_r7);\n  }\n}\nfunction CalendarWeekViewHeaderComponent_ng_template_2_Template(rf, ctx) {}\nconst _c16 = function (a0, a1, a2, a3, a4, a5) {\n  return {\n    days: a0,\n    locale: a1,\n    dayHeaderClicked: a2,\n    eventDropped: a3,\n    dragEnter: a4,\n    trackByWeekDayHeaderDate: a5\n  };\n};\nconst _c17 = function (a0, a1, a2) {\n  return {\n    color: a0,\n    backgroundColor: a1,\n    borderColor: a2\n  };\n};\nfunction CalendarWeekViewEventComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"mwlClick\", function CalendarWeekViewEventComponent_ng_template_0_Template_div_mwlClick_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const eventClicked_r5 = restoredCtx.eventClicked;\n      return i0.ɵɵresetView(eventClicked_r5.emit({\n        sourceEvent: $event\n      }));\n    })(\"mwlKeydownEnter\", function CalendarWeekViewEventComponent_ng_template_0_Template_div_mwlKeydownEnter_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const eventClicked_r5 = restoredCtx.eventClicked;\n      return i0.ɵɵresetView(eventClicked_r5.emit({\n        sourceEvent: $event\n      }));\n    });\n    i0.ɵɵpipe(1, \"calendarEventTitle\");\n    i0.ɵɵpipe(2, \"calendarA11y\");\n    i0.ɵɵelement(3, \"mwl-calendar-event-actions\", 3);\n    i0.ɵɵtext(4, \" \");\n    i0.ɵɵelement(5, \"mwl-calendar-event-title\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const weekEvent_r3 = ctx.weekEvent;\n    const tooltipPlacement_r4 = ctx.tooltipPlacement;\n    const tooltipTemplate_r6 = ctx.tooltipTemplate;\n    const tooltipAppendToBody_r7 = ctx.tooltipAppendToBody;\n    const tooltipDisabled_r8 = ctx.tooltipDisabled;\n    const tooltipDelay_r9 = ctx.tooltipDelay;\n    const daysInWeek_r11 = ctx.daysInWeek;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction3(20, _c17, weekEvent_r3.event.color == null ? null : weekEvent_r3.event.color.secondaryText, weekEvent_r3.event.color == null ? null : weekEvent_r3.event.color.secondary, weekEvent_r3.event.color == null ? null : weekEvent_r3.event.color.primary))(\"mwlCalendarTooltip\", !tooltipDisabled_r8 ? i0.ɵɵpipeBind3(1, 13, weekEvent_r3.event.title, daysInWeek_r11 === 1 ? \"dayTooltip\" : \"weekTooltip\", weekEvent_r3.tempEvent || weekEvent_r3.event) : \"\")(\"tooltipPlacement\", tooltipPlacement_r4)(\"tooltipEvent\", weekEvent_r3.tempEvent || weekEvent_r3.event)(\"tooltipTemplate\", tooltipTemplate_r6)(\"tooltipAppendToBody\", tooltipAppendToBody_r7)(\"tooltipDelay\", tooltipDelay_r9);\n    i0.ɵɵattribute(\"aria-label\", i0.ɵɵpipeBind2(2, 17, i0.ɵɵpureFunction2(24, _c12, weekEvent_r3.tempEvent || weekEvent_r3.event, ctx_r1.locale), \"eventDescription\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"event\", weekEvent_r3.tempEvent || weekEvent_r3.event)(\"customTemplate\", ctx_r1.eventActionsTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"event\", weekEvent_r3.tempEvent || weekEvent_r3.event)(\"customTemplate\", ctx_r1.eventTitleTemplate)(\"view\", daysInWeek_r11 === 1 ? \"day\" : \"week\");\n  }\n}\nfunction CalendarWeekViewEventComponent_ng_template_2_Template(rf, ctx) {}\nconst _c18 = function (a0, a1, a2, a3, a4, a5, a6, a7, a8) {\n  return {\n    weekEvent: a0,\n    tooltipPlacement: a1,\n    eventClicked: a2,\n    tooltipTemplate: a3,\n    tooltipAppendToBody: a4,\n    tooltipDisabled: a5,\n    tooltipDelay: a6,\n    column: a7,\n    daysInWeek: a8\n  };\n};\nfunction CalendarWeekViewHourSegmentComponent_ng_template_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"calendarDate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    const segment_r3 = ctx_r9.segment;\n    const daysInWeek_r7 = ctx_r9.daysInWeek;\n    const locale_r4 = ctx_r9.locale;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind3(2, 1, segment_r3.displayDate, daysInWeek_r7 === 1 ? \"dayViewHour\" : \"weekViewHour\", locale_r4), \" \");\n  }\n}\nfunction CalendarWeekViewHourSegmentComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵpipe(1, \"calendarA11y\");\n    i0.ɵɵtemplate(2, CalendarWeekViewHourSegmentComponent_ng_template_0_div_2_Template, 3, 5, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r3 = ctx.segment;\n    const segmentHeight_r5 = ctx.segmentHeight;\n    const isTimeLabel_r6 = ctx.isTimeLabel;\n    const daysInWeek_r7 = ctx.daysInWeek;\n    i0.ɵɵstyleProp(\"height\", segmentHeight_r5, \"px\");\n    i0.ɵɵclassProp(\"cal-hour-start\", segment_r3.isStart)(\"cal-after-hour-start\", !segment_r3.isStart);\n    i0.ɵɵproperty(\"ngClass\", segment_r3.cssClass);\n    i0.ɵɵattribute(\"aria-hidden\", i0.ɵɵpipeBind2(1, 9, i0.ɵɵpureFunction0(12, _c2), daysInWeek_r7 === 1 ? \"hideDayHourSegment\" : \"hideWeekHourSegment\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", isTimeLabel_r6);\n  }\n}\nfunction CalendarWeekViewHourSegmentComponent_ng_template_2_Template(rf, ctx) {}\nconst _c19 = function (a0, a1, a2, a3, a4) {\n  return {\n    segment: a0,\n    locale: a1,\n    segmentHeight: a2,\n    isTimeLabel: a3,\n    daysInWeek: a4\n  };\n};\nfunction CalendarWeekViewCurrentTimeMarkerComponent_ng_template_0_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 3);\n  }\n  if (rf & 2) {\n    const topPx_r9 = i0.ɵɵnextContext().topPx;\n    i0.ɵɵstyleProp(\"top\", topPx_r9, \"px\");\n  }\n}\nfunction CalendarWeekViewCurrentTimeMarkerComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CalendarWeekViewCurrentTimeMarkerComponent_ng_template_0_div_0_Template, 1, 2, \"div\", 2);\n  }\n  if (rf & 2) {\n    const isVisible_r8 = ctx.isVisible;\n    i0.ɵɵproperty(\"ngIf\", isVisible_r8);\n  }\n}\nfunction CalendarWeekViewCurrentTimeMarkerComponent_ng_template_2_Template(rf, ctx) {}\nconst _c20 = function (a0, a1, a2, a3, a4, a5, a6) {\n  return {\n    columnDate: a0,\n    dayStartHour: a1,\n    dayStartMinute: a2,\n    dayEndHour: a3,\n    dayEndMinute: a4,\n    isVisible: a5,\n    topPx: a6\n  };\n};\nfunction CalendarWeekViewComponent_div_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction CalendarWeekViewComponent_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵlistener(\"drop\", function CalendarWeekViewComponent_div_2_div_5_Template_div_drop_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const day_r8 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.eventDropped($event, day_r8.date, true));\n    })(\"dragEnter\", function CalendarWeekViewComponent_div_2_div_5_Template_div_dragEnter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const day_r8 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.dateDragEnter(day_r8.date));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c21 = function () {\n  return {\n    left: true\n  };\n};\nfunction CalendarWeekViewComponent_div_2_div_6_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 23);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"resizeEdges\", i0.ɵɵpureFunction0(1, _c21));\n  }\n}\nconst _c22 = function () {\n  return {\n    right: true\n  };\n};\nfunction CalendarWeekViewComponent_div_2_div_6_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"resizeEdges\", i0.ɵɵpureFunction0(1, _c22));\n  }\n}\nconst _c23 = function (a0, a1) {\n  return {\n    left: a0,\n    right: a1\n  };\n};\nconst _c24 = function (a0, a1) {\n  return {\n    event: a0,\n    calendarId: a1\n  };\n};\nconst _c25 = function (a0) {\n  return {\n    x: a0\n  };\n};\nfunction CalendarWeekViewComponent_div_2_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18, 19);\n    i0.ɵɵlistener(\"resizeStart\", function CalendarWeekViewComponent_div_2_div_6_div_2_Template_div_resizeStart_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const allDayEvent_r15 = restoredCtx.$implicit;\n      i0.ɵɵnextContext();\n      const _r13 = i0.ɵɵreference(1);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.allDayEventResizeStarted(_r13, allDayEvent_r15, $event));\n    })(\"resizing\", function CalendarWeekViewComponent_div_2_div_6_div_2_Template_div_resizing_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const allDayEvent_r15 = restoredCtx.$implicit;\n      const ctx_r21 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r21.allDayEventResizing(allDayEvent_r15, $event, ctx_r21.dayColumnWidth));\n    })(\"resizeEnd\", function CalendarWeekViewComponent_div_2_div_6_div_2_Template_div_resizeEnd_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const allDayEvent_r15 = restoredCtx.$implicit;\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.allDayEventResizeEnded(allDayEvent_r15));\n    })(\"dragStart\", function CalendarWeekViewComponent_div_2_div_6_div_2_Template_div_dragStart_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const allDayEvent_r15 = restoredCtx.$implicit;\n      const _r16 = i0.ɵɵreference(1);\n      i0.ɵɵnextContext();\n      const _r13 = i0.ɵɵreference(1);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.dragStarted(_r13, _r16, allDayEvent_r15, false));\n    })(\"dragging\", function CalendarWeekViewComponent_div_2_div_6_div_2_Template_div_dragging_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.allDayEventDragMove());\n    })(\"dragEnd\", function CalendarWeekViewComponent_div_2_div_6_div_2_Template_div_dragEnd_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const allDayEvent_r15 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.dragEnded(allDayEvent_r15, $event, ctx_r25.dayColumnWidth));\n    });\n    i0.ɵɵtemplate(2, CalendarWeekViewComponent_div_2_div_6_div_2_div_2_Template, 1, 2, \"div\", 20);\n    i0.ɵɵelementStart(3, \"mwl-calendar-week-view-event\", 21);\n    i0.ɵɵlistener(\"eventClicked\", function CalendarWeekViewComponent_div_2_div_6_div_2_Template_mwl_calendar_week_view_event_eventClicked_3_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const allDayEvent_r15 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r26.eventClicked.emit({\n        event: allDayEvent_r15.event,\n        sourceEvent: $event.sourceEvent\n      }));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CalendarWeekViewComponent_div_2_div_6_div_2_div_4_Template, 1, 2, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const allDayEvent_r15 = ctx.$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"width\", 100 / ctx_r14.days.length * allDayEvent_r15.span, \"%\")(\"margin-left\", ctx_r14.rtl ? null : 100 / ctx_r14.days.length * allDayEvent_r15.offset, \"%\")(\"margin-right\", ctx_r14.rtl ? 100 / ctx_r14.days.length * allDayEvent_r15.offset : null, \"%\");\n    i0.ɵɵclassProp(\"cal-draggable\", allDayEvent_r15.event.draggable && ctx_r14.allDayEventResizes.size === 0)(\"cal-starts-within-week\", !allDayEvent_r15.startsBeforeWeek)(\"cal-ends-within-week\", !allDayEvent_r15.endsAfterWeek);\n    i0.ɵɵproperty(\"ngClass\", allDayEvent_r15.event == null ? null : allDayEvent_r15.event.cssClass)(\"resizeCursors\", ctx_r14.resizeCursors)(\"resizeSnapGrid\", i0.ɵɵpureFunction2(33, _c23, ctx_r14.dayColumnWidth, ctx_r14.dayColumnWidth))(\"validateResize\", ctx_r14.validateResize)(\"dropData\", i0.ɵɵpureFunction2(36, _c24, allDayEvent_r15.event, ctx_r14.calendarId))(\"dragAxis\", i0.ɵɵpureFunction2(39, _c7, allDayEvent_r15.event.draggable && ctx_r14.allDayEventResizes.size === 0, !ctx_r14.snapDraggedEvents && allDayEvent_r15.event.draggable && ctx_r14.allDayEventResizes.size === 0))(\"dragSnapGrid\", ctx_r14.snapDraggedEvents ? i0.ɵɵpureFunction1(42, _c25, ctx_r14.dayColumnWidth) : i0.ɵɵpureFunction0(44, _c2))(\"validateDrag\", ctx_r14.validateDrag)(\"touchStartLongPress\", i0.ɵɵpureFunction0(45, _c8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (allDayEvent_r15.event == null ? null : allDayEvent_r15.event.resizable == null ? null : allDayEvent_r15.event.resizable.beforeStart) && !allDayEvent_r15.startsBeforeWeek);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"locale\", ctx_r14.locale)(\"weekEvent\", allDayEvent_r15)(\"tooltipPlacement\", ctx_r14.tooltipPlacement)(\"tooltipTemplate\", ctx_r14.tooltipTemplate)(\"tooltipAppendToBody\", ctx_r14.tooltipAppendToBody)(\"tooltipDelay\", ctx_r14.tooltipDelay)(\"customTemplate\", ctx_r14.eventTemplate)(\"eventTitleTemplate\", ctx_r14.eventTitleTemplate)(\"eventActionsTemplate\", ctx_r14.eventActionsTemplate)(\"daysInWeek\", ctx_r14.daysInWeek);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (allDayEvent_r15.event == null ? null : allDayEvent_r15.event.resizable == null ? null : allDayEvent_r15.event.resizable.afterEnd) && !allDayEvent_r15.endsAfterWeek);\n  }\n}\nfunction CalendarWeekViewComponent_div_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15, 16);\n    i0.ɵɵtemplate(2, CalendarWeekViewComponent_div_2_div_6_div_2_Template, 5, 46, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const eventRow_r12 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", eventRow_r12.row)(\"ngForTrackBy\", ctx_r7.trackByWeekAllDayEvent);\n  }\n}\nfunction CalendarWeekViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8, 9);\n    i0.ɵɵlistener(\"dragEnter\", function CalendarWeekViewComponent_div_2_Template_div_dragEnter_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.dragEnter(\"allDay\"));\n    })(\"dragLeave\", function CalendarWeekViewComponent_div_2_Template_div_dragLeave_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.dragLeave(\"allDay\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 5)(3, \"div\", 10);\n    i0.ɵɵtemplate(4, CalendarWeekViewComponent_div_2_ng_container_4_Template, 1, 0, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CalendarWeekViewComponent_div_2_div_5_Template, 1, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, CalendarWeekViewComponent_div_2_div_6_Template, 3, 2, \"div\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.allDayEventsLabelTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.days)(\"ngForTrackBy\", ctx_r0.trackByWeekDayHeaderDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.view.allDayEventRows)(\"ngForTrackBy\", ctx_r0.trackById);\n  }\n}\nfunction CalendarWeekViewComponent_div_4_div_1_mwl_calendar_week_view_hour_segment_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mwl-calendar-week-view-hour-segment\", 28);\n  }\n  if (rf & 2) {\n    const segment_r34 = ctx.$implicit;\n    const ctx_r33 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"height\", ctx_r33.hourSegmentHeight, \"px\");\n    i0.ɵɵproperty(\"segment\", segment_r34)(\"segmentHeight\", ctx_r33.hourSegmentHeight)(\"locale\", ctx_r33.locale)(\"customTemplate\", ctx_r33.hourSegmentTemplate)(\"isTimeLabel\", true)(\"daysInWeek\", ctx_r33.daysInWeek);\n  }\n}\nfunction CalendarWeekViewComponent_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtemplate(1, CalendarWeekViewComponent_div_4_div_1_mwl_calendar_week_view_hour_segment_1_Template, 1, 8, \"mwl-calendar-week-view-hour-segment\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hour_r31 = ctx.$implicit;\n    const odd_r32 = ctx.odd;\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"cal-hour-odd\", odd_r32);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", hour_r31.segments)(\"ngForTrackBy\", ctx_r30.trackByHourSegment);\n  }\n}\nfunction CalendarWeekViewComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtemplate(1, CalendarWeekViewComponent_div_4_div_1_Template, 2, 4, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.view.hourColumns[0].hours)(\"ngForTrackBy\", ctx_r1.trackByHour);\n  }\n}\nconst _c26 = function () {\n  return {\n    left: true,\n    top: true\n  };\n};\nfunction CalendarWeekViewComponent_div_7_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 23);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"resizeEdges\", i0.ɵɵpureFunction0(1, _c26));\n  }\n}\nfunction CalendarWeekViewComponent_div_7_div_3_ng_template_3_Template(rf, ctx) {}\nfunction CalendarWeekViewComponent_div_7_div_3_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mwl-calendar-week-view-event\", 36);\n    i0.ɵɵlistener(\"eventClicked\", function CalendarWeekViewComponent_div_7_div_3_ng_template_4_Template_mwl_calendar_week_view_event_eventClicked_0_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const timeEvent_r38 = i0.ɵɵnextContext().$implicit;\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.eventClicked.emit({\n        event: timeEvent_r38.event,\n        sourceEvent: $event.sourceEvent\n      }));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const timeEvent_r38 = i0.ɵɵnextContext().$implicit;\n    const column_r35 = i0.ɵɵnextContext().$implicit;\n    const ctx_r43 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"locale\", ctx_r43.locale)(\"weekEvent\", timeEvent_r38)(\"tooltipPlacement\", ctx_r43.tooltipPlacement)(\"tooltipTemplate\", ctx_r43.tooltipTemplate)(\"tooltipAppendToBody\", ctx_r43.tooltipAppendToBody)(\"tooltipDisabled\", ctx_r43.dragActive || ctx_r43.timeEventResizes.size > 0)(\"tooltipDelay\", ctx_r43.tooltipDelay)(\"customTemplate\", ctx_r43.eventTemplate)(\"eventTitleTemplate\", ctx_r43.eventTitleTemplate)(\"eventActionsTemplate\", ctx_r43.eventActionsTemplate)(\"column\", column_r35)(\"daysInWeek\", ctx_r43.daysInWeek);\n  }\n}\nconst _c27 = function () {\n  return {\n    right: true,\n    bottom: true\n  };\n};\nfunction CalendarWeekViewComponent_div_7_div_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"resizeEdges\", i0.ɵɵpureFunction0(1, _c27));\n  }\n}\nconst _c28 = function (a0, a1, a2, a3) {\n  return {\n    left: a0,\n    right: a1,\n    top: a2,\n    bottom: a3\n  };\n};\nfunction CalendarWeekViewComponent_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33, 19);\n    i0.ɵɵlistener(\"resizeStart\", function CalendarWeekViewComponent_div_7_div_3_Template_div_resizeStart_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r51);\n      const timeEvent_r38 = restoredCtx.$implicit;\n      const ctx_r50 = i0.ɵɵnextContext(2);\n      const _r2 = i0.ɵɵreference(6);\n      return i0.ɵɵresetView(ctx_r50.timeEventResizeStarted(_r2, timeEvent_r38, $event));\n    })(\"resizing\", function CalendarWeekViewComponent_div_7_div_3_Template_div_resizing_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r51);\n      const timeEvent_r38 = restoredCtx.$implicit;\n      const ctx_r52 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r52.timeEventResizing(timeEvent_r38, $event));\n    })(\"resizeEnd\", function CalendarWeekViewComponent_div_7_div_3_Template_div_resizeEnd_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r51);\n      const timeEvent_r38 = restoredCtx.$implicit;\n      const ctx_r53 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r53.timeEventResizeEnded(timeEvent_r38));\n    })(\"dragStart\", function CalendarWeekViewComponent_div_7_div_3_Template_div_dragStart_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r51);\n      const timeEvent_r38 = restoredCtx.$implicit;\n      const _r39 = i0.ɵɵreference(1);\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      const _r2 = i0.ɵɵreference(6);\n      return i0.ɵɵresetView(ctx_r54.dragStarted(_r2, _r39, timeEvent_r38, true));\n    })(\"dragging\", function CalendarWeekViewComponent_div_7_div_3_Template_div_dragging_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r51);\n      const timeEvent_r38 = restoredCtx.$implicit;\n      const ctx_r55 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r55.dragMove(timeEvent_r38, $event));\n    })(\"dragEnd\", function CalendarWeekViewComponent_div_7_div_3_Template_div_dragEnd_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r51);\n      const timeEvent_r38 = restoredCtx.$implicit;\n      const ctx_r56 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r56.dragEnded(timeEvent_r38, $event, ctx_r56.dayColumnWidth, true));\n    });\n    i0.ɵɵtemplate(2, CalendarWeekViewComponent_div_7_div_3_div_2_Template, 1, 2, \"div\", 20);\n    i0.ɵɵtemplate(3, CalendarWeekViewComponent_div_7_div_3_ng_template_3_Template, 0, 0, \"ng-template\", 34);\n    i0.ɵɵtemplate(4, CalendarWeekViewComponent_div_7_div_3_ng_template_4_Template, 1, 12, \"ng-template\", null, 35, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(6, CalendarWeekViewComponent_div_7_div_3_div_6_Template, 1, 2, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const timeEvent_r38 = ctx.$implicit;\n    const _r42 = i0.ɵɵreference(5);\n    const ctx_r36 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"top\", timeEvent_r38.top, \"px\")(\"height\", timeEvent_r38.height, \"px\")(\"left\", timeEvent_r38.left, \"%\")(\"width\", timeEvent_r38.width, \"%\");\n    i0.ɵɵclassProp(\"cal-draggable\", timeEvent_r38.event.draggable && ctx_r36.timeEventResizes.size === 0)(\"cal-starts-within-day\", !timeEvent_r38.startsBeforeDay)(\"cal-ends-within-day\", !timeEvent_r38.endsAfterDay);\n    i0.ɵɵproperty(\"ngClass\", timeEvent_r38.event.cssClass)(\"hidden\", timeEvent_r38.height === 0 && timeEvent_r38.width === 0)(\"resizeCursors\", ctx_r36.resizeCursors)(\"resizeSnapGrid\", i0.ɵɵpureFunction4(30, _c28, ctx_r36.dayColumnWidth, ctx_r36.dayColumnWidth, ctx_r36.eventSnapSize || ctx_r36.hourSegmentHeight, ctx_r36.eventSnapSize || ctx_r36.hourSegmentHeight))(\"validateResize\", ctx_r36.validateResize)(\"allowNegativeResizes\", true)(\"dropData\", i0.ɵɵpureFunction2(35, _c24, timeEvent_r38.event, ctx_r36.calendarId))(\"dragAxis\", i0.ɵɵpureFunction2(38, _c7, timeEvent_r38.event.draggable && ctx_r36.timeEventResizes.size === 0, timeEvent_r38.event.draggable && ctx_r36.timeEventResizes.size === 0))(\"dragSnapGrid\", ctx_r36.snapDraggedEvents ? i0.ɵɵpureFunction2(41, _c7, ctx_r36.dayColumnWidth, ctx_r36.eventSnapSize || ctx_r36.hourSegmentHeight) : i0.ɵɵpureFunction0(44, _c2))(\"touchStartLongPress\", i0.ɵɵpureFunction0(45, _c8))(\"ghostDragEnabled\", !ctx_r36.snapDraggedEvents)(\"ghostElementTemplate\", _r42)(\"validateDrag\", ctx_r36.validateDrag);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (timeEvent_r38.event == null ? null : timeEvent_r38.event.resizable == null ? null : timeEvent_r38.event.resizable.beforeStart) && !timeEvent_r38.startsBeforeDay);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r42);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (timeEvent_r38.event == null ? null : timeEvent_r38.event.resizable == null ? null : timeEvent_r38.event.resizable.afterEnd) && !timeEvent_r38.endsAfterDay);\n  }\n}\nfunction CalendarWeekViewComponent_div_7_div_4_mwl_calendar_week_view_hour_segment_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mwl-calendar-week-view-hour-segment\", 38);\n    i0.ɵɵlistener(\"mwlClick\", function CalendarWeekViewComponent_div_7_div_4_mwl_calendar_week_view_hour_segment_1_Template_mwl_calendar_week_view_hour_segment_mwlClick_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r62);\n      const segment_r60 = restoredCtx.$implicit;\n      const ctx_r61 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r61.hourSegmentClicked.emit({\n        date: segment_r60.date,\n        sourceEvent: $event\n      }));\n    })(\"drop\", function CalendarWeekViewComponent_div_7_div_4_mwl_calendar_week_view_hour_segment_1_Template_mwl_calendar_week_view_hour_segment_drop_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r62);\n      const segment_r60 = restoredCtx.$implicit;\n      const ctx_r63 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r63.eventDropped($event, segment_r60.date, false));\n    })(\"dragEnter\", function CalendarWeekViewComponent_div_7_div_4_mwl_calendar_week_view_hour_segment_1_Template_mwl_calendar_week_view_hour_segment_dragEnter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r62);\n      const segment_r60 = restoredCtx.$implicit;\n      const ctx_r64 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r64.dateDragEnter(segment_r60.date));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r60 = ctx.$implicit;\n    const ctx_r59 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"height\", ctx_r59.hourSegmentHeight, \"px\");\n    i0.ɵɵproperty(\"segment\", segment_r60)(\"segmentHeight\", ctx_r59.hourSegmentHeight)(\"locale\", ctx_r59.locale)(\"customTemplate\", ctx_r59.hourSegmentTemplate)(\"daysInWeek\", ctx_r59.daysInWeek)(\"clickListenerDisabled\", ctx_r59.hourSegmentClicked.observers.length === 0)(\"dragOverClass\", !ctx_r59.dragActive || !ctx_r59.snapDraggedEvents ? \"cal-drag-over\" : null)(\"isTimeLabel\", ctx_r59.daysInWeek === 1);\n  }\n}\nfunction CalendarWeekViewComponent_div_7_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtemplate(1, CalendarWeekViewComponent_div_7_div_4_mwl_calendar_week_view_hour_segment_1_Template, 1, 10, \"mwl-calendar-week-view-hour-segment\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hour_r57 = ctx.$implicit;\n    const odd_r58 = ctx.odd;\n    const ctx_r37 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"cal-hour-odd\", odd_r58);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", hour_r57.segments)(\"ngForTrackBy\", ctx_r37.trackByHourSegment);\n  }\n}\nfunction CalendarWeekViewComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"mwl-calendar-week-view-current-time-marker\", 30);\n    i0.ɵɵelementStart(2, \"div\", 31);\n    i0.ɵɵtemplate(3, CalendarWeekViewComponent_div_7_div_3_Template, 7, 46, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CalendarWeekViewComponent_div_7_div_4_Template, 2, 4, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const column_r35 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"columnDate\", column_r35.date)(\"dayStartHour\", ctx_r3.dayStartHour)(\"dayStartMinute\", ctx_r3.dayStartMinute)(\"dayEndHour\", ctx_r3.dayEndHour)(\"dayEndMinute\", ctx_r3.dayEndMinute)(\"hourSegments\", ctx_r3.hourSegments)(\"hourDuration\", ctx_r3.hourDuration)(\"hourSegmentHeight\", ctx_r3.hourSegmentHeight)(\"customTemplate\", ctx_r3.currentTimeMarkerTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", column_r35.events)(\"ngForTrackBy\", ctx_r3.trackByWeekTimeEvent);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", column_r35.hours)(\"ngForTrackBy\", ctx_r3.trackByHour);\n  }\n}\nlet ClickDirective = /*#__PURE__*/(() => {\n  class ClickDirective {\n    constructor(renderer, elm, document) {\n      this.renderer = renderer;\n      this.elm = elm;\n      this.document = document;\n      this.clickListenerDisabled = false;\n      this.click = new EventEmitter(); // eslint-disable-line\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      if (!this.clickListenerDisabled) {\n        this.listen().pipe(takeUntil(this.destroy$)).subscribe(event => {\n          event.stopPropagation();\n          this.click.emit(event);\n        });\n      }\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n    }\n    listen() {\n      return new Observable(observer => {\n        return this.renderer.listen(this.elm.nativeElement, 'click', event => {\n          observer.next(event);\n        });\n      });\n    }\n  }\n  ClickDirective.ɵfac = function ClickDirective_Factory(t) {\n    return new (t || ClickDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT));\n  };\n  ClickDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ClickDirective,\n    selectors: [[\"\", \"mwlClick\", \"\"]],\n    inputs: {\n      clickListenerDisabled: \"clickListenerDisabled\"\n    },\n    outputs: {\n      click: \"mwlClick\"\n    }\n  });\n  return ClickDirective;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet KeydownEnterDirective = /*#__PURE__*/(() => {\n  class KeydownEnterDirective {\n    constructor(host, ngZone, renderer) {\n      this.host = host;\n      this.ngZone = ngZone;\n      this.renderer = renderer;\n      this.keydown = new EventEmitter(); // eslint-disable-line\n      this.keydownListener = null;\n    }\n    ngOnInit() {\n      this.ngZone.runOutsideAngular(() => {\n        this.keydownListener = this.renderer.listen(this.host.nativeElement, 'keydown', event => {\n          if (event.keyCode === 13 || event.which === 13 || event.key === 'Enter') {\n            event.preventDefault();\n            event.stopPropagation();\n            this.ngZone.run(() => {\n              this.keydown.emit(event);\n            });\n          }\n        });\n      });\n    }\n    ngOnDestroy() {\n      if (this.keydownListener !== null) {\n        this.keydownListener();\n        this.keydownListener = null;\n      }\n    }\n  }\n  KeydownEnterDirective.ɵfac = function KeydownEnterDirective_Factory(t) {\n    return new (t || KeydownEnterDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  KeydownEnterDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: KeydownEnterDirective,\n    selectors: [[\"\", \"mwlKeydownEnter\", \"\"]],\n    outputs: {\n      keydown: \"mwlKeydownEnter\"\n    }\n  });\n  return KeydownEnterDirective;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * This class is responsible for adding accessibility to the calendar.\n * You may override any of its methods via angulars DI to suit your requirements.\n * For example:\n *\n * ```typescript\n * import { A11yParams, CalendarA11y } from 'angular-calendar';\n * import { formatDate, I18nPluralPipe } from '@angular/common';\n * import { Injectable } from '@angular/core';\n *\n * // adding your own a11y params\n * export interface CustomA11yParams extends A11yParams {\n *   isDrSuess?: boolean;\n * }\n *\n * @Injectable()\n * export class CustomCalendarA11y extends CalendarA11y {\n *   constructor(protected i18nPlural: I18nPluralPipe) {\n *     super(i18nPlural);\n *   }\n *\n *   // overriding a function\n *   public openDayEventsLandmark({ date, locale, isDrSuess }: CustomA11yParams): string {\n *     if (isDrSuess) {\n *       return `\n *         ${formatDate(date, 'EEEE MMMM d', locale)}\n *          Today you are you! That is truer than true! There is no one alive\n *          who is you-er than you!\n *       `;\n *     }\n *   }\n * }\n *\n * // in your component that uses the calendar\n * providers: [{\n *  provide: CalendarA11y,\n *  useClass: CustomCalendarA11y\n * }]\n * ```\n */\nlet CalendarA11y = /*#__PURE__*/(() => {\n  class CalendarA11y {\n    constructor(i18nPlural) {\n      this.i18nPlural = i18nPlural;\n    }\n    /**\n     * Aria label for the badges/date of a cell\n     * @example: `Saturday October 19 1 event click to expand`\n     */\n    monthCell({\n      day,\n      locale\n    }) {\n      if (day.badgeTotal > 0) {\n        return `\n        ${formatDate(day.date, 'EEEE MMMM d', locale)},\n        ${this.i18nPlural.transform(day.badgeTotal, {\n          '=0': 'No events',\n          '=1': 'One event',\n          other: '# events'\n        })},\n         click to expand\n      `;\n      } else {\n        return `${formatDate(day.date, 'EEEE MMMM d', locale)}`;\n      }\n    }\n    /**\n     * Aria label for the open day events start landmark\n     * @example: `Saturday October 19 expanded view`\n     */\n    openDayEventsLandmark({\n      date,\n      locale\n    }) {\n      return `\n      Beginning of expanded view for ${formatDate(date, 'EEEE MMMM dd', locale)}\n    `;\n    }\n    /**\n     * Aria label for alert that a day in the month view was expanded\n     * @example: `Saturday October 19 expanded`\n     */\n    openDayEventsAlert({\n      date,\n      locale\n    }) {\n      return `${formatDate(date, 'EEEE MMMM dd', locale)} expanded`;\n    }\n    /**\n     * Descriptive aria label for an event\n     * @example: `Saturday October 19th, Scott's Pizza Party, from 11:00am to 5:00pm`\n     */\n    eventDescription({\n      event,\n      locale\n    }) {\n      if (event.allDay === true) {\n        return this.allDayEventDescription({\n          event,\n          locale\n        });\n      }\n      const aria = `\n      ${formatDate(event.start, 'EEEE MMMM dd', locale)},\n      ${event.title}, from ${formatDate(event.start, 'hh:mm a', locale)}\n    `;\n      if (event.end) {\n        return aria + ` to ${formatDate(event.end, 'hh:mm a', locale)}`;\n      }\n      return aria;\n    }\n    /**\n     * Descriptive aria label for an all day event\n     * @example:\n     * `Scott's Party, event spans multiple days: start time October 19 5:00pm, no stop time`\n     */\n    allDayEventDescription({\n      event,\n      locale\n    }) {\n      const aria = `\n      ${event.title}, event spans multiple days:\n      start time ${formatDate(event.start, 'MMMM dd hh:mm a', locale)}\n    `;\n      if (event.end) {\n        return aria + `, stop time ${formatDate(event.end, 'MMMM d hh:mm a', locale)}`;\n      }\n      return aria + `, no stop time`;\n    }\n    /**\n     * Aria label for the calendar event actions icons\n     * @returns 'Edit' for fa-pencil icons, and 'Delete' for fa-times icons\n     */\n    actionButtonLabel({\n      action\n    }) {\n      return action.a11yLabel;\n    }\n    /**\n     * @returns {number} Tab index to be given to month cells\n     */\n    monthCellTabIndex() {\n      return 0;\n    }\n    /**\n     * @returns true if the events inside the month cell should be aria-hidden\n     */\n    hideMonthCellEvents() {\n      return true;\n    }\n    /**\n     * @returns true if event titles should be aria-hidden (global)\n     */\n    hideEventTitle() {\n      return true;\n    }\n    /**\n     * @returns true if hour segments in the week view should be aria-hidden\n     */\n    hideWeekHourSegment() {\n      return true;\n    }\n    /**\n     * @returns true if hour segments in the day view should be aria-hidden\n     */\n    hideDayHourSegment() {\n      return true;\n    }\n  }\n  CalendarA11y.ɵfac = function CalendarA11y_Factory(t) {\n    return new (t || CalendarA11y)(i0.ɵɵinject(i1.I18nPluralPipe));\n  };\n  CalendarA11y.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: CalendarA11y,\n    factory: CalendarA11y.ɵfac\n  });\n  return CalendarA11y;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * This pipe is primarily for rendering aria labels. Example usage:\n * ```typescript\n * // where `myEvent` is a `CalendarEvent` and myLocale is a locale identifier\n * {{ { event: myEvent, locale: myLocale } | calendarA11y: 'eventDescription' }}\n * ```\n */\nlet CalendarA11yPipe = /*#__PURE__*/(() => {\n  class CalendarA11yPipe {\n    constructor(calendarA11y, locale) {\n      this.calendarA11y = calendarA11y;\n      this.locale = locale;\n    }\n    transform(a11yParams, method) {\n      a11yParams.locale = a11yParams.locale || this.locale;\n      if (typeof this.calendarA11y[method] === 'undefined') {\n        const allowedMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(CalendarA11y.prototype)).filter(iMethod => iMethod !== 'constructor');\n        throw new Error(`${method} is not a valid a11y method. Can only be one of ${allowedMethods.join(', ')}`);\n      }\n      return this.calendarA11y[method](a11yParams);\n    }\n  }\n  CalendarA11yPipe.ɵfac = function CalendarA11yPipe_Factory(t) {\n    return new (t || CalendarA11yPipe)(i0.ɵɵdirectiveInject(CalendarA11y, 16), i0.ɵɵdirectiveInject(LOCALE_ID, 16));\n  };\n  CalendarA11yPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"calendarA11y\",\n    type: CalendarA11yPipe,\n    pure: true\n  });\n  return CalendarA11yPipe;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CalendarEventActionsComponent = /*#__PURE__*/(() => {\n  class CalendarEventActionsComponent {\n    constructor() {\n      this.trackByActionId = (index, action) => action.id ? action.id : action;\n    }\n  }\n  CalendarEventActionsComponent.ɵfac = function CalendarEventActionsComponent_Factory(t) {\n    return new (t || CalendarEventActionsComponent)();\n  };\n  CalendarEventActionsComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CalendarEventActionsComponent,\n    selectors: [[\"mwl-calendar-event-actions\"]],\n    inputs: {\n      event: \"event\",\n      customTemplate: \"customTemplate\"\n    },\n    decls: 3,\n    vars: 5,\n    consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"cal-event-actions\", 4, \"ngIf\"], [1, \"cal-event-actions\"], [\"class\", \"cal-event-action\", \"href\", \"javascript:;\", \"tabindex\", \"0\", \"role\", \"button\", 3, \"ngClass\", \"innerHtml\", \"mwlClick\", \"mwlKeydownEnter\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"href\", \"javascript:;\", \"tabindex\", \"0\", \"role\", \"button\", 1, \"cal-event-action\", 3, \"ngClass\", \"innerHtml\", \"mwlClick\", \"mwlKeydownEnter\"]],\n    template: function CalendarEventActionsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CalendarEventActionsComponent_ng_template_0_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(2, CalendarEventActionsComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || _r0)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c1, ctx.event, ctx.trackByActionId));\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, ClickDirective, KeydownEnterDirective, CalendarA11yPipe],\n    encapsulation: 2\n  });\n  return CalendarEventActionsComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * This class is responsible for displaying all event titles within the calendar. You may override any of its methods via angulars DI to suit your requirements. For example:\n *\n * ```typescript\n * import { Injectable } from '@angular/core';\n * import { CalendarEventTitleFormatter, CalendarEvent } from 'angular-calendar';\n *\n * @Injectable()\n * class CustomEventTitleFormatter extends CalendarEventTitleFormatter {\n *\n *   month(event: CalendarEvent): string {\n *     return `Custom prefix: ${event.title}`;\n *   }\n *\n * }\n *\n * // in your component\n * providers: [{\n *  provide: CalendarEventTitleFormatter,\n *  useClass: CustomEventTitleFormatter\n * }]\n * ```\n */\nclass CalendarEventTitleFormatter {\n  /**\n   * The month view event title.\n   */\n  month(event, title) {\n    return event.title;\n  }\n  /**\n   * The month view event tooltip. Return a falsey value from this to disable the tooltip.\n   */\n  monthTooltip(event, title) {\n    return event.title;\n  }\n  /**\n   * The week view event title.\n   */\n  week(event, title) {\n    return event.title;\n  }\n  /**\n   * The week view event tooltip. Return a falsey value from this to disable the tooltip.\n   */\n  weekTooltip(event, title) {\n    return event.title;\n  }\n  /**\n   * The day view event title.\n   */\n  day(event, title) {\n    return event.title;\n  }\n  /**\n   * The day view event tooltip. Return a falsey value from this to disable the tooltip.\n   */\n  dayTooltip(event, title) {\n    return event.title;\n  }\n}\nlet CalendarEventTitlePipe = /*#__PURE__*/(() => {\n  class CalendarEventTitlePipe {\n    constructor(calendarEventTitle) {\n      this.calendarEventTitle = calendarEventTitle;\n    }\n    transform(title, titleType, event) {\n      return this.calendarEventTitle[titleType](event, title);\n    }\n  }\n  CalendarEventTitlePipe.ɵfac = function CalendarEventTitlePipe_Factory(t) {\n    return new (t || CalendarEventTitlePipe)(i0.ɵɵdirectiveInject(CalendarEventTitleFormatter, 16));\n  };\n  CalendarEventTitlePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"calendarEventTitle\",\n    type: CalendarEventTitlePipe,\n    pure: true\n  });\n  return CalendarEventTitlePipe;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CalendarEventTitleComponent = /*#__PURE__*/(() => {\n  class CalendarEventTitleComponent {}\n  CalendarEventTitleComponent.ɵfac = function CalendarEventTitleComponent_Factory(t) {\n    return new (t || CalendarEventTitleComponent)();\n  };\n  CalendarEventTitleComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CalendarEventTitleComponent,\n    selectors: [[\"mwl-calendar-event-title\"]],\n    inputs: {\n      event: \"event\",\n      customTemplate: \"customTemplate\",\n      view: \"view\"\n    },\n    decls: 3,\n    vars: 5,\n    consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"cal-event-title\", 3, \"innerHTML\"]],\n    template: function CalendarEventTitleComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CalendarEventTitleComponent_ng_template_0_Template, 3, 10, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(2, CalendarEventTitleComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || _r0)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c3, ctx.event, ctx.view));\n      }\n    },\n    dependencies: [i1.NgTemplateOutlet, CalendarEventTitlePipe, CalendarA11yPipe],\n    encapsulation: 2\n  });\n  return CalendarEventTitleComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CalendarTooltipWindowComponent = /*#__PURE__*/(() => {\n  class CalendarTooltipWindowComponent {}\n  CalendarTooltipWindowComponent.ɵfac = function CalendarTooltipWindowComponent_Factory(t) {\n    return new (t || CalendarTooltipWindowComponent)();\n  };\n  CalendarTooltipWindowComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CalendarTooltipWindowComponent,\n    selectors: [[\"mwl-calendar-tooltip-window\"]],\n    inputs: {\n      contents: \"contents\",\n      placement: \"placement\",\n      event: \"event\",\n      customTemplate: \"customTemplate\"\n    },\n    decls: 3,\n    vars: 6,\n    consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"cal-tooltip\", 3, \"ngClass\"], [1, \"cal-tooltip-arrow\"], [1, \"cal-tooltip-inner\", 3, \"innerHtml\"]],\n    template: function CalendarTooltipWindowComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CalendarTooltipWindowComponent_ng_template_0_Template, 3, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(2, CalendarTooltipWindowComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || _r0)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(2, _c4, ctx.contents, ctx.placement, ctx.event));\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgTemplateOutlet],\n    encapsulation: 2\n  });\n  return CalendarTooltipWindowComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CalendarTooltipDirective = /*#__PURE__*/(() => {\n  class CalendarTooltipDirective {\n    constructor(elementRef, injector, renderer, componentFactoryResolver, viewContainerRef, document // eslint-disable-line\n    ) {\n      this.elementRef = elementRef;\n      this.injector = injector;\n      this.renderer = renderer;\n      this.viewContainerRef = viewContainerRef;\n      this.document = document;\n      this.placement = 'auto'; // eslint-disable-line  @angular-eslint/no-input-rename\n      this.delay = null; // eslint-disable-line  @angular-eslint/no-input-rename\n      this.cancelTooltipDelay$ = new Subject();\n      this.tooltipFactory = componentFactoryResolver.resolveComponentFactory(CalendarTooltipWindowComponent);\n    }\n    ngOnChanges(changes) {\n      if (this.tooltipRef && (changes.contents || changes.customTemplate || changes.event)) {\n        this.tooltipRef.instance.contents = this.contents;\n        this.tooltipRef.instance.customTemplate = this.customTemplate;\n        this.tooltipRef.instance.event = this.event;\n        this.tooltipRef.changeDetectorRef.markForCheck();\n        if (!this.contents) {\n          this.hide();\n        }\n      }\n    }\n    ngOnDestroy() {\n      this.hide();\n    }\n    onMouseOver() {\n      const delay$ = this.delay === null ? of('now') : timer(this.delay);\n      delay$.pipe(takeUntil(this.cancelTooltipDelay$)).subscribe(() => {\n        this.show();\n      });\n    }\n    onMouseOut() {\n      this.hide();\n    }\n    show() {\n      if (!this.tooltipRef && this.contents) {\n        this.tooltipRef = this.viewContainerRef.createComponent(this.tooltipFactory, 0, this.injector, []);\n        this.tooltipRef.instance.contents = this.contents;\n        this.tooltipRef.instance.customTemplate = this.customTemplate;\n        this.tooltipRef.instance.event = this.event;\n        if (this.appendToBody) {\n          this.document.body.appendChild(this.tooltipRef.location.nativeElement);\n        }\n        requestAnimationFrame(() => {\n          this.positionTooltip();\n        });\n      }\n    }\n    hide() {\n      if (this.tooltipRef) {\n        this.viewContainerRef.remove(this.viewContainerRef.indexOf(this.tooltipRef.hostView));\n        this.tooltipRef = null;\n      }\n      this.cancelTooltipDelay$.next();\n    }\n    positionTooltip(previousPositions = []) {\n      if (this.tooltipRef) {\n        this.tooltipRef.changeDetectorRef.detectChanges();\n        this.tooltipRef.instance.placement = positionElements(this.elementRef.nativeElement, this.tooltipRef.location.nativeElement.children[0], this.placement, this.appendToBody);\n        // keep re-positioning the tooltip until the arrow position doesn't make a difference\n        if (previousPositions.indexOf(this.tooltipRef.instance.placement) === -1) {\n          this.positionTooltip([...previousPositions, this.tooltipRef.instance.placement]);\n        }\n      }\n    }\n  }\n  CalendarTooltipDirective.ɵfac = function CalendarTooltipDirective_Factory(t) {\n    return new (t || CalendarTooltipDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT));\n  };\n  CalendarTooltipDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CalendarTooltipDirective,\n    selectors: [[\"\", \"mwlCalendarTooltip\", \"\"]],\n    hostBindings: function CalendarTooltipDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"mouseenter\", function CalendarTooltipDirective_mouseenter_HostBindingHandler() {\n          return ctx.onMouseOver();\n        })(\"mouseleave\", function CalendarTooltipDirective_mouseleave_HostBindingHandler() {\n          return ctx.onMouseOut();\n        });\n      }\n    },\n    inputs: {\n      contents: [\"mwlCalendarTooltip\", \"contents\"],\n      placement: [\"tooltipPlacement\", \"placement\"],\n      customTemplate: [\"tooltipTemplate\", \"customTemplate\"],\n      event: [\"tooltipEvent\", \"event\"],\n      appendToBody: [\"tooltipAppendToBody\", \"appendToBody\"],\n      delay: [\"tooltipDelay\", \"delay\"]\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n  return CalendarTooltipDirective;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nvar CalendarView = /*#__PURE__*/function (CalendarView) {\n  CalendarView[\"Month\"] = \"month\";\n  CalendarView[\"Week\"] = \"week\";\n  CalendarView[\"Day\"] = \"day\";\n  return CalendarView;\n}(CalendarView || {});\nconst validateEvents = events => {\n  const warn = (...args) => console.warn('angular-calendar', ...args);\n  return validateEvents$1(events, warn);\n};\nfunction isInsideLeftAndRight(outer, inner) {\n  return Math.floor(outer.left) <= Math.ceil(inner.left) && Math.floor(inner.left) <= Math.ceil(outer.right) && Math.floor(outer.left) <= Math.ceil(inner.right) && Math.floor(inner.right) <= Math.ceil(outer.right);\n}\nfunction isInsideTopAndBottom(outer, inner) {\n  return Math.floor(outer.top) <= Math.ceil(inner.top) && Math.floor(inner.top) <= Math.ceil(outer.bottom) && Math.floor(outer.top) <= Math.ceil(inner.bottom) && Math.floor(inner.bottom) <= Math.ceil(outer.bottom);\n}\nfunction isInside(outer, inner) {\n  return isInsideLeftAndRight(outer, inner) && isInsideTopAndBottom(outer, inner);\n}\nfunction roundToNearest(amount, precision) {\n  return Math.round(amount / precision) * precision;\n}\nconst trackByEventId = (index, event) => event.id ? event.id : event;\nconst trackByWeekDayHeaderDate = (index, day) => day.date.toISOString();\nconst trackByHourSegment = (index, segment) => segment.date.toISOString();\nconst trackByHour = (index, hour) => hour.segments[0].date.toISOString();\nconst trackByWeekAllDayEvent = (index, weekEvent) => weekEvent.event.id ? weekEvent.event.id : weekEvent.event;\nconst trackByWeekTimeEvent = (index, weekEvent) => weekEvent.event.id ? weekEvent.event.id : weekEvent.event;\nconst MINUTES_IN_HOUR = 60;\nfunction getPixelAmountInMinutes(hourSegments, hourSegmentHeight, hourDuration) {\n  return (hourDuration || MINUTES_IN_HOUR) / (hourSegments * hourSegmentHeight);\n}\nfunction getMinutesMoved(movedY, hourSegments, hourSegmentHeight, eventSnapSize, hourDuration) {\n  const draggedInPixelsSnapSize = roundToNearest(movedY, eventSnapSize || hourSegmentHeight);\n  const pixelAmountInMinutes = getPixelAmountInMinutes(hourSegments, hourSegmentHeight, hourDuration);\n  return draggedInPixelsSnapSize * pixelAmountInMinutes;\n}\nfunction getDefaultEventEnd(dateAdapter, event, minimumMinutes) {\n  if (event.end) {\n    return event.end;\n  } else {\n    return dateAdapter.addMinutes(event.start, minimumMinutes);\n  }\n}\nfunction addDaysWithExclusions(dateAdapter, date, days, excluded) {\n  let daysCounter = 0;\n  let daysToAdd = 0;\n  const changeDays = days < 0 ? dateAdapter.subDays : dateAdapter.addDays;\n  let result = date;\n  while (daysToAdd <= Math.abs(days)) {\n    result = changeDays(date, daysCounter);\n    const day = dateAdapter.getDay(result);\n    if (excluded.indexOf(day) === -1) {\n      daysToAdd++;\n    }\n    daysCounter++;\n  }\n  return result;\n}\nfunction isDraggedWithinPeriod(newStart, newEnd, period) {\n  const end = newEnd || newStart;\n  return period.start <= newStart && newStart <= period.end || period.start <= end && end <= period.end;\n}\nfunction shouldFireDroppedEvent(dropEvent, date, allDay, calendarId) {\n  return dropEvent.dropData && dropEvent.dropData.event && (dropEvent.dropData.calendarId !== calendarId || dropEvent.dropData.event.allDay && !allDay || !dropEvent.dropData.event.allDay && allDay);\n}\nfunction getWeekViewPeriod(dateAdapter, viewDate, weekStartsOn, excluded = [], daysInWeek) {\n  let viewStart = daysInWeek ? dateAdapter.startOfDay(viewDate) : dateAdapter.startOfWeek(viewDate, {\n    weekStartsOn\n  });\n  const endOfWeek = dateAdapter.endOfWeek(viewDate, {\n    weekStartsOn\n  });\n  while (excluded.indexOf(dateAdapter.getDay(viewStart)) > -1 && viewStart < endOfWeek) {\n    viewStart = dateAdapter.addDays(viewStart, 1);\n  }\n  if (daysInWeek) {\n    const viewEnd = dateAdapter.endOfDay(addDaysWithExclusions(dateAdapter, viewStart, daysInWeek - 1, excluded));\n    return {\n      viewStart,\n      viewEnd\n    };\n  } else {\n    let viewEnd = endOfWeek;\n    while (excluded.indexOf(dateAdapter.getDay(viewEnd)) > -1 && viewEnd > viewStart) {\n      viewEnd = dateAdapter.subDays(viewEnd, 1);\n    }\n    return {\n      viewStart,\n      viewEnd\n    };\n  }\n}\nfunction isWithinThreshold({\n  x,\n  y\n}) {\n  const DRAG_THRESHOLD = 1;\n  return Math.abs(x) > DRAG_THRESHOLD || Math.abs(y) > DRAG_THRESHOLD;\n}\nclass DateAdapter {}\n\n/**\n * Change the view date to the previous view. For example:\n *\n * ```typescript\n * <button\n *  mwlCalendarPreviousView\n *  [(viewDate)]=\"viewDate\"\n *  [view]=\"view\">\n *  Previous\n * </button>\n * ```\n */\nlet CalendarPreviousViewDirective = /*#__PURE__*/(() => {\n  class CalendarPreviousViewDirective {\n    constructor(dateAdapter) {\n      this.dateAdapter = dateAdapter;\n      /**\n       * Days to skip when going back by 1 day\n       */\n      this.excludeDays = [];\n      /**\n       * Called when the view date is changed\n       */\n      this.viewDateChange = new EventEmitter();\n    }\n    /**\n     * @hidden\n     */\n    onClick() {\n      const subFn = {\n        day: this.dateAdapter.subDays,\n        week: this.dateAdapter.subWeeks,\n        month: this.dateAdapter.subMonths\n      }[this.view];\n      if (this.view === CalendarView.Day) {\n        this.viewDateChange.emit(addDaysWithExclusions(this.dateAdapter, this.viewDate, -1, this.excludeDays));\n      } else if (this.view === CalendarView.Week && this.daysInWeek) {\n        this.viewDateChange.emit(addDaysWithExclusions(this.dateAdapter, this.viewDate, -this.daysInWeek, this.excludeDays));\n      } else {\n        this.viewDateChange.emit(subFn(this.viewDate, 1));\n      }\n    }\n  }\n  CalendarPreviousViewDirective.ɵfac = function CalendarPreviousViewDirective_Factory(t) {\n    return new (t || CalendarPreviousViewDirective)(i0.ɵɵdirectiveInject(DateAdapter));\n  };\n  CalendarPreviousViewDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CalendarPreviousViewDirective,\n    selectors: [[\"\", \"mwlCalendarPreviousView\", \"\"]],\n    hostBindings: function CalendarPreviousViewDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function CalendarPreviousViewDirective_click_HostBindingHandler() {\n          return ctx.onClick();\n        });\n      }\n    },\n    inputs: {\n      view: \"view\",\n      viewDate: \"viewDate\",\n      excludeDays: \"excludeDays\",\n      daysInWeek: \"daysInWeek\"\n    },\n    outputs: {\n      viewDateChange: \"viewDateChange\"\n    }\n  });\n  return CalendarPreviousViewDirective;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Change the view date to the next view. For example:\n *\n * ```typescript\n * <button\n *  mwlCalendarNextView\n *  [(viewDate)]=\"viewDate\"\n *  [view]=\"view\">\n *  Next\n * </button>\n * ```\n */\nlet CalendarNextViewDirective = /*#__PURE__*/(() => {\n  class CalendarNextViewDirective {\n    constructor(dateAdapter) {\n      this.dateAdapter = dateAdapter;\n      /**\n       * Days to skip when going forward by 1 day\n       */\n      this.excludeDays = [];\n      /**\n       * Called when the view date is changed\n       */\n      this.viewDateChange = new EventEmitter();\n    }\n    /**\n     * @hidden\n     */\n    onClick() {\n      const addFn = {\n        day: this.dateAdapter.addDays,\n        week: this.dateAdapter.addWeeks,\n        month: this.dateAdapter.addMonths\n      }[this.view];\n      if (this.view === CalendarView.Day) {\n        this.viewDateChange.emit(addDaysWithExclusions(this.dateAdapter, this.viewDate, 1, this.excludeDays));\n      } else if (this.view === CalendarView.Week && this.daysInWeek) {\n        this.viewDateChange.emit(addDaysWithExclusions(this.dateAdapter, this.viewDate, this.daysInWeek, this.excludeDays));\n      } else {\n        this.viewDateChange.emit(addFn(this.viewDate, 1));\n      }\n    }\n  }\n  CalendarNextViewDirective.ɵfac = function CalendarNextViewDirective_Factory(t) {\n    return new (t || CalendarNextViewDirective)(i0.ɵɵdirectiveInject(DateAdapter));\n  };\n  CalendarNextViewDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CalendarNextViewDirective,\n    selectors: [[\"\", \"mwlCalendarNextView\", \"\"]],\n    hostBindings: function CalendarNextViewDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function CalendarNextViewDirective_click_HostBindingHandler() {\n          return ctx.onClick();\n        });\n      }\n    },\n    inputs: {\n      view: \"view\",\n      viewDate: \"viewDate\",\n      excludeDays: \"excludeDays\",\n      daysInWeek: \"daysInWeek\"\n    },\n    outputs: {\n      viewDateChange: \"viewDateChange\"\n    }\n  });\n  return CalendarNextViewDirective;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Change the view date to the current day. For example:\n *\n * ```typescript\n * <button\n *  mwlCalendarToday\n *  [(viewDate)]=\"viewDate\">\n *  Today\n * </button>\n * ```\n */\nlet CalendarTodayDirective = /*#__PURE__*/(() => {\n  class CalendarTodayDirective {\n    constructor(dateAdapter) {\n      this.dateAdapter = dateAdapter;\n      /**\n       * Called when the view date is changed\n       */\n      this.viewDateChange = new EventEmitter();\n    }\n    /**\n     * @hidden\n     */\n    onClick() {\n      this.viewDateChange.emit(this.dateAdapter.startOfDay(new Date()));\n    }\n  }\n  CalendarTodayDirective.ɵfac = function CalendarTodayDirective_Factory(t) {\n    return new (t || CalendarTodayDirective)(i0.ɵɵdirectiveInject(DateAdapter));\n  };\n  CalendarTodayDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CalendarTodayDirective,\n    selectors: [[\"\", \"mwlCalendarToday\", \"\"]],\n    hostBindings: function CalendarTodayDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function CalendarTodayDirective_click_HostBindingHandler() {\n          return ctx.onClick();\n        });\n      }\n    },\n    inputs: {\n      viewDate: \"viewDate\"\n    },\n    outputs: {\n      viewDateChange: \"viewDateChange\"\n    }\n  });\n  return CalendarTodayDirective;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * This will use the angular date pipe to do all date formatting. It is the default date formatter used by the calendar.\n */\nlet CalendarAngularDateFormatter = /*#__PURE__*/(() => {\n  class CalendarAngularDateFormatter {\n    constructor(dateAdapter) {\n      this.dateAdapter = dateAdapter;\n    }\n    /**\n     * The month view header week day labels\n     */\n    monthViewColumnHeader({\n      date,\n      locale\n    }) {\n      return formatDate(date, 'EEEE', locale);\n    }\n    /**\n     * The month view cell day number\n     */\n    monthViewDayNumber({\n      date,\n      locale\n    }) {\n      return formatDate(date, 'd', locale);\n    }\n    /**\n     * The month view title\n     */\n    monthViewTitle({\n      date,\n      locale\n    }) {\n      return formatDate(date, 'LLLL y', locale);\n    }\n    /**\n     * The week view header week day labels\n     */\n    weekViewColumnHeader({\n      date,\n      locale\n    }) {\n      return formatDate(date, 'EEEE', locale);\n    }\n    /**\n     * The week view sub header day and month labels\n     */\n    weekViewColumnSubHeader({\n      date,\n      locale\n    }) {\n      return formatDate(date, 'MMM d', locale);\n    }\n    /**\n     * The week view title\n     */\n    weekViewTitle({\n      date,\n      locale,\n      weekStartsOn,\n      excludeDays,\n      daysInWeek\n    }) {\n      const {\n        viewStart,\n        viewEnd\n      } = getWeekViewPeriod(this.dateAdapter, date, weekStartsOn, excludeDays, daysInWeek);\n      const format = (dateToFormat, showYear) => formatDate(dateToFormat, 'MMM d' + (showYear ? ', yyyy' : ''), locale);\n      return `${format(viewStart, viewStart.getUTCFullYear() !== viewEnd.getUTCFullYear())} - ${format(viewEnd, true)}`;\n    }\n    /**\n     * The time formatting down the left hand side of the week view\n     */\n    weekViewHour({\n      date,\n      locale\n    }) {\n      return formatDate(date, 'h a', locale);\n    }\n    /**\n     * The time formatting down the left hand side of the day view\n     */\n    dayViewHour({\n      date,\n      locale\n    }) {\n      return formatDate(date, 'h a', locale);\n    }\n    /**\n     * The day view title\n     */\n    dayViewTitle({\n      date,\n      locale\n    }) {\n      return formatDate(date, 'EEEE, MMMM d, y', locale);\n    }\n  }\n  CalendarAngularDateFormatter.ɵfac = function CalendarAngularDateFormatter_Factory(t) {\n    return new (t || CalendarAngularDateFormatter)(i0.ɵɵinject(DateAdapter));\n  };\n  CalendarAngularDateFormatter.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: CalendarAngularDateFormatter,\n    factory: CalendarAngularDateFormatter.ɵfac\n  });\n  return CalendarAngularDateFormatter;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * This class is responsible for all formatting of dates. There are 3 implementations available, the `CalendarAngularDateFormatter` (default) which uses the angular date pipe to format dates, the `CalendarNativeDateFormatter` which will use the <a href=\"https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Intl\" target=\"_blank\">Intl</a> API to format dates, or there is the `CalendarMomentDateFormatter` which uses <a href=\"http://momentjs.com/\" target=\"_blank\">moment</a>.\n *\n * If you wish, you may override any of the defaults via angulars DI. For example:\n *\n * ```typescript\n * import { CalendarDateFormatter, DateFormatterParams } from 'angular-calendar';\n * import { formatDate } from '@angular/common';\n * import { Injectable } from '@angular/core';\n *\n * @Injectable()\n * class CustomDateFormatter extends CalendarDateFormatter {\n *\n *   public monthViewColumnHeader({date, locale}: DateFormatterParams): string {\n *     return formatDate(date, 'EEE', locale); // use short week days\n *   }\n *\n * }\n *\n * // in your component that uses the calendar\n * providers: [{\n *   provide: CalendarDateFormatter,\n *   useClass: CustomDateFormatter\n * }]\n * ```\n */\nlet CalendarDateFormatter = /*#__PURE__*/(() => {\n  class CalendarDateFormatter extends CalendarAngularDateFormatter {}\n  CalendarDateFormatter.ɵfac = /* @__PURE__ */function () {\n    let ɵCalendarDateFormatter_BaseFactory;\n    return function CalendarDateFormatter_Factory(t) {\n      return (ɵCalendarDateFormatter_BaseFactory || (ɵCalendarDateFormatter_BaseFactory = i0.ɵɵgetInheritedFactory(CalendarDateFormatter)))(t || CalendarDateFormatter);\n    };\n  }();\n  CalendarDateFormatter.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: CalendarDateFormatter,\n    factory: CalendarDateFormatter.ɵfac\n  });\n  return CalendarDateFormatter;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * This pipe is primarily for rendering the current view title. Example usage:\n * ```typescript\n * // where `viewDate` is a `Date` and view is `'month' | 'week' | 'day'`\n * {{ viewDate | calendarDate:(view + 'ViewTitle'):'en' }}\n * ```\n */\nlet CalendarDatePipe = /*#__PURE__*/(() => {\n  class CalendarDatePipe {\n    constructor(dateFormatter, locale) {\n      this.dateFormatter = dateFormatter;\n      this.locale = locale;\n    }\n    transform(date, method, locale = this.locale, weekStartsOn = 0, excludeDays = [], daysInWeek) {\n      if (typeof this.dateFormatter[method] === 'undefined') {\n        const allowedMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(CalendarDateFormatter.prototype)).filter(iMethod => iMethod !== 'constructor');\n        throw new Error(`${method} is not a valid date formatter. Can only be one of ${allowedMethods.join(', ')}`);\n      }\n      return this.dateFormatter[method]({\n        date,\n        locale,\n        weekStartsOn,\n        excludeDays,\n        daysInWeek\n      });\n    }\n  }\n  CalendarDatePipe.ɵfac = function CalendarDatePipe_Factory(t) {\n    return new (t || CalendarDatePipe)(i0.ɵɵdirectiveInject(CalendarDateFormatter, 16), i0.ɵɵdirectiveInject(LOCALE_ID, 16));\n  };\n  CalendarDatePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"calendarDate\",\n    type: CalendarDatePipe,\n    pure: true\n  });\n  return CalendarDatePipe;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CalendarUtils = /*#__PURE__*/(() => {\n  class CalendarUtils {\n    constructor(dateAdapter) {\n      this.dateAdapter = dateAdapter;\n    }\n    getMonthView(args) {\n      return getMonthView(this.dateAdapter, args);\n    }\n    getWeekViewHeader(args) {\n      return getWeekViewHeader(this.dateAdapter, args);\n    }\n    getWeekView(args) {\n      return getWeekView(this.dateAdapter, args);\n    }\n  }\n  CalendarUtils.ɵfac = function CalendarUtils_Factory(t) {\n    return new (t || CalendarUtils)(i0.ɵɵinject(DateAdapter));\n  };\n  CalendarUtils.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: CalendarUtils,\n    factory: CalendarUtils.ɵfac\n  });\n  return CalendarUtils;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst MOMENT = new InjectionToken('Moment');\n/**\n * This will use <a href=\"http://momentjs.com/\" target=\"_blank\">moment</a> to do all date formatting. To use this class:\n *\n * ```typescript\n * import { CalendarDateFormatter, CalendarMomentDateFormatter, MOMENT } from 'angular-calendar';\n * import moment from 'moment';\n *\n * // in your component\n * provide: [{\n *   provide: MOMENT, useValue: moment\n * }, {\n *   provide: CalendarDateFormatter, useClass: CalendarMomentDateFormatter\n * }]\n *\n * ```\n */\nlet CalendarMomentDateFormatter = /*#__PURE__*/(() => {\n  class CalendarMomentDateFormatter {\n    /**\n     * @hidden\n     */\n    constructor(moment, dateAdapter) {\n      this.moment = moment;\n      this.dateAdapter = dateAdapter;\n    }\n    /**\n     * The month view header week day labels\n     */\n    monthViewColumnHeader({\n      date,\n      locale\n    }) {\n      return this.moment(date).locale(locale).format('dddd');\n    }\n    /**\n     * The month view cell day number\n     */\n    monthViewDayNumber({\n      date,\n      locale\n    }) {\n      return this.moment(date).locale(locale).format('D');\n    }\n    /**\n     * The month view title\n     */\n    monthViewTitle({\n      date,\n      locale\n    }) {\n      return this.moment(date).locale(locale).format('MMMM YYYY');\n    }\n    /**\n     * The week view header week day labels\n     */\n    weekViewColumnHeader({\n      date,\n      locale\n    }) {\n      return this.moment(date).locale(locale).format('dddd');\n    }\n    /**\n     * The week view sub header day and month labels\n     */\n    weekViewColumnSubHeader({\n      date,\n      locale\n    }) {\n      return this.moment(date).locale(locale).format('MMM D');\n    }\n    /**\n     * The week view title\n     */\n    weekViewTitle({\n      date,\n      locale,\n      weekStartsOn,\n      excludeDays,\n      daysInWeek\n    }) {\n      const {\n        viewStart,\n        viewEnd\n      } = getWeekViewPeriod(this.dateAdapter, date, weekStartsOn, excludeDays, daysInWeek);\n      const format = (dateToFormat, showYear) => this.moment(dateToFormat).locale(locale).format('MMM D' + (showYear ? ', YYYY' : ''));\n      return `${format(viewStart, viewStart.getUTCFullYear() !== viewEnd.getUTCFullYear())} - ${format(viewEnd, true)}`;\n    }\n    /**\n     * The time formatting down the left hand side of the week view\n     */\n    weekViewHour({\n      date,\n      locale\n    }) {\n      return this.moment(date).locale(locale).format('ha');\n    }\n    /**\n     * The time formatting down the left hand side of the day view\n     */\n    dayViewHour({\n      date,\n      locale\n    }) {\n      return this.moment(date).locale(locale).format('ha');\n    }\n    /**\n     * The day view title\n     */\n    dayViewTitle({\n      date,\n      locale\n    }) {\n      return this.moment(date).locale(locale).format('dddd, LL'); // dddd = Thursday\n    } // LL = locale-dependent Month Day, Year\n  }\n\n  CalendarMomentDateFormatter.ɵfac = function CalendarMomentDateFormatter_Factory(t) {\n    return new (t || CalendarMomentDateFormatter)(i0.ɵɵinject(MOMENT), i0.ɵɵinject(DateAdapter));\n  };\n  CalendarMomentDateFormatter.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: CalendarMomentDateFormatter,\n    factory: CalendarMomentDateFormatter.ɵfac\n  });\n  return CalendarMomentDateFormatter;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * This will use <a href=\"https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Intl\" target=\"_blank\">Intl</a> API to do all date formatting.\n *\n * You will need to include a <a href=\"https://github.com/andyearnshaw/Intl.js/\">polyfill</a> for older browsers.\n */\nlet CalendarNativeDateFormatter = /*#__PURE__*/(() => {\n  class CalendarNativeDateFormatter {\n    constructor(dateAdapter) {\n      this.dateAdapter = dateAdapter;\n    }\n    /**\n     * The month view header week day labels\n     */\n    monthViewColumnHeader({\n      date,\n      locale\n    }) {\n      return new Intl.DateTimeFormat(locale, {\n        weekday: 'long'\n      }).format(date);\n    }\n    /**\n     * The month view cell day number\n     */\n    monthViewDayNumber({\n      date,\n      locale\n    }) {\n      return new Intl.DateTimeFormat(locale, {\n        day: 'numeric'\n      }).format(date);\n    }\n    /**\n     * The month view title\n     */\n    monthViewTitle({\n      date,\n      locale\n    }) {\n      return new Intl.DateTimeFormat(locale, {\n        year: 'numeric',\n        month: 'long'\n      }).format(date);\n    }\n    /**\n     * The week view header week day labels\n     */\n    weekViewColumnHeader({\n      date,\n      locale\n    }) {\n      return new Intl.DateTimeFormat(locale, {\n        weekday: 'long'\n      }).format(date);\n    }\n    /**\n     * The week view sub header day and month labels\n     */\n    weekViewColumnSubHeader({\n      date,\n      locale\n    }) {\n      return new Intl.DateTimeFormat(locale, {\n        day: 'numeric',\n        month: 'short'\n      }).format(date);\n    }\n    /**\n     * The week view title\n     */\n    weekViewTitle({\n      date,\n      locale,\n      weekStartsOn,\n      excludeDays,\n      daysInWeek\n    }) {\n      const {\n        viewStart,\n        viewEnd\n      } = getWeekViewPeriod(this.dateAdapter, date, weekStartsOn, excludeDays, daysInWeek);\n      const format = (dateToFormat, showYear) => new Intl.DateTimeFormat(locale, {\n        day: 'numeric',\n        month: 'short',\n        year: showYear ? 'numeric' : undefined\n      }).format(dateToFormat);\n      return `${format(viewStart, viewStart.getUTCFullYear() !== viewEnd.getUTCFullYear())} - ${format(viewEnd, true)}`;\n    }\n    /**\n     * The time formatting down the left hand side of the week view\n     */\n    weekViewHour({\n      date,\n      locale\n    }) {\n      return new Intl.DateTimeFormat(locale, {\n        hour: 'numeric'\n      }).format(date);\n    }\n    /**\n     * The time formatting down the left hand side of the day view\n     */\n    dayViewHour({\n      date,\n      locale\n    }) {\n      return new Intl.DateTimeFormat(locale, {\n        hour: 'numeric'\n      }).format(date);\n    }\n    /**\n     * The day view title\n     */\n    dayViewTitle({\n      date,\n      locale\n    }) {\n      return new Intl.DateTimeFormat(locale, {\n        day: 'numeric',\n        month: 'long',\n        year: 'numeric',\n        weekday: 'long'\n      }).format(date);\n    }\n  }\n  CalendarNativeDateFormatter.ɵfac = function CalendarNativeDateFormatter_Factory(t) {\n    return new (t || CalendarNativeDateFormatter)(i0.ɵɵinject(DateAdapter));\n  };\n  CalendarNativeDateFormatter.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: CalendarNativeDateFormatter,\n    factory: CalendarNativeDateFormatter.ɵfac\n  });\n  return CalendarNativeDateFormatter;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nvar CalendarEventTimesChangedEventType = /*#__PURE__*/function (CalendarEventTimesChangedEventType) {\n  CalendarEventTimesChangedEventType[\"Drag\"] = \"drag\";\n  CalendarEventTimesChangedEventType[\"Drop\"] = \"drop\";\n  CalendarEventTimesChangedEventType[\"Resize\"] = \"resize\";\n  return CalendarEventTimesChangedEventType;\n}(CalendarEventTimesChangedEventType || {});\n/**\n * Import this module to if you're just using a singular view and want to save on bundle size. Example usage:\n *\n * ```typescript\n * import { CalendarCommonModule, CalendarMonthModule } from 'angular-calendar';\n *\n * @NgModule({\n *   imports: [\n *     CalendarCommonModule.forRoot(),\n *     CalendarMonthModule\n *   ]\n * })\n * class MyModule {}\n * ```\n *\n */\nlet CalendarCommonModule = /*#__PURE__*/(() => {\n  class CalendarCommonModule {\n    static forRoot(dateAdapter, config = {}) {\n      return {\n        ngModule: CalendarCommonModule,\n        providers: [dateAdapter, config.eventTitleFormatter || CalendarEventTitleFormatter, config.dateFormatter || CalendarDateFormatter, config.utils || CalendarUtils, config.a11y || CalendarA11y]\n      };\n    }\n  }\n  CalendarCommonModule.ɵfac = function CalendarCommonModule_Factory(t) {\n    return new (t || CalendarCommonModule)();\n  };\n  CalendarCommonModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CalendarCommonModule\n  });\n  CalendarCommonModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [I18nPluralPipe],\n    imports: [CommonModule]\n  });\n  return CalendarCommonModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CalendarMonthCellComponent = /*#__PURE__*/(() => {\n  class CalendarMonthCellComponent {\n    constructor() {\n      this.highlightDay = new EventEmitter();\n      this.unhighlightDay = new EventEmitter();\n      this.eventClicked = new EventEmitter();\n      this.trackByEventId = trackByEventId;\n      this.validateDrag = isWithinThreshold;\n    }\n  }\n  CalendarMonthCellComponent.ɵfac = function CalendarMonthCellComponent_Factory(t) {\n    return new (t || CalendarMonthCellComponent)();\n  };\n  CalendarMonthCellComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CalendarMonthCellComponent,\n    selectors: [[\"mwl-calendar-month-cell\"]],\n    hostAttrs: [1, \"cal-cell\", \"cal-day-cell\"],\n    hostVars: 18,\n    hostBindings: function CalendarMonthCellComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"cal-past\", ctx.day.isPast)(\"cal-today\", ctx.day.isToday)(\"cal-future\", ctx.day.isFuture)(\"cal-weekend\", ctx.day.isWeekend)(\"cal-in-month\", ctx.day.inMonth)(\"cal-out-month\", !ctx.day.inMonth)(\"cal-has-events\", ctx.day.events.length > 0)(\"cal-open\", ctx.day === ctx.openDay)(\"cal-event-highlight\", !!ctx.day.backgroundColor);\n      }\n    },\n    inputs: {\n      day: \"day\",\n      openDay: \"openDay\",\n      locale: \"locale\",\n      tooltipPlacement: \"tooltipPlacement\",\n      tooltipAppendToBody: \"tooltipAppendToBody\",\n      customTemplate: \"customTemplate\",\n      tooltipTemplate: \"tooltipTemplate\",\n      tooltipDelay: \"tooltipDelay\"\n    },\n    outputs: {\n      highlightDay: \"highlightDay\",\n      unhighlightDay: \"unhighlightDay\",\n      eventClicked: \"eventClicked\"\n    },\n    decls: 3,\n    vars: 15,\n    consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"cal-cell-top\"], [\"aria-hidden\", \"true\"], [\"class\", \"cal-day-badge\", 4, \"ngIf\"], [1, \"cal-day-number\"], [\"class\", \"cal-events\", 4, \"ngIf\"], [1, \"cal-day-badge\"], [1, \"cal-events\"], [\"class\", \"cal-event\", \"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 3, \"ngStyle\", \"ngClass\", \"mwlCalendarTooltip\", \"tooltipPlacement\", \"tooltipEvent\", \"tooltipTemplate\", \"tooltipAppendToBody\", \"tooltipDelay\", \"cal-draggable\", \"dropData\", \"dragAxis\", \"validateDrag\", \"touchStartLongPress\", \"mouseenter\", \"mouseleave\", \"mwlClick\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 1, \"cal-event\", 3, \"ngStyle\", \"ngClass\", \"mwlCalendarTooltip\", \"tooltipPlacement\", \"tooltipEvent\", \"tooltipTemplate\", \"tooltipAppendToBody\", \"tooltipDelay\", \"dropData\", \"dragAxis\", \"validateDrag\", \"touchStartLongPress\", \"mouseenter\", \"mouseleave\", \"mwlClick\"]],\n    template: function CalendarMonthCellComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CalendarMonthCellComponent_ng_template_0_Template, 8, 14, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(2, CalendarMonthCellComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || _r0)(\"ngTemplateOutletContext\", i0.ɵɵpureFunctionV(2, _c10, [ctx.day, ctx.openDay, ctx.locale, ctx.tooltipPlacement, ctx.highlightDay, ctx.unhighlightDay, ctx.eventClicked, ctx.tooltipTemplate, ctx.tooltipAppendToBody, ctx.tooltipDelay, ctx.trackByEventId, ctx.validateDrag]));\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.DraggableDirective, CalendarTooltipDirective, ClickDirective, CalendarDatePipe, CalendarEventTitlePipe, CalendarA11yPipe],\n    encapsulation: 2\n  });\n  return CalendarMonthCellComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst collapseAnimation = trigger('collapse', [state('void', style({\n  height: 0,\n  overflow: 'hidden',\n  'padding-top': 0,\n  'padding-bottom': 0\n})), state('*', style({\n  height: '*',\n  overflow: 'hidden',\n  'padding-top': '*',\n  'padding-bottom': '*'\n})), transition('* => void', animate('150ms ease-out')), transition('void => *', animate('150ms ease-in'))]);\nlet CalendarOpenDayEventsComponent = /*#__PURE__*/(() => {\n  class CalendarOpenDayEventsComponent {\n    constructor() {\n      this.isOpen = false;\n      this.eventClicked = new EventEmitter();\n      this.trackByEventId = trackByEventId;\n      this.validateDrag = isWithinThreshold;\n    }\n  }\n  CalendarOpenDayEventsComponent.ɵfac = function CalendarOpenDayEventsComponent_Factory(t) {\n    return new (t || CalendarOpenDayEventsComponent)();\n  };\n  CalendarOpenDayEventsComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CalendarOpenDayEventsComponent,\n    selectors: [[\"mwl-calendar-open-day-events\"]],\n    inputs: {\n      locale: \"locale\",\n      isOpen: \"isOpen\",\n      events: \"events\",\n      customTemplate: \"customTemplate\",\n      eventTitleTemplate: \"eventTitleTemplate\",\n      eventActionsTemplate: \"eventActionsTemplate\",\n      date: \"date\"\n    },\n    outputs: {\n      eventClicked: \"eventClicked\"\n    },\n    decls: 3,\n    vars: 8,\n    consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"cal-open-day-events\", \"role\", \"application\", 4, \"ngIf\"], [\"role\", \"application\", 1, \"cal-open-day-events\"], [\"tabindex\", \"-1\", \"role\", \"alert\"], [\"tabindex\", \"0\", \"role\", \"landmark\"], [\"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 3, \"ngClass\", \"cal-draggable\", \"dropData\", \"dragAxis\", \"validateDrag\", \"touchStartLongPress\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 3, \"ngClass\", \"dropData\", \"dragAxis\", \"validateDrag\", \"touchStartLongPress\"], [1, \"cal-event\", 3, \"ngStyle\"], [\"view\", \"month\", \"tabindex\", \"0\", 3, \"event\", \"customTemplate\", \"mwlClick\", \"mwlKeydownEnter\"], [3, \"event\", \"customTemplate\"]],\n    template: function CalendarOpenDayEventsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CalendarOpenDayEventsComponent_ng_template_0_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(2, CalendarOpenDayEventsComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || _r0)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction5(2, _c14, ctx.events, ctx.eventClicked, ctx.isOpen, ctx.trackByEventId, ctx.validateDrag));\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.DraggableDirective, CalendarEventActionsComponent, CalendarEventTitleComponent, ClickDirective, KeydownEnterDirective, CalendarA11yPipe],\n    encapsulation: 2,\n    data: {\n      animation: [collapseAnimation]\n    }\n  });\n  return CalendarOpenDayEventsComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CalendarMonthViewHeaderComponent = /*#__PURE__*/(() => {\n  class CalendarMonthViewHeaderComponent {\n    constructor() {\n      this.columnHeaderClicked = new EventEmitter();\n      this.trackByWeekDayHeaderDate = trackByWeekDayHeaderDate;\n    }\n  }\n  CalendarMonthViewHeaderComponent.ɵfac = function CalendarMonthViewHeaderComponent_Factory(t) {\n    return new (t || CalendarMonthViewHeaderComponent)();\n  };\n  CalendarMonthViewHeaderComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CalendarMonthViewHeaderComponent,\n    selectors: [[\"mwl-calendar-month-view-header\"]],\n    inputs: {\n      days: \"days\",\n      locale: \"locale\",\n      customTemplate: \"customTemplate\"\n    },\n    outputs: {\n      columnHeaderClicked: \"columnHeaderClicked\"\n    },\n    decls: 3,\n    vars: 6,\n    consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"role\", \"row\", 1, \"cal-cell-row\", \"cal-header\"], [\"class\", \"cal-cell\", \"tabindex\", \"0\", \"role\", \"columnheader\", 3, \"cal-past\", \"cal-today\", \"cal-future\", \"cal-weekend\", \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"tabindex\", \"0\", \"role\", \"columnheader\", 1, \"cal-cell\", 3, \"ngClass\", \"click\"]],\n    template: function CalendarMonthViewHeaderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CalendarMonthViewHeaderComponent_ng_template_0_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(2, CalendarMonthViewHeaderComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || _r0)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(2, _c15, ctx.days, ctx.locale, ctx.trackByWeekDayHeaderDate));\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgTemplateOutlet, CalendarDatePipe],\n    encapsulation: 2\n  });\n  return CalendarMonthViewHeaderComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Shows all events on a given month. Example usage:\n *\n * ```typescript\n * <mwl-calendar-month-view\n *  [viewDate]=\"viewDate\"\n *  [events]=\"events\">\n * </mwl-calendar-month-view>\n * ```\n */\nlet CalendarMonthViewComponent = /*#__PURE__*/(() => {\n  class CalendarMonthViewComponent {\n    /**\n     * @hidden\n     */\n    constructor(cdr, utils, locale, dateAdapter) {\n      this.cdr = cdr;\n      this.utils = utils;\n      this.dateAdapter = dateAdapter;\n      /**\n       * An array of events to display on view.\n       * The schema is available here: https://github.com/mattlewis92/calendar-utils/blob/c51689985f59a271940e30bc4e2c4e1fee3fcb5c/src/calendarUtils.ts#L49-L63\n       */\n      this.events = [];\n      /**\n       * An array of day indexes (0 = sunday, 1 = monday etc) that will be hidden on the view\n       */\n      this.excludeDays = [];\n      /**\n       * Whether the events list for the day of the `viewDate` option is visible or not\n       */\n      this.activeDayIsOpen = false;\n      /**\n       * The placement of the event tooltip\n       */\n      this.tooltipPlacement = 'auto';\n      /**\n       * Whether to append tooltips to the body or next to the trigger element\n       */\n      this.tooltipAppendToBody = true;\n      /**\n       * The delay in milliseconds before the tooltip should be displayed. If not provided the tooltip\n       * will be displayed immediately.\n       */\n      this.tooltipDelay = null;\n      /**\n       * An output that will be called before the view is rendered for the current month.\n       * If you add the `cssClass` property to a day in the body it will add that class to the cell element in the template\n       */\n      this.beforeViewRender = new EventEmitter();\n      /**\n       * Called when the day cell is clicked\n       */\n      this.dayClicked = new EventEmitter();\n      /**\n       * Called when the event title is clicked\n       */\n      this.eventClicked = new EventEmitter();\n      /**\n       * Called when a header week day is clicked. Returns ISO day number.\n       */\n      this.columnHeaderClicked = new EventEmitter();\n      /**\n       * Called when an event is dragged and dropped\n       */\n      this.eventTimesChanged = new EventEmitter();\n      /**\n       * @hidden\n       */\n      this.trackByRowOffset = (index, offset) => this.view.days.slice(offset, this.view.totalDaysVisibleInWeek).map(day => day.date.toISOString()).join('-');\n      /**\n       * @hidden\n       */\n      this.trackByDate = (index, day) => day.date.toISOString();\n      this.locale = locale;\n    }\n    /**\n     * @hidden\n     */\n    ngOnInit() {\n      if (this.refresh) {\n        this.refreshSubscription = this.refresh.subscribe(() => {\n          this.refreshAll();\n          this.cdr.markForCheck();\n        });\n      }\n    }\n    /**\n     * @hidden\n     */\n    ngOnChanges(changes) {\n      const refreshHeader = changes.viewDate || changes.excludeDays || changes.weekendDays;\n      const refreshBody = changes.viewDate || changes.events || changes.excludeDays || changes.weekendDays;\n      if (refreshHeader) {\n        this.refreshHeader();\n      }\n      if (changes.events) {\n        validateEvents(this.events);\n      }\n      if (refreshBody) {\n        this.refreshBody();\n      }\n      if (refreshHeader || refreshBody) {\n        this.emitBeforeViewRender();\n      }\n      if (changes.activeDayIsOpen || changes.viewDate || changes.events || changes.excludeDays || changes.activeDay) {\n        this.checkActiveDayIsOpen();\n      }\n    }\n    /**\n     * @hidden\n     */\n    ngOnDestroy() {\n      if (this.refreshSubscription) {\n        this.refreshSubscription.unsubscribe();\n      }\n    }\n    /**\n     * @hidden\n     */\n    toggleDayHighlight(event, isHighlighted) {\n      this.view.days.forEach(day => {\n        if (isHighlighted && day.events.indexOf(event) > -1) {\n          day.backgroundColor = event.color && event.color.secondary || '#D1E8FF';\n        } else {\n          delete day.backgroundColor;\n        }\n      });\n    }\n    /**\n     * @hidden\n     */\n    eventDropped(droppedOn, event, draggedFrom) {\n      if (droppedOn !== draggedFrom) {\n        const year = this.dateAdapter.getYear(droppedOn.date);\n        const month = this.dateAdapter.getMonth(droppedOn.date);\n        const date = this.dateAdapter.getDate(droppedOn.date);\n        const newStart = this.dateAdapter.setDate(this.dateAdapter.setMonth(this.dateAdapter.setYear(event.start, year), month), date);\n        let newEnd;\n        if (event.end) {\n          const secondsDiff = this.dateAdapter.differenceInSeconds(newStart, event.start);\n          newEnd = this.dateAdapter.addSeconds(event.end, secondsDiff);\n        }\n        this.eventTimesChanged.emit({\n          event,\n          newStart,\n          newEnd,\n          day: droppedOn,\n          type: CalendarEventTimesChangedEventType.Drop\n        });\n      }\n    }\n    refreshHeader() {\n      this.columnHeaders = this.utils.getWeekViewHeader({\n        viewDate: this.viewDate,\n        weekStartsOn: this.weekStartsOn,\n        excluded: this.excludeDays,\n        weekendDays: this.weekendDays\n      });\n    }\n    refreshBody() {\n      this.view = this.utils.getMonthView({\n        events: this.events,\n        viewDate: this.viewDate,\n        weekStartsOn: this.weekStartsOn,\n        excluded: this.excludeDays,\n        weekendDays: this.weekendDays\n      });\n    }\n    checkActiveDayIsOpen() {\n      if (this.activeDayIsOpen === true) {\n        const activeDay = this.activeDay || this.viewDate;\n        this.openDay = this.view.days.find(day => this.dateAdapter.isSameDay(day.date, activeDay));\n        const index = this.view.days.indexOf(this.openDay);\n        this.openRowIndex = Math.floor(index / this.view.totalDaysVisibleInWeek) * this.view.totalDaysVisibleInWeek;\n      } else {\n        this.openRowIndex = null;\n        this.openDay = null;\n      }\n    }\n    refreshAll() {\n      this.refreshHeader();\n      this.refreshBody();\n      this.emitBeforeViewRender();\n      this.checkActiveDayIsOpen();\n    }\n    emitBeforeViewRender() {\n      if (this.columnHeaders && this.view) {\n        this.beforeViewRender.emit({\n          header: this.columnHeaders,\n          body: this.view.days,\n          period: this.view.period\n        });\n      }\n    }\n  }\n  CalendarMonthViewComponent.ɵfac = function CalendarMonthViewComponent_Factory(t) {\n    return new (t || CalendarMonthViewComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(CalendarUtils), i0.ɵɵdirectiveInject(LOCALE_ID), i0.ɵɵdirectiveInject(DateAdapter));\n  };\n  CalendarMonthViewComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CalendarMonthViewComponent,\n    selectors: [[\"mwl-calendar-month-view\"]],\n    inputs: {\n      viewDate: \"viewDate\",\n      events: \"events\",\n      excludeDays: \"excludeDays\",\n      activeDayIsOpen: \"activeDayIsOpen\",\n      activeDay: \"activeDay\",\n      refresh: \"refresh\",\n      locale: \"locale\",\n      tooltipPlacement: \"tooltipPlacement\",\n      tooltipTemplate: \"tooltipTemplate\",\n      tooltipAppendToBody: \"tooltipAppendToBody\",\n      tooltipDelay: \"tooltipDelay\",\n      weekStartsOn: \"weekStartsOn\",\n      headerTemplate: \"headerTemplate\",\n      cellTemplate: \"cellTemplate\",\n      openDayEventsTemplate: \"openDayEventsTemplate\",\n      eventTitleTemplate: \"eventTitleTemplate\",\n      eventActionsTemplate: \"eventActionsTemplate\",\n      weekendDays: \"weekendDays\"\n    },\n    outputs: {\n      beforeViewRender: \"beforeViewRender\",\n      dayClicked: \"dayClicked\",\n      eventClicked: \"eventClicked\",\n      columnHeaderClicked: \"columnHeaderClicked\",\n      eventTimesChanged: \"eventTimesChanged\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 4,\n    vars: 5,\n    consts: [[\"role\", \"grid\", 1, \"cal-month-view\"], [3, \"days\", \"locale\", \"customTemplate\", \"columnHeaderClicked\"], [1, \"cal-days\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"role\", \"row\", 1, \"cal-cell-row\"], [\"role\", \"gridcell\", \"mwlDroppable\", \"\", \"dragOverClass\", \"cal-drag-over\", 3, \"ngClass\", \"day\", \"openDay\", \"locale\", \"tooltipPlacement\", \"tooltipAppendToBody\", \"tooltipTemplate\", \"tooltipDelay\", \"customTemplate\", \"ngStyle\", \"clickListenerDisabled\", \"mwlClick\", \"mwlKeydownEnter\", \"highlightDay\", \"unhighlightDay\", \"drop\", \"eventClicked\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlDroppable\", \"\", \"dragOverClass\", \"cal-drag-over\", 3, \"locale\", \"isOpen\", \"events\", \"date\", \"customTemplate\", \"eventTitleTemplate\", \"eventActionsTemplate\", \"eventClicked\", \"drop\"], [\"role\", \"gridcell\", \"mwlDroppable\", \"\", \"dragOverClass\", \"cal-drag-over\", 3, \"ngClass\", \"day\", \"openDay\", \"locale\", \"tooltipPlacement\", \"tooltipAppendToBody\", \"tooltipTemplate\", \"tooltipDelay\", \"customTemplate\", \"ngStyle\", \"clickListenerDisabled\", \"mwlClick\", \"mwlKeydownEnter\", \"highlightDay\", \"unhighlightDay\", \"drop\", \"eventClicked\"]],\n    template: function CalendarMonthViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mwl-calendar-month-view-header\", 1);\n        i0.ɵɵlistener(\"columnHeaderClicked\", function CalendarMonthViewComponent_Template_mwl_calendar_month_view_header_columnHeaderClicked_1_listener($event) {\n          return ctx.columnHeaderClicked.emit($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵtemplate(3, CalendarMonthViewComponent_div_3_Template, 5, 13, \"div\", 3);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"days\", ctx.columnHeaders)(\"locale\", ctx.locale)(\"customTemplate\", ctx.headerTemplate);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.view.rowOffsets)(\"ngForTrackBy\", ctx.trackByRowOffset);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgStyle, i2.DroppableDirective, ClickDirective, KeydownEnterDirective, CalendarMonthCellComponent, CalendarOpenDayEventsComponent, CalendarMonthViewHeaderComponent, i1.SlicePipe, CalendarA11yPipe],\n    encapsulation: 2\n  });\n  return CalendarMonthViewComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CalendarMonthModule = /*#__PURE__*/(() => {\n  class CalendarMonthModule {}\n  CalendarMonthModule.ɵfac = function CalendarMonthModule_Factory(t) {\n    return new (t || CalendarMonthModule)();\n  };\n  CalendarMonthModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CalendarMonthModule\n  });\n  CalendarMonthModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, DragAndDropModule, CalendarCommonModule, DragAndDropModule]\n  });\n  return CalendarMonthModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nclass CalendarDragHelper {\n  constructor(dragContainerElement, draggableElement) {\n    this.dragContainerElement = dragContainerElement;\n    this.startPosition = draggableElement.getBoundingClientRect();\n  }\n  validateDrag({\n    x,\n    y,\n    snapDraggedEvents,\n    dragAlreadyMoved,\n    transform\n  }) {\n    const isDraggedWithinThreshold = isWithinThreshold({\n      x,\n      y\n    }) || dragAlreadyMoved;\n    if (snapDraggedEvents) {\n      const inner = Object.assign({}, this.startPosition, {\n        left: this.startPosition.left + transform.x,\n        right: this.startPosition.right + transform.x,\n        top: this.startPosition.top + transform.y,\n        bottom: this.startPosition.bottom + transform.y\n      });\n      if (isDraggedWithinThreshold) {\n        const outer = this.dragContainerElement.getBoundingClientRect();\n        const isTopInside = outer.top < inner.top && inner.top < outer.bottom;\n        const isBottomInside = outer.top < inner.bottom && inner.bottom < outer.bottom;\n        return isInsideLeftAndRight(outer, inner) && (isTopInside || isBottomInside);\n      }\n      /* istanbul ignore next */\n      return false;\n    } else {\n      return isDraggedWithinThreshold;\n    }\n  }\n}\nclass CalendarResizeHelper {\n  constructor(resizeContainerElement, minWidth, rtl) {\n    this.resizeContainerElement = resizeContainerElement;\n    this.minWidth = minWidth;\n    this.rtl = rtl;\n  }\n  validateResize({\n    rectangle,\n    edges\n  }) {\n    if (this.rtl) {\n      // TODO - find a way of testing this, for some reason the tests always fail but it does actually work\n      /* istanbul ignore next */\n      if (typeof edges.left !== 'undefined') {\n        rectangle.left -= edges.left;\n        rectangle.right += edges.left;\n      } else if (typeof edges.right !== 'undefined') {\n        rectangle.left += edges.right;\n        rectangle.right -= edges.right;\n      }\n      rectangle.width = rectangle.right - rectangle.left;\n    }\n    if (this.minWidth && Math.ceil(rectangle.width) < Math.ceil(this.minWidth)) {\n      return false;\n    }\n    return isInside(this.resizeContainerElement.getBoundingClientRect(), rectangle);\n  }\n}\nlet CalendarWeekViewHeaderComponent = /*#__PURE__*/(() => {\n  class CalendarWeekViewHeaderComponent {\n    constructor() {\n      this.dayHeaderClicked = new EventEmitter();\n      this.eventDropped = new EventEmitter();\n      this.dragEnter = new EventEmitter();\n      this.trackByWeekDayHeaderDate = trackByWeekDayHeaderDate;\n    }\n  }\n  CalendarWeekViewHeaderComponent.ɵfac = function CalendarWeekViewHeaderComponent_Factory(t) {\n    return new (t || CalendarWeekViewHeaderComponent)();\n  };\n  CalendarWeekViewHeaderComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CalendarWeekViewHeaderComponent,\n    selectors: [[\"mwl-calendar-week-view-header\"]],\n    inputs: {\n      days: \"days\",\n      locale: \"locale\",\n      customTemplate: \"customTemplate\"\n    },\n    outputs: {\n      dayHeaderClicked: \"dayHeaderClicked\",\n      eventDropped: \"eventDropped\",\n      dragEnter: \"dragEnter\"\n    },\n    decls: 3,\n    vars: 9,\n    consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"role\", \"row\", 1, \"cal-day-headers\"], [\"class\", \"cal-header\", \"mwlDroppable\", \"\", \"dragOverClass\", \"cal-drag-over\", \"tabindex\", \"0\", \"role\", \"columnheader\", 3, \"cal-past\", \"cal-today\", \"cal-future\", \"cal-weekend\", \"ngClass\", \"mwlClick\", \"drop\", \"dragEnter\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlDroppable\", \"\", \"dragOverClass\", \"cal-drag-over\", \"tabindex\", \"0\", \"role\", \"columnheader\", 1, \"cal-header\", 3, \"ngClass\", \"mwlClick\", \"drop\", \"dragEnter\"]],\n    template: function CalendarWeekViewHeaderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CalendarWeekViewHeaderComponent_ng_template_0_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(2, CalendarWeekViewHeaderComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || _r0)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction6(2, _c16, ctx.days, ctx.locale, ctx.dayHeaderClicked, ctx.eventDropped, ctx.dragEnter, ctx.trackByWeekDayHeaderDate));\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgTemplateOutlet, i2.DroppableDirective, ClickDirective, CalendarDatePipe],\n    encapsulation: 2\n  });\n  return CalendarWeekViewHeaderComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CalendarWeekViewEventComponent = /*#__PURE__*/(() => {\n  class CalendarWeekViewEventComponent {\n    constructor() {\n      this.eventClicked = new EventEmitter();\n    }\n  }\n  CalendarWeekViewEventComponent.ɵfac = function CalendarWeekViewEventComponent_Factory(t) {\n    return new (t || CalendarWeekViewEventComponent)();\n  };\n  CalendarWeekViewEventComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CalendarWeekViewEventComponent,\n    selectors: [[\"mwl-calendar-week-view-event\"]],\n    inputs: {\n      locale: \"locale\",\n      weekEvent: \"weekEvent\",\n      tooltipPlacement: \"tooltipPlacement\",\n      tooltipAppendToBody: \"tooltipAppendToBody\",\n      tooltipDisabled: \"tooltipDisabled\",\n      tooltipDelay: \"tooltipDelay\",\n      customTemplate: \"customTemplate\",\n      eventTitleTemplate: \"eventTitleTemplate\",\n      eventActionsTemplate: \"eventActionsTemplate\",\n      tooltipTemplate: \"tooltipTemplate\",\n      column: \"column\",\n      daysInWeek: \"daysInWeek\"\n    },\n    outputs: {\n      eventClicked: \"eventClicked\"\n    },\n    decls: 3,\n    vars: 12,\n    consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"tabindex\", \"0\", \"role\", \"application\", 1, \"cal-event\", 3, \"ngStyle\", \"mwlCalendarTooltip\", \"tooltipPlacement\", \"tooltipEvent\", \"tooltipTemplate\", \"tooltipAppendToBody\", \"tooltipDelay\", \"mwlClick\", \"mwlKeydownEnter\"], [3, \"event\", \"customTemplate\"], [3, \"event\", \"customTemplate\", \"view\"]],\n    template: function CalendarWeekViewEventComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CalendarWeekViewEventComponent_ng_template_0_Template, 6, 27, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(2, CalendarWeekViewEventComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || _r0)(\"ngTemplateOutletContext\", i0.ɵɵpureFunctionV(2, _c18, [ctx.weekEvent, ctx.tooltipPlacement, ctx.eventClicked, ctx.tooltipTemplate, ctx.tooltipAppendToBody, ctx.tooltipDisabled, ctx.tooltipDelay, ctx.column, ctx.daysInWeek]));\n      }\n    },\n    dependencies: [i1.NgTemplateOutlet, i1.NgStyle, CalendarEventActionsComponent, CalendarEventTitleComponent, CalendarTooltipDirective, ClickDirective, KeydownEnterDirective, CalendarEventTitlePipe, CalendarA11yPipe],\n    encapsulation: 2\n  });\n  return CalendarWeekViewEventComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CalendarWeekViewHourSegmentComponent = /*#__PURE__*/(() => {\n  class CalendarWeekViewHourSegmentComponent {}\n  CalendarWeekViewHourSegmentComponent.ɵfac = function CalendarWeekViewHourSegmentComponent_Factory(t) {\n    return new (t || CalendarWeekViewHourSegmentComponent)();\n  };\n  CalendarWeekViewHourSegmentComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CalendarWeekViewHourSegmentComponent,\n    selectors: [[\"mwl-calendar-week-view-hour-segment\"]],\n    inputs: {\n      segment: \"segment\",\n      segmentHeight: \"segmentHeight\",\n      locale: \"locale\",\n      isTimeLabel: \"isTimeLabel\",\n      daysInWeek: \"daysInWeek\",\n      customTemplate: \"customTemplate\"\n    },\n    decls: 3,\n    vars: 8,\n    consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"cal-hour-segment\", 3, \"ngClass\"], [\"class\", \"cal-time\", 4, \"ngIf\"], [1, \"cal-time\"]],\n    template: function CalendarWeekViewHourSegmentComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CalendarWeekViewHourSegmentComponent_ng_template_0_Template, 3, 13, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(2, CalendarWeekViewHourSegmentComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || _r0)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction5(2, _c19, ctx.segment, ctx.locale, ctx.segmentHeight, ctx.isTimeLabel, ctx.daysInWeek));\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, CalendarDatePipe, CalendarA11yPipe],\n    encapsulation: 2\n  });\n  return CalendarWeekViewHourSegmentComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CalendarWeekViewCurrentTimeMarkerComponent = /*#__PURE__*/(() => {\n  class CalendarWeekViewCurrentTimeMarkerComponent {\n    constructor(dateAdapter, zone) {\n      this.dateAdapter = dateAdapter;\n      this.zone = zone;\n      this.columnDate$ = new BehaviorSubject(undefined);\n      this.marker$ = this.zone.onStable.pipe(switchMap(() => interval(60 * 1000)), startWith(0), switchMapTo(this.columnDate$), map(columnDate => {\n        const startOfDay = this.dateAdapter.setMinutes(this.dateAdapter.setHours(columnDate, this.dayStartHour), this.dayStartMinute);\n        const endOfDay = this.dateAdapter.setMinutes(this.dateAdapter.setHours(columnDate, this.dayEndHour), this.dayEndMinute);\n        const hourHeightModifier = this.hourSegments * this.hourSegmentHeight / (this.hourDuration || 60);\n        const now = new Date();\n        return {\n          isVisible: this.dateAdapter.isSameDay(columnDate, now) && now >= startOfDay && now <= endOfDay,\n          top: this.dateAdapter.differenceInMinutes(now, startOfDay) * hourHeightModifier\n        };\n      }));\n    }\n    ngOnChanges(changes) {\n      if (changes.columnDate) {\n        this.columnDate$.next(changes.columnDate.currentValue);\n      }\n    }\n  }\n  CalendarWeekViewCurrentTimeMarkerComponent.ɵfac = function CalendarWeekViewCurrentTimeMarkerComponent_Factory(t) {\n    return new (t || CalendarWeekViewCurrentTimeMarkerComponent)(i0.ɵɵdirectiveInject(DateAdapter), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  CalendarWeekViewCurrentTimeMarkerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CalendarWeekViewCurrentTimeMarkerComponent,\n    selectors: [[\"mwl-calendar-week-view-current-time-marker\"]],\n    inputs: {\n      columnDate: \"columnDate\",\n      dayStartHour: \"dayStartHour\",\n      dayStartMinute: \"dayStartMinute\",\n      dayEndHour: \"dayEndHour\",\n      dayEndMinute: \"dayEndMinute\",\n      hourSegments: \"hourSegments\",\n      hourDuration: \"hourDuration\",\n      hourSegmentHeight: \"hourSegmentHeight\",\n      customTemplate: \"customTemplate\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 5,\n    vars: 14,\n    consts: [[\"defaultTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"cal-current-time-marker\", 3, \"top\", 4, \"ngIf\"], [1, \"cal-current-time-marker\"]],\n    template: function CalendarWeekViewCurrentTimeMarkerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CalendarWeekViewCurrentTimeMarkerComponent_ng_template_0_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵtemplate(2, CalendarWeekViewCurrentTimeMarkerComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n        i0.ɵɵpipe(3, \"async\");\n        i0.ɵɵpipe(4, \"async\");\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(1);\n        let tmp_1_0;\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.customTemplate || _r0)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction7(6, _c20, ctx.columnDate, ctx.dayStartHour, ctx.dayStartMinute, ctx.dayEndHour, ctx.dayEndMinute, (tmp_1_0 = i0.ɵɵpipeBind1(3, 2, ctx.marker$)) == null ? null : tmp_1_0.isVisible, (tmp_1_0 = i0.ɵɵpipeBind1(4, 4, ctx.marker$)) == null ? null : tmp_1_0.top));\n      }\n    },\n    dependencies: [i1.NgIf, i1.NgTemplateOutlet, i1.AsyncPipe],\n    encapsulation: 2\n  });\n  return CalendarWeekViewCurrentTimeMarkerComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Shows all events on a given week. Example usage:\n *\n * ```typescript\n * <mwl-calendar-week-view\n *  [viewDate]=\"viewDate\"\n *  [events]=\"events\">\n * </mwl-calendar-week-view>\n * ```\n */\nlet CalendarWeekViewComponent = /*#__PURE__*/(() => {\n  class CalendarWeekViewComponent {\n    /**\n     * @hidden\n     */\n    constructor(cdr, utils, locale, dateAdapter, element) {\n      this.cdr = cdr;\n      this.utils = utils;\n      this.dateAdapter = dateAdapter;\n      this.element = element;\n      /**\n       * An array of events to display on view\n       * The schema is available here: https://github.com/mattlewis92/calendar-utils/blob/c51689985f59a271940e30bc4e2c4e1fee3fcb5c/src/calendarUtils.ts#L49-L63\n       */\n      this.events = [];\n      /**\n       * An array of day indexes (0 = sunday, 1 = monday etc) that will be hidden on the view\n       */\n      this.excludeDays = [];\n      /**\n       * The placement of the event tooltip\n       */\n      this.tooltipPlacement = 'auto';\n      /**\n       * Whether to append tooltips to the body or next to the trigger element\n       */\n      this.tooltipAppendToBody = true;\n      /**\n       * The delay in milliseconds before the tooltip should be displayed. If not provided the tooltip\n       * will be displayed immediately.\n       */\n      this.tooltipDelay = null;\n      /**\n       * The precision to display events.\n       * `days` will round event start and end dates to the nearest day and `minutes` will not do this rounding\n       */\n      this.precision = 'days';\n      /**\n       * Whether to snap events to a grid when dragging\n       */\n      this.snapDraggedEvents = true;\n      /**\n       * The number of segments in an hour. Must divide equally into 60.\n       */\n      this.hourSegments = 2;\n      /**\n       * The height in pixels of each hour segment\n       */\n      this.hourSegmentHeight = 30;\n      /**\n       * The minimum height in pixels of each event\n       */\n      this.minimumEventHeight = 30;\n      /**\n       * The day start hours in 24 hour time. Must be 0-23\n       */\n      this.dayStartHour = 0;\n      /**\n       * The day start minutes. Must be 0-59\n       */\n      this.dayStartMinute = 0;\n      /**\n       * The day end hours in 24 hour time. Must be 0-23\n       */\n      this.dayEndHour = 23;\n      /**\n       * The day end minutes. Must be 0-59\n       */\n      this.dayEndMinute = 59;\n      /**\n       * Called when a header week day is clicked. Adding a `cssClass` property on `$event.day` will add that class to the header element\n       */\n      this.dayHeaderClicked = new EventEmitter();\n      /**\n       * Called when an event title is clicked\n       */\n      this.eventClicked = new EventEmitter();\n      /**\n       * Called when an event is resized or dragged and dropped\n       */\n      this.eventTimesChanged = new EventEmitter();\n      /**\n       * An output that will be called before the view is rendered for the current week.\n       * If you add the `cssClass` property to a day in the header it will add that class to the cell element in the template\n       */\n      this.beforeViewRender = new EventEmitter();\n      /**\n       * Called when an hour segment is clicked\n       */\n      this.hourSegmentClicked = new EventEmitter();\n      /**\n       * @hidden\n       */\n      this.allDayEventResizes = new Map();\n      /**\n       * @hidden\n       */\n      this.timeEventResizes = new Map();\n      /**\n       * @hidden\n       */\n      this.eventDragEnterByType = {\n        allDay: 0,\n        time: 0\n      };\n      /**\n       * @hidden\n       */\n      this.dragActive = false;\n      /**\n       * @hidden\n       */\n      this.dragAlreadyMoved = false;\n      /**\n       * @hidden\n       */\n      this.calendarId = Symbol('angular calendar week view id');\n      /**\n       * @hidden\n       */\n      this.rtl = false;\n      /**\n       * @hidden\n       */\n      this.trackByWeekDayHeaderDate = trackByWeekDayHeaderDate;\n      /**\n       * @hidden\n       */\n      this.trackByHourSegment = trackByHourSegment;\n      /**\n       * @hidden\n       */\n      this.trackByHour = trackByHour;\n      /**\n       * @hidden\n       */\n      this.trackByWeekAllDayEvent = trackByWeekAllDayEvent;\n      /**\n       * @hidden\n       */\n      this.trackByWeekTimeEvent = trackByWeekTimeEvent;\n      /**\n       * @hidden\n       */\n      this.trackByHourColumn = (index, column) => column.hours[0] ? column.hours[0].segments[0].date.toISOString() : column;\n      /**\n       * @hidden\n       */\n      this.trackById = (index, row) => row.id;\n      this.locale = locale;\n    }\n    /**\n     * @hidden\n     */\n    ngOnInit() {\n      if (this.refresh) {\n        this.refreshSubscription = this.refresh.subscribe(() => {\n          this.refreshAll();\n          this.cdr.markForCheck();\n        });\n      }\n    }\n    /**\n     * @hidden\n     */\n    ngOnChanges(changes) {\n      const refreshHeader = changes.viewDate || changes.excludeDays || changes.weekendDays || changes.daysInWeek || changes.weekStartsOn;\n      const refreshBody = changes.viewDate || changes.dayStartHour || changes.dayStartMinute || changes.dayEndHour || changes.dayEndMinute || changes.hourSegments || changes.hourDuration || changes.weekStartsOn || changes.weekendDays || changes.excludeDays || changes.hourSegmentHeight || changes.events || changes.daysInWeek || changes.minimumEventHeight;\n      if (refreshHeader) {\n        this.refreshHeader();\n      }\n      if (changes.events) {\n        validateEvents(this.events);\n      }\n      if (refreshBody) {\n        this.refreshBody();\n      }\n      if (refreshHeader || refreshBody) {\n        this.emitBeforeViewRender();\n      }\n    }\n    /**\n     * @hidden\n     */\n    ngOnDestroy() {\n      if (this.refreshSubscription) {\n        this.refreshSubscription.unsubscribe();\n      }\n    }\n    /**\n     * @hidden\n     */\n    ngAfterViewInit() {\n      this.rtl = typeof window !== 'undefined' && getComputedStyle(this.element.nativeElement).direction === 'rtl';\n      this.cdr.detectChanges();\n    }\n    /**\n     * @hidden\n     */\n    timeEventResizeStarted(eventsContainer, timeEvent, resizeEvent) {\n      this.timeEventResizes.set(timeEvent.event, resizeEvent);\n      this.resizeStarted(eventsContainer, timeEvent);\n    }\n    /**\n     * @hidden\n     */\n    timeEventResizing(timeEvent, resizeEvent) {\n      this.timeEventResizes.set(timeEvent.event, resizeEvent);\n      const adjustedEvents = new Map();\n      const tempEvents = [...this.events];\n      this.timeEventResizes.forEach((lastResizeEvent, event) => {\n        const newEventDates = this.getTimeEventResizedDates(event, lastResizeEvent);\n        const adjustedEvent = {\n          ...event,\n          ...newEventDates\n        };\n        adjustedEvents.set(adjustedEvent, event);\n        const eventIndex = tempEvents.indexOf(event);\n        tempEvents[eventIndex] = adjustedEvent;\n      });\n      this.restoreOriginalEvents(tempEvents, adjustedEvents, true);\n    }\n    /**\n     * @hidden\n     */\n    timeEventResizeEnded(timeEvent) {\n      this.view = this.getWeekView(this.events);\n      const lastResizeEvent = this.timeEventResizes.get(timeEvent.event);\n      if (lastResizeEvent) {\n        this.timeEventResizes.delete(timeEvent.event);\n        const newEventDates = this.getTimeEventResizedDates(timeEvent.event, lastResizeEvent);\n        this.eventTimesChanged.emit({\n          newStart: newEventDates.start,\n          newEnd: newEventDates.end,\n          event: timeEvent.event,\n          type: CalendarEventTimesChangedEventType.Resize\n        });\n      }\n    }\n    /**\n     * @hidden\n     */\n    allDayEventResizeStarted(allDayEventsContainer, allDayEvent, resizeEvent) {\n      this.allDayEventResizes.set(allDayEvent, {\n        originalOffset: allDayEvent.offset,\n        originalSpan: allDayEvent.span,\n        edge: typeof resizeEvent.edges.left !== 'undefined' ? 'left' : 'right'\n      });\n      this.resizeStarted(allDayEventsContainer, allDayEvent, this.getDayColumnWidth(allDayEventsContainer));\n    }\n    /**\n     * @hidden\n     */\n    allDayEventResizing(allDayEvent, resizeEvent, dayWidth) {\n      const currentResize = this.allDayEventResizes.get(allDayEvent);\n      const modifier = this.rtl ? -1 : 1;\n      if (typeof resizeEvent.edges.left !== 'undefined') {\n        const diff = Math.round(+resizeEvent.edges.left / dayWidth) * modifier;\n        allDayEvent.offset = currentResize.originalOffset + diff;\n        allDayEvent.span = currentResize.originalSpan - diff;\n      } else if (typeof resizeEvent.edges.right !== 'undefined') {\n        const diff = Math.round(+resizeEvent.edges.right / dayWidth) * modifier;\n        allDayEvent.span = currentResize.originalSpan + diff;\n      }\n    }\n    /**\n     * @hidden\n     */\n    allDayEventResizeEnded(allDayEvent) {\n      const currentResize = this.allDayEventResizes.get(allDayEvent);\n      if (currentResize) {\n        const allDayEventResizingBeforeStart = currentResize.edge === 'left';\n        let daysDiff;\n        if (allDayEventResizingBeforeStart) {\n          daysDiff = allDayEvent.offset - currentResize.originalOffset;\n        } else {\n          daysDiff = allDayEvent.span - currentResize.originalSpan;\n        }\n        allDayEvent.offset = currentResize.originalOffset;\n        allDayEvent.span = currentResize.originalSpan;\n        const newDates = this.getAllDayEventResizedDates(allDayEvent.event, daysDiff, allDayEventResizingBeforeStart);\n        this.eventTimesChanged.emit({\n          newStart: newDates.start,\n          newEnd: newDates.end,\n          event: allDayEvent.event,\n          type: CalendarEventTimesChangedEventType.Resize\n        });\n        this.allDayEventResizes.delete(allDayEvent);\n      }\n    }\n    /**\n     * @hidden\n     */\n    getDayColumnWidth(eventRowContainer) {\n      return Math.floor(eventRowContainer.offsetWidth / this.days.length);\n    }\n    /**\n     * @hidden\n     */\n    dateDragEnter(date) {\n      this.lastDragEnterDate = date;\n    }\n    /**\n     * @hidden\n     */\n    eventDropped(dropEvent, date, allDay) {\n      if (shouldFireDroppedEvent(dropEvent, date, allDay, this.calendarId) && this.lastDragEnterDate.getTime() === date.getTime() && (!this.snapDraggedEvents || dropEvent.dropData.event !== this.lastDraggedEvent)) {\n        this.eventTimesChanged.emit({\n          type: CalendarEventTimesChangedEventType.Drop,\n          event: dropEvent.dropData.event,\n          newStart: date,\n          allDay\n        });\n      }\n      this.lastDraggedEvent = null;\n    }\n    /**\n     * @hidden\n     */\n    dragEnter(type) {\n      this.eventDragEnterByType[type]++;\n    }\n    /**\n     * @hidden\n     */\n    dragLeave(type) {\n      this.eventDragEnterByType[type]--;\n    }\n    /**\n     * @hidden\n     */\n    dragStarted(eventsContainerElement, eventElement, event, useY) {\n      this.dayColumnWidth = this.getDayColumnWidth(eventsContainerElement);\n      const dragHelper = new CalendarDragHelper(eventsContainerElement, eventElement);\n      this.validateDrag = ({\n        x,\n        y,\n        transform\n      }) => {\n        const isAllowed = this.allDayEventResizes.size === 0 && this.timeEventResizes.size === 0 && dragHelper.validateDrag({\n          x,\n          y,\n          snapDraggedEvents: this.snapDraggedEvents,\n          dragAlreadyMoved: this.dragAlreadyMoved,\n          transform\n        });\n        if (isAllowed && this.validateEventTimesChanged) {\n          const newEventTimes = this.getDragMovedEventTimes(event, {\n            x,\n            y\n          }, this.dayColumnWidth, useY);\n          return this.validateEventTimesChanged({\n            type: CalendarEventTimesChangedEventType.Drag,\n            event: event.event,\n            newStart: newEventTimes.start,\n            newEnd: newEventTimes.end\n          });\n        }\n        return isAllowed;\n      };\n      this.dragActive = true;\n      this.dragAlreadyMoved = false;\n      this.lastDraggedEvent = null;\n      this.eventDragEnterByType = {\n        allDay: 0,\n        time: 0\n      };\n      if (!this.snapDraggedEvents && useY) {\n        this.view.hourColumns.forEach(column => {\n          const linkedEvent = column.events.find(columnEvent => columnEvent.event === event.event && columnEvent !== event);\n          // hide any linked events while dragging\n          if (linkedEvent) {\n            linkedEvent.width = 0;\n            linkedEvent.height = 0;\n          }\n        });\n      }\n      this.cdr.markForCheck();\n    }\n    /**\n     * @hidden\n     */\n    dragMove(dayEvent, dragEvent) {\n      const newEventTimes = this.getDragMovedEventTimes(dayEvent, dragEvent, this.dayColumnWidth, true);\n      const originalEvent = dayEvent.event;\n      const adjustedEvent = {\n        ...originalEvent,\n        ...newEventTimes\n      };\n      const tempEvents = this.events.map(event => {\n        if (event === originalEvent) {\n          return adjustedEvent;\n        }\n        return event;\n      });\n      this.restoreOriginalEvents(tempEvents, new Map([[adjustedEvent, originalEvent]]), this.snapDraggedEvents);\n      this.dragAlreadyMoved = true;\n    }\n    /**\n     * @hidden\n     */\n    allDayEventDragMove() {\n      this.dragAlreadyMoved = true;\n    }\n    /**\n     * @hidden\n     */\n    dragEnded(weekEvent, dragEndEvent, dayWidth, useY = false) {\n      this.view = this.getWeekView(this.events);\n      this.dragActive = false;\n      this.validateDrag = null;\n      const {\n        start,\n        end\n      } = this.getDragMovedEventTimes(weekEvent, dragEndEvent, dayWidth, useY);\n      if ((this.snapDraggedEvents || this.eventDragEnterByType[useY ? 'time' : 'allDay'] > 0) && isDraggedWithinPeriod(start, end, this.view.period)) {\n        this.lastDraggedEvent = weekEvent.event;\n        this.eventTimesChanged.emit({\n          newStart: start,\n          newEnd: end,\n          event: weekEvent.event,\n          type: CalendarEventTimesChangedEventType.Drag,\n          allDay: !useY\n        });\n      }\n    }\n    refreshHeader() {\n      this.days = this.utils.getWeekViewHeader({\n        viewDate: this.viewDate,\n        weekStartsOn: this.weekStartsOn,\n        excluded: this.excludeDays,\n        weekendDays: this.weekendDays,\n        ...getWeekViewPeriod(this.dateAdapter, this.viewDate, this.weekStartsOn, this.excludeDays, this.daysInWeek)\n      });\n    }\n    refreshBody() {\n      this.view = this.getWeekView(this.events);\n    }\n    refreshAll() {\n      this.refreshHeader();\n      this.refreshBody();\n      this.emitBeforeViewRender();\n    }\n    emitBeforeViewRender() {\n      if (this.days && this.view) {\n        this.beforeViewRender.emit({\n          header: this.days,\n          ...this.view\n        });\n      }\n    }\n    getWeekView(events) {\n      return this.utils.getWeekView({\n        events,\n        viewDate: this.viewDate,\n        weekStartsOn: this.weekStartsOn,\n        excluded: this.excludeDays,\n        precision: this.precision,\n        absolutePositionedEvents: true,\n        hourSegments: this.hourSegments,\n        hourDuration: this.hourDuration,\n        dayStart: {\n          hour: this.dayStartHour,\n          minute: this.dayStartMinute\n        },\n        dayEnd: {\n          hour: this.dayEndHour,\n          minute: this.dayEndMinute\n        },\n        segmentHeight: this.hourSegmentHeight,\n        weekendDays: this.weekendDays,\n        minimumEventHeight: this.minimumEventHeight,\n        ...getWeekViewPeriod(this.dateAdapter, this.viewDate, this.weekStartsOn, this.excludeDays, this.daysInWeek)\n      });\n    }\n    getDragMovedEventTimes(weekEvent, dragEndEvent, dayWidth, useY) {\n      const daysDragged = roundToNearest(dragEndEvent.x, dayWidth) / dayWidth * (this.rtl ? -1 : 1);\n      const minutesMoved = useY ? getMinutesMoved(dragEndEvent.y, this.hourSegments, this.hourSegmentHeight, this.eventSnapSize, this.hourDuration) : 0;\n      const start = this.dateAdapter.addMinutes(addDaysWithExclusions(this.dateAdapter, weekEvent.event.start, daysDragged, this.excludeDays), minutesMoved);\n      let end;\n      if (weekEvent.event.end) {\n        end = this.dateAdapter.addMinutes(addDaysWithExclusions(this.dateAdapter, weekEvent.event.end, daysDragged, this.excludeDays), minutesMoved);\n      }\n      return {\n        start,\n        end\n      };\n    }\n    restoreOriginalEvents(tempEvents, adjustedEvents, snapDraggedEvents = true) {\n      const previousView = this.view;\n      if (snapDraggedEvents) {\n        this.view = this.getWeekView(tempEvents);\n      }\n      const adjustedEventsArray = tempEvents.filter(event => adjustedEvents.has(event));\n      this.view.hourColumns.forEach((column, columnIndex) => {\n        previousView.hourColumns[columnIndex].hours.forEach((hour, hourIndex) => {\n          hour.segments.forEach((segment, segmentIndex) => {\n            column.hours[hourIndex].segments[segmentIndex].cssClass = segment.cssClass;\n          });\n        });\n        adjustedEventsArray.forEach(adjustedEvent => {\n          const originalEvent = adjustedEvents.get(adjustedEvent);\n          const existingColumnEvent = column.events.find(columnEvent => columnEvent.event === (snapDraggedEvents ? adjustedEvent : originalEvent));\n          if (existingColumnEvent) {\n            // restore the original event so trackBy kicks in and the dom isn't changed\n            existingColumnEvent.event = originalEvent;\n            existingColumnEvent['tempEvent'] = adjustedEvent;\n            if (!snapDraggedEvents) {\n              existingColumnEvent.height = 0;\n              existingColumnEvent.width = 0;\n            }\n          } else {\n            // add a dummy event to the drop so if the event was removed from the original column the drag doesn't end early\n            const event = {\n              event: originalEvent,\n              left: 0,\n              top: 0,\n              height: 0,\n              width: 0,\n              startsBeforeDay: false,\n              endsAfterDay: false,\n              tempEvent: adjustedEvent\n            };\n            column.events.push(event);\n          }\n        });\n      });\n      adjustedEvents.clear();\n    }\n    getTimeEventResizedDates(calendarEvent, resizeEvent) {\n      const newEventDates = {\n        start: calendarEvent.start,\n        end: getDefaultEventEnd(this.dateAdapter, calendarEvent, this.minimumEventHeight)\n      };\n      const {\n        end,\n        ...eventWithoutEnd\n      } = calendarEvent;\n      const smallestResizes = {\n        start: this.dateAdapter.addMinutes(newEventDates.end, this.minimumEventHeight * -1),\n        end: getDefaultEventEnd(this.dateAdapter, eventWithoutEnd, this.minimumEventHeight)\n      };\n      const modifier = this.rtl ? -1 : 1;\n      if (typeof resizeEvent.edges.left !== 'undefined') {\n        const daysDiff = Math.round(+resizeEvent.edges.left / this.dayColumnWidth) * modifier;\n        const newStart = addDaysWithExclusions(this.dateAdapter, newEventDates.start, daysDiff, this.excludeDays);\n        if (newStart < smallestResizes.start) {\n          newEventDates.start = newStart;\n        } else {\n          newEventDates.start = smallestResizes.start;\n        }\n      } else if (typeof resizeEvent.edges.right !== 'undefined') {\n        const daysDiff = Math.round(+resizeEvent.edges.right / this.dayColumnWidth) * modifier;\n        const newEnd = addDaysWithExclusions(this.dateAdapter, newEventDates.end, daysDiff, this.excludeDays);\n        if (newEnd > smallestResizes.end) {\n          newEventDates.end = newEnd;\n        } else {\n          newEventDates.end = smallestResizes.end;\n        }\n      }\n      if (typeof resizeEvent.edges.top !== 'undefined') {\n        const minutesMoved = getMinutesMoved(resizeEvent.edges.top, this.hourSegments, this.hourSegmentHeight, this.eventSnapSize, this.hourDuration);\n        const newStart = this.dateAdapter.addMinutes(newEventDates.start, minutesMoved);\n        if (newStart < smallestResizes.start) {\n          newEventDates.start = newStart;\n        } else {\n          newEventDates.start = smallestResizes.start;\n        }\n      } else if (typeof resizeEvent.edges.bottom !== 'undefined') {\n        const minutesMoved = getMinutesMoved(resizeEvent.edges.bottom, this.hourSegments, this.hourSegmentHeight, this.eventSnapSize, this.hourDuration);\n        const newEnd = this.dateAdapter.addMinutes(newEventDates.end, minutesMoved);\n        if (newEnd > smallestResizes.end) {\n          newEventDates.end = newEnd;\n        } else {\n          newEventDates.end = smallestResizes.end;\n        }\n      }\n      return newEventDates;\n    }\n    resizeStarted(eventsContainer, event, dayWidth) {\n      this.dayColumnWidth = this.getDayColumnWidth(eventsContainer);\n      const resizeHelper = new CalendarResizeHelper(eventsContainer, dayWidth, this.rtl);\n      this.validateResize = ({\n        rectangle,\n        edges\n      }) => {\n        const isWithinBoundary = resizeHelper.validateResize({\n          rectangle: {\n            ...rectangle\n          },\n          edges\n        });\n        if (isWithinBoundary && this.validateEventTimesChanged) {\n          let newEventDates;\n          if (!dayWidth) {\n            newEventDates = this.getTimeEventResizedDates(event.event, {\n              rectangle,\n              edges\n            });\n          } else {\n            const modifier = this.rtl ? -1 : 1;\n            if (typeof edges.left !== 'undefined') {\n              const diff = Math.round(+edges.left / dayWidth) * modifier;\n              newEventDates = this.getAllDayEventResizedDates(event.event, diff, !this.rtl);\n            } else {\n              const diff = Math.round(+edges.right / dayWidth) * modifier;\n              newEventDates = this.getAllDayEventResizedDates(event.event, diff, this.rtl);\n            }\n          }\n          return this.validateEventTimesChanged({\n            type: CalendarEventTimesChangedEventType.Resize,\n            event: event.event,\n            newStart: newEventDates.start,\n            newEnd: newEventDates.end\n          });\n        }\n        return isWithinBoundary;\n      };\n      this.cdr.markForCheck();\n    }\n    /**\n     * @hidden\n     */\n    getAllDayEventResizedDates(event, daysDiff, beforeStart) {\n      let start = event.start;\n      let end = event.end || event.start;\n      if (beforeStart) {\n        start = addDaysWithExclusions(this.dateAdapter, start, daysDiff, this.excludeDays);\n      } else {\n        end = addDaysWithExclusions(this.dateAdapter, end, daysDiff, this.excludeDays);\n      }\n      return {\n        start,\n        end\n      };\n    }\n  }\n  CalendarWeekViewComponent.ɵfac = function CalendarWeekViewComponent_Factory(t) {\n    return new (t || CalendarWeekViewComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(CalendarUtils), i0.ɵɵdirectiveInject(LOCALE_ID), i0.ɵɵdirectiveInject(DateAdapter), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  CalendarWeekViewComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CalendarWeekViewComponent,\n    selectors: [[\"mwl-calendar-week-view\"]],\n    inputs: {\n      viewDate: \"viewDate\",\n      events: \"events\",\n      excludeDays: \"excludeDays\",\n      refresh: \"refresh\",\n      locale: \"locale\",\n      tooltipPlacement: \"tooltipPlacement\",\n      tooltipTemplate: \"tooltipTemplate\",\n      tooltipAppendToBody: \"tooltipAppendToBody\",\n      tooltipDelay: \"tooltipDelay\",\n      weekStartsOn: \"weekStartsOn\",\n      headerTemplate: \"headerTemplate\",\n      eventTemplate: \"eventTemplate\",\n      eventTitleTemplate: \"eventTitleTemplate\",\n      eventActionsTemplate: \"eventActionsTemplate\",\n      precision: \"precision\",\n      weekendDays: \"weekendDays\",\n      snapDraggedEvents: \"snapDraggedEvents\",\n      hourSegments: \"hourSegments\",\n      hourDuration: \"hourDuration\",\n      hourSegmentHeight: \"hourSegmentHeight\",\n      minimumEventHeight: \"minimumEventHeight\",\n      dayStartHour: \"dayStartHour\",\n      dayStartMinute: \"dayStartMinute\",\n      dayEndHour: \"dayEndHour\",\n      dayEndMinute: \"dayEndMinute\",\n      hourSegmentTemplate: \"hourSegmentTemplate\",\n      eventSnapSize: \"eventSnapSize\",\n      allDayEventsLabelTemplate: \"allDayEventsLabelTemplate\",\n      daysInWeek: \"daysInWeek\",\n      currentTimeMarkerTemplate: \"currentTimeMarkerTemplate\",\n      validateEventTimesChanged: \"validateEventTimesChanged\",\n      resizeCursors: \"resizeCursors\"\n    },\n    outputs: {\n      dayHeaderClicked: \"dayHeaderClicked\",\n      eventClicked: \"eventClicked\",\n      eventTimesChanged: \"eventTimesChanged\",\n      beforeViewRender: \"beforeViewRender\",\n      hourSegmentClicked: \"hourSegmentClicked\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 8,\n    vars: 9,\n    consts: [[\"role\", \"grid\", 1, \"cal-week-view\"], [3, \"days\", \"locale\", \"customTemplate\", \"dayHeaderClicked\", \"eventDropped\", \"dragEnter\"], [\"class\", \"cal-all-day-events\", \"mwlDroppable\", \"\", 3, \"dragEnter\", \"dragLeave\", 4, \"ngIf\"], [\"mwlDroppable\", \"\", 1, \"cal-time-events\", 3, \"dragEnter\", \"dragLeave\"], [\"class\", \"cal-time-label-column\", 4, \"ngIf\"], [1, \"cal-day-columns\"], [\"dayColumns\", \"\"], [\"class\", \"cal-day-column\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlDroppable\", \"\", 1, \"cal-all-day-events\", 3, \"dragEnter\", \"dragLeave\"], [\"allDayEventsContainer\", \"\"], [1, \"cal-time-label-column\"], [4, \"ngTemplateOutlet\"], [\"class\", \"cal-day-column\", \"mwlDroppable\", \"\", \"dragOverClass\", \"cal-drag-over\", 3, \"drop\", \"dragEnter\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"cal-events-row\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlDroppable\", \"\", \"dragOverClass\", \"cal-drag-over\", 1, \"cal-day-column\", 3, \"drop\", \"dragEnter\"], [1, \"cal-events-row\"], [\"eventRowContainer\", \"\"], [\"class\", \"cal-event-container\", \"mwlResizable\", \"\", \"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 3, \"cal-draggable\", \"cal-starts-within-week\", \"cal-ends-within-week\", \"ngClass\", \"width\", \"marginLeft\", \"marginRight\", \"resizeCursors\", \"resizeSnapGrid\", \"validateResize\", \"dropData\", \"dragAxis\", \"dragSnapGrid\", \"validateDrag\", \"touchStartLongPress\", \"resizeStart\", \"resizing\", \"resizeEnd\", \"dragStart\", \"dragging\", \"dragEnd\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlResizable\", \"\", \"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 1, \"cal-event-container\", 3, \"ngClass\", \"resizeCursors\", \"resizeSnapGrid\", \"validateResize\", \"dropData\", \"dragAxis\", \"dragSnapGrid\", \"validateDrag\", \"touchStartLongPress\", \"resizeStart\", \"resizing\", \"resizeEnd\", \"dragStart\", \"dragging\", \"dragEnd\"], [\"event\", \"\"], [\"class\", \"cal-resize-handle cal-resize-handle-before-start\", \"mwlResizeHandle\", \"\", 3, \"resizeEdges\", 4, \"ngIf\"], [3, \"locale\", \"weekEvent\", \"tooltipPlacement\", \"tooltipTemplate\", \"tooltipAppendToBody\", \"tooltipDelay\", \"customTemplate\", \"eventTitleTemplate\", \"eventActionsTemplate\", \"daysInWeek\", \"eventClicked\"], [\"class\", \"cal-resize-handle cal-resize-handle-after-end\", \"mwlResizeHandle\", \"\", 3, \"resizeEdges\", 4, \"ngIf\"], [\"mwlResizeHandle\", \"\", 1, \"cal-resize-handle\", \"cal-resize-handle-before-start\", 3, \"resizeEdges\"], [\"mwlResizeHandle\", \"\", 1, \"cal-resize-handle\", \"cal-resize-handle-after-end\", 3, \"resizeEdges\"], [\"class\", \"cal-hour\", 3, \"cal-hour-odd\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"cal-hour\"], [3, \"height\", \"segment\", \"segmentHeight\", \"locale\", \"customTemplate\", \"isTimeLabel\", \"daysInWeek\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"segment\", \"segmentHeight\", \"locale\", \"customTemplate\", \"isTimeLabel\", \"daysInWeek\"], [1, \"cal-day-column\"], [3, \"columnDate\", \"dayStartHour\", \"dayStartMinute\", \"dayEndHour\", \"dayEndMinute\", \"hourSegments\", \"hourDuration\", \"hourSegmentHeight\", \"customTemplate\"], [1, \"cal-events-container\"], [\"class\", \"cal-event-container\", \"mwlResizable\", \"\", \"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 3, \"cal-draggable\", \"cal-starts-within-day\", \"cal-ends-within-day\", \"ngClass\", \"hidden\", \"top\", \"height\", \"left\", \"width\", \"resizeCursors\", \"resizeSnapGrid\", \"validateResize\", \"allowNegativeResizes\", \"dropData\", \"dragAxis\", \"dragSnapGrid\", \"touchStartLongPress\", \"ghostDragEnabled\", \"ghostElementTemplate\", \"validateDrag\", \"resizeStart\", \"resizing\", \"resizeEnd\", \"dragStart\", \"dragging\", \"dragEnd\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlResizable\", \"\", \"mwlDraggable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 1, \"cal-event-container\", 3, \"ngClass\", \"hidden\", \"resizeCursors\", \"resizeSnapGrid\", \"validateResize\", \"allowNegativeResizes\", \"dropData\", \"dragAxis\", \"dragSnapGrid\", \"touchStartLongPress\", \"ghostDragEnabled\", \"ghostElementTemplate\", \"validateDrag\", \"resizeStart\", \"resizing\", \"resizeEnd\", \"dragStart\", \"dragging\", \"dragEnd\"], [3, \"ngTemplateOutlet\"], [\"weekEventTemplate\", \"\"], [3, \"locale\", \"weekEvent\", \"tooltipPlacement\", \"tooltipTemplate\", \"tooltipAppendToBody\", \"tooltipDisabled\", \"tooltipDelay\", \"customTemplate\", \"eventTitleTemplate\", \"eventActionsTemplate\", \"column\", \"daysInWeek\", \"eventClicked\"], [\"mwlDroppable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 3, \"height\", \"segment\", \"segmentHeight\", \"locale\", \"customTemplate\", \"daysInWeek\", \"clickListenerDisabled\", \"dragOverClass\", \"isTimeLabel\", \"mwlClick\", \"drop\", \"dragEnter\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"mwlDroppable\", \"\", \"dragActiveClass\", \"cal-drag-active\", 3, \"segment\", \"segmentHeight\", \"locale\", \"customTemplate\", \"daysInWeek\", \"clickListenerDisabled\", \"dragOverClass\", \"isTimeLabel\", \"mwlClick\", \"drop\", \"dragEnter\"]],\n    template: function CalendarWeekViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"mwl-calendar-week-view-header\", 1);\n        i0.ɵɵlistener(\"dayHeaderClicked\", function CalendarWeekViewComponent_Template_mwl_calendar_week_view_header_dayHeaderClicked_1_listener($event) {\n          return ctx.dayHeaderClicked.emit($event);\n        })(\"eventDropped\", function CalendarWeekViewComponent_Template_mwl_calendar_week_view_header_eventDropped_1_listener($event) {\n          return ctx.eventDropped({\n            dropData: $event\n          }, $event.newStart, true);\n        })(\"dragEnter\", function CalendarWeekViewComponent_Template_mwl_calendar_week_view_header_dragEnter_1_listener($event) {\n          return ctx.dateDragEnter($event.date);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(2, CalendarWeekViewComponent_div_2_Template, 7, 5, \"div\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵlistener(\"dragEnter\", function CalendarWeekViewComponent_Template_div_dragEnter_3_listener() {\n          return ctx.dragEnter(\"time\");\n        })(\"dragLeave\", function CalendarWeekViewComponent_Template_div_dragLeave_3_listener() {\n          return ctx.dragLeave(\"time\");\n        });\n        i0.ɵɵtemplate(4, CalendarWeekViewComponent_div_4_Template, 2, 2, \"div\", 4);\n        i0.ɵɵelementStart(5, \"div\", 5, 6);\n        i0.ɵɵtemplate(7, CalendarWeekViewComponent_div_7_Template, 5, 13, \"div\", 7);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"days\", ctx.days)(\"locale\", ctx.locale)(\"customTemplate\", ctx.headerTemplate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.view.allDayEventRows.length > 0);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.view.hourColumns.length > 0 && ctx.daysInWeek !== 1);\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"cal-resize-active\", ctx.timeEventResizes.size > 0);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.view.hourColumns)(\"ngForTrackBy\", ctx.trackByHourColumn);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i4.ResizableDirective, i4.ResizeHandleDirective, i2.DraggableDirective, i2.DroppableDirective, ClickDirective, CalendarWeekViewHeaderComponent, CalendarWeekViewEventComponent, CalendarWeekViewHourSegmentComponent, CalendarWeekViewCurrentTimeMarkerComponent],\n    encapsulation: 2\n  });\n  return CalendarWeekViewComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CalendarWeekModule = /*#__PURE__*/(() => {\n  class CalendarWeekModule {}\n  CalendarWeekModule.ɵfac = function CalendarWeekModule_Factory(t) {\n    return new (t || CalendarWeekModule)();\n  };\n  CalendarWeekModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CalendarWeekModule\n  });\n  CalendarWeekModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, ResizableModule, DragAndDropModule, CalendarCommonModule, ResizableModule, DragAndDropModule]\n  });\n  return CalendarWeekModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Shows all events on a given day. Example usage:\n *\n * ```typescript\n * <mwl-calendar-day-view\n *  [viewDate]=\"viewDate\"\n *  [events]=\"events\">\n * </mwl-calendar-day-view>\n * ```\n */\nlet CalendarDayViewComponent = /*#__PURE__*/(() => {\n  class CalendarDayViewComponent {\n    constructor() {\n      /**\n       * An array of events to display on view\n       * The schema is available here: https://github.com/mattlewis92/calendar-utils/blob/c51689985f59a271940e30bc4e2c4e1fee3fcb5c/src/calendarUtils.ts#L49-L63\n       */\n      this.events = [];\n      /**\n       * The number of segments in an hour. Must divide equally into 60.\n       */\n      this.hourSegments = 2;\n      /**\n       * The height in pixels of each hour segment\n       */\n      this.hourSegmentHeight = 30;\n      /**\n       * The minimum height in pixels of each event\n       */\n      this.minimumEventHeight = 30;\n      /**\n       * The day start hours in 24 hour time. Must be 0-23\n       */\n      this.dayStartHour = 0;\n      /**\n       * The day start minutes. Must be 0-59\n       */\n      this.dayStartMinute = 0;\n      /**\n       * The day end hours in 24 hour time. Must be 0-23\n       */\n      this.dayEndHour = 23;\n      /**\n       * The day end minutes. Must be 0-59\n       */\n      this.dayEndMinute = 59;\n      /**\n       * The placement of the event tooltip\n       */\n      this.tooltipPlacement = 'auto';\n      /**\n       * Whether to append tooltips to the body or next to the trigger element\n       */\n      this.tooltipAppendToBody = true;\n      /**\n       * The delay in milliseconds before the tooltip should be displayed. If not provided the tooltip\n       * will be displayed immediately.\n       */\n      this.tooltipDelay = null;\n      /**\n       * Whether to snap events to a grid when dragging\n       */\n      this.snapDraggedEvents = true;\n      /**\n       * Called when an event title is clicked\n       */\n      this.eventClicked = new EventEmitter();\n      /**\n       * Called when an hour segment is clicked\n       */\n      this.hourSegmentClicked = new EventEmitter();\n      /**\n       * Called when an event is resized or dragged and dropped\n       */\n      this.eventTimesChanged = new EventEmitter();\n      /**\n       * An output that will be called before the view is rendered for the current day.\n       * If you add the `cssClass` property to an hour grid segment it will add that class to the hour segment in the template\n       */\n      this.beforeViewRender = new EventEmitter();\n    }\n  }\n  CalendarDayViewComponent.ɵfac = function CalendarDayViewComponent_Factory(t) {\n    return new (t || CalendarDayViewComponent)();\n  };\n  CalendarDayViewComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CalendarDayViewComponent,\n    selectors: [[\"mwl-calendar-day-view\"]],\n    inputs: {\n      viewDate: \"viewDate\",\n      events: \"events\",\n      hourSegments: \"hourSegments\",\n      hourSegmentHeight: \"hourSegmentHeight\",\n      hourDuration: \"hourDuration\",\n      minimumEventHeight: \"minimumEventHeight\",\n      dayStartHour: \"dayStartHour\",\n      dayStartMinute: \"dayStartMinute\",\n      dayEndHour: \"dayEndHour\",\n      dayEndMinute: \"dayEndMinute\",\n      refresh: \"refresh\",\n      locale: \"locale\",\n      eventSnapSize: \"eventSnapSize\",\n      tooltipPlacement: \"tooltipPlacement\",\n      tooltipTemplate: \"tooltipTemplate\",\n      tooltipAppendToBody: \"tooltipAppendToBody\",\n      tooltipDelay: \"tooltipDelay\",\n      hourSegmentTemplate: \"hourSegmentTemplate\",\n      eventTemplate: \"eventTemplate\",\n      eventTitleTemplate: \"eventTitleTemplate\",\n      eventActionsTemplate: \"eventActionsTemplate\",\n      snapDraggedEvents: \"snapDraggedEvents\",\n      allDayEventsLabelTemplate: \"allDayEventsLabelTemplate\",\n      currentTimeMarkerTemplate: \"currentTimeMarkerTemplate\",\n      validateEventTimesChanged: \"validateEventTimesChanged\",\n      resizeCursors: \"resizeCursors\"\n    },\n    outputs: {\n      eventClicked: \"eventClicked\",\n      hourSegmentClicked: \"hourSegmentClicked\",\n      eventTimesChanged: \"eventTimesChanged\",\n      beforeViewRender: \"beforeViewRender\"\n    },\n    decls: 1,\n    vars: 27,\n    consts: [[1, \"cal-day-view\", 3, \"daysInWeek\", \"viewDate\", \"events\", \"hourSegments\", \"hourDuration\", \"hourSegmentHeight\", \"minimumEventHeight\", \"dayStartHour\", \"dayStartMinute\", \"dayEndHour\", \"dayEndMinute\", \"refresh\", \"locale\", \"eventSnapSize\", \"tooltipPlacement\", \"tooltipTemplate\", \"tooltipAppendToBody\", \"tooltipDelay\", \"resizeCursors\", \"hourSegmentTemplate\", \"eventTemplate\", \"eventTitleTemplate\", \"eventActionsTemplate\", \"snapDraggedEvents\", \"allDayEventsLabelTemplate\", \"currentTimeMarkerTemplate\", \"validateEventTimesChanged\", \"eventClicked\", \"hourSegmentClicked\", \"eventTimesChanged\", \"beforeViewRender\"]],\n    template: function CalendarDayViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"mwl-calendar-week-view\", 0);\n        i0.ɵɵlistener(\"eventClicked\", function CalendarDayViewComponent_Template_mwl_calendar_week_view_eventClicked_0_listener($event) {\n          return ctx.eventClicked.emit($event);\n        })(\"hourSegmentClicked\", function CalendarDayViewComponent_Template_mwl_calendar_week_view_hourSegmentClicked_0_listener($event) {\n          return ctx.hourSegmentClicked.emit($event);\n        })(\"eventTimesChanged\", function CalendarDayViewComponent_Template_mwl_calendar_week_view_eventTimesChanged_0_listener($event) {\n          return ctx.eventTimesChanged.emit($event);\n        })(\"beforeViewRender\", function CalendarDayViewComponent_Template_mwl_calendar_week_view_beforeViewRender_0_listener($event) {\n          return ctx.beforeViewRender.emit($event);\n        });\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"daysInWeek\", 1)(\"viewDate\", ctx.viewDate)(\"events\", ctx.events)(\"hourSegments\", ctx.hourSegments)(\"hourDuration\", ctx.hourDuration)(\"hourSegmentHeight\", ctx.hourSegmentHeight)(\"minimumEventHeight\", ctx.minimumEventHeight)(\"dayStartHour\", ctx.dayStartHour)(\"dayStartMinute\", ctx.dayStartMinute)(\"dayEndHour\", ctx.dayEndHour)(\"dayEndMinute\", ctx.dayEndMinute)(\"refresh\", ctx.refresh)(\"locale\", ctx.locale)(\"eventSnapSize\", ctx.eventSnapSize)(\"tooltipPlacement\", ctx.tooltipPlacement)(\"tooltipTemplate\", ctx.tooltipTemplate)(\"tooltipAppendToBody\", ctx.tooltipAppendToBody)(\"tooltipDelay\", ctx.tooltipDelay)(\"resizeCursors\", ctx.resizeCursors)(\"hourSegmentTemplate\", ctx.hourSegmentTemplate)(\"eventTemplate\", ctx.eventTemplate)(\"eventTitleTemplate\", ctx.eventTitleTemplate)(\"eventActionsTemplate\", ctx.eventActionsTemplate)(\"snapDraggedEvents\", ctx.snapDraggedEvents)(\"allDayEventsLabelTemplate\", ctx.allDayEventsLabelTemplate)(\"currentTimeMarkerTemplate\", ctx.currentTimeMarkerTemplate)(\"validateEventTimesChanged\", ctx.validateEventTimesChanged);\n      }\n    },\n    dependencies: [CalendarWeekViewComponent],\n    encapsulation: 2\n  });\n  return CalendarDayViewComponent;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CalendarDayModule = /*#__PURE__*/(() => {\n  class CalendarDayModule {}\n  CalendarDayModule.ɵfac = function CalendarDayModule_Factory(t) {\n    return new (t || CalendarDayModule)();\n  };\n  CalendarDayModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CalendarDayModule\n  });\n  CalendarDayModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, CalendarCommonModule, CalendarWeekModule]\n  });\n  return CalendarDayModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * The main module of this library. Example usage:\n *\n * ```typescript\n * import { CalenderModule } from 'angular-calendar';\n *\n * @NgModule({\n *   imports: [\n *     CalenderModule.forRoot()\n *   ]\n * })\n * class MyModule {}\n * ```\n *\n */\nlet CalendarModule = /*#__PURE__*/(() => {\n  class CalendarModule {\n    static forRoot(dateAdapter, config = {}) {\n      return {\n        ngModule: CalendarModule,\n        providers: [dateAdapter, config.eventTitleFormatter || CalendarEventTitleFormatter, config.dateFormatter || CalendarDateFormatter, config.utils || CalendarUtils, config.a11y || CalendarA11y]\n      };\n    }\n  }\n  CalendarModule.ɵfac = function CalendarModule_Factory(t) {\n    return new (t || CalendarModule)();\n  };\n  CalendarModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: CalendarModule\n  });\n  CalendarModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CalendarCommonModule, CalendarMonthModule, CalendarWeekModule, CalendarDayModule, CalendarCommonModule, CalendarMonthModule, CalendarWeekModule, CalendarDayModule]\n  });\n  return CalendarModule;\n})();\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/*\n * Public API Surface of angular-calendar\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CalendarA11y, CalendarAngularDateFormatter, CalendarCommonModule, CalendarDateFormatter, CalendarDayModule, CalendarDayViewComponent, CalendarEventTimesChangedEventType, CalendarEventTitleFormatter, CalendarModule, CalendarMomentDateFormatter, CalendarMonthModule, CalendarMonthViewComponent, CalendarNativeDateFormatter, CalendarUtils, CalendarView, CalendarWeekModule, CalendarWeekViewComponent, DateAdapter, MOMENT, collapseAnimation, getWeekViewPeriod, CalendarA11yPipe as ɵCalendarA11yPipe, CalendarDatePipe as ɵCalendarDatePipe, CalendarEventActionsComponent as ɵCalendarEventActionsComponent, CalendarEventTitleComponent as ɵCalendarEventTitleComponent, CalendarEventTitlePipe as ɵCalendarEventTitlePipe, CalendarMonthCellComponent as ɵCalendarMonthCellComponent, CalendarMonthViewHeaderComponent as ɵCalendarMonthViewHeaderComponent, CalendarNextViewDirective as ɵCalendarNextViewDirective, CalendarOpenDayEventsComponent as ɵCalendarOpenDayEventsComponent, CalendarPreviousViewDirective as ɵCalendarPreviousViewDirective, CalendarTodayDirective as ɵCalendarTodayDirective, CalendarTooltipDirective as ɵCalendarTooltipDirective, CalendarTooltipWindowComponent as ɵCalendarTooltipWindowComponent, CalendarWeekViewCurrentTimeMarkerComponent as ɵCalendarWeekViewCurrentTimeMarkerComponent, CalendarWeekViewEventComponent as ɵCalendarWeekViewEventComponent, CalendarWeekViewHeaderComponent as ɵCalendarWeekViewHeaderComponent, CalendarWeekViewHourSegmentComponent as ɵCalendarWeekViewHourSegmentComponent, ClickDirective as ɵClickDirective, KeydownEnterDirective as ɵKeydownEnterDirective };\n//# sourceMappingURL=angular-calendar.mjs.map\n//# sourceMappingURL=angular-calendar.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}