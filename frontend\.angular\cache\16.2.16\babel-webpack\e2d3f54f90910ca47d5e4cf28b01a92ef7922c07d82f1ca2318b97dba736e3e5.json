{"ast": null, "code": "import \"../../utilities/globals/index.js\";\nexport { empty } from \"./empty.js\";\nexport { from } from \"./from.js\";\nexport { split } from \"./split.js\";\nexport { concat } from \"./concat.js\";\nexport { execute } from \"./execute.js\";\nexport { ApolloLink } from \"./ApolloLink.js\";\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}