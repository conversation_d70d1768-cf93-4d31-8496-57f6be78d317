{"ast": null, "code": "import { startOfISOWeekYear } from \"./startOfISOWeekYear.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link isSameISOWeekYear} function options.\n */\n\n/**\n * @name isSameISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Are the given dates in the same ISO week-numbering year?\n *\n * @description\n * Are the given dates in the same ISO week-numbering year?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same ISO week-numbering year\n *\n * @example\n * // Are 29 December 2003 and 2 January 2005 in the same ISO week-numbering year?\n * const result = isSameISOWeekYear(new Date(2003, 11, 29), new Date(2005, 0, 2))\n * //=> true\n */\nexport function isSameISOWeekYear(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  return +startOfISOWeekYear(laterDate_) === +startOfISOWeekYear(earlierDate_);\n}\n\n// Fallback for modularized imports:\nexport default isSameISOWeekYear;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}