{"ast": null, "code": "import { invariant } from \"../../utilities/globals/index.js\";\nexport function toPromise(observable) {\n  var completed = false;\n  return new Promise(function (resolve, reject) {\n    observable.subscribe({\n      next: function (data) {\n        if (completed) {\n          globalThis.__DEV__ !== false && invariant.warn(45);\n        } else {\n          completed = true;\n          resolve(data);\n        }\n      },\n      error: reject\n    });\n  });\n}\n//# sourceMappingURL=toPromise.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}