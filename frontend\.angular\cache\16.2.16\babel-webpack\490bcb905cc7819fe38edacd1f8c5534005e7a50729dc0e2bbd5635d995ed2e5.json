{"ast": null, "code": "import { Observable } from \"zen-observable-ts\";\n// This simplified polyfill attempts to follow the ECMAScript Observable\n// proposal (https://github.com/zenparsing/es-observable)\nimport \"symbol-observable\";\n// The zen-observable package defines Observable.prototype[Symbol.observable]\n// when Symbol is supported, but RxJS interop depends on also setting this fake\n// '@@observable' string as a polyfill for Symbol.observable.\nvar prototype = Observable.prototype;\nvar fakeObsSymbol = \"@@observable\";\nif (!prototype[fakeObsSymbol]) {\n  // @ts-expect-error\n  prototype[fakeObsSymbol] = function () {\n    return this;\n  };\n}\nexport { Observable };\n//# sourceMappingURL=Observable.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}