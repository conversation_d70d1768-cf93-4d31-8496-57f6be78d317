{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction SignupComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"div\", 38);\n    i0.ɵɵelement(3, \"i\", 39)(4, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 41)(6, \"p\", 42);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction SignupComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 37)(2, \"div\", 44);\n    i0.ɵɵelement(3, \"i\", 45)(4, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 41)(6, \"p\", 47);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.message, \" \");\n  }\n}\nexport let SignupComponent = /*#__PURE__*/(() => {\n  class SignupComponent {\n    constructor(fb, authService, router) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.message = '';\n      this.error = '';\n      this.submittedEmail = '';\n      this.signupForm = this.fb.group({\n        fullName: ['', Validators.required],\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', Validators.required]\n      });\n    }\n    onSignupSubmit() {\n      if (this.signupForm.invalid) return;\n      const signupData = this.signupForm.value;\n      this.submittedEmail = signupData.email;\n      this.authService.signup(signupData).subscribe({\n        next: res => {\n          console.log('Signup successful:', res);\n          this.message = res.message;\n          this.error = '';\n          // Attendre un court instant avant de rediriger\n          setTimeout(() => {\n            // Rediriger vers le composant de vérification d'email\n            this.router.navigate(['/verify-email'], {\n              queryParams: {\n                email: this.submittedEmail\n              }\n            });\n          }, 500);\n        },\n        error: err => {\n          console.error('Signup error:', err);\n          // Si l'utilisateur existe déjà mais que nous avons besoin de vérifier l'email\n          if (err.error && err.error.message === 'Email already exists' && err.error.needsVerification) {\n            // Rediriger vers la vérification d'email\n            this.router.navigate(['/verify-email'], {\n              queryParams: {\n                email: this.submittedEmail\n              }\n            });\n            return;\n          }\n          // Gérer les autres erreurs\n          this.error = err.error?.message || 'Signup failed';\n          this.message = '';\n        }\n      });\n    }\n    static {\n      this.ɵfac = function SignupComponent_Factory(t) {\n        return new (t || SignupComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SignupComponent,\n        selectors: [[\"app-signup\"]],\n        decls: 65,\n        vars: 3,\n        consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"flex\", \"items-center\", \"justify-center\", \"p-4\", \"relative\", \"futuristic-layout\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"max-w-md\", \"relative\", \"z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"p-6\", \"text-center\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"p-6\"], [1, \"space-y-5\", 3, \"formGroup\", \"ngSubmit\"], [1, \"group\"], [1, \"flex\", \"items-center\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-user\", \"mr-1.5\", \"text-xs\"], [1, \"relative\"], [\"formControlName\", \"fullName\", \"placeholder\", \"John Doe\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [1, \"fas\", \"fa-envelope\", \"mr-1.5\", \"text-xs\"], [\"formControlName\", \"email\", \"type\", \"email\", \"placeholder\", \"<EMAIL>\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"fas\", \"fa-lock\", \"mr-1.5\", \"text-xs\"], [\"formControlName\", \"password\", \"type\", \"password\", \"placeholder\", \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"w-full\", \"relative\", \"overflow-hidden\", \"group\", \"mt-6\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"font-medium\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"transition-all\", \"z-10\"], [1, \"fas\", \"fa-user-plus\", \"mr-2\"], [1, \"text-center\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"space-y-2\", \"pt-4\"], [\"routerLink\", \"/login\", 1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"transition-colors\", \"font-medium\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"flex-1\"], [1, \"text-xs\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"]],\n        template: function SignupComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n            i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n            i0.ɵɵelementStart(21, \"div\", 11)(22, \"h1\", 12);\n            i0.ɵɵtext(23, \" Create Account \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"p\", 13);\n            i0.ɵɵtext(25, \" Sign up to join DevBridge \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 14)(27, \"form\", 15);\n            i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_27_listener() {\n              return ctx.onSignupSubmit();\n            });\n            i0.ɵɵelementStart(28, \"div\", 16)(29, \"label\", 17);\n            i0.ɵɵelement(30, \"i\", 18);\n            i0.ɵɵtext(31, \" Full Name \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"div\", 19);\n            i0.ɵɵelement(33, \"input\", 20);\n            i0.ɵɵelementStart(34, \"div\", 21);\n            i0.ɵɵelement(35, \"div\", 22);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(36, \"div\", 16)(37, \"label\", 17);\n            i0.ɵɵelement(38, \"i\", 23);\n            i0.ɵɵtext(39, \" Email \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"div\", 19);\n            i0.ɵɵelement(41, \"input\", 24);\n            i0.ɵɵelementStart(42, \"div\", 21);\n            i0.ɵɵelement(43, \"div\", 22);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(44, \"div\", 16)(45, \"label\", 17);\n            i0.ɵɵelement(46, \"i\", 25);\n            i0.ɵɵtext(47, \" Password \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"div\", 19);\n            i0.ɵɵelement(49, \"input\", 26);\n            i0.ɵɵelementStart(50, \"div\", 21);\n            i0.ɵɵelement(51, \"div\", 22);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(52, SignupComponent_div_52_Template, 8, 1, \"div\", 27);\n            i0.ɵɵtemplate(53, SignupComponent_div_53_Template, 8, 1, \"div\", 28);\n            i0.ɵɵelementStart(54, \"button\", 29);\n            i0.ɵɵelement(55, \"div\", 30)(56, \"div\", 31);\n            i0.ɵɵelementStart(57, \"span\", 32);\n            i0.ɵɵelement(58, \"i\", 33);\n            i0.ɵɵtext(59, \" Sign Up \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(60, \"div\", 34)(61, \"div\");\n            i0.ɵɵtext(62, \" Already have an account? \");\n            i0.ɵɵelementStart(63, \"a\", 35);\n            i0.ɵɵtext(64, \" Sign in \");\n            i0.ɵɵelementEnd()()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(27);\n            i0.ɵɵproperty(\"formGroup\", ctx.signupForm);\n            i0.ɵɵadvance(25);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.message);\n          }\n        },\n        dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink]\n      });\n    }\n  }\n  return SignupComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}