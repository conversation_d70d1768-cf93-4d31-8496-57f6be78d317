{"ast": null, "code": "export var selectURI = function (operation, fallbackURI) {\n  var context = operation.getContext();\n  var contextURI = context.uri;\n  if (contextURI) {\n    return contextURI;\n  } else if (typeof fallbackURI === \"function\") {\n    return fallbackURI(operation);\n  } else {\n    return fallbackURI || \"/graphql\";\n  }\n};\n//# sourceMappingURL=selectURI.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}