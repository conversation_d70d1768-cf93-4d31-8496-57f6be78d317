{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let NotificationService = /*#__PURE__*/(() => {\n  class NotificationService {\n    constructor() {\n      this.notificationSubject = new BehaviorSubject(null);\n    }\n    getNotifications() {\n      return this.notificationSubject.asObservable();\n    }\n    showSuccess(message, timeout = 5000) {\n      this.show({\n        message,\n        type: 'success',\n        timeout\n      });\n    }\n    showError(message, timeout = 5000) {\n      this.show({\n        message,\n        type: 'error',\n        timeout\n      });\n    }\n    showInfo(message, timeout = 5000) {\n      this.show({\n        message,\n        type: 'info',\n        timeout\n      });\n    }\n    showWarning(message, timeout = 5000) {\n      this.show({\n        message,\n        type: 'warning',\n        timeout\n      });\n    }\n    show(notification) {\n      this.notificationSubject.next(notification);\n      if (notification.timeout) {\n        setTimeout(() => {\n          // Effacer la notification seulement si c'est toujours la même\n          if (this.notificationSubject.value === notification) {\n            this.notificationSubject.next(null);\n          }\n        }, notification.timeout);\n      }\n    }\n    clear() {\n      this.notificationSubject.next(null);\n    }\n    static {\n      this.ɵfac = function NotificationService_Factory(t) {\n        return new (t || NotificationService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: NotificationService,\n        factory: NotificationService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return NotificationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}