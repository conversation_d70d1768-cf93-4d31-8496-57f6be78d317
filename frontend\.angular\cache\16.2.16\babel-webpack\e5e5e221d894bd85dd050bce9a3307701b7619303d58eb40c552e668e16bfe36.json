{"ast": null, "code": "import { __assign, __awaiter, __generator } from \"tslib\";\nimport { invariant, newInvariantError } from \"../utilities/globals/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { execute } from \"../link/core/index.js\";\nimport { addNonReactiveToNamedFragments, hasDirectives, isExecutionPatchIncrementalResult, isExecutionPatchResult, isFullyUnmaskedOperation, removeDirectivesFromDocument } from \"../utilities/index.js\";\nimport { canonicalStringify } from \"../cache/index.js\";\nimport { getDefaultValues, getOperationDefinition, getOperationName, hasClientExports, graphQLResultHasError, getGraphQLErrorsFromResult, Observable, asyncMap, isNonEmptyArray, Concast, makeUniqueId, isDocumentNode, isNonNullObject, DocumentTransform } from \"../utilities/index.js\";\nimport { mergeIncrementalData } from \"../utilities/common/incrementalResult.js\";\nimport { ApolloError, isApolloError, graphQLResultHasProtocolErrors } from \"../errors/index.js\";\nimport { ObservableQuery, logMissingFieldErrors } from \"./ObservableQuery.js\";\nimport { NetworkStatus, isNetworkRequestInFlight } from \"./networkStatus.js\";\nimport { QueryInfo, shouldWriteResult } from \"./QueryInfo.js\";\nimport { PROTOCOL_ERRORS_SYMBOL } from \"../errors/index.js\";\nimport { print } from \"../utilities/index.js\";\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar IGNORE = Object.create(null);\nimport { Trie } from \"@wry/trie\";\nimport { AutoCleanedWeakCache, cacheSizes } from \"../utilities/index.js\";\nimport { maskFragment, maskOperation } from \"../masking/index.js\";\nvar QueryManager = /** @class */function () {\n  function QueryManager(options) {\n    var _this = this;\n    this.clientAwareness = {};\n    // All the queries that the QueryManager is currently managing (not\n    // including mutations and subscriptions).\n    this.queries = new Map();\n    // Maps from queryId strings to Promise rejection functions for\n    // currently active queries and fetches.\n    // Use protected instead of private field so\n    // @apollo/experimental-nextjs-app-support can access type info.\n    this.fetchCancelFns = new Map();\n    this.transformCache = new AutoCleanedWeakCache(cacheSizes[\"queryManager.getDocumentInfo\"] || 2000 /* defaultCacheSizes[\"queryManager.getDocumentInfo\"] */);\n    this.queryIdCounter = 1;\n    this.requestIdCounter = 1;\n    this.mutationIdCounter = 1;\n    // Use protected instead of private field so\n    // @apollo/experimental-nextjs-app-support can access type info.\n    this.inFlightLinkObservables = new Trie(false);\n    this.noCacheWarningsByQueryId = new Set();\n    var defaultDocumentTransform = new DocumentTransform(function (document) {\n      return _this.cache.transformDocument(document);\n    },\n    // Allow the apollo cache to manage its own transform caches\n    {\n      cache: false\n    });\n    this.cache = options.cache;\n    this.link = options.link;\n    this.defaultOptions = options.defaultOptions;\n    this.queryDeduplication = options.queryDeduplication;\n    this.clientAwareness = options.clientAwareness;\n    this.localState = options.localState;\n    this.ssrMode = options.ssrMode;\n    this.assumeImmutableResults = options.assumeImmutableResults;\n    this.dataMasking = options.dataMasking;\n    var documentTransform = options.documentTransform;\n    this.documentTransform = documentTransform ? defaultDocumentTransform.concat(documentTransform)\n    // The custom document transform may add new fragment spreads or new\n    // field selections, so we want to give the cache a chance to run\n    // again. For example, the InMemoryCache adds __typename to field\n    // selections and fragments from the fragment registry.\n    .concat(defaultDocumentTransform) : defaultDocumentTransform;\n    this.defaultContext = options.defaultContext || Object.create(null);\n    if (this.onBroadcast = options.onBroadcast) {\n      this.mutationStore = Object.create(null);\n    }\n  }\n  /**\n   * Call this method to terminate any active query processes, making it safe\n   * to dispose of this QueryManager instance.\n   */\n  QueryManager.prototype.stop = function () {\n    var _this = this;\n    this.queries.forEach(function (_info, queryId) {\n      _this.stopQueryNoBroadcast(queryId);\n    });\n    this.cancelPendingFetches(newInvariantError(27));\n  };\n  QueryManager.prototype.cancelPendingFetches = function (error) {\n    this.fetchCancelFns.forEach(function (cancel) {\n      return cancel(error);\n    });\n    this.fetchCancelFns.clear();\n  };\n  QueryManager.prototype.mutate = function (_a) {\n    return __awaiter(this, arguments, void 0, function (_b) {\n      var mutationId, hasClientExports, mutationStoreValue, isOptimistic, self;\n      var _c, _d;\n      var mutation = _b.mutation,\n        variables = _b.variables,\n        optimisticResponse = _b.optimisticResponse,\n        updateQueries = _b.updateQueries,\n        _e = _b.refetchQueries,\n        refetchQueries = _e === void 0 ? [] : _e,\n        _f = _b.awaitRefetchQueries,\n        awaitRefetchQueries = _f === void 0 ? false : _f,\n        updateWithProxyFn = _b.update,\n        onQueryUpdated = _b.onQueryUpdated,\n        _g = _b.fetchPolicy,\n        fetchPolicy = _g === void 0 ? ((_c = this.defaultOptions.mutate) === null || _c === void 0 ? void 0 : _c.fetchPolicy) || \"network-only\" : _g,\n        _h = _b.errorPolicy,\n        errorPolicy = _h === void 0 ? ((_d = this.defaultOptions.mutate) === null || _d === void 0 ? void 0 : _d.errorPolicy) || \"none\" : _h,\n        keepRootFields = _b.keepRootFields,\n        context = _b.context;\n      return __generator(this, function (_j) {\n        switch (_j.label) {\n          case 0:\n            invariant(mutation, 28);\n            invariant(fetchPolicy === \"network-only\" || fetchPolicy === \"no-cache\", 29);\n            mutationId = this.generateMutationId();\n            mutation = this.cache.transformForLink(this.transform(mutation));\n            hasClientExports = this.getDocumentInfo(mutation).hasClientExports;\n            variables = this.getVariables(mutation, variables);\n            if (!hasClientExports) return [3 /*break*/, 2];\n            return [4 /*yield*/, this.localState.addExportedVariables(mutation, variables, context)];\n          case 1:\n            variables = _j.sent();\n            _j.label = 2;\n          case 2:\n            mutationStoreValue = this.mutationStore && (this.mutationStore[mutationId] = {\n              mutation: mutation,\n              variables: variables,\n              loading: true,\n              error: null\n            });\n            isOptimistic = optimisticResponse && this.markMutationOptimistic(optimisticResponse, {\n              mutationId: mutationId,\n              document: mutation,\n              variables: variables,\n              fetchPolicy: fetchPolicy,\n              errorPolicy: errorPolicy,\n              context: context,\n              updateQueries: updateQueries,\n              update: updateWithProxyFn,\n              keepRootFields: keepRootFields\n            });\n            this.broadcastQueries();\n            self = this;\n            return [2 /*return*/, new Promise(function (resolve, reject) {\n              return asyncMap(self.getObservableFromLink(mutation, __assign(__assign({}, context), {\n                optimisticResponse: isOptimistic ? optimisticResponse : void 0\n              }), variables, {}, false), function (result) {\n                if (graphQLResultHasError(result) && errorPolicy === \"none\") {\n                  throw new ApolloError({\n                    graphQLErrors: getGraphQLErrorsFromResult(result)\n                  });\n                }\n                if (mutationStoreValue) {\n                  mutationStoreValue.loading = false;\n                  mutationStoreValue.error = null;\n                }\n                var storeResult = __assign({}, result);\n                if (typeof refetchQueries === \"function\") {\n                  refetchQueries = refetchQueries(storeResult);\n                }\n                if (errorPolicy === \"ignore\" && graphQLResultHasError(storeResult)) {\n                  delete storeResult.errors;\n                }\n                return self.markMutationResult({\n                  mutationId: mutationId,\n                  result: storeResult,\n                  document: mutation,\n                  variables: variables,\n                  fetchPolicy: fetchPolicy,\n                  errorPolicy: errorPolicy,\n                  context: context,\n                  update: updateWithProxyFn,\n                  updateQueries: updateQueries,\n                  awaitRefetchQueries: awaitRefetchQueries,\n                  refetchQueries: refetchQueries,\n                  removeOptimistic: isOptimistic ? mutationId : void 0,\n                  onQueryUpdated: onQueryUpdated,\n                  keepRootFields: keepRootFields\n                });\n              }).subscribe({\n                next: function (storeResult) {\n                  self.broadcastQueries();\n                  // Since mutations might receive multiple payloads from the\n                  // ApolloLink chain (e.g. when used with @defer),\n                  // we resolve with a SingleExecutionResult or after the final\n                  // ExecutionPatchResult has arrived and we have assembled the\n                  // multipart response into a single result.\n                  if (!(\"hasNext\" in storeResult) || storeResult.hasNext === false) {\n                    resolve(__assign(__assign({}, storeResult), {\n                      data: self.maskOperation({\n                        document: mutation,\n                        data: storeResult.data,\n                        fetchPolicy: fetchPolicy,\n                        id: mutationId\n                      })\n                    }));\n                  }\n                },\n                error: function (err) {\n                  if (mutationStoreValue) {\n                    mutationStoreValue.loading = false;\n                    mutationStoreValue.error = err;\n                  }\n                  if (isOptimistic) {\n                    self.cache.removeOptimistic(mutationId);\n                  }\n                  self.broadcastQueries();\n                  reject(err instanceof ApolloError ? err : new ApolloError({\n                    networkError: err\n                  }));\n                }\n              });\n            })];\n        }\n      });\n    });\n  };\n  QueryManager.prototype.markMutationResult = function (mutation, cache) {\n    var _this = this;\n    if (cache === void 0) {\n      cache = this.cache;\n    }\n    var result = mutation.result;\n    var cacheWrites = [];\n    var skipCache = mutation.fetchPolicy === \"no-cache\";\n    if (!skipCache && shouldWriteResult(result, mutation.errorPolicy)) {\n      if (!isExecutionPatchIncrementalResult(result)) {\n        cacheWrites.push({\n          result: result.data,\n          dataId: \"ROOT_MUTATION\",\n          query: mutation.document,\n          variables: mutation.variables\n        });\n      }\n      if (isExecutionPatchIncrementalResult(result) && isNonEmptyArray(result.incremental)) {\n        var diff = cache.diff({\n          id: \"ROOT_MUTATION\",\n          // The cache complains if passed a mutation where it expects a\n          // query, so we transform mutations and subscriptions to queries\n          // (only once, thanks to this.transformCache).\n          query: this.getDocumentInfo(mutation.document).asQuery,\n          variables: mutation.variables,\n          optimistic: false,\n          returnPartialData: true\n        });\n        var mergedData = void 0;\n        if (diff.result) {\n          mergedData = mergeIncrementalData(diff.result, result);\n        }\n        if (typeof mergedData !== \"undefined\") {\n          // cast the ExecutionPatchResult to FetchResult here since\n          // ExecutionPatchResult never has `data` when returned from the server\n          result.data = mergedData;\n          cacheWrites.push({\n            result: mergedData,\n            dataId: \"ROOT_MUTATION\",\n            query: mutation.document,\n            variables: mutation.variables\n          });\n        }\n      }\n      var updateQueries_1 = mutation.updateQueries;\n      if (updateQueries_1) {\n        this.queries.forEach(function (_a, queryId) {\n          var observableQuery = _a.observableQuery;\n          var queryName = observableQuery && observableQuery.queryName;\n          if (!queryName || !hasOwnProperty.call(updateQueries_1, queryName)) {\n            return;\n          }\n          var updater = updateQueries_1[queryName];\n          var _b = _this.queries.get(queryId),\n            document = _b.document,\n            variables = _b.variables;\n          // Read the current query result from the store.\n          var _c = cache.diff({\n              query: document,\n              variables: variables,\n              returnPartialData: true,\n              optimistic: false\n            }),\n            currentQueryResult = _c.result,\n            complete = _c.complete;\n          if (complete && currentQueryResult) {\n            // Run our reducer using the current query result and the mutation result.\n            var nextQueryResult = updater(currentQueryResult, {\n              mutationResult: result,\n              queryName: document && getOperationName(document) || void 0,\n              queryVariables: variables\n            });\n            // Write the modified result back into the store if we got a new result.\n            if (nextQueryResult) {\n              cacheWrites.push({\n                result: nextQueryResult,\n                dataId: \"ROOT_QUERY\",\n                query: document,\n                variables: variables\n              });\n            }\n          }\n        });\n      }\n    }\n    if (cacheWrites.length > 0 || (mutation.refetchQueries || \"\").length > 0 || mutation.update || mutation.onQueryUpdated || mutation.removeOptimistic) {\n      var results_1 = [];\n      this.refetchQueries({\n        updateCache: function (cache) {\n          if (!skipCache) {\n            cacheWrites.forEach(function (write) {\n              return cache.write(write);\n            });\n          }\n          // If the mutation has some writes associated with it then we need to\n          // apply those writes to the store by running this reducer again with\n          // a write action.\n          var update = mutation.update;\n          // Determine whether result is a SingleExecutionResult,\n          // or the final ExecutionPatchResult.\n          var isFinalResult = !isExecutionPatchResult(result) || isExecutionPatchIncrementalResult(result) && !result.hasNext;\n          if (update) {\n            if (!skipCache) {\n              // Re-read the ROOT_MUTATION data we just wrote into the cache\n              // (the first cache.write call in the cacheWrites.forEach loop\n              // above), so field read functions have a chance to run for\n              // fields within mutation result objects.\n              var diff = cache.diff({\n                id: \"ROOT_MUTATION\",\n                // The cache complains if passed a mutation where it expects a\n                // query, so we transform mutations and subscriptions to queries\n                // (only once, thanks to this.transformCache).\n                query: _this.getDocumentInfo(mutation.document).asQuery,\n                variables: mutation.variables,\n                optimistic: false,\n                returnPartialData: true\n              });\n              if (diff.complete) {\n                result = __assign(__assign({}, result), {\n                  data: diff.result\n                });\n                if (\"incremental\" in result) {\n                  delete result.incremental;\n                }\n                if (\"hasNext\" in result) {\n                  delete result.hasNext;\n                }\n              }\n            }\n            // If we've received the whole response,\n            // either a SingleExecutionResult or the final ExecutionPatchResult,\n            // call the update function.\n            if (isFinalResult) {\n              update(cache, result, {\n                context: mutation.context,\n                variables: mutation.variables\n              });\n            }\n          }\n          // TODO Do this with cache.evict({ id: 'ROOT_MUTATION' }) but make it\n          // shallow to allow rolling back optimistic evictions.\n          if (!skipCache && !mutation.keepRootFields && isFinalResult) {\n            cache.modify({\n              id: \"ROOT_MUTATION\",\n              fields: function (value, _a) {\n                var fieldName = _a.fieldName,\n                  DELETE = _a.DELETE;\n                return fieldName === \"__typename\" ? value : DELETE;\n              }\n            });\n          }\n        },\n        include: mutation.refetchQueries,\n        // Write the final mutation.result to the root layer of the cache.\n        optimistic: false,\n        // Remove the corresponding optimistic layer at the same time as we\n        // write the final non-optimistic result.\n        removeOptimistic: mutation.removeOptimistic,\n        // Let the caller of client.mutate optionally determine the refetching\n        // behavior for watched queries after the mutation.update function runs.\n        // If no onQueryUpdated function was provided for this mutation, pass\n        // null instead of undefined to disable the default refetching behavior.\n        onQueryUpdated: mutation.onQueryUpdated || null\n      }).forEach(function (result) {\n        return results_1.push(result);\n      });\n      if (mutation.awaitRefetchQueries || mutation.onQueryUpdated) {\n        // Returning a promise here makes the mutation await that promise, so we\n        // include results in that promise's work if awaitRefetchQueries or an\n        // onQueryUpdated function was specified.\n        return Promise.all(results_1).then(function () {\n          return result;\n        });\n      }\n    }\n    return Promise.resolve(result);\n  };\n  QueryManager.prototype.markMutationOptimistic = function (optimisticResponse, mutation) {\n    var _this = this;\n    var data = typeof optimisticResponse === \"function\" ? optimisticResponse(mutation.variables, {\n      IGNORE: IGNORE\n    }) : optimisticResponse;\n    if (data === IGNORE) {\n      return false;\n    }\n    this.cache.recordOptimisticTransaction(function (cache) {\n      try {\n        _this.markMutationResult(__assign(__assign({}, mutation), {\n          result: {\n            data: data\n          }\n        }), cache);\n      } catch (error) {\n        globalThis.__DEV__ !== false && invariant.error(error);\n      }\n    }, mutation.mutationId);\n    return true;\n  };\n  QueryManager.prototype.fetchQuery = function (queryId, options, networkStatus) {\n    return this.fetchConcastWithInfo(this.getOrCreateQuery(queryId), options, networkStatus).concast.promise;\n  };\n  QueryManager.prototype.getQueryStore = function () {\n    var store = Object.create(null);\n    this.queries.forEach(function (info, queryId) {\n      store[queryId] = {\n        variables: info.variables,\n        networkStatus: info.networkStatus,\n        networkError: info.networkError,\n        graphQLErrors: info.graphQLErrors\n      };\n    });\n    return store;\n  };\n  QueryManager.prototype.resetErrors = function (queryId) {\n    var queryInfo = this.queries.get(queryId);\n    if (queryInfo) {\n      queryInfo.networkError = undefined;\n      queryInfo.graphQLErrors = [];\n    }\n  };\n  QueryManager.prototype.transform = function (document) {\n    return this.documentTransform.transformDocument(document);\n  };\n  QueryManager.prototype.getDocumentInfo = function (document) {\n    var transformCache = this.transformCache;\n    if (!transformCache.has(document)) {\n      var cacheEntry = {\n        // TODO These three calls (hasClientExports, shouldForceResolvers, and\n        // usesNonreactiveDirective) are performing independent full traversals\n        // of the transformed document. We should consider merging these\n        // traversals into a single pass in the future, though the work is\n        // cached after the first time.\n        hasClientExports: hasClientExports(document),\n        hasForcedResolvers: this.localState.shouldForceResolvers(document),\n        hasNonreactiveDirective: hasDirectives([\"nonreactive\"], document),\n        nonReactiveQuery: addNonReactiveToNamedFragments(document),\n        clientQuery: this.localState.clientQuery(document),\n        serverQuery: removeDirectivesFromDocument([{\n          name: \"client\",\n          remove: true\n        }, {\n          name: \"connection\"\n        }, {\n          name: \"nonreactive\"\n        }, {\n          name: \"unmask\"\n        }], document),\n        defaultVars: getDefaultValues(getOperationDefinition(document)),\n        // Transform any mutation or subscription operations to query operations\n        // so we can read/write them from/to the cache.\n        asQuery: __assign(__assign({}, document), {\n          definitions: document.definitions.map(function (def) {\n            if (def.kind === \"OperationDefinition\" && def.operation !== \"query\") {\n              return __assign(__assign({}, def), {\n                operation: \"query\"\n              });\n            }\n            return def;\n          })\n        })\n      };\n      transformCache.set(document, cacheEntry);\n    }\n    return transformCache.get(document);\n  };\n  QueryManager.prototype.getVariables = function (document, variables) {\n    return __assign(__assign({}, this.getDocumentInfo(document).defaultVars), variables);\n  };\n  QueryManager.prototype.watchQuery = function (options) {\n    var query = this.transform(options.query);\n    // assign variable default values if supplied\n    // NOTE: We don't modify options.query here with the transformed query to\n    // ensure observable.options.query is set to the raw untransformed query.\n    options = __assign(__assign({}, options), {\n      variables: this.getVariables(query, options.variables)\n    });\n    if (typeof options.notifyOnNetworkStatusChange === \"undefined\") {\n      options.notifyOnNetworkStatusChange = false;\n    }\n    var queryInfo = new QueryInfo(this);\n    var observable = new ObservableQuery({\n      queryManager: this,\n      queryInfo: queryInfo,\n      options: options\n    });\n    observable[\"lastQuery\"] = query;\n    if (!ObservableQuery[\"inactiveOnCreation\"].getValue()) {\n      this.queries.set(observable.queryId, queryInfo);\n    }\n    // We give queryInfo the transformed query to ensure the first cache diff\n    // uses the transformed query instead of the raw query\n    queryInfo.init({\n      document: query,\n      observableQuery: observable,\n      variables: observable.variables\n    });\n    return observable;\n  };\n  QueryManager.prototype.query = function (options, queryId) {\n    var _this = this;\n    if (queryId === void 0) {\n      queryId = this.generateQueryId();\n    }\n    invariant(options.query, 30);\n    invariant(options.query.kind === \"Document\", 31);\n    invariant(!options.returnPartialData, 32);\n    invariant(!options.pollInterval, 33);\n    var query = this.transform(options.query);\n    return this.fetchQuery(queryId, __assign(__assign({}, options), {\n      query: query\n    })).then(function (result) {\n      return result && __assign(__assign({}, result), {\n        data: _this.maskOperation({\n          document: query,\n          data: result.data,\n          fetchPolicy: options.fetchPolicy,\n          id: queryId\n        })\n      });\n    }).finally(function () {\n      return _this.stopQuery(queryId);\n    });\n  };\n  QueryManager.prototype.generateQueryId = function () {\n    return String(this.queryIdCounter++);\n  };\n  QueryManager.prototype.generateRequestId = function () {\n    return this.requestIdCounter++;\n  };\n  QueryManager.prototype.generateMutationId = function () {\n    return String(this.mutationIdCounter++);\n  };\n  QueryManager.prototype.stopQueryInStore = function (queryId) {\n    this.stopQueryInStoreNoBroadcast(queryId);\n    this.broadcastQueries();\n  };\n  QueryManager.prototype.stopQueryInStoreNoBroadcast = function (queryId) {\n    var queryInfo = this.queries.get(queryId);\n    if (queryInfo) queryInfo.stop();\n  };\n  QueryManager.prototype.clearStore = function (options) {\n    if (options === void 0) {\n      options = {\n        discardWatches: true\n      };\n    }\n    // Before we have sent the reset action to the store, we can no longer\n    // rely on the results returned by in-flight requests since these may\n    // depend on values that previously existed in the data portion of the\n    // store. So, we cancel the promises and observers that we have issued\n    // so far and not yet resolved (in the case of queries).\n    this.cancelPendingFetches(newInvariantError(34));\n    this.queries.forEach(function (queryInfo) {\n      if (queryInfo.observableQuery) {\n        // Set loading to true so listeners don't trigger unless they want\n        // results with partial data.\n        queryInfo.networkStatus = NetworkStatus.loading;\n      } else {\n        queryInfo.stop();\n      }\n    });\n    if (this.mutationStore) {\n      this.mutationStore = Object.create(null);\n    }\n    // begin removing data from the store\n    return this.cache.reset(options);\n  };\n  QueryManager.prototype.getObservableQueries = function (include) {\n    var _this = this;\n    if (include === void 0) {\n      include = \"active\";\n    }\n    var queries = new Map();\n    var queryNames = new Map();\n    var queryNamesAndQueryStrings = new Map();\n    var legacyQueryOptions = new Set();\n    if (Array.isArray(include)) {\n      include.forEach(function (desc) {\n        if (typeof desc === \"string\") {\n          queryNames.set(desc, desc);\n          queryNamesAndQueryStrings.set(desc, false);\n        } else if (isDocumentNode(desc)) {\n          var queryString = print(_this.transform(desc));\n          queryNames.set(queryString, getOperationName(desc));\n          queryNamesAndQueryStrings.set(queryString, false);\n        } else if (isNonNullObject(desc) && desc.query) {\n          legacyQueryOptions.add(desc);\n        }\n      });\n    }\n    this.queries.forEach(function (_a, queryId) {\n      var oq = _a.observableQuery,\n        document = _a.document;\n      if (oq) {\n        if (include === \"all\") {\n          queries.set(queryId, oq);\n          return;\n        }\n        var queryName = oq.queryName,\n          fetchPolicy = oq.options.fetchPolicy;\n        if (fetchPolicy === \"standby\" || include === \"active\" && !oq.hasObservers()) {\n          return;\n        }\n        if (include === \"active\" || queryName && queryNamesAndQueryStrings.has(queryName) || document && queryNamesAndQueryStrings.has(print(document))) {\n          queries.set(queryId, oq);\n          if (queryName) queryNamesAndQueryStrings.set(queryName, true);\n          if (document) queryNamesAndQueryStrings.set(print(document), true);\n        }\n      }\n    });\n    if (legacyQueryOptions.size) {\n      legacyQueryOptions.forEach(function (options) {\n        // We will be issuing a fresh network request for this query, so we\n        // pre-allocate a new query ID here, using a special prefix to enable\n        // cleaning up these temporary queries later, after fetching.\n        var queryId = makeUniqueId(\"legacyOneTimeQuery\");\n        var queryInfo = _this.getOrCreateQuery(queryId).init({\n          document: options.query,\n          variables: options.variables\n        });\n        var oq = new ObservableQuery({\n          queryManager: _this,\n          queryInfo: queryInfo,\n          options: __assign(__assign({}, options), {\n            fetchPolicy: \"network-only\"\n          })\n        });\n        invariant(oq.queryId === queryId);\n        queryInfo.setObservableQuery(oq);\n        queries.set(queryId, oq);\n      });\n    }\n    if (globalThis.__DEV__ !== false && queryNamesAndQueryStrings.size) {\n      queryNamesAndQueryStrings.forEach(function (included, nameOrQueryString) {\n        if (!included) {\n          var queryName = queryNames.get(nameOrQueryString);\n          if (queryName) {\n            globalThis.__DEV__ !== false && invariant.warn(35, queryName);\n          } else {\n            globalThis.__DEV__ !== false && invariant.warn(36);\n          }\n        }\n      });\n    }\n    return queries;\n  };\n  QueryManager.prototype.reFetchObservableQueries = function (includeStandby) {\n    var _this = this;\n    if (includeStandby === void 0) {\n      includeStandby = false;\n    }\n    var observableQueryPromises = [];\n    this.getObservableQueries(includeStandby ? \"all\" : \"active\").forEach(function (observableQuery, queryId) {\n      var fetchPolicy = observableQuery.options.fetchPolicy;\n      observableQuery.resetLastResults();\n      if (includeStandby || fetchPolicy !== \"standby\" && fetchPolicy !== \"cache-only\") {\n        observableQueryPromises.push(observableQuery.refetch());\n      }\n      (_this.queries.get(queryId) || observableQuery[\"queryInfo\"]).setDiff(null);\n    });\n    this.broadcastQueries();\n    return Promise.all(observableQueryPromises);\n  };\n  QueryManager.prototype.startGraphQLSubscription = function (options) {\n    var _this = this;\n    var query = options.query,\n      variables = options.variables;\n    var fetchPolicy = options.fetchPolicy,\n      _a = options.errorPolicy,\n      errorPolicy = _a === void 0 ? \"none\" : _a,\n      _b = options.context,\n      context = _b === void 0 ? {} : _b,\n      _c = options.extensions,\n      extensions = _c === void 0 ? {} : _c;\n    query = this.transform(query);\n    variables = this.getVariables(query, variables);\n    var makeObservable = function (variables) {\n      return _this.getObservableFromLink(query, context, variables, extensions).map(function (result) {\n        if (fetchPolicy !== \"no-cache\") {\n          // the subscription interface should handle not sending us results we no longer subscribe to.\n          // XXX I don't think we ever send in an object with errors, but we might in the future...\n          if (shouldWriteResult(result, errorPolicy)) {\n            _this.cache.write({\n              query: query,\n              result: result.data,\n              dataId: \"ROOT_SUBSCRIPTION\",\n              variables: variables\n            });\n          }\n          _this.broadcastQueries();\n        }\n        var hasErrors = graphQLResultHasError(result);\n        var hasProtocolErrors = graphQLResultHasProtocolErrors(result);\n        if (hasErrors || hasProtocolErrors) {\n          var errors = {};\n          if (hasErrors) {\n            errors.graphQLErrors = result.errors;\n          }\n          if (hasProtocolErrors) {\n            errors.protocolErrors = result.extensions[PROTOCOL_ERRORS_SYMBOL];\n          }\n          // `errorPolicy` is a mechanism for handling GraphQL errors, according\n          // to our documentation, so we throw protocol errors regardless of the\n          // set error policy.\n          if (errorPolicy === \"none\" || hasProtocolErrors) {\n            throw new ApolloError(errors);\n          }\n        }\n        if (errorPolicy === \"ignore\") {\n          delete result.errors;\n        }\n        return result;\n      });\n    };\n    if (this.getDocumentInfo(query).hasClientExports) {\n      var observablePromise_1 = this.localState.addExportedVariables(query, variables, context).then(makeObservable);\n      return new Observable(function (observer) {\n        var sub = null;\n        observablePromise_1.then(function (observable) {\n          return sub = observable.subscribe(observer);\n        }, observer.error);\n        return function () {\n          return sub && sub.unsubscribe();\n        };\n      });\n    }\n    return makeObservable(variables);\n  };\n  QueryManager.prototype.stopQuery = function (queryId) {\n    this.stopQueryNoBroadcast(queryId);\n    this.broadcastQueries();\n  };\n  QueryManager.prototype.stopQueryNoBroadcast = function (queryId) {\n    this.stopQueryInStoreNoBroadcast(queryId);\n    this.removeQuery(queryId);\n  };\n  QueryManager.prototype.removeQuery = function (queryId) {\n    var _a;\n    // teardown all links\n    // Both `QueryManager.fetchRequest` and `QueryManager.query` create separate promises\n    // that each add their reject functions to fetchCancelFns.\n    // A query created with `QueryManager.query()` could trigger a `QueryManager.fetchRequest`.\n    // The same queryId could have two rejection fns for two promises\n    this.fetchCancelFns.delete(queryId);\n    if (this.queries.has(queryId)) {\n      (_a = this.queries.get(queryId)) === null || _a === void 0 ? void 0 : _a.stop();\n      this.queries.delete(queryId);\n    }\n  };\n  QueryManager.prototype.broadcastQueries = function () {\n    if (this.onBroadcast) this.onBroadcast();\n    this.queries.forEach(function (info) {\n      var _a;\n      return (_a = info.observableQuery) === null || _a === void 0 ? void 0 : _a[\"notify\"]();\n    });\n  };\n  QueryManager.prototype.getLocalState = function () {\n    return this.localState;\n  };\n  QueryManager.prototype.getObservableFromLink = function (query, context, variables, extensions,\n  // Prefer context.queryDeduplication if specified.\n  deduplication) {\n    var _this = this;\n    var _a;\n    if (deduplication === void 0) {\n      deduplication = (_a = context === null || context === void 0 ? void 0 : context.queryDeduplication) !== null && _a !== void 0 ? _a : this.queryDeduplication;\n    }\n    var observable;\n    var _b = this.getDocumentInfo(query),\n      serverQuery = _b.serverQuery,\n      clientQuery = _b.clientQuery;\n    if (serverQuery) {\n      var _c = this,\n        inFlightLinkObservables_1 = _c.inFlightLinkObservables,\n        link = _c.link;\n      var operation = {\n        query: serverQuery,\n        variables: variables,\n        operationName: getOperationName(serverQuery) || void 0,\n        context: this.prepareContext(__assign(__assign({}, context), {\n          forceFetch: !deduplication\n        })),\n        extensions: extensions\n      };\n      context = operation.context;\n      if (deduplication) {\n        var printedServerQuery_1 = print(serverQuery);\n        var varJson_1 = canonicalStringify(variables);\n        var entry = inFlightLinkObservables_1.lookup(printedServerQuery_1, varJson_1);\n        observable = entry.observable;\n        if (!observable) {\n          var concast_1 = new Concast([execute(link, operation)]);\n          observable = entry.observable = concast_1;\n          concast_1.beforeNext(function cb(method, arg) {\n            if (method === \"next\" && \"hasNext\" in arg && arg.hasNext) {\n              concast_1.beforeNext(cb);\n            } else {\n              inFlightLinkObservables_1.remove(printedServerQuery_1, varJson_1);\n            }\n          });\n        }\n      } else {\n        observable = new Concast([execute(link, operation)]);\n      }\n    } else {\n      observable = new Concast([Observable.of({\n        data: {}\n      })]);\n      context = this.prepareContext(context);\n    }\n    if (clientQuery) {\n      observable = asyncMap(observable, function (result) {\n        return _this.localState.runResolvers({\n          document: clientQuery,\n          remoteResult: result,\n          context: context,\n          variables: variables\n        });\n      });\n    }\n    return observable;\n  };\n  QueryManager.prototype.getResultsFromLink = function (queryInfo, cacheWriteBehavior, options) {\n    var requestId = queryInfo.lastRequestId = this.generateRequestId();\n    // Performing transformForLink here gives this.cache a chance to fill in\n    // missing fragment definitions (for example) before sending this document\n    // through the link chain.\n    var linkDocument = this.cache.transformForLink(options.query);\n    return asyncMap(this.getObservableFromLink(linkDocument, options.context, options.variables), function (result) {\n      var graphQLErrors = getGraphQLErrorsFromResult(result);\n      var hasErrors = graphQLErrors.length > 0;\n      var errorPolicy = options.errorPolicy;\n      // If we interrupted this request by calling getResultsFromLink again\n      // with the same QueryInfo object, we ignore the old results.\n      if (requestId >= queryInfo.lastRequestId) {\n        if (hasErrors && errorPolicy === \"none\") {\n          // Throwing here effectively calls observer.error.\n          throw queryInfo.markError(new ApolloError({\n            graphQLErrors: graphQLErrors\n          }));\n        }\n        // Use linkDocument rather than queryInfo.document so the\n        // operation/fragments used to write the result are the same as the\n        // ones used to obtain it from the link.\n        queryInfo.markResult(result, linkDocument, options, cacheWriteBehavior);\n        queryInfo.markReady();\n      }\n      var aqr = {\n        data: result.data,\n        loading: false,\n        networkStatus: NetworkStatus.ready\n      };\n      // In the case we start multiple network requests simulatenously, we\n      // want to ensure we properly set `data` if we're reporting on an old\n      // result which will not be caught by the conditional above that ends up\n      // throwing the markError result.\n      if (hasErrors && errorPolicy === \"none\") {\n        aqr.data = void 0;\n      }\n      if (hasErrors && errorPolicy !== \"ignore\") {\n        aqr.errors = graphQLErrors;\n        aqr.networkStatus = NetworkStatus.error;\n      }\n      return aqr;\n    }, function (networkError) {\n      var error = isApolloError(networkError) ? networkError : new ApolloError({\n        networkError: networkError\n      });\n      // Avoid storing errors from older interrupted queries.\n      if (requestId >= queryInfo.lastRequestId) {\n        queryInfo.markError(error);\n      }\n      throw error;\n    });\n  };\n  QueryManager.prototype.fetchConcastWithInfo = function (queryInfo, options,\n  // The initial networkStatus for this fetch, most often\n  // NetworkStatus.loading, but also possibly fetchMore, poll, refetch,\n  // or setVariables.\n  networkStatus, query) {\n    var _this = this;\n    if (networkStatus === void 0) {\n      networkStatus = NetworkStatus.loading;\n    }\n    if (query === void 0) {\n      query = options.query;\n    }\n    var variables = this.getVariables(query, options.variables);\n    var defaults = this.defaultOptions.watchQuery;\n    var _a = options.fetchPolicy,\n      fetchPolicy = _a === void 0 ? defaults && defaults.fetchPolicy || \"cache-first\" : _a,\n      _b = options.errorPolicy,\n      errorPolicy = _b === void 0 ? defaults && defaults.errorPolicy || \"none\" : _b,\n      _c = options.returnPartialData,\n      returnPartialData = _c === void 0 ? false : _c,\n      _d = options.notifyOnNetworkStatusChange,\n      notifyOnNetworkStatusChange = _d === void 0 ? false : _d,\n      _e = options.context,\n      context = _e === void 0 ? {} : _e;\n    var normalized = Object.assign({}, options, {\n      query: query,\n      variables: variables,\n      fetchPolicy: fetchPolicy,\n      errorPolicy: errorPolicy,\n      returnPartialData: returnPartialData,\n      notifyOnNetworkStatusChange: notifyOnNetworkStatusChange,\n      context: context\n    });\n    var fromVariables = function (variables) {\n      // Since normalized is always a fresh copy of options, it's safe to\n      // modify its properties here, rather than creating yet another new\n      // WatchQueryOptions object.\n      normalized.variables = variables;\n      var sourcesWithInfo = _this.fetchQueryByPolicy(queryInfo, normalized, networkStatus);\n      if (\n      // If we're in standby, postpone advancing options.fetchPolicy using\n      // applyNextFetchPolicy.\n      normalized.fetchPolicy !== \"standby\" &&\n      // The \"standby\" policy currently returns [] from fetchQueryByPolicy, so\n      // this is another way to detect when nothing was done/fetched.\n      sourcesWithInfo.sources.length > 0 && queryInfo.observableQuery) {\n        queryInfo.observableQuery[\"applyNextFetchPolicy\"](\"after-fetch\", options);\n      }\n      return sourcesWithInfo;\n    };\n    // This cancel function needs to be set before the concast is created,\n    // in case concast creation synchronously cancels the request.\n    var cleanupCancelFn = function () {\n      return _this.fetchCancelFns.delete(queryInfo.queryId);\n    };\n    this.fetchCancelFns.set(queryInfo.queryId, function (reason) {\n      cleanupCancelFn();\n      // This delay ensures the concast variable has been initialized.\n      setTimeout(function () {\n        return concast.cancel(reason);\n      });\n    });\n    var concast, containsDataFromLink;\n    // If the query has @export(as: ...) directives, then we need to\n    // process those directives asynchronously. When there are no\n    // @export directives (the common case), we deliberately avoid\n    // wrapping the result of this.fetchQueryByPolicy in a Promise,\n    // since the timing of result delivery is (unfortunately) important\n    // for backwards compatibility. TODO This code could be simpler if\n    // we deprecated and removed LocalState.\n    if (this.getDocumentInfo(normalized.query).hasClientExports) {\n      concast = new Concast(this.localState.addExportedVariables(normalized.query, normalized.variables, normalized.context).then(fromVariables).then(function (sourcesWithInfo) {\n        return sourcesWithInfo.sources;\n      }));\n      // there is just no way we can synchronously get the *right* value here,\n      // so we will assume `true`, which is the behaviour before the bug fix in\n      // #10597. This means that bug is not fixed in that case, and is probably\n      // un-fixable with reasonable effort for the edge case of @export as\n      // directives.\n      containsDataFromLink = true;\n    } else {\n      var sourcesWithInfo = fromVariables(normalized.variables);\n      containsDataFromLink = sourcesWithInfo.fromLink;\n      concast = new Concast(sourcesWithInfo.sources);\n    }\n    concast.promise.then(cleanupCancelFn, cleanupCancelFn);\n    return {\n      concast: concast,\n      fromLink: containsDataFromLink\n    };\n  };\n  QueryManager.prototype.refetchQueries = function (_a) {\n    var _this = this;\n    var updateCache = _a.updateCache,\n      include = _a.include,\n      _b = _a.optimistic,\n      optimistic = _b === void 0 ? false : _b,\n      _c = _a.removeOptimistic,\n      removeOptimistic = _c === void 0 ? optimistic ? makeUniqueId(\"refetchQueries\") : void 0 : _c,\n      onQueryUpdated = _a.onQueryUpdated;\n    var includedQueriesById = new Map();\n    if (include) {\n      this.getObservableQueries(include).forEach(function (oq, queryId) {\n        includedQueriesById.set(queryId, {\n          oq: oq,\n          lastDiff: (_this.queries.get(queryId) || oq[\"queryInfo\"]).getDiff()\n        });\n      });\n    }\n    var results = new Map();\n    if (updateCache) {\n      this.cache.batch({\n        update: updateCache,\n        // Since you can perform any combination of cache reads and/or writes in\n        // the cache.batch update function, its optimistic option can be either\n        // a boolean or a string, representing three distinct modes of\n        // operation:\n        //\n        // * false: read/write only the root layer\n        // * true: read/write the topmost layer\n        // * string: read/write a fresh optimistic layer with that ID string\n        //\n        // When typeof optimistic === \"string\", a new optimistic layer will be\n        // temporarily created within cache.batch with that string as its ID. If\n        // we then pass that same string as the removeOptimistic option, we can\n        // make cache.batch immediately remove the optimistic layer after\n        // running the updateCache function, triggering only one broadcast.\n        //\n        // However, the refetchQueries method accepts only true or false for its\n        // optimistic option (not string). We interpret true to mean a temporary\n        // optimistic layer should be created, to allow efficiently rolling back\n        // the effect of the updateCache function, which involves passing a\n        // string instead of true as the optimistic option to cache.batch, when\n        // refetchQueries receives optimistic: true.\n        //\n        // In other words, we are deliberately not supporting the use case of\n        // writing to an *existing* optimistic layer (using the refetchQueries\n        // updateCache function), since that would potentially interfere with\n        // other optimistic updates in progress. Instead, you can read/write\n        // only the root layer by passing optimistic: false to refetchQueries,\n        // or you can read/write a brand new optimistic layer that will be\n        // automatically removed by passing optimistic: true.\n        optimistic: optimistic && removeOptimistic || false,\n        // The removeOptimistic option can also be provided by itself, even if\n        // optimistic === false, to remove some previously-added optimistic\n        // layer safely and efficiently, like we do in markMutationResult.\n        //\n        // If an explicit removeOptimistic string is provided with optimistic:\n        // true, the removeOptimistic string will determine the ID of the\n        // temporary optimistic layer, in case that ever matters.\n        removeOptimistic: removeOptimistic,\n        onWatchUpdated: function (watch, diff, lastDiff) {\n          var oq = watch.watcher instanceof QueryInfo && watch.watcher.observableQuery;\n          if (oq) {\n            if (onQueryUpdated) {\n              // Since we're about to handle this query now, remove it from\n              // includedQueriesById, in case it was added earlier because of\n              // options.include.\n              includedQueriesById.delete(oq.queryId);\n              var result = onQueryUpdated(oq, diff, lastDiff);\n              if (result === true) {\n                // The onQueryUpdated function requested the default refetching\n                // behavior by returning true.\n                result = oq.refetch();\n              }\n              // Record the result in the results Map, as long as onQueryUpdated\n              // did not return false to skip/ignore this result.\n              if (result !== false) {\n                results.set(oq, result);\n              }\n              // Allow the default cache broadcast to happen, except when\n              // onQueryUpdated returns false.\n              return result;\n            }\n            if (onQueryUpdated !== null) {\n              // If we don't have an onQueryUpdated function, and onQueryUpdated\n              // was not disabled by passing null, make sure this query is\n              // \"included\" like any other options.include-specified query.\n              includedQueriesById.set(oq.queryId, {\n                oq: oq,\n                lastDiff: lastDiff,\n                diff: diff\n              });\n            }\n          }\n        }\n      });\n    }\n    if (includedQueriesById.size) {\n      includedQueriesById.forEach(function (_a, queryId) {\n        var oq = _a.oq,\n          lastDiff = _a.lastDiff,\n          diff = _a.diff;\n        var result;\n        // If onQueryUpdated is provided, we want to use it for all included\n        // queries, even the QueryOptions ones.\n        if (onQueryUpdated) {\n          if (!diff) {\n            diff = _this.cache.diff(oq[\"queryInfo\"][\"getDiffOptions\"]());\n          }\n          result = onQueryUpdated(oq, diff, lastDiff);\n        }\n        // Otherwise, we fall back to refetching.\n        if (!onQueryUpdated || result === true) {\n          result = oq.refetch();\n        }\n        if (result !== false) {\n          results.set(oq, result);\n        }\n        if (queryId.indexOf(\"legacyOneTimeQuery\") >= 0) {\n          _this.stopQueryNoBroadcast(queryId);\n        }\n      });\n    }\n    if (removeOptimistic) {\n      // In case no updateCache callback was provided (so cache.batch was not\n      // called above, and thus did not already remove the optimistic layer),\n      // remove it here. Since this is a no-op when the layer has already been\n      // removed, we do it even if we called cache.batch above, since it's\n      // possible this.cache is an instance of some ApolloCache subclass other\n      // than InMemoryCache, and does not fully support the removeOptimistic\n      // option for cache.batch.\n      this.cache.removeOptimistic(removeOptimistic);\n    }\n    return results;\n  };\n  QueryManager.prototype.maskOperation = function (options) {\n    var _a, _b, _c;\n    var document = options.document,\n      data = options.data;\n    if (globalThis.__DEV__ !== false) {\n      var fetchPolicy = options.fetchPolicy,\n        id = options.id;\n      var operationType = (_a = getOperationDefinition(document)) === null || _a === void 0 ? void 0 : _a.operation;\n      var operationId = ((_b = operationType === null || operationType === void 0 ? void 0 : operationType[0]) !== null && _b !== void 0 ? _b : \"o\") + id;\n      if (this.dataMasking && fetchPolicy === \"no-cache\" && !isFullyUnmaskedOperation(document) && !this.noCacheWarningsByQueryId.has(operationId)) {\n        this.noCacheWarningsByQueryId.add(operationId);\n        globalThis.__DEV__ !== false && invariant.warn(37, (_c = getOperationName(document)) !== null && _c !== void 0 ? _c : \"Unnamed \".concat(operationType !== null && operationType !== void 0 ? operationType : \"operation\"));\n      }\n    }\n    return this.dataMasking ? maskOperation(data, document, this.cache) : data;\n  };\n  QueryManager.prototype.maskFragment = function (options) {\n    var data = options.data,\n      fragment = options.fragment,\n      fragmentName = options.fragmentName;\n    return this.dataMasking ? maskFragment(data, fragment, this.cache, fragmentName) : data;\n  };\n  QueryManager.prototype.fetchQueryByPolicy = function (queryInfo, _a,\n  // The initial networkStatus for this fetch, most often\n  // NetworkStatus.loading, but also possibly fetchMore, poll, refetch,\n  // or setVariables.\n  networkStatus) {\n    var _this = this;\n    var query = _a.query,\n      variables = _a.variables,\n      fetchPolicy = _a.fetchPolicy,\n      refetchWritePolicy = _a.refetchWritePolicy,\n      errorPolicy = _a.errorPolicy,\n      returnPartialData = _a.returnPartialData,\n      context = _a.context,\n      notifyOnNetworkStatusChange = _a.notifyOnNetworkStatusChange;\n    var oldNetworkStatus = queryInfo.networkStatus;\n    queryInfo.init({\n      document: query,\n      variables: variables,\n      networkStatus: networkStatus\n    });\n    var readCache = function () {\n      return queryInfo.getDiff();\n    };\n    var resultsFromCache = function (diff, networkStatus) {\n      if (networkStatus === void 0) {\n        networkStatus = queryInfo.networkStatus || NetworkStatus.loading;\n      }\n      var data = diff.result;\n      if (globalThis.__DEV__ !== false && !returnPartialData && !equal(data, {})) {\n        logMissingFieldErrors(diff.missing);\n      }\n      var fromData = function (data) {\n        return Observable.of(__assign({\n          data: data,\n          loading: isNetworkRequestInFlight(networkStatus),\n          networkStatus: networkStatus\n        }, diff.complete ? null : {\n          partial: true\n        }));\n      };\n      if (data && _this.getDocumentInfo(query).hasForcedResolvers) {\n        return _this.localState.runResolvers({\n          document: query,\n          remoteResult: {\n            data: data\n          },\n          context: context,\n          variables: variables,\n          onlyRunForcedResolvers: true\n        }).then(function (resolved) {\n          return fromData(resolved.data || void 0);\n        });\n      }\n      // Resolves https://github.com/apollographql/apollo-client/issues/10317.\n      // If errorPolicy is 'none' and notifyOnNetworkStatusChange is true,\n      // data was incorrectly returned from the cache on refetch:\n      // if diff.missing exists, we should not return cache data.\n      if (errorPolicy === \"none\" && networkStatus === NetworkStatus.refetch && Array.isArray(diff.missing)) {\n        return fromData(void 0);\n      }\n      return fromData(data);\n    };\n    var cacheWriteBehavior = fetchPolicy === \"no-cache\" ? 0 /* CacheWriteBehavior.FORBID */\n    // Watched queries must opt into overwriting existing data on refetch,\n    // by passing refetchWritePolicy: \"overwrite\" in their WatchQueryOptions.\n    : networkStatus === NetworkStatus.refetch && refetchWritePolicy !== \"merge\" ? 1 /* CacheWriteBehavior.OVERWRITE */ : 2 /* CacheWriteBehavior.MERGE */;\n    var resultsFromLink = function () {\n      return _this.getResultsFromLink(queryInfo, cacheWriteBehavior, {\n        query: query,\n        variables: variables,\n        context: context,\n        fetchPolicy: fetchPolicy,\n        errorPolicy: errorPolicy\n      });\n    };\n    var shouldNotify = notifyOnNetworkStatusChange && typeof oldNetworkStatus === \"number\" && oldNetworkStatus !== networkStatus && isNetworkRequestInFlight(networkStatus);\n    switch (fetchPolicy) {\n      default:\n      case \"cache-first\":\n        {\n          var diff = readCache();\n          if (diff.complete) {\n            return {\n              fromLink: false,\n              sources: [resultsFromCache(diff, queryInfo.markReady())]\n            };\n          }\n          if (returnPartialData || shouldNotify) {\n            return {\n              fromLink: true,\n              sources: [resultsFromCache(diff), resultsFromLink()]\n            };\n          }\n          return {\n            fromLink: true,\n            sources: [resultsFromLink()]\n          };\n        }\n      case \"cache-and-network\":\n        {\n          var diff = readCache();\n          if (diff.complete || returnPartialData || shouldNotify) {\n            return {\n              fromLink: true,\n              sources: [resultsFromCache(diff), resultsFromLink()]\n            };\n          }\n          return {\n            fromLink: true,\n            sources: [resultsFromLink()]\n          };\n        }\n      case \"cache-only\":\n        return {\n          fromLink: false,\n          sources: [resultsFromCache(readCache(), queryInfo.markReady())]\n        };\n      case \"network-only\":\n        if (shouldNotify) {\n          return {\n            fromLink: true,\n            sources: [resultsFromCache(readCache()), resultsFromLink()]\n          };\n        }\n        return {\n          fromLink: true,\n          sources: [resultsFromLink()]\n        };\n      case \"no-cache\":\n        if (shouldNotify) {\n          return {\n            fromLink: true,\n            // Note that queryInfo.getDiff() for no-cache queries does not call\n            // cache.diff, but instead returns a { complete: false } stub result\n            // when there is no queryInfo.diff already defined.\n            sources: [resultsFromCache(queryInfo.getDiff()), resultsFromLink()]\n          };\n        }\n        return {\n          fromLink: true,\n          sources: [resultsFromLink()]\n        };\n      case \"standby\":\n        return {\n          fromLink: false,\n          sources: []\n        };\n    }\n  };\n  QueryManager.prototype.getOrCreateQuery = function (queryId) {\n    if (queryId && !this.queries.has(queryId)) {\n      this.queries.set(queryId, new QueryInfo(this, queryId));\n    }\n    return this.queries.get(queryId);\n  };\n  QueryManager.prototype.prepareContext = function (context) {\n    if (context === void 0) {\n      context = {};\n    }\n    var newContext = this.localState.prepareContext(context);\n    return __assign(__assign(__assign({}, this.defaultContext), newContext), {\n      clientAwareness: this.clientAwareness\n    });\n  };\n  return QueryManager;\n}();\nexport { QueryManager };\n//# sourceMappingURL=QueryManager.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}