{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/logger.service\";\nimport * as i2 from \"@angular/common\";\nconst _c0 = [\"audioPlayer\"];\nfunction VoiceMessagePlayerComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.isCurrentUser ? \"from-[#3d4a85]/5 to-[#4f5fad]/5 dark:from-[#6d78c9]/5 dark:to-[#4f5fad]/5\" : \"from-[#3d4a85]/5 to-[#4f5fad]/5 dark:from-[#6d78c9]/5 dark:to-[#4f5fad]/5\");\n  }\n}\nfunction VoiceMessagePlayerComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.isCurrentUser ? \"bg-[#4f5fad] dark:bg-[#6d78c9]\" : \"bg-[#4f5fad] dark:bg-[#6d78c9]\");\n  }\n}\nfunction VoiceMessagePlayerComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 20);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"bg-[#4f5fad] dark:bg-[#6d78c9]\": a0,\n    \"bg-[#bdc6cc] dark:bg-[#4a4a4a]\": a1\n  };\n};\nfunction VoiceMessagePlayerComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, VoiceMessagePlayerComponent_div_12_div_1_Template, 1, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r4 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"height\", ctx_r3.getRandomBarHeight(i_r4), \"px\")(\"transition-delay\", i_r4 * 20, \"ms\");\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c1, ctx_r3.isPlaying && ctx_r3.progressPercentage > i_r4 * 3.7, !ctx_r3.isPlaying || ctx_r3.progressPercentage <= i_r4 * 3.7));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isPlaying && ctx_r3.progressPercentage > i_r4 * 3.7);\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    \"bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#6d78c9]/10 dark:to-[#4f5fad]/10\": a0,\n    \"bg-gradient-to-r from-[#3d4a85]/20 to-[#4f5fad]/20 dark:from-[#6d78c9]/20 dark:to-[#4f5fad]/20\": a1\n  };\n};\nconst _c3 = function (a0, a1) {\n  return {\n    \"bg-[#4f5fad] dark:bg-[#6d78c9] text-white\": a0,\n    \"bg-[#edf1f4] dark:bg-[#2a2a2a] text-[#4f5fad] dark:text-[#6d78c9]\": a1\n  };\n};\nconst _c4 = function (a0, a1) {\n  return {\n    \"opacity-100 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50\": a0,\n    \"opacity-0 group-hover:opacity-50 bg-[#4f5fad]/30 dark:bg-[#6d78c9]/30\": a1\n  };\n};\nconst _c5 = function () {\n  return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26];\n};\nconst _c6 = function (a0, a1) {\n  return {\n    \"opacity-100\": a0,\n    \"opacity-70\": a1\n  };\n};\nexport let VoiceMessagePlayerComponent = /*#__PURE__*/(() => {\n  class VoiceMessagePlayerComponent {\n    // Ajouter une classe CSS lorsque l'audio est en lecture\n    get playingClass() {\n      return this.isPlaying;\n    }\n    // Ajouter une classe CSS pour les messages de l'utilisateur courant\n    get currentUserClass() {\n      return this.isCurrentUser;\n    }\n    constructor(logger, renderer, cdr) {\n      this.logger = logger;\n      this.renderer = renderer;\n      this.cdr = cdr;\n      this.audioUrl = '';\n      this.duration = 0;\n      this.isCurrentUser = false;\n      this.isPlaying = false;\n      this.currentTime = 0;\n      // Tableau des indices pour les barres de l'onde sonore\n      this.waveformBars = Array.from({\n        length: 27\n      }, (_, i) => i);\n      // Hauteurs pré-calculées pour les barres de l'onde sonore (style Messenger)\n      this.barHeights = [];\n    }\n    ngOnInit() {\n      // Générer des hauteurs aléatoires pour les barres de l'onde sonore\n      this.generateWaveformPattern();\n      // Appliquer les styles immédiatement\n      this.applyMessengerStyles();\n      // Forcer la détection des changements immédiatement\n      this.cdr.detectChanges();\n    }\n    ngAfterViewInit() {\n      // Forcer la détection des changements après le rendu initial\n      setTimeout(() => {\n        this.applyMessengerStyles();\n        this.cdr.detectChanges();\n      }, 0);\n      // Vérifier si l'audio est chargé\n      if (this.audioPlayerRef?.nativeElement) {\n        this.audioPlayerRef.nativeElement.onloadedmetadata = () => {\n          this.logger.debug('Audio metadata loaded');\n          this.cdr.detectChanges();\n        };\n      }\n    }\n    ngOnDestroy() {\n      this.stopProgressTracking();\n    }\n    /**\n     * Applique les styles Messenger directement aux éléments DOM\n     * pour s'assurer qu'ils sont appliqués immédiatement sans rechargement\n     */\n    applyMessengerStyles() {\n      try {\n        // Appliquer les styles directement via le renderer pour contourner les problèmes de rafraîchissement\n        if (this.audioPlayerRef?.nativeElement) {\n          this.renderer.setStyle(this.audioPlayerRef.nativeElement, 'outline', 'none');\n          this.renderer.setStyle(this.audioPlayerRef.nativeElement, 'border', 'none');\n          this.renderer.setStyle(this.audioPlayerRef.nativeElement, 'box-shadow', 'none');\n        }\n        // Forcer la détection des changements\n        this.cdr.detectChanges();\n      } catch (error) {\n        this.logger.error('Error applying Messenger styles:', error);\n      }\n    }\n    /**\n     * Génère un motif d'onde sonore pseudo-aléatoire mais cohérent\n     * similaire à celui de Messenger\n     */\n    generateWaveformPattern() {\n      // Réinitialiser les hauteurs\n      this.barHeights = [];\n      // Créer un motif avec des hauteurs variables (entre 4 et 20px)\n      // Le motif est pseudo-aléatoire mais suit une courbe naturelle\n      const basePattern = [8, 10, 14, 16, 18, 16, 12, 10, 8, 12, 16, 20, 18, 14, 10, 8, 10, 14, 18, 16, 12, 8, 10, 14, 12, 8, 6];\n      // Ajouter une légère variation aléatoire pour chaque barre\n      for (let i = 0; i < 27; i++) {\n        const baseHeight = basePattern[i] || 10;\n        const variation = Math.floor(Math.random() * 5) - 2; // Variation de -2 à +2\n        this.barHeights.push(Math.max(4, Math.min(20, baseHeight + variation)));\n      }\n    }\n    /**\n     * Retourne la hauteur d'une barre spécifique de l'onde sonore\n     */\n    getRandomBarHeight(index) {\n      return this.barHeights[index] || 10;\n    }\n    /**\n     * Joue ou met en pause l'audio\n     */\n    togglePlay() {\n      const audioPlayer = this.audioPlayerRef.nativeElement;\n      if (this.isPlaying) {\n        audioPlayer.pause();\n        this.isPlaying = false;\n        this.stopProgressTracking();\n        this.cdr.detectChanges(); // Forcer la mise à jour de l'UI\n      } else {\n        // Réinitialiser l'audio si la lecture est terminée\n        if (audioPlayer.ended || audioPlayer.currentTime >= audioPlayer.duration) {\n          audioPlayer.currentTime = 0;\n        }\n        // Jouer l'audio avec gestion d'erreur améliorée\n        audioPlayer.play().then(() => {\n          this.logger.debug('Audio playback started successfully');\n          // Forcer la mise à jour de l'UI\n          this.cdr.detectChanges();\n        }).catch(error => {\n          this.logger.error('Error playing audio:', error);\n          this.isPlaying = false;\n          this.cdr.detectChanges();\n        });\n        this.isPlaying = true;\n        this.startProgressTracking();\n        this.cdr.detectChanges(); // Forcer la mise à jour de l'UI immédiatement\n      }\n    }\n    /**\n     * Démarre le suivi de la progression de la lecture\n     */\n    startProgressTracking() {\n      this.stopProgressTracking();\n      // Utiliser requestAnimationFrame pour une animation plus fluide\n      const updateProgress = () => {\n        if (!this.isPlaying) return;\n        const audioPlayer = this.audioPlayerRef.nativeElement;\n        // Mettre à jour le temps actuel\n        this.currentTime = audioPlayer.currentTime;\n        // Si la lecture est terminée\n        if (audioPlayer.ended) {\n          this.isPlaying = false;\n          this.currentTime = 0;\n          this.cdr.detectChanges(); // Forcer la mise à jour de l'UI\n          return;\n        }\n        // Forcer la détection des changements pour mettre à jour l'UI\n        this.cdr.detectChanges();\n        // Continuer la boucle d'animation\n        this.progressInterval = requestAnimationFrame(updateProgress);\n      };\n      // Démarrer la boucle d'animation\n      this.progressInterval = requestAnimationFrame(updateProgress);\n    }\n    /**\n     * Arrête le suivi de la progression\n     */\n    stopProgressTracking() {\n      if (this.progressInterval) {\n        cancelAnimationFrame(this.progressInterval);\n        this.progressInterval = null;\n      }\n    }\n    /**\n     * Gère l'événement de fin de lecture\n     */\n    onAudioEnded() {\n      this.isPlaying = false;\n      this.currentTime = 0;\n      this.stopProgressTracking();\n      // Forcer la mise à jour de l'UI\n      this.cdr.detectChanges();\n      // Réinitialiser l'audio pour la prochaine lecture\n      if (this.audioPlayerRef?.nativeElement) {\n        this.audioPlayerRef.nativeElement.currentTime = 0;\n      }\n    }\n    /**\n     * Calcule la progression de la lecture en pourcentage\n     */\n    get progressPercentage() {\n      if (!this.audioPlayerRef) return 0;\n      const audioPlayer = this.audioPlayerRef.nativeElement;\n      // Si la durée n'est pas disponible, utiliser la durée fournie en entrée\n      const totalDuration = audioPlayer.duration || this.duration || 1;\n      return this.currentTime / totalDuration * 100;\n    }\n    /**\n     * Formate le temps de lecture en MM:SS\n     */\n    get formattedTime() {\n      const totalSeconds = Math.floor(this.currentTime);\n      const minutes = Math.floor(totalSeconds / 60);\n      const seconds = totalSeconds % 60;\n      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    }\n    /**\n     * Formate la durée totale en MM:SS\n     */\n    get formattedDuration() {\n      if (!this.audioPlayerRef || !this.audioPlayerRef.nativeElement.duration) {\n        const totalSeconds = this.duration || 0;\n        const minutes = Math.floor(totalSeconds / 60);\n        const seconds = Math.floor(totalSeconds % 60);\n        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n      }\n      const totalSeconds = Math.floor(this.audioPlayerRef.nativeElement.duration);\n      const minutes = Math.floor(totalSeconds / 60);\n      const seconds = totalSeconds % 60;\n      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    }\n    static {\n      this.ɵfac = function VoiceMessagePlayerComponent_Factory(t) {\n        return new (t || VoiceMessagePlayerComponent)(i0.ɵɵdirectiveInject(i1.LoggerService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: VoiceMessagePlayerComponent,\n        selectors: [[\"app-voice-message-player\"]],\n        viewQuery: function VoiceMessagePlayerComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.audioPlayerRef = _t.first);\n          }\n        },\n        hostVars: 4,\n        hostBindings: function VoiceMessagePlayerComponent_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"is-playing\", ctx.playingClass)(\"is-current-user\", ctx.currentUserClass);\n          }\n        },\n        inputs: {\n          audioUrl: \"audioUrl\",\n          duration: \"duration\",\n          isCurrentUser: \"isCurrentUser\"\n        },\n        decls: 17,\n        vars: 25,\n        consts: [[1, \"flex\", \"items-center\", \"p-2\", \"rounded-lg\", \"relative\", \"overflow-hidden\", 3, \"ngClass\"], [\"preload\", \"metadata\", 2, \"display\", \"none\", 3, \"src\", \"ended\"], [\"audioPlayer\", \"\"], [1, \"absolute\", \"inset-0\", \"pointer-events-none\", \"overflow-hidden\"], [\"class\", \"absolute inset-0 bg-gradient-to-r opacity-30 animate-pulse\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"absolute -left-4 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full animate-ping opacity-30\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"w-10\", \"h-10\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"relative\", \"group\", \"mr-3\", \"z-10\", 3, \"ngClass\", \"click\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"blur-md\", \"transition-opacity\", 3, \"ngClass\"], [1, \"fas\", \"relative\", \"z-10\", \"group-hover:scale-110\", \"transition-transform\", 3, \"ngClass\"], [1, \"flex-1\", \"relative\", \"z-10\"], [1, \"relative\"], [1, \"flex\", \"items-center\", \"h-8\", \"space-x-0.5\"], [\"class\", \"w-1 rounded-full transition-all duration-300\", 3, \"ngClass\", \"height\", \"transition-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"absolute\", \"bottom-0\", \"left-0\", \"h-0.5\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"transition-all\", \"duration-300\"], [1, \"mt-1\", \"text-xs\", \"text-right\", \"transition-opacity\", \"duration-300\", 3, \"ngClass\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"opacity-30\", \"animate-pulse\", 3, \"ngClass\"], [1, \"absolute\", \"-left-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"w-8\", \"h-8\", \"rounded-full\", \"animate-ping\", \"opacity-30\", 3, \"ngClass\"], [1, \"w-1\", \"rounded-full\", \"transition-all\", \"duration-300\", 3, \"ngClass\"], [\"class\", \"absolute inset-0 opacity-70 blur-sm rounded-full -z-10 bg-[#4f5fad]/50 dark:bg-[#6d78c9]/50\", 4, \"ngIf\"], [1, \"absolute\", \"inset-0\", \"opacity-70\", \"blur-sm\", \"rounded-full\", \"-z-10\", \"bg-[#4f5fad]/50\", \"dark:bg-[#6d78c9]/50\"]],\n        template: function VoiceMessagePlayerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"audio\", 1, 2);\n            i0.ɵɵlistener(\"ended\", function VoiceMessagePlayerComponent_Template_audio_ended_1_listener() {\n              return ctx.onAudioEnded();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"div\", 3);\n            i0.ɵɵtemplate(4, VoiceMessagePlayerComponent_div_4_Template, 1, 1, \"div\", 4);\n            i0.ɵɵtemplate(5, VoiceMessagePlayerComponent_div_5_Template, 1, 1, \"div\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function VoiceMessagePlayerComponent_Template_button_click_6_listener() {\n              return ctx.togglePlay();\n            });\n            i0.ɵɵelement(7, \"div\", 7)(8, \"i\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 9)(10, \"div\", 10)(11, \"div\", 11);\n            i0.ɵɵtemplate(12, VoiceMessagePlayerComponent_div_12_Template, 2, 9, \"div\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(13, \"div\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"div\", 14)(15, \"span\", 15);\n            i0.ɵɵtext(16);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(12, _c2, !ctx.isCurrentUser, ctx.isCurrentUser));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"src\", ctx.audioUrl, i0.ɵɵsanitizeUrl);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isPlaying);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isPlaying);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(15, _c3, ctx.isPlaying, !ctx.isPlaying));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c4, ctx.isPlaying, !ctx.isPlaying));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngClass\", ctx.isPlaying ? \"fa-pause\" : \"fa-play\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(21, _c5));\n            i0.ɵɵadvance(1);\n            i0.ɵɵstyleProp(\"width\", ctx.progressPercentage, \"%\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(22, _c6, ctx.isPlaying, !ctx.isPlaying));\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.formattedDuration, \" \");\n          }\n        },\n        dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf],\n        styles: [\"@charset \\\"UTF-8\\\";.voice-message-player[_ngcontent-%COMP%]{display:flex;align-items:center;background-color:transparent;border-radius:18px;padding:8px 12px;width:100%;max-width:240px;border:none;outline:none;box-shadow:none;position:relative;overflow:hidden}.voice-message-player.is-current-user[_ngcontent-%COMP%]   .play-button[_ngcontent-%COMP%]{background-color:#ffffffe6;color:#4f5fad;box-shadow:0 2px 8px #fff3}.voice-message-player.is-current-user[_ngcontent-%COMP%]   .play-button[_ngcontent-%COMP%]:hover{background-color:#fff;box-shadow:0 2px 12px #ffffff4d}.voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .play-button[_ngcontent-%COMP%]{background-color:#fff;color:#4f5fad;box-shadow:0 2px 8px #4f5fad33}.voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .play-button[_ngcontent-%COMP%]:hover{background-color:#f8f9ff;box-shadow:0 2px 12px #4f5fad4d}.dark-mode[_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .play-button[_ngcontent-%COMP%], .dark-mode   [_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .play-button[_ngcontent-%COMP%]{background-color:#2a2a2a;color:#6d78c9;box-shadow:0 2px 8px #6d78c933}.dark-mode[_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .play-button[_ngcontent-%COMP%]:hover, .dark-mode   [_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .play-button[_ngcontent-%COMP%]:hover{background-color:#333;box-shadow:0 2px 12px #6d78c94d}.voice-message-player[_ngcontent-%COMP%]   .play-button[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;margin-right:12px;flex-shrink:0;transition:all .3s ease;position:relative;z-index:1}.voice-message-player[_ngcontent-%COMP%]   .play-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px;transition:transform .3s ease}.voice-message-player[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]{flex-grow:1;display:flex;flex-direction:column}.voice-message-player[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .waveform-container[_ngcontent-%COMP%]{width:100%;position:relative}.voice-message-player[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .waveform-container[_ngcontent-%COMP%]   .waveform[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:24px;margin-bottom:4px}.voice-message-player[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .waveform-container[_ngcontent-%COMP%]   .waveform[_ngcontent-%COMP%]   .waveform-bar[_ngcontent-%COMP%]{width:2px;border-radius:1px;position:relative;transition:all .3s ease}.voice-message-player.is-current-user[_ngcontent-%COMP%]   .waveform-bar[_ngcontent-%COMP%]{background-color:#fff6}.voice-message-player.is-current-user[_ngcontent-%COMP%]   .waveform-bar.active[_ngcontent-%COMP%]{background-color:#ffffffe6}.voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .waveform-bar[_ngcontent-%COMP%]{background-color:#4f5fad4d}.voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .waveform-bar.active[_ngcontent-%COMP%]{background-color:#4f5fade6}.dark-mode[_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .waveform-bar[_ngcontent-%COMP%], .dark-mode   [_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .waveform-bar[_ngcontent-%COMP%]{background-color:#6d78c94d}.dark-mode[_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .waveform-bar.active[_ngcontent-%COMP%], .dark-mode   [_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .waveform-bar.active[_ngcontent-%COMP%]{background-color:#6d78c9e6}.voice-message-player[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%]   .waveform-container[_ngcontent-%COMP%]   .time-display[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;font-size:11px;margin-top:2px;transition:opacity .3s ease}.voice-message-player.is-current-user[_ngcontent-%COMP%]   .time-display[_ngcontent-%COMP%]   .duration[_ngcontent-%COMP%]{font-weight:500}.voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .time-display[_ngcontent-%COMP%]   .duration[_ngcontent-%COMP%]{font-weight:500;color:#4f5fade6}.dark-mode[_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .time-display[_ngcontent-%COMP%]   .duration[_ngcontent-%COMP%], .dark-mode   [_nghost-%COMP%]   .voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .time-display[_ngcontent-%COMP%]   .duration[_ngcontent-%COMP%]{color:#6d78c9e6}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:.7;filter:brightness(.9)}50%{opacity:1;filter:brightness(1.2)}to{opacity:.7;filter:brightness(.9)}}@keyframes _ngcontent-%COMP%_pulse-button{0%{transform:scale(1);box-shadow:0 0 #4f5fad66}50%{transform:scale(1.05);box-shadow:0 0 10px 3px #4f5fad4d}to{transform:scale(1);box-shadow:0 0 #4f5fad66}}@keyframes _ngcontent-%COMP%_pulse-button-current-user{0%{transform:scale(1);box-shadow:0 0 #fff6}50%{transform:scale(1.05);box-shadow:0 0 10px 3px #ffffff4d}to{transform:scale(1);box-shadow:0 0 #fff6}}@keyframes _ngcontent-%COMP%_bar-animation{0%{transform:scaleY(.8)}50%{transform:scaleY(1.2)}to{transform:scaleY(.8)}}@keyframes _ngcontent-%COMP%_glow{0%{filter:drop-shadow(0 0 2px rgba(79,95,173,.5))}50%{filter:drop-shadow(0 0 5px rgba(79,95,173,.8))}to{filter:drop-shadow(0 0 2px rgba(79,95,173,.5))}}@keyframes _ngcontent-%COMP%_glow-current-user{0%{filter:drop-shadow(0 0 2px rgba(255,255,255,.5))}50%{filter:drop-shadow(0 0 5px rgba(255,255,255,.8))}to{filter:drop-shadow(0 0 2px rgba(255,255,255,.5))}}@keyframes _ngcontent-%COMP%_fade-in{0%{opacity:.7;transform:translateY(2px)}to{opacity:1;transform:translateY(0)}}.voice-message-player.is-playing[_ngcontent-%COMP%]:not(.is-current-user)   .waveform-bar.active[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s infinite,bar-animation 1.2s ease-in-out infinite,glow 2s infinite}.voice-message-player.is-playing.is-current-user[_ngcontent-%COMP%]   .waveform-bar.active[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s infinite,bar-animation 1.2s ease-in-out infinite,glow-current-user 2s infinite}.voice-message-player[_ngcontent-%COMP%]:not(.is-current-user)   .pulse-animation[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse-button 2s infinite}.voice-message-player.is-current-user[_ngcontent-%COMP%]   .pulse-animation[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse-button-current-user 2s infinite}.voice-message-player[_ngcontent-%COMP%]   .fade-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fade-in .5s ease-in-out}.voice-message-player[_ngcontent-%COMP%]   .waveform-bar[_ngcontent-%COMP%]{transition:background-color .3s ease,transform .3s ease,filter .3s ease}.voice-message-player[_ngcontent-%COMP%]:hover   .waveform-bar[_ngcontent-%COMP%]{transform:scaleY(1.05)}\"]\n      });\n    }\n  }\n  return VoiceMessagePlayerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}