{"ast": null, "code": "function defaultDispose() {}\nexport class StrongCache {\n  constructor(max = Infinity, dispose = defaultDispose) {\n    this.max = max;\n    this.dispose = dispose;\n    this.map = new Map();\n    this.newest = null;\n    this.oldest = null;\n  }\n  has(key) {\n    return this.map.has(key);\n  }\n  get(key) {\n    const node = this.getNode(key);\n    return node && node.value;\n  }\n  get size() {\n    return this.map.size;\n  }\n  getNode(key) {\n    const node = this.map.get(key);\n    if (node && node !== this.newest) {\n      const {\n        older,\n        newer\n      } = node;\n      if (newer) {\n        newer.older = older;\n      }\n      if (older) {\n        older.newer = newer;\n      }\n      node.older = this.newest;\n      node.older.newer = node;\n      node.newer = null;\n      this.newest = node;\n      if (node === this.oldest) {\n        this.oldest = newer;\n      }\n    }\n    return node;\n  }\n  set(key, value) {\n    let node = this.getNode(key);\n    if (node) {\n      return node.value = value;\n    }\n    node = {\n      key,\n      value,\n      newer: null,\n      older: this.newest\n    };\n    if (this.newest) {\n      this.newest.newer = node;\n    }\n    this.newest = node;\n    this.oldest = this.oldest || node;\n    this.map.set(key, node);\n    return node.value;\n  }\n  clean() {\n    while (this.oldest && this.map.size > this.max) {\n      this.delete(this.oldest.key);\n    }\n  }\n  delete(key) {\n    const node = this.map.get(key);\n    if (node) {\n      if (node === this.newest) {\n        this.newest = node.older;\n      }\n      if (node === this.oldest) {\n        this.oldest = node.newer;\n      }\n      if (node.newer) {\n        node.newer.older = node.older;\n      }\n      if (node.older) {\n        node.older.newer = node.newer;\n      }\n      this.map.delete(key);\n      this.dispose(node.value, key);\n      return true;\n    }\n    return false;\n  }\n}\n//# sourceMappingURL=strong.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}