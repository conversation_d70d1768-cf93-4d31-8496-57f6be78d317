{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/rendus.service\";\nimport * as i4 from \"@angular/common\";\nfunction ProjectEvaluationComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"div\", 15)(3, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 17);\n    i0.ɵɵtext(5, \"Chargement des donn\\u00E9es...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectEvaluationComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 20);\n    i0.ɵɵelement(3, \"path\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"p\", 22);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_32_ng_container_7_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 56);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 57);\n    i0.ɵɵelement(2, \"path\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 59);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fichier_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"href\", \"http://localhost:3000/\" + fichier_r7, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((fichier_r7 == null ? null : fichier_r7.split(\"/\").pop()) || \"Fichier\");\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_32_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectEvaluationComponent_div_15_div_32_ng_container_7_a_1_Template, 5, 2, \"a\", 55);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const fichier_r7 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", fichier_r7);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 51);\n    i0.ɵɵelement(3, \"path\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 53);\n    i0.ɵɵtemplate(7, ProjectEvaluationComponent_div_15_div_32_ng_container_7_Template, 2, 1, \"ng-container\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Fichiers joints (\", ctx_r3.rendu.fichiers.length, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.rendu.fichiers);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_form_51__svg_svg_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 107);\n    i0.ɵɵelement(1, \"path\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_form_51__svg_svg_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 108);\n    i0.ɵɵelement(1, \"path\", 109);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_form_51_span_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumettre l'\\u00E9valuation\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_form_51_span_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumission en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_form_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"form\", 60);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectEvaluationComponent_div_15_form_51_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 61)(2, \"div\", 25)(3, \"div\", 62);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 27);\n    i0.ɵɵelement(5, \"path\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"h3\", 64);\n    i0.ɵɵtext(7, \"Crit\\u00E8res d'\\u00E9valuation\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 65)(9, \"div\", 66)(10, \"label\", 67)(11, \"div\", 68);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 69);\n    i0.ɵɵelement(13, \"path\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"Structure du code\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 14);\n    i0.ɵɵelement(17, \"input\", 71);\n    i0.ɵɵelementStart(18, \"div\", 72)(19, \"span\", 73);\n    i0.ɵɵtext(20, \"/5\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(21, \"div\", 66)(22, \"label\", 67)(23, \"div\", 68);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(24, \"svg\", 69);\n    i0.ɵɵelement(25, \"path\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27, \"Bonnes pratiques\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 14);\n    i0.ɵɵelement(29, \"input\", 74);\n    i0.ɵɵelementStart(30, \"div\", 72)(31, \"span\", 73);\n    i0.ɵɵtext(32, \"/5\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(33, \"div\", 66)(34, \"label\", 67)(35, \"div\", 68);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(36, \"svg\", 69);\n    i0.ɵɵelement(37, \"path\", 75)(38, \"path\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(39, \"span\");\n    i0.ɵɵtext(40, \"Fonctionnalit\\u00E9\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 14);\n    i0.ɵɵelement(42, \"input\", 77);\n    i0.ɵɵelementStart(43, \"div\", 72)(44, \"span\", 73);\n    i0.ɵɵtext(45, \"/5\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(46, \"div\", 66)(47, \"label\", 67)(48, \"div\", 68);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(49, \"svg\", 69);\n    i0.ɵɵelement(50, \"path\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(51, \"span\");\n    i0.ɵɵtext(52, \"Originalit\\u00E9\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 14);\n    i0.ɵɵelement(54, \"input\", 78);\n    i0.ɵɵelementStart(55, \"div\", 72)(56, \"span\", 73);\n    i0.ɵɵtext(57, \"/5\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(58, \"div\", 79)(59, \"div\", 80)(60, \"div\", 19)(61, \"div\", 26);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(62, \"svg\", 27);\n    i0.ɵɵelement(63, \"path\", 63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(64, \"span\", 81);\n    i0.ɵɵtext(65, \"Score total\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"div\", 82)(67, \"div\", 83);\n    i0.ɵɵtext(68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 84);\n    i0.ɵɵtext(70);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(71, \"div\", 85);\n    i0.ɵɵelement(72, \"div\", 86);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"div\", 87)(74, \"span\");\n    i0.ɵɵtext(75, \"0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"span\");\n    i0.ɵɵtext(77);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(78, \"div\", 61)(79, \"div\", 25)(80, \"div\", 88);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(81, \"svg\", 27);\n    i0.ɵɵelement(82, \"path\", 89);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(83, \"h3\", 64);\n    i0.ɵɵtext(84, \"Commentaires d\\u00E9taill\\u00E9s\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"div\", 14);\n    i0.ɵɵelement(86, \"textarea\", 90);\n    i0.ɵɵelementStart(87, \"div\", 91);\n    i0.ɵɵtext(88, \" Minimum 50 caract\\u00E8res \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(89, \"div\", 92);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(90, \"svg\", 93);\n    i0.ɵɵelement(91, \"path\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(92, \"div\", 95)(93, \"p\", 96);\n    i0.ɵɵtext(94, \"Conseils pour une \\u00E9valuation de qualit\\u00E9 :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(95, \"ul\", 97)(96, \"li\");\n    i0.ɵɵtext(97, \"\\u2022 Mentionnez les aspects techniques r\\u00E9ussis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"li\");\n    i0.ɵɵtext(99, \"\\u2022 Identifiez les points d'am\\u00E9lioration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"li\");\n    i0.ɵɵtext(101, \"\\u2022 Proposez des suggestions constructives\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(102, \"li\");\n    i0.ɵɵtext(103, \"\\u2022 \\u00C9valuez la cr\\u00E9ativit\\u00E9 et l'originalit\\u00E9\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(104, \"div\", 98)(105, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_form_51_Template_button_click_105_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.annuler());\n    });\n    i0.ɵɵelementStart(106, \"div\", 100);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(107, \"svg\", 101);\n    i0.ɵɵelement(108, \"path\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(109, \"span\");\n    i0.ɵɵtext(110, \"Annuler\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(111, \"button\", 103)(112, \"div\", 100);\n    i0.ɵɵtemplate(113, ProjectEvaluationComponent_div_15_form_51__svg_svg_113_Template, 2, 0, \"svg\", 104);\n    i0.ɵɵtemplate(114, ProjectEvaluationComponent_div_15_form_51__svg_svg_114_Template, 2, 0, \"svg\", 105);\n    i0.ɵɵtemplate(115, ProjectEvaluationComponent_div_15_form_51_span_115_Template, 2, 0, \"span\", 106);\n    i0.ɵɵtemplate(116, ProjectEvaluationComponent_div_15_form_51_span_116_Template, 2, 0, \"span\", 106);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r4.evaluationForm);\n    i0.ɵɵadvance(68);\n    i0.ɵɵtextInterpolate(ctx_r4.getScoreTotal());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"sur \", ctx_r4.getScoreMaximum(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r4.getScoreTotal() / ctx_r4.getScoreMaximum() * 100, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.getScoreMaximum());\n    i0.ɵɵadvance(36);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_52_div_1__svg_svg_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 107);\n    i0.ɵɵelement(1, \"path\", 125);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_52_div_1__svg_svg_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 108);\n    i0.ɵɵelement(1, \"path\", 109);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_52_div_1_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Lancer l'\\u00E9valuation IA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_52_div_1_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Lancement en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_52_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 25)(2, \"div\", 112);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 113);\n    i0.ɵɵelement(4, \"path\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 29);\n    i0.ɵɵtext(7, \"\\u00C9valuation automatique par IA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 114);\n    i0.ɵɵtext(9, \"Syst\\u00E8me d'intelligence artificielle Mistral 7B\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 115)(11, \"div\", 116)(12, \"div\", 117);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(13, \"svg\", 27);\n    i0.ɵɵelement(14, \"path\", 94);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(15, \"div\")(16, \"h4\", 118);\n    i0.ɵɵtext(17, \"Comment fonctionne l'\\u00E9valuation IA ?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 119);\n    i0.ɵɵtext(19, \"Notre syst\\u00E8me d'IA analysera automatiquement le code soumis selon les crit\\u00E8res suivants :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 120)(21, \"div\", 68);\n    i0.ɵɵelement(22, \"div\", 121);\n    i0.ɵɵelementStart(23, \"span\", 122);\n    i0.ɵɵtext(24, \"Structure et organisation\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 68);\n    i0.ɵɵelement(26, \"div\", 121);\n    i0.ɵɵelementStart(27, \"span\", 122);\n    i0.ɵɵtext(28, \"Bonnes pratiques\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 68);\n    i0.ɵɵelement(30, \"div\", 121);\n    i0.ɵɵelementStart(31, \"span\", 122);\n    i0.ɵɵtext(32, \"Fonctionnalit\\u00E9s\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 68);\n    i0.ɵɵelement(34, \"div\", 121);\n    i0.ɵɵelementStart(35, \"span\", 122);\n    i0.ɵɵtext(36, \"Originalit\\u00E9\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(37, \"div\", 123)(38, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_div_52_div_1_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r23.annuler());\n    });\n    i0.ɵɵelementStart(39, \"div\", 100);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(40, \"svg\", 101);\n    i0.ɵɵelement(41, \"path\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(42, \"span\");\n    i0.ɵɵtext(43, \"Annuler\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"button\", 124);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_div_52_div_1_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.onSubmit());\n    });\n    i0.ɵɵelementStart(45, \"div\", 100);\n    i0.ɵɵtemplate(46, ProjectEvaluationComponent_div_15_div_52_div_1__svg_svg_46_Template, 2, 0, \"svg\", 104);\n    i0.ɵɵtemplate(47, ProjectEvaluationComponent_div_15_div_52_div_1__svg_svg_47_Template, 2, 0, \"svg\", 105);\n    i0.ɵɵtemplate(48, ProjectEvaluationComponent_div_15_div_52_div_1_span_48_Template, 2, 0, \"span\", 106);\n    i0.ɵɵtemplate(49, ProjectEvaluationComponent_div_15_div_52_div_1_span_49_Template, 2, 0, \"span\", 106);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(44);\n    i0.ɵɵproperty(\"disabled\", ctx_r17.isSubmitting);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r17.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r17.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.isSubmitting);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_52_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 126)(1, \"div\", 127);\n    i0.ɵɵelement(2, \"div\", 128)(3, \"div\", 129);\n    i0.ɵɵelementStart(4, \"div\", 130);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 131);\n    i0.ɵɵelement(6, \"path\", 41);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"h3\", 132);\n    i0.ɵɵtext(8, \"L'IA analyse le projet...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 133);\n    i0.ɵɵtext(10, \"Notre syst\\u00E8me examine le code selon les crit\\u00E8res d'\\u00E9valuation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 134)(12, \"div\", 135);\n    i0.ɵɵelement(13, \"div\", 136)(14, \"div\", 137)(15, \"div\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 84);\n    i0.ɵɵtext(17, \"Cela peut prendre quelques instants\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectEvaluationComponent_div_15_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtemplate(1, ProjectEvaluationComponent_div_15_div_52_div_1_Template, 50, 5, \"div\", 106);\n    i0.ɵɵtemplate(2, ProjectEvaluationComponent_div_15_div_52_div_2_Template, 18, 0, \"div\", 111);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.aiProcessing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.aiProcessing);\n  }\n}\nfunction ProjectEvaluationComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"div\", 25)(3, \"div\", 26);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 27);\n    i0.ɵɵelement(5, \"path\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"h2\", 29);\n    i0.ɵɵtext(7, \"Informations sur le rendu\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 30)(9, \"div\", 31)(10, \"div\", 32)(11, \"p\", 33);\n    i0.ɵɵtext(12, \"Projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 34);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 35)(16, \"p\", 33);\n    i0.ɵɵtext(17, \"\\u00C9tudiant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 34);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 31)(21, \"div\", 36)(22, \"p\", 33);\n    i0.ɵɵtext(23, \"Date de soumission\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\", 34);\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 37)(28, \"p\", 33);\n    i0.ɵɵtext(29, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\", 38);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(32, ProjectEvaluationComponent_div_15_div_32_Template, 8, 2, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 24)(34, \"div\", 40)(35, \"div\", 19)(36, \"div\", 26);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(37, \"svg\", 27);\n    i0.ɵɵelement(38, \"path\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(39, \"h2\", 29);\n    i0.ɵɵtext(40, \"Mode d'\\u00E9valuation\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 4)(42, \"div\", 42)(43, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.evaluationMode = \"manual\");\n    });\n    i0.ɵɵtext(44, \" Manuel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.evaluationMode = \"ai\");\n    });\n    i0.ɵɵtext(46, \" IA \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ProjectEvaluationComponent_div_15_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.toggleEvaluationMode());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(48, \"svg\", 45);\n    i0.ɵɵelement(49, \"path\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(50, \" Basculer \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(51, ProjectEvaluationComponent_div_15_form_51_Template, 117, 10, \"form\", 47);\n    i0.ɵɵtemplate(52, ProjectEvaluationComponent_div_15_div_52_Template, 3, 2, \"div\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate(ctx_r2.rendu.projet.titre);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.rendu.etudiant.nom, \" \", ctx_r2.rendu.etudiant.prenom, \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(26, 12, ctx_r2.rendu.dateSoumission, \"dd/MM/yyyy HH:mm\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.rendu.description || \"Aucune description\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.fichiers && ctx_r2.rendu.fichiers.length > 0);\n    i0.ɵɵadvance(11);\n    i0.ɵɵclassMap(ctx_r2.evaluationMode === \"manual\" ? \"bg-white dark:bg-dark-bg-secondary text-primary dark:text-dark-accent-primary shadow-md\" : \"text-text dark:text-dark-text-secondary\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r2.evaluationMode === \"ai\" ? \"bg-white dark:bg-dark-bg-secondary text-primary dark:text-dark-accent-primary shadow-md\" : \"text-text dark:text-dark-text-secondary\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.evaluationMode === \"manual\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.evaluationMode === \"ai\");\n  }\n}\nexport let ProjectEvaluationComponent = /*#__PURE__*/(() => {\n  class ProjectEvaluationComponent {\n    constructor(fb, route, router, rendusService) {\n      this.fb = fb;\n      this.route = route;\n      this.router = router;\n      this.rendusService = rendusService;\n      this.renduId = '';\n      this.rendu = null;\n      this.isLoading = true;\n      this.isSubmitting = false;\n      this.error = '';\n      this.evaluationMode = 'manual';\n      this.aiProcessing = false;\n      this.evaluationForm = this.fb.group({\n        scores: this.fb.group({\n          structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n          pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n          fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n          originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\n        }),\n        commentaires: ['', Validators.required],\n        utiliserIA: [false]\n      });\n    }\n    ngOnInit() {\n      this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n      // Récupérer le mode d'évaluation des query params\n      const mode = this.route.snapshot.queryParamMap.get('mode');\n      if (mode === 'ai' || mode === 'manual') {\n        this.evaluationMode = mode;\n        this.evaluationForm.patchValue({\n          utiliserIA: mode === 'ai'\n        });\n        // Sauvegarder le mode dans localStorage pour les futures évaluations\n        localStorage.setItem('evaluationMode', mode);\n      } else {\n        // Récupérer le mode d'évaluation du localStorage\n        const storedMode = localStorage.getItem('evaluationMode');\n        if (storedMode === 'ai' || storedMode === 'manual') {\n          this.evaluationMode = storedMode;\n          this.evaluationForm.patchValue({\n            utiliserIA: storedMode === 'ai'\n          });\n        }\n      }\n      if (this.renduId) {\n        this.loadRendu();\n      } else {\n        this.error = 'ID de rendu manquant';\n        this.isLoading = false;\n      }\n    }\n    loadRendu() {\n      this.isLoading = true;\n      this.rendusService.getRenduById(this.renduId).subscribe({\n        next: data => {\n          this.rendu = data;\n          // Filter out null/undefined files\n          if (this.rendu.fichiers) {\n            this.rendu.fichiers = this.rendu.fichiers.filter(fichier => fichier != null && fichier !== '');\n          }\n          this.isLoading = false;\n        },\n        error: err => {\n          this.error = 'Erreur lors du chargement du rendu';\n          this.isLoading = false;\n          console.error(err);\n        }\n      });\n    }\n    toggleEvaluationMode() {\n      this.evaluationMode = this.evaluationMode === 'manual' ? 'ai' : 'manual';\n      this.evaluationForm.patchValue({\n        utiliserIA: this.evaluationMode === 'ai'\n      });\n      localStorage.setItem('evaluationMode', this.evaluationMode);\n    }\n    onSubmit() {\n      console.log('Submit clicked, form valid:', this.evaluationForm.valid);\n      console.log('Form values:', this.evaluationForm.value);\n      this.isSubmitting = true;\n      this.error = '';\n      // Si mode IA, mettre à jour le formulaire pour indiquer l'utilisation de l'IA\n      if (this.evaluationMode === 'ai') {\n        this.evaluationForm.patchValue({\n          utiliserIA: true\n        });\n        this.aiProcessing = true;\n      }\n      const evaluationData = this.evaluationForm.value;\n      console.log('Sending evaluation data:', evaluationData);\n      this.rendusService.evaluateRendu(this.renduId, evaluationData).subscribe({\n        next: response => {\n          // Si l'évaluation a été faite par l'IA, mettre à jour le formulaire avec les résultats\n          if (this.evaluationMode === 'ai' && response.evaluation) {\n            const aiScores = response.evaluation.scores;\n            const aiCommentaires = response.evaluation.commentaires;\n            this.evaluationForm.patchValue({\n              scores: {\n                structure: aiScores.structure || 0,\n                pratiques: aiScores.pratiques || 0,\n                fonctionnalite: aiScores.fonctionnalite || 0,\n                originalite: aiScores.originalite || 0\n              },\n              commentaires: aiCommentaires || 'Évaluation générée par IA'\n            });\n            this.aiProcessing = false;\n            this.isSubmitting = false;\n            // Afficher un message de succès\n            this.error = '';\n            alert('Évaluation par IA réussie! Vous pouvez modifier les résultats avant de confirmer.');\n          } else {\n            // Si évaluation manuelle ou confirmation après IA, rediriger vers la liste des rendus\n            this.isSubmitting = false;\n            alert('Évaluation soumise avec succès!');\n            this.router.navigate(['/admin/projects/rendus']);\n          }\n        },\n        error: err => {\n          this.error = 'Erreur lors de l\\'évaluation du rendu: ' + (err.error?.message || err.message || 'Erreur inconnue');\n          this.isSubmitting = false;\n          this.aiProcessing = false;\n          console.error(err);\n        }\n      });\n    }\n    getScoreTotal() {\n      const scores = this.evaluationForm.get('scores')?.value;\n      if (!scores) return 0;\n      return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n    }\n    getScoreMaximum() {\n      return 20; // 4 critères x 5 points maximum\n    }\n\n    annuler() {\n      // Confirmer avant d'annuler si des données ont été saisies\n      const formData = this.evaluationForm.value;\n      const hasData = formData.scores?.structure || formData.scores?.pratiques || formData.scores?.fonctionnalite || formData.scores?.originalite || formData.commentaires;\n      if (hasData) {\n        const confirmation = confirm('Êtes-vous sûr de vouloir annuler ? Toutes les données saisies seront perdues.');\n        if (!confirmation) {\n          return;\n        }\n      }\n      console.log('Navigation vers la liste des rendus...');\n      this.router.navigate(['/admin/projects/rendus']);\n    }\n    static {\n      this.ɵfac = function ProjectEvaluationComponent_Factory(t) {\n        return new (t || ProjectEvaluationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RendusService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectEvaluationComponent,\n        selectors: [[\"app-project-evaluation\"]],\n        decls: 16,\n        vars: 3,\n        consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-dark-bg-primary\", \"transition-colors\", \"duration-300\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"max-w-5xl\", \"mx-auto\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-2xl\", \"p-8\", \"mb-8\", \"shadow-xl\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"bg-white/20\", \"dark:bg-black/20\", \"p-3\", \"rounded-xl\", \"backdrop-blur-sm\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\"], [1, \"text-white/80\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-4 mb-6 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"space-y-8\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"relative\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-primary/30\", \"dark:border-dark-accent-primary/30\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-transparent\", \"border-t-primary\", \"dark:border-t-dark-accent-primary\", \"absolute\", \"top-0\", \"left-0\"], [1, \"mt-4\", \"text-text\", \"dark:text-dark-text-secondary\", \"animate-pulse\"], [1, \"bg-danger/10\", \"dark:bg-danger-dark/20\", \"border\", \"border-danger/30\", \"dark:border-danger-dark/40\", \"text-danger\", \"dark:text-danger-dark\", \"rounded-xl\", \"p-4\", \"mb-6\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-danger\", \"dark:text-danger-dark\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"font-medium\"], [1, \"space-y-8\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-6\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-2xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-2\", \"gap-6\"], [1, \"space-y-4\"], [1, \"bg-gradient-to-r\", \"from-primary/5\", \"to-primary-dark/5\", \"dark:from-dark-accent-primary/10\", \"dark:to-dark-accent-secondary/10\", \"rounded-xl\", \"p-4\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\", \"mb-1\"], [1, \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"text-lg\"], [1, \"bg-gradient-to-r\", \"from-secondary/5\", \"to-secondary-dark/5\", \"dark:from-dark-accent-secondary/10\", \"dark:to-dark-accent-primary/10\", \"rounded-xl\", \"p-4\"], [1, \"bg-gradient-to-r\", \"from-info/5\", \"to-info/10\", \"dark:from-dark-accent-primary/5\", \"dark:to-dark-accent-primary/10\", \"rounded-xl\", \"p-4\"], [1, \"bg-gradient-to-r\", \"from-success/5\", \"to-success/10\", \"dark:from-success/10\", \"dark:to-success/5\", \"rounded-xl\", \"p-4\"], [1, \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"class\", \"mt-6 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-bg-tertiary/30 dark:to-dark-bg-tertiary/50 rounded-xl p-4\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"sm:items-center\", \"sm:justify-between\", \"gap-4\", \"mb-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"], [1, \"flex\", \"items-center\", \"bg-gray-100\", \"dark:bg-dark-bg-tertiary\", \"rounded-xl\", \"p-1\", \"shadow-inner\"], [1, \"px-4\", \"py-2\", \"rounded-lg\", \"font-medium\", \"transition-all\", \"duration-200\", \"text-sm\", 3, \"click\"], [1, \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"inline\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4\"], [\"class\", \"space-y-8\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [\"class\", \"bg-gradient-to-br from-gray-50/50 to-white/50 dark:from-dark-bg-tertiary/30 dark:to-dark-bg-secondary/30 rounded-2xl p-8 border border-gray-200/50 dark:border-dark-bg-tertiary/50\", 4, \"ngIf\"], [1, \"mt-6\", \"bg-gradient-to-r\", \"from-gray-50\", \"to-gray-100\", \"dark:from-dark-bg-tertiary/30\", \"dark:to-dark-bg-tertiary/50\", \"rounded-xl\", \"p-4\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mb-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"lg:grid-cols-3\", \"gap-3\"], [4, \"ngFor\", \"ngForOf\"], [\"target\", \"_blank\", \"class\", \"flex items-center space-x-2 p-3 bg-white dark:bg-dark-bg-secondary rounded-lg hover:bg-primary/5 dark:hover:bg-dark-accent-primary/10 transition-all duration-200 border border-gray-200 dark:border-dark-bg-tertiary group\", 3, \"href\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"flex\", \"items-center\", \"space-x-2\", \"p-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"rounded-lg\", \"hover:bg-primary/5\", \"dark:hover:bg-dark-accent-primary/10\", \"transition-all\", \"duration-200\", \"border\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"group\", 3, \"href\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"truncate\"], [1, \"space-y-8\", 3, \"formGroup\", \"ngSubmit\"], [1, \"bg-gradient-to-br\", \"from-gray-50/50\", \"to-white/50\", \"dark:from-dark-bg-tertiary/30\", \"dark:to-dark-bg-secondary/30\", \"rounded-2xl\", \"p-6\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"bg-gradient-to-r\", \"from-success\", \"to-success-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"p-2\", \"rounded-lg\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"formGroupName\", \"scores\", 1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-2\", \"gap-6\"], [1, \"group\"], [1, \"block\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-3\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [\"type\", \"number\", \"formControlName\", \"structure\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-text\", \"dark:placeholder-dark-text-secondary\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"flex\", \"items-center\", \"pr-4\"], [1, \"text-sm\", \"font-medium\", \"text-text\", \"dark:text-dark-text-secondary\"], [\"type\", \"number\", \"formControlName\", \"pratiques\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-text\", \"dark:placeholder-dark-text-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [\"type\", \"number\", \"formControlName\", \"fonctionnalite\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-text\", \"dark:placeholder-dark-text-secondary\"], [\"type\", \"number\", \"formControlName\", \"originalite\", \"min\", \"0\", \"max\", \"5\", \"placeholder\", \"0-5\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-text\", \"dark:placeholder-dark-text-secondary\"], [1, \"mt-8\", \"bg-gradient-to-r\", \"from-primary/5\", \"to-primary-dark/5\", \"dark:from-dark-accent-primary/10\", \"dark:to-dark-accent-secondary/10\", \"rounded-2xl\", \"p-6\", \"border\", \"border-primary/20\", \"dark:border-dark-accent-primary/30\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"text-lg\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-right\"], [1, \"text-3xl\", \"font-bold\", \"text-primary\", \"dark:text-dark-accent-primary\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"w-full\", \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"rounded-full\", \"h-3\", \"overflow-hidden\"], [1, \"h-full\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-full\", \"transition-all\", \"duration-500\", \"ease-out\"], [1, \"flex\", \"justify-between\", \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\", \"mt-2\"], [1, \"bg-gradient-to-r\", \"from-info\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"p-2\", \"rounded-lg\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"], [\"formControlName\", \"commentaires\", \"rows\", \"6\", \"placeholder\", \"D\\u00E9crivez les points forts et les axes d'am\\u00E9lioration du projet. Soyez pr\\u00E9cis et constructif dans vos commentaires...\", 1, \"w-full\", \"px-4\", \"py-4\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-text\", \"dark:placeholder-dark-text-secondary\", \"resize-none\"], [1, \"absolute\", \"bottom-3\", \"right-3\", \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\", \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"px-2\", \"py-1\", \"rounded-lg\", \"backdrop-blur-sm\"], [1, \"mt-4\", \"flex\", \"items-start\", \"space-x-3\", \"p-4\", \"bg-primary/5\", \"dark:bg-dark-accent-primary/10\", \"rounded-xl\", \"border\", \"border-primary/20\", \"dark:border-dark-accent-primary/30\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-primary\", \"dark:text-dark-accent-primary\", \"mt-0.5\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"font-medium\", \"mb-1\"], [1, \"space-y-1\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\", \"justify-between\", \"items-center\", \"pt-6\"], [\"type\", \"button\", 1, \"w-full\", \"sm:w-auto\", \"group\", \"px-6\", \"py-3\", \"bg-gray-100\", \"dark:bg-dark-bg-tertiary\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"rounded-xl\", \"hover:bg-gray-200\", \"dark:hover:bg-dark-bg-tertiary/80\", \"transition-all\", \"duration-200\", \"font-medium\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"hover:border-gray-300\", \"dark:hover:border-dark-bg-tertiary/60\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [\"type\", \"submit\", 1, \"w-full\", \"sm:w-auto\", \"group\", \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-success\", \"to-success-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-xl\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-semibold\", \"border-2\", \"border-transparent\", \"hover:border-success/30\", \"dark:hover:border-dark-accent-primary/30\"], [\"class\", \"w-5 h-5 group-hover:scale-110 transition-transform\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [\"class\", \"w-5 h-5 animate-spin\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [4, \"ngIf\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"group-hover:scale-110\", \"transition-transform\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"animate-spin\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"], [1, \"bg-gradient-to-br\", \"from-gray-50/50\", \"to-white/50\", \"dark:from-dark-bg-tertiary/30\", \"dark:to-dark-bg-secondary/30\", \"rounded-2xl\", \"p-8\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [\"class\", \"text-center py-12\", 4, \"ngIf\"], [1, \"bg-gradient-to-r\", \"from-secondary\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"p-3\", \"rounded-xl\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-white\"], [1, \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"bg-gradient-to-r\", \"from-primary/5\", \"to-secondary/5\", \"dark:from-dark-accent-primary/10\", \"dark:to-dark-accent-secondary/10\", \"rounded-2xl\", \"p-6\", \"mb-8\", \"border\", \"border-primary/20\", \"dark:border-dark-accent-primary/30\"], [1, \"flex\", \"items-start\", \"space-x-4\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-secondary\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"p-2\", \"rounded-lg\", \"flex-shrink-0\"], [1, \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-3\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-3\"], [1, \"w-2\", \"h-2\", \"bg-gradient-to-r\", \"from-primary\", \"to-secondary\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-full\"], [1, \"text-sm\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"font-medium\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\", \"justify-between\", \"items-center\"], [1, \"w-full\", \"sm:w-auto\", \"group\", \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-xl\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-semibold\", \"border-2\", \"border-transparent\", \"hover:border-secondary/30\", \"dark:hover:border-dark-accent-secondary/30\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:hover:scale-100\", 3, \"disabled\", \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 10V3L4 14h7v7l9-11h-7z\"], [1, \"text-center\", \"py-12\"], [1, \"relative\", \"mb-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-20\", \"w-20\", \"border-4\", \"border-primary/30\", \"dark:border-dark-accent-primary/30\", \"mx-auto\"], [1, \"animate-spin\", \"rounded-full\", \"h-20\", \"w-20\", \"border-4\", \"border-transparent\", \"border-t-primary\", \"dark:border-t-dark-accent-primary\", \"absolute\", \"top-0\", \"left-1/2\", \"transform\", \"-translate-x-1/2\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-primary\", \"dark:text-dark-accent-primary\", \"animate-pulse\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-2\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"mb-6\"], [1, \"bg-gradient-to-r\", \"from-primary/5\", \"to-secondary/5\", \"dark:from-dark-accent-primary/10\", \"dark:to-dark-accent-secondary/10\", \"rounded-2xl\", \"p-4\", \"max-w-md\", \"mx-auto\", \"border\", \"border-primary/20\", \"dark:border-dark-accent-primary/30\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", \"mb-2\"], [1, \"w-2\", \"h-2\", \"bg-gradient-to-r\", \"from-primary\", \"to-secondary\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-full\", \"animate-bounce\"], [1, \"w-2\", \"h-2\", \"bg-gradient-to-r\", \"from-primary\", \"to-secondary\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-2\", \"h-2\", \"bg-gradient-to-r\", \"from-primary\", \"to-secondary\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-full\", \"animate-bounce\", 2, \"animation-delay\", \"0.2s\"]],\n        template: function ProjectEvaluationComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(6, \"svg\", 6);\n            i0.ɵɵelement(7, \"path\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(8, \"div\")(9, \"h1\", 8);\n            i0.ɵɵtext(10, \"\\u00C9valuation du projet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"p\", 9);\n            i0.ɵɵtext(12, \"Syst\\u00E8me d'\\u00E9valuation intelligent avec IA int\\u00E9gr\\u00E9e\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(13, ProjectEvaluationComponent_div_13_Template, 6, 0, \"div\", 10);\n            i0.ɵɵtemplate(14, ProjectEvaluationComponent_div_14_Template, 6, 1, \"div\", 11);\n            i0.ɵɵtemplate(15, ProjectEvaluationComponent_div_15_Template, 53, 15, \"div\", 12);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.rendu && !ctx.isLoading);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i4.DatePipe],\n        styles: [\".container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}.form-group[_ngcontent-%COMP%]{margin-bottom:1rem}.error-message[_ngcontent-%COMP%]{color:#dc3545;margin-top:.25rem}.loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:2rem 0}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_glow{0%,to{box-shadow:0 0 5px #4f5fad4d}50%{box-shadow:0 0 20px #4f5fad99,0 0 30px #4f5fad66}}@keyframes _ngcontent-%COMP%_glowDark{0%,to{box-shadow:0 0 5px #00f7ff4d}50%{box-shadow:0 0 20px #00f7ff99,0 0 30px #00f7ff66}}.form-input-enhanced[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}.form-input-enhanced[_ngcontent-%COMP%]:focus{transform:translateY(-2px)}.btn-modern[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1)}.btn-modern[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.btn-modern[_ngcontent-%COMP%]:hover:before{left:100%}.glass-card[_ngcontent-%COMP%]{backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2)}.dark[_ngcontent-%COMP%]   .glass-card[_ngcontent-%COMP%]{border:1px solid rgba(255,255,255,.1)}.animate-fade-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}.animate-slide-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInRight .6s ease-out}.progress-bar-animated[_ngcontent-%COMP%]{background:linear-gradient(90deg,#4f5fad,#7826b5);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 2s infinite}.dark[_ngcontent-%COMP%]   .progress-bar-animated[_ngcontent-%COMP%]{background:linear-gradient(90deg,#00f7ff,#9d4edd);background-size:200% 100%}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200% 0}to{background-position:200% 0}}.tooltip[_ngcontent-%COMP%]{position:relative}.tooltip[_ngcontent-%COMP%]:after{content:attr(data-tooltip);position:absolute;bottom:100%;left:50%;transform:translate(-50%);background:rgba(0,0,0,.8);color:#fff;padding:.5rem;border-radius:.375rem;font-size:.75rem;white-space:nowrap;opacity:0;pointer-events:none;transition:opacity .3s;z-index:1000}.tooltip[_ngcontent-%COMP%]:hover:after{opacity:1}@media (max-width: 768px){.container[_ngcontent-%COMP%]{padding:1rem}.grid-responsive[_ngcontent-%COMP%]{grid-template-columns:1fr}.btn-modern[_ngcontent-%COMP%]{width:100%;justify-content:center}}.focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #4f5fad;outline-offset:2px}.dark[_ngcontent-%COMP%]   .focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #00f7ff}.icon-hover[_ngcontent-%COMP%]{transition:transform .2s ease}.icon-hover[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(5deg)}.alert-modern[_ngcontent-%COMP%]{border-left:4px solid;background:rgba(255,255,255,.9);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.dark[_ngcontent-%COMP%]   .alert-modern[_ngcontent-%COMP%]{background:rgba(0,0,0,.3)}\"]\n      });\n    }\n  }\n  return ProjectEvaluationComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}