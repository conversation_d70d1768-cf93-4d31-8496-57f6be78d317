{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/rendus.service\";\nimport * as i4 from \"@angular/common\";\nfunction EditEvaluationComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"div\", 12);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Chargement des donn\\u00E9es...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EditEvaluationComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"i\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction EditEvaluationComponent_div_17_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"label\");\n    i0.ɵɵtext(2, \"Fichier soumis:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 61);\n    i0.ɵɵelement(4, \"i\", 62);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r3.getFileUrl(ctx_r3.rendu.fichierRendu), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getFileName(ctx_r3.rendu.fichierRendu), \" \");\n  }\n}\nfunction EditEvaluationComponent_div_17_div_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"small\", 64);\n    i0.ɵɵtext(2, \"Les commentaires sont obligatoires\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EditEvaluationComponent_div_17_i_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 65);\n  }\n}\nfunction EditEvaluationComponent_div_17_i_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 66);\n  }\n}\nfunction EditEvaluationComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"h2\", 17);\n    i0.ɵɵelement(3, \"i\", 18);\n    i0.ɵɵtext(4, \" Informations sur le rendu \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 19)(6, \"div\", 20)(7, \"label\");\n    i0.ɵɵtext(8, \"Projet:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 20)(12, \"label\");\n    i0.ɵɵtext(13, \"\\u00C9tudiant:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 20)(17, \"label\");\n    i0.ɵɵtext(18, \"Groupe:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 20)(22, \"label\");\n    i0.ɵɵtext(23, \"Date de soumission:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 21)(28, \"label\");\n    i0.ɵɵtext(29, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, EditEvaluationComponent_div_17_div_32_Template, 6, 2, \"div\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"form\", 23);\n    i0.ɵɵlistener(\"ngSubmit\", function EditEvaluationComponent_div_17_Template_form_ngSubmit_33_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onSubmit());\n    });\n    i0.ɵɵelementStart(34, \"div\", 24)(35, \"h3\", 17);\n    i0.ɵɵelement(36, \"i\", 25);\n    i0.ɵɵtext(37, \" Crit\\u00E8res d'\\u00E9valuation \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 26)(39, \"div\", 27)(40, \"div\", 28)(41, \"label\", 29);\n    i0.ɵɵelement(42, \"i\", 30);\n    i0.ɵɵtext(43, \" Structure et organisation du code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 31);\n    i0.ɵɵelement(45, \"input\", 32);\n    i0.ɵɵelementStart(46, \"span\", 33);\n    i0.ɵɵtext(47, \"/ 5\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"small\", 34);\n    i0.ɵɵtext(49, \"Qualit\\u00E9 de l'organisation et de la structure du code\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 28)(51, \"label\", 35);\n    i0.ɵɵelement(52, \"i\", 36);\n    i0.ɵɵtext(53, \" Bonnes pratiques \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 31);\n    i0.ɵɵelement(55, \"input\", 37);\n    i0.ɵɵelementStart(56, \"span\", 33);\n    i0.ɵɵtext(57, \"/ 5\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"small\", 34);\n    i0.ɵɵtext(59, \"Respect des conventions et bonnes pratiques\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 28)(61, \"label\", 38);\n    i0.ɵɵelement(62, \"i\", 39);\n    i0.ɵɵtext(63, \" Fonctionnalit\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"div\", 31);\n    i0.ɵɵelement(65, \"input\", 40);\n    i0.ɵɵelementStart(66, \"span\", 33);\n    i0.ɵɵtext(67, \"/ 5\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"small\", 34);\n    i0.ɵɵtext(69, \"Fonctionnement correct et complet\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"div\", 28)(71, \"label\", 41);\n    i0.ɵɵelement(72, \"i\", 42);\n    i0.ɵɵtext(73, \" Originalit\\u00E9 et cr\\u00E9ativit\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"div\", 31);\n    i0.ɵɵelement(75, \"input\", 43);\n    i0.ɵɵelementStart(76, \"span\", 33);\n    i0.ɵɵtext(77, \"/ 5\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(78, \"small\", 34);\n    i0.ɵɵtext(79, \"Innovation et cr\\u00E9ativit\\u00E9 dans la solution\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(80, \"div\", 44)(81, \"div\", 45)(82, \"span\", 46);\n    i0.ɵɵtext(83, \"Score total:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"span\", 47);\n    i0.ɵɵtext(85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"span\", 48);\n    i0.ɵɵtext(87);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(88, \"div\", 49);\n    i0.ɵɵtext(89);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(90, \"div\", 50)(91, \"label\", 51);\n    i0.ɵɵelement(92, \"i\", 52);\n    i0.ɵɵtext(93, \" Commentaires et observations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(94, \"textarea\", 53);\n    i0.ɵɵtemplate(95, EditEvaluationComponent_div_17_div_95_Template, 3, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(96, \"div\", 55)(97, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function EditEvaluationComponent_div_17_Template_button_click_97_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.annuler());\n    });\n    i0.ɵɵelement(98, \"i\", 57);\n    i0.ɵɵtext(99, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"button\", 58);\n    i0.ɵɵtemplate(101, EditEvaluationComponent_div_17_i_101_Template, 1, 0, \"i\", 59);\n    i0.ɵɵtemplate(102, EditEvaluationComponent_div_17_i_102_Template, 1, 0, \"i\", 60);\n    i0.ɵɵtext(103);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_10_0;\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate((ctx_r2.rendu.projet == null ? null : ctx_r2.rendu.projet.titre) || \"Non sp\\u00E9cifi\\u00E9\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.nom, \" \", ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.prenom, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.groupe) || \"Non sp\\u00E9cifi\\u00E9\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(26, 17, ctx_r2.rendu.dateSoumission, \"dd/MM/yyyy \\u00E0 HH:mm\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.rendu.description || \"Aucune description\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.fichierRendu);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.evaluationForm);\n    i0.ɵɵadvance(52);\n    i0.ɵɵtextInterpolate(ctx_r2.getScoreTotal());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"/ \", ctx_r2.getScoreMaximum(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.getScoreTotal() / ctx_r2.getScoreMaximum() * 100).toFixed(1), \"% \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r2.evaluationForm.get(\"commentaires\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx_r2.evaluationForm.get(\"commentaires\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isSubmitting);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.evaluationForm.invalid || ctx_r2.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isSubmitting ? \"Enregistrement...\" : \"Enregistrer l'\\u00E9valuation\", \" \");\n  }\n}\nexport let EditEvaluationComponent = /*#__PURE__*/(() => {\n  class EditEvaluationComponent {\n    constructor(fb, route, router, rendusService) {\n      this.fb = fb;\n      this.route = route;\n      this.router = router;\n      this.rendusService = rendusService;\n      this.renduId = '';\n      this.rendu = null;\n      this.isLoading = true;\n      this.isSubmitting = false;\n      this.error = '';\n      this.evaluationForm = this.fb.group({\n        scores: this.fb.group({\n          structure: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n          pratiques: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n          fonctionnalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]],\n          originalite: [0, [Validators.required, Validators.min(0), Validators.max(5)]]\n        }),\n        commentaires: ['', Validators.required]\n      });\n    }\n    ngOnInit() {\n      this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n      if (this.renduId) {\n        this.loadRendu();\n      } else {\n        this.error = 'ID de rendu manquant';\n        this.isLoading = false;\n      }\n    }\n    loadRendu() {\n      this.isLoading = true;\n      this.rendusService.getRenduById(this.renduId).subscribe({\n        next: data => {\n          this.rendu = data;\n          // Remplir le formulaire avec les données existantes\n          if (this.rendu.evaluation && this.rendu.evaluation.scores) {\n            this.evaluationForm.patchValue({\n              scores: {\n                structure: this.rendu.evaluation.scores.structure || 0,\n                pratiques: this.rendu.evaluation.scores.pratiques || 0,\n                fonctionnalite: this.rendu.evaluation.scores.fonctionnalite || 0,\n                originalite: this.rendu.evaluation.scores.originalite || 0\n              },\n              commentaires: this.rendu.evaluation.commentaires || ''\n            });\n          }\n          this.isLoading = false;\n        },\n        error: err => {\n          this.error = 'Erreur lors du chargement du rendu';\n          this.isLoading = false;\n          console.error(err);\n        }\n      });\n    }\n    onSubmit() {\n      if (this.evaluationForm.invalid) {\n        return;\n      }\n      this.isSubmitting = true;\n      const evaluationData = this.evaluationForm.value;\n      // Assurez-vous que renduId est disponible\n      if (!this.renduId) {\n        this.error = \"ID du rendu manquant\";\n        this.isSubmitting = false;\n        return;\n      }\n      this.rendusService.updateEvaluation(this.renduId, evaluationData).subscribe({\n        next: response => {\n          this.isSubmitting = false;\n          // Redirection vers la page de liste des rendus après succès\n          this.router.navigate(['/admin/projects/list-rendus']);\n        },\n        error: err => {\n          this.error = `Erreur lors de la mise à jour de l'évaluation: ${err.message || 'Erreur inconnue'}`;\n          this.isSubmitting = false;\n          console.error(err);\n        }\n      });\n    }\n    getScoreTotal() {\n      const scores = this.evaluationForm.get('scores')?.value;\n      if (!scores) return 0;\n      return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n    }\n    getScoreMaximum() {\n      return 20; // 4 critères x 5 points maximum\n    }\n\n    annuler() {\n      this.router.navigate(['/admin/projects/list-rendus']);\n    }\n    // Méthodes pour gérer les fichiers\n    getFileUrl(filePath) {\n      if (!filePath) return '';\n      // Extraire uniquement le nom du fichier\n      let fileName = filePath;\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        fileName = parts[parts.length - 1];\n      }\n      // Utiliser la route spécifique pour le téléchargement\n      return `${environment.urlBackend}projets/telecharger/${fileName}`;\n    }\n    getFileName(filePath) {\n      if (!filePath) return 'Fichier';\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        return parts[parts.length - 1];\n      }\n      return filePath;\n    }\n    static {\n      this.ɵfac = function EditEvaluationComponent_Factory(t) {\n        return new (t || EditEvaluationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.RendusService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EditEvaluationComponent,\n        selectors: [[\"app-edit-evaluation\"]],\n        decls: 18,\n        vars: 3,\n        consts: [[1, \"evaluation-container\"], [1, \"header-section\"], [1, \"page-title\"], [1, \"breadcrumb\"], [\"routerLink\", \"/admin/projects\", 1, \"breadcrumb-link\"], [1, \"breadcrumb-separator\"], [\"routerLink\", \"/admin/projects/list-rendus\", 1, \"breadcrumb-link\"], [1, \"breadcrumb-current\"], [\"class\", \"loading-spinner\", 4, \"ngIf\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"class\", \"main-content\", 4, \"ngIf\"], [1, \"loading-spinner\"], [1, \"spinner\"], [1, \"error-message\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"main-content\"], [1, \"rendu-info-card\"], [1, \"card-title\"], [1, \"fas\", \"fa-file-alt\"], [1, \"info-grid\"], [1, \"info-item\"], [1, \"info-item\", \"full-width\"], [\"class\", \"info-item full-width\", 4, \"ngIf\"], [1, \"evaluation-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-card\"], [1, \"fas\", \"fa-star\"], [\"formGroupName\", \"scores\", 1, \"scores-section\"], [1, \"score-grid\"], [1, \"score-item\"], [\"for\", \"structure\", 1, \"score-label\"], [1, \"fas\", \"fa-code\"], [1, \"score-input-container\"], [\"id\", \"structure\", \"type\", \"number\", \"formControlName\", \"structure\", \"min\", \"0\", \"max\", \"5\", \"step\", \"0.5\", \"placeholder\", \"0\", 1, \"score-input\"], [1, \"score-max\"], [1, \"score-description\"], [\"for\", \"pratiques\", 1, \"score-label\"], [1, \"fas\", \"fa-check-circle\"], [\"id\", \"pratiques\", \"type\", \"number\", \"formControlName\", \"pratiques\", \"min\", \"0\", \"max\", \"5\", \"step\", \"0.5\", \"placeholder\", \"0\", 1, \"score-input\"], [\"for\", \"fonctionnalite\", 1, \"score-label\"], [1, \"fas\", \"fa-cogs\"], [\"id\", \"fonctionnalite\", \"type\", \"number\", \"formControlName\", \"fonctionnalite\", \"min\", \"0\", \"max\", \"5\", \"step\", \"0.5\", \"placeholder\", \"0\", 1, \"score-input\"], [\"for\", \"originalite\", 1, \"score-label\"], [1, \"fas\", \"fa-lightbulb\"], [\"id\", \"originalite\", \"type\", \"number\", \"formControlName\", \"originalite\", \"min\", \"0\", \"max\", \"5\", \"step\", \"0.5\", \"placeholder\", \"0\", 1, \"score-input\"], [1, \"score-total\"], [1, \"total-display\"], [1, \"total-label\"], [1, \"total-value\"], [1, \"total-max\"], [1, \"total-percentage\"], [1, \"comments-section\"], [\"for\", \"commentaires\", 1, \"comments-label\"], [1, \"fas\", \"fa-comment-alt\"], [\"id\", \"commentaires\", \"formControlName\", \"commentaires\", \"rows\", \"6\", \"placeholder\", \"Ajoutez vos commentaires d\\u00E9taill\\u00E9s sur le travail de l'\\u00E9tudiant...\", 1, \"comments-textarea\"], [\"class\", \"form-validation\", 4, \"ngIf\"], [1, \"actions\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-times\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [\"class\", \"fas fa-save\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"file-link\", 3, \"href\"], [1, \"fas\", \"fa-download\"], [1, \"form-validation\"], [1, \"error-text\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"fas\", \"fa-save\"]],\n        template: function EditEvaluationComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n            i0.ɵɵtext(3, \"Modifier l'\\u00E9valuation\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"nav\", 3)(5, \"a\", 4);\n            i0.ɵɵtext(6, \"Projets\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"span\", 5);\n            i0.ɵɵtext(8, \">\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"a\", 6);\n            i0.ɵɵtext(10, \"Rendus\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"span\", 5);\n            i0.ɵɵtext(12, \">\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"span\", 7);\n            i0.ɵɵtext(14, \"Modifier \\u00E9valuation\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(15, EditEvaluationComponent_div_15_Template, 4, 0, \"div\", 8);\n            i0.ɵɵtemplate(16, EditEvaluationComponent_div_16_Template, 3, 1, \"div\", 9);\n            i0.ɵɵtemplate(17, EditEvaluationComponent_div_17_Template, 104, 20, \"div\", 10);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.rendu && !ctx.isLoading);\n          }\n        },\n        dependencies: [i4.NgIf, i2.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i4.DatePipe],\n        styles: [\".evaluation-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px;background-color:#f8fafc;min-height:100vh}.header-section[_ngcontent-%COMP%]{margin-bottom:30px}.page-title[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#1e293b;margin-bottom:10px;display:flex;align-items:center;gap:10px}.page-title[_ngcontent-%COMP%]:before{content:\\\"\\\\1f4dd\\\";font-size:1.5rem}.breadcrumb[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:.875rem;color:#64748b}.breadcrumb-link[_ngcontent-%COMP%]{color:#3b82f6;text-decoration:none;transition:color .2s}.breadcrumb-link[_ngcontent-%COMP%]:hover{color:#1d4ed8;text-decoration:underline}.breadcrumb-separator[_ngcontent-%COMP%]{color:#94a3b8}.breadcrumb-current[_ngcontent-%COMP%]{color:#1e293b;font-weight:500}.loading-spinner[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:60px 20px;text-align:center}.spinner[_ngcontent-%COMP%]{width:50px;height:50px;border:4px solid #e2e8f0;border-top:4px solid #3b82f6;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin-bottom:15px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.loading-spinner[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#64748b;font-size:1rem;margin:0}.error-message[_ngcontent-%COMP%]{background-color:#fef2f2;border:1px solid #fecaca;color:#dc2626;padding:16px;border-radius:8px;margin-bottom:20px;display:flex;align-items:center;gap:10px;font-weight:500}.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.2rem}.main-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:25px}.rendu-info-card[_ngcontent-%COMP%]{background:white;border-radius:12px;padding:25px;box-shadow:0 1px 3px #0000001a;border:1px solid #e2e8f0}.card-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#1e293b;margin-bottom:20px;display:flex;align-items:center;gap:10px;padding-bottom:10px;border-bottom:2px solid #f1f5f9}.card-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3b82f6;font-size:1.1rem}.info-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:20px}.info-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:5px}.info-item.full-width[_ngcontent-%COMP%]{grid-column:1 / -1}.info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:600;color:#374151;font-size:.875rem;text-transform:uppercase;letter-spacing:.05em}.info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#1e293b;font-size:1rem;padding:8px 12px;background-color:#f8fafc;border-radius:6px;border:1px solid #e2e8f0}.file-link[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:8px;color:#3b82f6;text-decoration:none;padding:8px 12px;background-color:#eff6ff;border:1px solid #bfdbfe;border-radius:6px;transition:all .2s;font-weight:500}.file-link[_ngcontent-%COMP%]:hover{background-color:#dbeafe;border-color:#93c5fd;transform:translateY(-1px)}.file-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem}.evaluation-form[_ngcontent-%COMP%]{background:white;border-radius:12px;box-shadow:0 1px 3px #0000001a;border:1px solid #e2e8f0;overflow:hidden}.form-card[_ngcontent-%COMP%]{padding:25px}.scores-section[_ngcontent-%COMP%]{margin-bottom:30px}.score-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:25px;margin-bottom:25px}.score-item[_ngcontent-%COMP%]{background-color:#f8fafc;padding:20px;border-radius:10px;border:1px solid #e2e8f0;transition:all .2s}.score-item[_ngcontent-%COMP%]:hover{border-color:#cbd5e1;box-shadow:0 2px 4px #0000000d}.score-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:#374151;margin-bottom:10px;font-size:.95rem}.score-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3b82f6;font-size:1rem}.score-input-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;margin-bottom:8px}.score-input[_ngcontent-%COMP%]{flex:1;padding:12px 15px;border:2px solid #e2e8f0;border-radius:8px;font-size:1.1rem;font-weight:600;text-align:center;transition:all .2s;background-color:#fff}.score-input[_ngcontent-%COMP%]:focus{outline:none;border-color:#3b82f6;box-shadow:0 0 0 3px #3b82f61a}.score-input[_ngcontent-%COMP%]:invalid{border-color:#ef4444}.score-max[_ngcontent-%COMP%]{font-weight:600;color:#64748b;font-size:1rem}.score-description[_ngcontent-%COMP%]{color:#64748b;font-size:.8rem;line-height:1.4;font-style:italic}.score-total[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff;padding:20px;border-radius:12px;text-align:center;margin-top:20px;box-shadow:0 4px 6px #0000001a}.total-display[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:10px;margin-bottom:8px}.total-label[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:500}.total-value[_ngcontent-%COMP%]{font-size:2rem;font-weight:700}.total-max[_ngcontent-%COMP%]{font-size:1.1rem;opacity:.9}.total-percentage[_ngcontent-%COMP%]{font-size:1rem;opacity:.9;font-weight:500}.comments-section[_ngcontent-%COMP%]{margin-bottom:30px}.comments-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:#374151;margin-bottom:12px;font-size:1rem}.comments-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#3b82f6;font-size:1rem}.comments-textarea[_ngcontent-%COMP%]{width:100%;padding:15px;border:2px solid #e2e8f0;border-radius:8px;font-size:.95rem;line-height:1.6;resize:vertical;min-height:120px;transition:all .2s;font-family:inherit}.comments-textarea[_ngcontent-%COMP%]:focus{outline:none;border-color:#3b82f6;box-shadow:0 0 0 3px #3b82f61a}.comments-textarea[_ngcontent-%COMP%]::placeholder{color:#9ca3af;font-style:italic}.form-validation[_ngcontent-%COMP%]{margin-top:5px}.error-text[_ngcontent-%COMP%]{color:#ef4444;font-size:.8rem;font-weight:500}.actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;gap:15px;padding-top:25px;border-top:1px solid #e2e8f0}.btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:8px;padding:12px 24px;border:none;border-radius:8px;font-size:.95rem;font-weight:600;text-decoration:none;cursor:pointer;transition:all .2s;min-width:140px;justify-content:center}.btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed;transform:none!important}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6 0%,#1d4ed8 100%);color:#fff;box-shadow:0 2px 4px #3b82f64d}.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 4px 8px #3b82f666}.btn-primary[_ngcontent-%COMP%]:active:not(:disabled){transform:translateY(0)}.btn-secondary[_ngcontent-%COMP%]{background-color:#f8fafc;color:#64748b;border:2px solid #e2e8f0}.btn-secondary[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#f1f5f9;border-color:#cbd5e1;color:#475569;transform:translateY(-1px)}.btn-secondary[_ngcontent-%COMP%]:active:not(:disabled){transform:translateY(0)}.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.9rem}@media (max-width: 768px){.evaluation-container[_ngcontent-%COMP%]{padding:15px}.page-title[_ngcontent-%COMP%]{font-size:1.5rem}.score-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:20px}.info-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.actions[_ngcontent-%COMP%]{flex-direction:column-reverse;gap:10px}.btn[_ngcontent-%COMP%]{width:100%}.total-display[_ngcontent-%COMP%]{flex-direction:column;gap:5px}.total-value[_ngcontent-%COMP%]{font-size:1.8rem}}@media (max-width: 480px){.evaluation-container[_ngcontent-%COMP%]{padding:10px}.rendu-info-card[_ngcontent-%COMP%], .form-card[_ngcontent-%COMP%]{padding:20px}.page-title[_ngcontent-%COMP%]{font-size:1.3rem}.score-item[_ngcontent-%COMP%]{padding:15px}}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.main-content[_ngcontent-%COMP%], .score-item[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-out}.score-item[_ngcontent-%COMP%]:nth-child(1){animation-delay:.1s}.score-item[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.score-item[_ngcontent-%COMP%]:nth-child(3){animation-delay:.3s}.score-item[_ngcontent-%COMP%]:nth-child(4){animation-delay:.4s}.score-input[_ngcontent-%COMP%]:focus, .comments-textarea[_ngcontent-%COMP%]:focus{transform:translateY(-1px)}.btn[_ngcontent-%COMP%]:focus, .score-input[_ngcontent-%COMP%]:focus, .comments-textarea[_ngcontent-%COMP%]:focus{outline:2px solid #3b82f6;outline-offset:2px}.score-input[value=\\\"5\\\"][_ngcontent-%COMP%]{background-color:#dcfce7;border-color:#16a34a;color:#15803d}.score-input[value=\\\"4\\\"][_ngcontent-%COMP%]{background-color:#fef3c7;border-color:#d97706;color:#92400e}.score-input[value=\\\"3\\\"][_ngcontent-%COMP%]{background-color:#fef3c7;border-color:#f59e0b;color:#d97706}.score-input[value=\\\"2\\\"][_ngcontent-%COMP%]{background-color:#fed7aa;border-color:#ea580c;color:#c2410c}.score-input[value=\\\"1\\\"][_ngcontent-%COMP%]{background-color:#fecaca;border-color:#dc2626;color:#b91c1c}.score-input[value=\\\"0\\\"][_ngcontent-%COMP%]{background-color:#f3f4f6;border-color:#9ca3af;color:#6b7280}\"]\n      });\n    }\n  }\n  return EditEvaluationComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}