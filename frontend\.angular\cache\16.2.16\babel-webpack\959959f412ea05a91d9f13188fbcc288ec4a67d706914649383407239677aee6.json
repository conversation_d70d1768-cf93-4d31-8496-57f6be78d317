{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameISOWeek } from \"./isSameISOWeek.js\";\n\n/**\n * The {@link isThisISOWeek} function options.\n */\n\n/**\n * @name isThisISOWeek\n * @category ISO Week Helpers\n * @summary Is the given date in the same ISO week as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same ISO week as the current date?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is in this ISO week\n *\n * @example\n * // If today is 25 September 2014, is 22 September 2014 in this ISO week?\n * const result = isThisISOWeek(new Date(2014, 8, 22))\n * //=> true\n */\nexport function isThisISOWeek(date, options) {\n  return isSameISOWeek(constructFrom(options?.in || date, date), constructNow(options?.in || date));\n}\n\n// Fallback for modularized imports:\nexport default isThisISOWeek;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}