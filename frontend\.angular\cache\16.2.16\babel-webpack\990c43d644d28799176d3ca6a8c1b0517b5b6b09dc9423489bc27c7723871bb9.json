{"ast": null, "code": "import { GraphQLError } from './GraphQLError.mjs';\n/**\n * Produces a GraphQLError representing a syntax error, containing useful\n * descriptive information about the syntax error's position in the source.\n */\n\nexport function syntaxError(source, position, description) {\n  return new GraphQLError(`Syntax Error: ${description}`, {\n    source,\n    positions: [position]\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}