{"ast": null, "code": "const dayOfYearTokenRE = /^D+$/;\nconst weekYearTokenRE = /^Y+$/;\nconst throwTokens = [\"D\", \"DD\", \"YY\", \"YYYY\"];\nexport function isProtectedDayOfYearToken(token) {\n  return dayOfYearTokenRE.test(token);\n}\nexport function isProtectedWeekYearToken(token) {\n  return weekYearTokenRE.test(token);\n}\nexport function warnOrThrowProtectedError(token, format, input) {\n  const _message = message(token, format, input);\n  console.warn(_message);\n  if (throwTokens.includes(token)) throw new RangeError(_message);\n}\nfunction message(token, format, input) {\n  const subject = token[0] === \"Y\" ? \"years\" : \"days of the month\";\n  return `Use \\`${token.toLowerCase()}\\` instead of \\`${token}\\` (in \\`${format}\\`) for formatting ${subject} to the input \\`${input}\\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}