{"ast": null, "code": "import _asyncToGenerator from \"c:/Users/<USER>/Desktop/PI WEB/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { isAsyncIterable } from '../jsutils/isAsyncIterable.mjs';\nimport { addPath, pathToArray } from '../jsutils/Path.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { locatedError } from '../error/locatedError.mjs';\nimport { collectFields } from './collectFields.mjs';\nimport { assertValidExecutionArguments, buildExecutionContext, buildResolveInfo, execute, getFieldDef } from './execute.mjs';\nimport { mapAsyncIterator } from './mapAsyncIterator.mjs';\nimport { getArgumentValues } from './values.mjs';\n/**\n * Implements the \"Subscribe\" algorithm described in the GraphQL specification.\n *\n * Returns a Promise which resolves to either an AsyncIterator (if successful)\n * or an ExecutionResult (error). The promise will be rejected if the schema or\n * other arguments to this function are invalid, or if the resolved event stream\n * is not an async iterable.\n *\n * If the client-provided arguments to this function do not result in a\n * compliant subscription, a GraphQL Response (ExecutionResult) with\n * descriptive errors and no data will be returned.\n *\n * If the source stream could not be created due to faulty subscription\n * resolver logic or underlying systems, the promise will resolve to a single\n * ExecutionResult containing `errors` and no `data`.\n *\n * If the operation succeeded, the promise resolves to an AsyncIterator, which\n * yields a stream of ExecutionResults representing the response stream.\n *\n * Accepts either an object with named arguments, or individual arguments.\n */\n\nexport function subscribe(_x) {\n  return _subscribe.apply(this, arguments);\n}\nfunction _subscribe() {\n  _subscribe = _asyncToGenerator(function* (args) {\n    // Temporary for v15 to v16 migration. Remove in v17\n    arguments.length < 2 || devAssert(false, 'graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.');\n    const resultOrStream = yield createSourceEventStream(args);\n    if (!isAsyncIterable(resultOrStream)) {\n      return resultOrStream;\n    } // For each payload yielded from a subscription, map it over the normal\n    // GraphQL `execute` function, with `payload` as the rootValue.\n    // This implements the \"MapSourceToResponseEvent\" algorithm described in\n    // the GraphQL specification. The `execute` function provides the\n    // \"ExecuteSubscriptionEvent\" algorithm, as it is nearly identical to the\n    // \"ExecuteQuery\" algorithm, for which `execute` is also used.\n\n    const mapSourceToResponse = payload => execute({\n      ...args,\n      rootValue: payload\n    }); // Map every source value to a ExecutionResult value as described above.\n\n    return mapAsyncIterator(resultOrStream, mapSourceToResponse);\n  });\n  return _subscribe.apply(this, arguments);\n}\nfunction toNormalizedArgs(args) {\n  const firstArg = args[0];\n  if (firstArg && 'document' in firstArg) {\n    return firstArg;\n  }\n  return {\n    schema: firstArg,\n    // FIXME: when underlying TS bug fixed, see https://github.com/microsoft/TypeScript/issues/31613\n    document: args[1],\n    rootValue: args[2],\n    contextValue: args[3],\n    variableValues: args[4],\n    operationName: args[5],\n    subscribeFieldResolver: args[6]\n  };\n}\n/**\n * Implements the \"CreateSourceEventStream\" algorithm described in the\n * GraphQL specification, resolving the subscription source event stream.\n *\n * Returns a Promise which resolves to either an AsyncIterable (if successful)\n * or an ExecutionResult (error). The promise will be rejected if the schema or\n * other arguments to this function are invalid, or if the resolved event stream\n * is not an async iterable.\n *\n * If the client-provided arguments to this function do not result in a\n * compliant subscription, a GraphQL Response (ExecutionResult) with\n * descriptive errors and no data will be returned.\n *\n * If the the source stream could not be created due to faulty subscription\n * resolver logic or underlying systems, the promise will resolve to a single\n * ExecutionResult containing `errors` and no `data`.\n *\n * If the operation succeeded, the promise resolves to the AsyncIterable for the\n * event stream returned by the resolver.\n *\n * A Source Event Stream represents a sequence of events, each of which triggers\n * a GraphQL execution for that event.\n *\n * This may be useful when hosting the stateful subscription service in a\n * different process or machine than the stateless GraphQL execution engine,\n * or otherwise separating these two steps. For more on this, see the\n * \"Supporting Subscriptions at Scale\" information in the GraphQL specification.\n */\n\nexport function createSourceEventStream() {\n  return _createSourceEventStream.apply(this, arguments);\n}\nfunction _createSourceEventStream() {\n  _createSourceEventStream = _asyncToGenerator(function* (...rawArgs) {\n    const args = toNormalizedArgs(rawArgs);\n    const {\n      schema,\n      document,\n      variableValues\n    } = args; // If arguments are missing or incorrectly typed, this is an internal\n    // developer mistake which should throw an early error.\n\n    assertValidExecutionArguments(schema, document, variableValues); // If a valid execution context cannot be created due to incorrect arguments,\n    // a \"Response\" with only errors is returned.\n\n    const exeContext = buildExecutionContext(args); // Return early errors if execution context failed.\n\n    if (!('schema' in exeContext)) {\n      return {\n        errors: exeContext\n      };\n    }\n    try {\n      const eventStream = yield executeSubscription(exeContext); // Assert field returned an event stream, otherwise yield an error.\n\n      if (!isAsyncIterable(eventStream)) {\n        throw new Error('Subscription field must return Async Iterable. ' + `Received: ${inspect(eventStream)}.`);\n      }\n      return eventStream;\n    } catch (error) {\n      // If it GraphQLError, report it as an ExecutionResult, containing only errors and no data.\n      // Otherwise treat the error as a system-class error and re-throw it.\n      if (error instanceof GraphQLError) {\n        return {\n          errors: [error]\n        };\n      }\n      throw error;\n    }\n  });\n  return _createSourceEventStream.apply(this, arguments);\n}\nfunction executeSubscription(_x2) {\n  return _executeSubscription.apply(this, arguments);\n}\nfunction _executeSubscription() {\n  _executeSubscription = _asyncToGenerator(function* (exeContext) {\n    const {\n      schema,\n      fragments,\n      operation,\n      variableValues,\n      rootValue\n    } = exeContext;\n    const rootType = schema.getSubscriptionType();\n    if (rootType == null) {\n      throw new GraphQLError('Schema is not configured to execute subscription operation.', {\n        nodes: operation\n      });\n    }\n    const rootFields = collectFields(schema, fragments, variableValues, rootType, operation.selectionSet);\n    const [responseName, fieldNodes] = [...rootFields.entries()][0];\n    const fieldDef = getFieldDef(schema, rootType, fieldNodes[0]);\n    if (!fieldDef) {\n      const fieldName = fieldNodes[0].name.value;\n      throw new GraphQLError(`The subscription field \"${fieldName}\" is not defined.`, {\n        nodes: fieldNodes\n      });\n    }\n    const path = addPath(undefined, responseName, rootType.name);\n    const info = buildResolveInfo(exeContext, fieldDef, fieldNodes, rootType, path);\n    try {\n      var _fieldDef$subscribe;\n\n      // Implements the \"ResolveFieldEventStream\" algorithm from GraphQL specification.\n      // It differs from \"ResolveFieldValue\" due to providing a different `resolveFn`.\n      // Build a JS object of arguments from the field.arguments AST, using the\n      // variables scope to fulfill any variable references.\n      const args = getArgumentValues(fieldDef, fieldNodes[0], variableValues); // The resolve function's optional third argument is a context value that\n      // is provided to every resolve function within an execution. It is commonly\n      // used to represent an authenticated user, or request-specific caches.\n\n      const contextValue = exeContext.contextValue; // Call the `subscribe()` resolver or the default resolver to produce an\n      // AsyncIterable yielding raw payloads.\n\n      const resolveFn = (_fieldDef$subscribe = fieldDef.subscribe) !== null && _fieldDef$subscribe !== void 0 ? _fieldDef$subscribe : exeContext.subscribeFieldResolver;\n      const eventStream = yield resolveFn(rootValue, args, contextValue, info);\n      if (eventStream instanceof Error) {\n        throw eventStream;\n      }\n      return eventStream;\n    } catch (error) {\n      throw locatedError(error, fieldNodes, pathToArray(path));\n    }\n  });\n  return _executeSubscription.apply(this, arguments);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}