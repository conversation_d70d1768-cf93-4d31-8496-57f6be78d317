{"ast": null, "code": "import { getOperationName } from \"../../utilities/index.js\";\nexport function transformOperation(operation) {\n  var transformedOperation = {\n    variables: operation.variables || {},\n    extensions: operation.extensions || {},\n    operationName: operation.operationName,\n    query: operation.query\n  };\n  // Best guess at an operation name\n  if (!transformedOperation.operationName) {\n    transformedOperation.operationName = typeof transformedOperation.query !== \"string\" ? getOperationName(transformedOperation.query) || undefined : \"\";\n  }\n  return transformedOperation;\n}\n//# sourceMappingURL=transformOperation.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}