{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfHour} function options.\n */\n\n/**\n * @name endOfHour\n * @category Hour Helpers\n * @summary Return the end of an hour for the given date.\n *\n * @description\n * Return the end of an hour for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of an hour\n *\n * @example\n * // The end of an hour for 2 September 2014 11:55:00:\n * const result = endOfHour(new Date(2014, 8, 2, 11, 55))\n * //=> Tue Sep 02 2014 11:59:59.999\n */\nexport function endOfHour(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setMinutes(59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfHour;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}