{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isThursday} function options.\n */\n\n/**\n * @name isThursday\n * @category Weekday Helpers\n * @summary Is the given date Thursday?\n *\n * @description\n * Is the given date Thursday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Thursday\n *\n * @example\n * // Is 25 September 2014 Thursday?\n * const result = isThursday(new Date(2014, 8, 25))\n * //=> true\n */\nexport function isThursday(date, options) {\n  return toDate(date, options?.in).getDay() === 4;\n}\n\n// Fallback for modularized imports:\nexport default isThursday;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}