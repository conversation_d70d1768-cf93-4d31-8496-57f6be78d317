{"ast": null, "code": "/**\n * Creates an object map with the same keys as `map` and values generated by\n * running each value of `map` thru `fn`.\n */\nexport function mapValue(map, fn) {\n  const result = Object.create(null);\n  for (const key of Object.keys(map)) {\n    result[key] = fn(map[key], key);\n  }\n  return result;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}