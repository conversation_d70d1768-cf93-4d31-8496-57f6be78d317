{"ast": null, "code": "import { isNonEmptyArray } from \"./arrays.js\";\nimport { isExecutionPatchIncrementalResult } from \"./incrementalResult.js\";\nexport function graphQLResultHasError(result) {\n  var errors = getGraphQLErrorsFromResult(result);\n  return isNonEmptyArray(errors);\n}\nexport function getGraphQLErrorsFromResult(result) {\n  var graphQLErrors = isNonEmptyArray(result.errors) ? result.errors.slice(0) : [];\n  if (isExecutionPatchIncrementalResult(result) && isNonEmptyArray(result.incremental)) {\n    result.incremental.forEach(function (incrementalResult) {\n      if (incrementalResult.errors) {\n        graphQLErrors.push.apply(graphQLErrors, incrementalResult.errors);\n      }\n    });\n  }\n  return graphQLErrors;\n}\n//# sourceMappingURL=errorHandling.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}