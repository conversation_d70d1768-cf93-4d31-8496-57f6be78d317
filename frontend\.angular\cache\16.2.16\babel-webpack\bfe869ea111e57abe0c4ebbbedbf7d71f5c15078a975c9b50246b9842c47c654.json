{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@auth0/angular-jwt\";\nexport let PlanningService = /*#__PURE__*/(() => {\n  class PlanningService {\n    constructor(http, jwtHelper) {\n      this.http = http;\n      this.jwtHelper = jwtHelper;\n    }\n    getUserHeaders() {\n      const token = localStorage.getItem('token');\n      if (!token || this.jwtHelper.isTokenExpired(token)) {\n        throw new Error('Token invalide ou expiré');\n      }\n      return new HttpHeaders({\n        Authorization: `Bearer ${token || ''}`,\n        'Content-Type': 'application/json'\n      });\n    }\n    getAllPlannings() {\n      return this.http.get(`${environment.urlBackend}plannings/getall`);\n    }\n    getPlanningById(id) {\n      return this.http.get(`${environment.urlBackend}plannings/getone/${id}`);\n    }\n    createPlanning(planning) {\n      return this.http.post(`${environment.urlBackend}plannings/add`, planning, {\n        headers: this.getUserHeaders()\n      });\n    }\n    updatePlanning(id, planning) {\n      return this.http.put(`${environment.urlBackend}plannings/update/${id}`, planning, {\n        headers: this.getUserHeaders()\n      });\n    }\n    deletePlanning(id) {\n      return this.http.delete(`${environment.urlBackend}plannings/delete/${id}`, {\n        headers: this.getUserHeaders()\n      });\n    }\n    getPlanningsByUser(userId) {\n      return this.http.get(`${environment.urlBackend}plannings/user/${userId}`, {\n        headers: this.getUserHeaders()\n      });\n    }\n    getPlanningsWithDetails() {\n      return this.http.get(`${environment.urlBackend}plannings/with-details`, {\n        headers: this.getUserHeaders()\n      });\n    }\n    getPlanningWithReunions(id) {\n      return this.http.get(`${environment.urlBackend}plannings/with-reunions/${id}`, {\n        headers: this.getUserHeaders()\n      });\n    }\n    static {\n      this.ɵfac = function PlanningService_Factory(t) {\n        return new (t || PlanningService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.JwtHelperService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PlanningService,\n        factory: PlanningService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PlanningService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}