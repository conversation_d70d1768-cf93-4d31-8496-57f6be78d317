{"ast": null, "code": "import { dep, Slot } from \"optimism\";\n// Contextual Slot that acquires its value when custom read functions are\n// called in Policies#readField.\nexport var cacheSlot = new Slot();\nvar cacheInfoMap = new WeakMap();\nfunction getCacheInfo(cache) {\n  var info = cacheInfoMap.get(cache);\n  if (!info) {\n    cacheInfoMap.set(cache, info = {\n      vars: new Set(),\n      dep: dep()\n    });\n  }\n  return info;\n}\nexport function forgetCache(cache) {\n  getCacheInfo(cache).vars.forEach(function (rv) {\n    return rv.forgetCache(cache);\n  });\n}\n// Calling forgetCache(cache) serves to silence broadcasts and allows the\n// cache to be garbage collected. However, the varsByCache WeakMap\n// preserves the set of reactive variables that were previously associated\n// with this cache, which makes it possible to \"recall\" the cache at a\n// later time, by reattaching it to those variables. If the cache has been\n// garbage collected in the meantime, because it is no longer reachable,\n// you won't be able to call recallCache(cache), and the cache will\n// automatically disappear from the varsByCache WeakMap.\nexport function recallCache(cache) {\n  getCacheInfo(cache).vars.forEach(function (rv) {\n    return rv.attachCache(cache);\n  });\n}\nexport function makeVar(value) {\n  var caches = new Set();\n  var listeners = new Set();\n  var rv = function (newValue) {\n    if (arguments.length > 0) {\n      if (value !== newValue) {\n        value = newValue;\n        caches.forEach(function (cache) {\n          // Invalidate any fields with custom read functions that\n          // consumed this variable, so query results involving those\n          // fields will be recomputed the next time we read them.\n          getCacheInfo(cache).dep.dirty(rv);\n          // Broadcast changes to any caches that have previously read\n          // from this variable.\n          broadcast(cache);\n        });\n        // Finally, notify any listeners added via rv.onNextChange.\n        var oldListeners = Array.from(listeners);\n        listeners.clear();\n        oldListeners.forEach(function (listener) {\n          return listener(value);\n        });\n      }\n    } else {\n      // When reading from the variable, obtain the current cache from\n      // context via cacheSlot. This isn't entirely foolproof, but it's\n      // the same system that powers varDep.\n      var cache = cacheSlot.getValue();\n      if (cache) {\n        attach(cache);\n        getCacheInfo(cache).dep(rv);\n      }\n    }\n    return value;\n  };\n  rv.onNextChange = function (listener) {\n    listeners.add(listener);\n    return function () {\n      listeners.delete(listener);\n    };\n  };\n  var attach = rv.attachCache = function (cache) {\n    caches.add(cache);\n    getCacheInfo(cache).vars.add(rv);\n    return rv;\n  };\n  rv.forgetCache = function (cache) {\n    return caches.delete(cache);\n  };\n  return rv;\n}\nfunction broadcast(cache) {\n  if (cache.broadcastWatches) {\n    cache.broadcastWatches();\n  }\n}\n//# sourceMappingURL=reactiveVars.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}