{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setMilliseconds} function options.\n */\n\n/**\n * @name setMilliseconds\n * @category Millisecond Helpers\n * @summary Set the milliseconds to the given date.\n *\n * @description\n * Set the milliseconds to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param milliseconds - The milliseconds of the new date\n * @param options - The options\n *\n * @returns The new date with the milliseconds set\n *\n * @example\n * // Set 300 milliseconds to 1 September 2014 11:30:40.500:\n * const result = setMilliseconds(new Date(2014, 8, 1, 11, 30, 40, 500), 300)\n * //=> Mon Sep 01 2014 11:30:40.300\n */\nexport function setMilliseconds(date, milliseconds, options) {\n  const _date = toDate(date, options?.in);\n  _date.setMilliseconds(milliseconds);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setMilliseconds;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}