{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../services/reunion.service\";\nimport * as i3 from \"@angular/common\";\nfunction ReunionDetailComponent_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵelement(1, \"i\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate1(\"routerLink\", \"/reunions/edit/\", ctx_r0.reunion._id, \"\");\n  }\n}\nfunction ReunionDetailComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementStart(2, \"p\", 6);\n    i0.ɵɵtext(3, \"Chargement des d\\u00E9tails de la r\\u00E9union...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ReunionDetailComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵelement(2, \"i\", 20);\n    i0.ɵɵelementStart(3, \"div\")(4, \"h3\", 21);\n    i0.ɵɵtext(5, \"Erreur de chargement\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 22);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ReunionDetailComponent_div_15_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.loadReunion());\n    });\n    i0.ɵɵtext(9, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.error);\n  }\n}\nfunction ReunionDetailComponent_div_16_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h3\", 29);\n    i0.ɵɵtext(2, \" Lien de visioconf\\u00E9rence \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 34);\n    i0.ɵɵelement(4, \"i\", 35);\n    i0.ɵɵtext(5, \" Rejoindre la r\\u00E9union \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r6.reunion.lienVisio, i0.ɵɵsanitizeUrl);\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/plannings\", a1];\n};\nfunction ReunionDetailComponent_div_16_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h3\", 29);\n    i0.ɵɵtext(2, \" Planning associ\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(2, _c0, ctx_r7.reunion.planningId._id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.reunion.planningId.titre, \" \");\n  }\n}\nfunction ReunionDetailComponent_div_16_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵelement(2, \"img\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 6);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const participant_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", participant_r11.image || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(participant_r11.username);\n  }\n}\nfunction ReunionDetailComponent_div_16_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, ReunionDetailComponent_div_16_div_24_div_1_Template, 5, 2, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.reunion.participants);\n  }\n}\nfunction ReunionDetailComponent_div_16_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵtext(1, \" Aucun participant \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionDetailComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"h2\", 27);\n    i0.ɵɵtext(4, \" Informations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 28)(6, \"div\")(7, \"h3\", 29);\n    i0.ɵɵtext(8, \"Date et heure\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 6);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\")(14, \"h3\", 29);\n    i0.ɵɵtext(15, \"Lieu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 6);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, ReunionDetailComponent_div_16_div_18_Template, 6, 1, \"div\", 30);\n    i0.ɵɵtemplate(19, ReunionDetailComponent_div_16_div_19_Template, 5, 4, \"div\", 30);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 31)(21, \"div\", 26)(22, \"h2\", 27);\n    i0.ɵɵtext(23, \" Participants \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, ReunionDetailComponent_div_16_div_24_Template, 2, 1, \"div\", 32);\n    i0.ɵɵtemplate(25, ReunionDetailComponent_div_16_div_25_Template, 2, 0, \"div\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(11, 7, ctx_r3.reunion.dateDebut, \"dd/MM/yyyy HH:mm\"), \" - \", i0.ɵɵpipeBind2(12, 10, ctx_r3.reunion.dateFin, \"HH:mm\"), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.reunion.lieu || \"Non sp\\u00E9cifi\\u00E9\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.reunion.lienVisio);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.reunion.planningId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.reunion.participants && ctx_r3.reunion.participants.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.reunion.participants || ctx_r3.reunion.participants.length === 0);\n  }\n}\nexport let ReunionDetailComponent = /*#__PURE__*/(() => {\n  class ReunionDetailComponent {\n    constructor(route, reunionService) {\n      this.route = route;\n      this.reunionService = reunionService;\n      this.reunion = null;\n      this.loading = true;\n      this.error = null;\n    }\n    ngOnInit() {\n      this.loadReunion();\n    }\n    loadReunion() {\n      this.loading = true;\n      this.error = null;\n      const id = this.route.snapshot.paramMap.get('id');\n      if (!id) {\n        this.error = 'ID de réunion manquant';\n        this.loading = false;\n        return;\n      }\n      // Simulation de chargement (à remplacer par un appel au service)\n      setTimeout(() => {\n        // Données fictives pour la démonstration\n        this.reunion = {\n          _id: id,\n          titre: 'Réunion de projet',\n          description: \"Discussion sur l'avancement du projet et les prochaines étapes\",\n          dateDebut: new Date('2023-06-15T10:00:00'),\n          dateFin: new Date('2023-06-15T11:30:00'),\n          lieu: 'Salle de conférence A',\n          lienVisio: 'https://meet.google.com/abc-defg-hij',\n          planningId: {\n            _id: 'planning123',\n            titre: 'Planning du projet X'\n          },\n          participants: [{\n            _id: 'user1',\n            username: 'Jean Dupont',\n            image: 'assets/images/default-avatar.png'\n          }, {\n            _id: 'user2',\n            username: 'Marie Martin',\n            image: 'assets/images/default-avatar.png'\n          }]\n        };\n        this.loading = false;\n      }, 1000);\n    }\n    static {\n      this.ɵfac = function ReunionDetailComponent_Factory(t) {\n        return new (t || ReunionDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ReunionService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ReunionDetailComponent,\n        selectors: [[\"app-reunion-detail\"]],\n        decls: 17,\n        vars: 6,\n        consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"p-4\", \"md:p-6\"], [1, \"max-w-4xl\", \"mx-auto\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"overflow-hidden\", \"mb-6\"], [1, \"border-t-4\", \"border-[#4f5fad]\", \"p-6\"], [1, \"flex\", \"justify-between\", \"items-start\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-2\"], [1, \"text-[#6d6870]\"], [1, \"flex\", \"space-x-2\"], [\"class\", \"p-2 rounded-full text-[#4f5fad] hover:bg-[#4f5fad]/10 transition-colors\", 3, \"routerLink\", 4, \"ngIf\"], [\"routerLink\", \"/reunions\", 1, \"p-2\", \"rounded-full\", \"text-[#4f5fad]\", \"hover:bg-[#4f5fad]/10\", \"transition-colors\"], [1, \"fas\", \"fa-arrow-left\"], [\"class\", \"flex flex-col items-center justify-center p-12\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 border border-[#ff6b69] rounded-lg p-4 mb-6\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-3 gap-6\", 4, \"ngIf\"], [1, \"p-2\", \"rounded-full\", \"text-[#4f5fad]\", \"hover:bg-[#4f5fad]/10\", \"transition-colors\", 3, \"routerLink\"], [1, \"fas\", \"fa-edit\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"p-12\"], [1, \"w-12\", \"h-12\", \"border-4\", \"border-[#4f5fad]/20\", \"border-t-[#4f5fad]\", \"rounded-full\", \"animate-spin\", \"mb-4\"], [1, \"bg-[#ff6b69]/10\", \"border\", \"border-[#ff6b69]\", \"rounded-lg\", \"p-4\", \"mb-6\"], [1, \"flex\", \"items-start\"], [1, \"fas\", \"fa-exclamation-triangle\", \"text-[#ff6b69]\", \"text-xl\", \"mr-3\"], [1, \"font-medium\", \"text-[#ff6b69]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\"], [1, \"mt-3\", \"px-3\", \"py-1\", \"bg-[#ff6b69]\", \"text-white\", \"rounded-md\", \"hover:bg-[#ff6b69]/80\", \"transition-colors\", \"text-sm\", 3, \"click\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [1, \"md:col-span-2\", \"bg-white\", \"rounded-lg\", \"shadow-md\", \"overflow-hidden\"], [1, \"p-6\"], [1, \"text-lg\", \"font-semibold\", \"text-[#4f5fad]\", \"mb-4\"], [1, \"space-y-4\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\"], [4, \"ngIf\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"overflow-hidden\"], [\"class\", \"space-y-3\", 4, \"ngIf\"], [\"class\", \"text-[#6d6870] italic\", 4, \"ngIf\"], [\"target\", \"_blank\", 1, \"text-[#4f5fad]\", \"hover:underline\", \"flex\", \"items-center\", 3, \"href\"], [1, \"fas\", \"fa-video\", \"mr-2\"], [1, \"text-[#4f5fad]\", \"hover:underline\", 3, \"routerLink\"], [1, \"space-y-3\"], [\"class\", \"flex items-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\"], [1, \"relative\", \"mr-3\"], [\"alt\", \"Avatar\", 1, \"w-8\", \"h-8\", \"rounded-full\", \"object-cover\", 3, \"src\"], [1, \"text-[#6d6870]\", \"italic\"]],\n        template: function ReunionDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\")(6, \"h1\", 5);\n            i0.ɵɵtext(7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\", 6);\n            i0.ɵɵtext(9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 7);\n            i0.ɵɵtemplate(11, ReunionDetailComponent_button_11_Template, 2, 1, \"button\", 8);\n            i0.ɵɵelementStart(12, \"button\", 9);\n            i0.ɵɵelement(13, \"i\", 10);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(14, ReunionDetailComponent_div_14_Template, 4, 0, \"div\", 11);\n            i0.ɵɵtemplate(15, ReunionDetailComponent_div_15_Template, 10, 1, \"div\", 12);\n            i0.ɵɵtemplate(16, ReunionDetailComponent_div_16_Template, 26, 13, \"div\", 13);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.reunion == null ? null : ctx.reunion.titre) || \"D\\u00E9tails de la r\\u00E9union\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.reunion == null ? null : ctx.reunion.description) || \"Chargement des d\\u00E9tails...\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.reunion);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.reunion && !ctx.loading);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i1.RouterLink, i3.DatePipe]\n      });\n    }\n  }\n  return ReunionDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}