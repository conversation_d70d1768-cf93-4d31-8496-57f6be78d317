{"ast": null, "code": "import { Validators } from \"@angular/forms\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@app/services/planning.service\";\nimport * as i3 from \"@app/services/data.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nfunction PlanningEditComponent_div_12_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Titre is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningEditComponent_div_12_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"At least 3 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningEditComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, PlanningEditComponent_div_12_span_1_Template, 2, 0, \"span\", 21);\n    i0.ɵɵtemplate(2, PlanningEditComponent_div_12_span_2_Template, 2, 0, \"span\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.planningForm.get(\"titre\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.planningForm.get(\"titre\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction PlanningEditComponent_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r6._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(user_r6.username);\n  }\n}\nfunction PlanningEditComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \" Please select at least one participant \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningEditComponent__svg_svg_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 23);\n    i0.ɵɵelement(1, \"circle\", 24)(2, \"path\", 25);\n    i0.ɵɵelementEnd();\n  }\n}\nexport let PlanningEditComponent = /*#__PURE__*/(() => {\n  class PlanningEditComponent {\n    constructor(fb, planningService, userService, route, router) {\n      this.fb = fb;\n      this.planningService = planningService;\n      this.userService = userService;\n      this.route = route;\n      this.router = router;\n      this.users$ = this.userService.getAllUsers();\n      this.error = '';\n      this.isLoading = false;\n    }\n    ngOnInit() {\n      this.planningId = this.route.snapshot.paramMap.get('id');\n      this.initForm();\n      this.loadPlanning();\n    }\n    initForm() {\n      this.planningForm = this.fb.group({\n        titre: ['', [Validators.required, Validators.minLength(3)]],\n        description: [''],\n        dateDebut: ['', Validators.required],\n        dateFin: ['', Validators.required],\n        lieu: [''],\n        participants: [[], Validators.required] // FormArray for multiple participants\n      });\n    }\n\n    loadPlanning() {\n      this.planningService.getPlanningById(this.planningId).subscribe({\n        next: response => {\n          const planning = response.planning;\n          this.planningForm.patchValue({\n            titre: planning.titre,\n            description: planning.description,\n            dateDebut: planning.dateDebut,\n            dateFin: planning.dateFin,\n            lieu: planning.lieu\n          });\n          const participantsArray = this.planningForm.get('participants');\n          participantsArray.clear();\n          planning.participants.forEach(p => {\n            participantsArray.push(this.fb.control(p._id));\n          });\n        },\n        error: err => {\n          this.error = err.error?.message || 'Erreur lors du chargement du planning';\n        }\n      });\n    }\n    onSubmit() {\n      if (this.planningForm.invalid) return;\n      this.isLoading = true;\n      const formValue = this.planningForm.value;\n      const updatedPlanning = {\n        ...formValue,\n        participants: formValue.participants\n      };\n      this.planningService.updatePlanning(this.planningId, updatedPlanning).subscribe({\n        next: () => {\n          this.isLoading = false;\n          this.router.navigate(['/plannings']);\n        },\n        error: err => {\n          this.isLoading = false;\n          console.error('Erreur lors de la mise à jour du planning', err);\n        }\n      });\n    }\n    static {\n      this.ɵfac = function PlanningEditComponent_Factory(t) {\n        return new (t || PlanningEditComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.PlanningService), i0.ɵɵdirectiveInject(i3.DataService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PlanningEditComponent,\n        selectors: [[\"app-planning-edit\"]],\n        decls: 40,\n        vars: 11,\n        consts: [[1, \"bg-gray-50\", \"p-5\", \"sm:p-6\", \"rounded-xl\"], [1, \"mb-4\"], [1, \"text-lg\", \"font-medium\", \"text-gray-900\"], [1, \"text-sm\", \"text-gray-500\", \"mt-1\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\"], [1, \"grid\", \"grid-cols-1\", \"gap-y-4\", \"gap-x-4\", \"sm:grid-cols-6\"], [1, \"sm:col-span-3\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"type\", \"text\", \"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"class\", \"text-sm text-red-600 mt-1\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"lieu\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"type\", \"date\", \"formControlName\", \"dateDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [\"type\", \"date\", \"formControlName\", \"dateFin\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [1, \"sm:col-span-6\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"description\", \"rows\", \"3\", 1, \"mt-1\", \"block\", \"w-full\", \"px-3\", \"py-2\", \"border\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"sm:text-sm\"], [1, \"mt-6\", \"flex\", \"justify-end\"], [\"type\", \"submit\", 1, \"ml-3\", \"inline-flex\", \"justify-center\", \"py-2\", \"px-4\", \"border\", \"border-transparent\", \"shadow-sm\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"text-white\", \"bg-purple-600\", \"hover:bg-purple-700\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-offset-2\", \"focus:ring-purple-500\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-colors\", 3, \"disabled\"], [\"class\", \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"text-sm\", \"text-red-600\", \"mt-1\"], [4, \"ngIf\"], [3, \"value\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"-ml-1\", \"mr-2\", \"h-4\", \"w-4\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"]],\n        template: function PlanningEditComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n            i0.ɵɵtext(3, \"Planning Form\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\", 3);\n            i0.ɵɵtext(5, \"Fill in the details for the event\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"form\", 4);\n            i0.ɵɵlistener(\"ngSubmit\", function PlanningEditComponent_Template_form_ngSubmit_6_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"label\", 7);\n            i0.ɵɵtext(10, \"Titre\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(11, \"input\", 8);\n            i0.ɵɵtemplate(12, PlanningEditComponent_div_12_Template, 3, 2, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"div\", 6)(14, \"label\", 7);\n            i0.ɵɵtext(15, \"Lieu\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(16, \"input\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 6)(18, \"label\", 7);\n            i0.ɵɵtext(19, \"Date de d\\u00E9but\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(20, \"input\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"div\", 6)(22, \"label\", 7);\n            i0.ɵɵtext(23, \"Date de fin\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(24, \"input\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"div\", 13)(26, \"label\", 7);\n            i0.ɵɵtext(27, \"Participants\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"select\", 14);\n            i0.ɵɵtemplate(29, PlanningEditComponent_option_29_Template, 2, 2, \"option\", 15);\n            i0.ɵɵpipe(30, \"async\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(31, PlanningEditComponent_div_31_Template, 2, 0, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"div\", 13)(33, \"label\", 7);\n            i0.ɵɵtext(34, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(35, \"textarea\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(36, \"div\", 17)(37, \"button\", 18);\n            i0.ɵɵtemplate(38, PlanningEditComponent__svg_svg_38_Template, 3, 0, \"svg\", 19);\n            i0.ɵɵtext(39);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_1_0;\n            let tmp_2_0;\n            let tmp_4_0;\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"formGroup\", ctx.planningForm);\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"border-red-300\", ((tmp_1_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_1_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(17);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(30, 9, ctx.users$));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"disabled\", ctx.planningForm.invalid || ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"Saving...\" : \"Save Planning\", \" \");\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectMultipleControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.AsyncPipe]\n      });\n    }\n  }\n  return PlanningEditComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}