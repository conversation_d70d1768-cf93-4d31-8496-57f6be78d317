{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nexport const profileCompletionGuard = (route, state) => {\n  const authService = inject(AuthuserService);\n  const router = inject(Router);\n  // Check if user is logged in\n  if (!authService.userLoggedIn()) {\n    router.navigate(['/login']);\n    return false;\n  }\n  const currentUser = authService.getCurrentUser();\n  // If user is not found, redirect to login\n  if (!currentUser) {\n    router.navigate(['/login']);\n    return false;\n  }\n  // If this is the profile completion page itself, allow access\n  if (state.url === '/complete-profile') {\n    return true;\n  }\n  // Only redirect for first-time logins or truly incomplete profiles\n  const isFirstLogin = currentUser.isFirstLogin;\n  // Check if profile has essential information (more lenient check)\n  const hasEssentialInfo = currentUser.firstName && currentUser.lastName && currentUser.email;\n  // Only redirect if it's first login OR missing essential information\n  if (isFirstLogin || !hasEssentialInfo) {\n    router.navigate(['/complete-profile']);\n    return false;\n  }\n  // Allow access to other pages if profile is complete\n  return true;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}