{"ast": null, "code": "import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { inspect } from '../../jsutils/inspect.mjs';\nimport { invariant } from '../../jsutils/invariant.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { isTypeDefinitionNode } from '../../language/predicates.mjs';\nimport { isEnumType, isInputObjectType, isInterfaceType, isObjectType, isScalarType, isUnionType } from '../../type/definition.mjs';\n\n/**\n * Possible type extension\n *\n * A type extension is only valid if the type is defined and has the same kind.\n */\nexport function PossibleTypeExtensionsRule(context) {\n  const schema = context.getSchema();\n  const definedTypes = Object.create(null);\n  for (const def of context.getDocument().definitions) {\n    if (isTypeDefinitionNode(def)) {\n      definedTypes[def.name.value] = def;\n    }\n  }\n  return {\n    ScalarTypeExtension: checkExtension,\n    ObjectTypeExtension: checkExtension,\n    InterfaceTypeExtension: checkExtension,\n    UnionTypeExtension: checkExtension,\n    EnumTypeExtension: checkExtension,\n    InputObjectTypeExtension: checkExtension\n  };\n  function checkExtension(node) {\n    const typeName = node.name.value;\n    const defNode = definedTypes[typeName];\n    const existingType = schema === null || schema === void 0 ? void 0 : schema.getType(typeName);\n    let expectedKind;\n    if (defNode) {\n      expectedKind = defKindToExtKind[defNode.kind];\n    } else if (existingType) {\n      expectedKind = typeToExtKind(existingType);\n    }\n    if (expectedKind) {\n      if (expectedKind !== node.kind) {\n        const kindStr = extensionKindToTypeName(node.kind);\n        context.reportError(new GraphQLError(`Cannot extend non-${kindStr} type \"${typeName}\".`, {\n          nodes: defNode ? [defNode, node] : node\n        }));\n      }\n    } else {\n      const allTypeNames = Object.keys({\n        ...definedTypes,\n        ...(schema === null || schema === void 0 ? void 0 : schema.getTypeMap())\n      });\n      const suggestedTypes = suggestionList(typeName, allTypeNames);\n      context.reportError(new GraphQLError(`Cannot extend type \"${typeName}\" because it is not defined.` + didYouMean(suggestedTypes), {\n        nodes: node.name\n      }));\n    }\n  }\n}\nconst defKindToExtKind = {\n  [Kind.SCALAR_TYPE_DEFINITION]: Kind.SCALAR_TYPE_EXTENSION,\n  [Kind.OBJECT_TYPE_DEFINITION]: Kind.OBJECT_TYPE_EXTENSION,\n  [Kind.INTERFACE_TYPE_DEFINITION]: Kind.INTERFACE_TYPE_EXTENSION,\n  [Kind.UNION_TYPE_DEFINITION]: Kind.UNION_TYPE_EXTENSION,\n  [Kind.ENUM_TYPE_DEFINITION]: Kind.ENUM_TYPE_EXTENSION,\n  [Kind.INPUT_OBJECT_TYPE_DEFINITION]: Kind.INPUT_OBJECT_TYPE_EXTENSION\n};\nfunction typeToExtKind(type) {\n  if (isScalarType(type)) {\n    return Kind.SCALAR_TYPE_EXTENSION;\n  }\n  if (isObjectType(type)) {\n    return Kind.OBJECT_TYPE_EXTENSION;\n  }\n  if (isInterfaceType(type)) {\n    return Kind.INTERFACE_TYPE_EXTENSION;\n  }\n  if (isUnionType(type)) {\n    return Kind.UNION_TYPE_EXTENSION;\n  }\n  if (isEnumType(type)) {\n    return Kind.ENUM_TYPE_EXTENSION;\n  }\n  if (isInputObjectType(type)) {\n    return Kind.INPUT_OBJECT_TYPE_EXTENSION;\n  }\n  /* c8 ignore next 3 */\n  // Not reachable. All possible types have been considered\n\n  false || invariant(false, 'Unexpected type: ' + inspect(type));\n}\nfunction extensionKindToTypeName(kind) {\n  switch (kind) {\n    case Kind.SCALAR_TYPE_EXTENSION:\n      return 'scalar';\n    case Kind.OBJECT_TYPE_EXTENSION:\n      return 'object';\n    case Kind.INTERFACE_TYPE_EXTENSION:\n      return 'interface';\n    case Kind.UNION_TYPE_EXTENSION:\n      return 'union';\n    case Kind.ENUM_TYPE_EXTENSION:\n      return 'enum';\n    case Kind.INPUT_OBJECT_TYPE_EXTENSION:\n      return 'input object';\n    // Not reachable. All possible types have been considered\n\n    /* c8 ignore next */\n\n    default:\n      false || invariant(false, 'Unexpected kind: ' + inspect(kind));\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}