{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/toast.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction AdminSettingsComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function AdminSettingsComponent_button_23_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const tab_r5 = restoredCtx.$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.setActiveTab(tab_r5.id));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r5 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"border-[#4f5fad]\", ctx_r0.activeTab === tab_r5.id)(\"text-[#4f5fad]\", ctx_r0.activeTab === tab_r5.id)(\"border-transparent\", ctx_r0.activeTab !== tab_r5.id)(\"text-[#6d6870]\", ctx_r0.activeTab !== tab_r5.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(tab_r5.icon + \" mr-2\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tab_r5.label, \" \");\n  }\n}\nfunction AdminSettingsComponent_div_25_i_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 47);\n  }\n}\nfunction AdminSettingsComponent_div_25_i_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 48);\n  }\n}\nfunction AdminSettingsComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\", 24)(3, \"h3\", 25);\n    i0.ɵɵtext(4, \"Basic Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"label\", 26);\n    i0.ɵɵtext(7, \"Site Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 27);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_25_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.systemSettings.siteName = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\")(10, \"label\", 26);\n    i0.ɵɵtext(11, \"Site Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"textarea\", 28);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_25_Template_textarea_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.systemSettings.siteDescription = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\")(14, \"label\", 26);\n    i0.ɵɵtext(15, \"Default User Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"select\", 29);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_25_Template_select_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.systemSettings.defaultUserRole = $event);\n    });\n    i0.ɵɵelementStart(17, \"option\", 30);\n    i0.ɵɵtext(18, \"Student\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"option\", 31);\n    i0.ɵɵtext(20, \"Teacher\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(21, \"div\", 24)(22, \"h3\", 25);\n    i0.ɵɵtext(23, \"System Controls\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 32)(25, \"div\", 33)(26, \"span\", 34);\n    i0.ɵɵtext(27, \"Maintenance Mode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"label\", 35)(29, \"input\", 36);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_25_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.systemSettings.maintenanceMode = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"div\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 33)(32, \"span\", 34);\n    i0.ɵɵtext(33, \"Allow Registration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"label\", 35)(35, \"input\", 36);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_25_Template_input_ngModelChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.systemSettings.allowRegistration = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"div\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 33)(38, \"span\", 34);\n    i0.ɵɵtext(39, \"Email Verification Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"label\", 35)(41, \"input\", 36);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_25_Template_input_ngModelChange_41_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.systemSettings.emailVerificationRequired = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(42, \"div\", 37);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(43, \"div\", 38)(44, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function AdminSettingsComponent_div_25_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.clearCache());\n    });\n    i0.ɵɵelement(45, \"i\", 40);\n    i0.ɵɵtext(46, \"Clear System Cache \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function AdminSettingsComponent_div_25_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.restartSystem());\n    });\n    i0.ɵɵelement(48, \"i\", 42);\n    i0.ɵɵtext(49, \"Restart System \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(50, \"div\", 43)(51, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function AdminSettingsComponent_div_25_Template_button_click_51_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.saveSettings(\"System\"));\n    });\n    i0.ɵɵtemplate(52, AdminSettingsComponent_div_25_i_52_Template, 1, 0, \"i\", 45);\n    i0.ɵɵtemplate(53, AdminSettingsComponent_div_25_i_53_Template, 1, 0, \"i\", 46);\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.systemSettings.siteName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.systemSettings.siteDescription);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.systemSettings.defaultUserRole);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.systemSettings.maintenanceMode);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.systemSettings.allowRegistration);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.systemSettings.emailVerificationRequired);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.saving ? \"Saving...\" : \"Save System Settings\", \" \");\n  }\n}\nfunction AdminSettingsComponent_div_26_i_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 47);\n  }\n}\nfunction AdminSettingsComponent_div_26_i_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 48);\n  }\n}\nfunction AdminSettingsComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\", 24)(3, \"h3\", 25);\n    i0.ɵɵtext(4, \"SMTP Configuration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"label\", 26);\n    i0.ɵɵtext(7, \"SMTP Host\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 49);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_26_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.emailSettings.smtpHost = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\")(10, \"label\", 26);\n    i0.ɵɵtext(11, \"SMTP Port\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 50);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_26_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.emailSettings.smtpPort = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\")(14, \"label\", 26);\n    i0.ɵɵtext(15, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"input\", 27);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_26_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.emailSettings.smtpUsername = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\")(18, \"label\", 26);\n    i0.ɵɵtext(19, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"input\", 51);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_26_Template_input_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.emailSettings.smtpPassword = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 24)(22, \"h3\", 25);\n    i0.ɵɵtext(23, \"Email Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\")(25, \"label\", 26);\n    i0.ɵɵtext(26, \"From Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"input\", 52);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_26_Template_input_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.emailSettings.fromEmail = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\")(29, \"label\", 26);\n    i0.ɵɵtext(30, \"From Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"input\", 27);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_26_Template_input_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.emailSettings.fromName = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 33)(33, \"span\", 34);\n    i0.ɵɵtext(34, \"Use Secure Connection\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"label\", 35)(36, \"input\", 36);\n    i0.ɵɵlistener(\"ngModelChange\", function AdminSettingsComponent_div_26_Template_input_ngModelChange_36_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.emailSettings.smtpSecure = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(37, \"div\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 38)(39, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function AdminSettingsComponent_div_26_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.testEmailConnection());\n    });\n    i0.ɵɵelement(40, \"i\", 53);\n    i0.ɵɵtext(41, \"Test Connection \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function AdminSettingsComponent_div_26_Template_button_click_42_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.sendTestEmail());\n    });\n    i0.ɵɵelement(43, \"i\", 55);\n    i0.ɵɵtext(44, \"Send Test Email \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(45, \"div\", 43)(46, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function AdminSettingsComponent_div_26_Template_button_click_46_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.saveSettings(\"Email\"));\n    });\n    i0.ɵɵtemplate(47, AdminSettingsComponent_div_26_i_47_Template, 1, 0, \"i\", 45);\n    i0.ɵɵtemplate(48, AdminSettingsComponent_div_26_i_48_Template, 1, 0, \"i\", 46);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.emailSettings.smtpHost);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.emailSettings.smtpPort);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.emailSettings.smtpUsername);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.emailSettings.smtpPassword);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.emailSettings.fromEmail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.emailSettings.fromName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.emailSettings.smtpSecure);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.saving);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.saving ? \"Saving...\" : \"Save Email Settings\", \" \");\n  }\n}\nfunction AdminSettingsComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 56);\n    i0.ɵɵelement(2, \"i\", 57);\n    i0.ɵɵelementStart(3, \"h3\", 58);\n    i0.ɵɵtext(4, \"Security Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 7);\n    i0.ɵɵtext(6, \"Configure security policies and access controls\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AdminSettingsComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 56);\n    i0.ɵɵelement(2, \"i\", 59);\n    i0.ɵɵelementStart(3, \"h3\", 58);\n    i0.ɵɵtext(4, \"Backup & Restore\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 7);\n    i0.ɵɵtext(6, \"Manage system backups and data recovery\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst _c0 = function () {\n  return {\n    id: \"system\",\n    label: \"System\",\n    icon: \"fas fa-cog\"\n  };\n};\nconst _c1 = function () {\n  return {\n    id: \"email\",\n    label: \"Email\",\n    icon: \"fas fa-envelope\"\n  };\n};\nconst _c2 = function () {\n  return {\n    id: \"security\",\n    label: \"Security\",\n    icon: \"fas fa-shield-alt\"\n  };\n};\nconst _c3 = function () {\n  return {\n    id: \"backup\",\n    label: \"Backup\",\n    icon: \"fas fa-database\"\n  };\n};\nconst _c4 = function (a0, a1, a2, a3) {\n  return [a0, a1, a2, a3];\n};\nexport let AdminSettingsComponent = /*#__PURE__*/(() => {\n  class AdminSettingsComponent {\n    constructor(toastService) {\n      this.toastService = toastService;\n      // System Settings\n      this.systemSettings = {\n        siteName: 'DevBridge Admin',\n        siteDescription: 'Project Management System',\n        maintenanceMode: false,\n        allowRegistration: true,\n        emailVerificationRequired: true,\n        maxFileUploadSize: 10,\n        sessionTimeout: 30,\n        defaultUserRole: 'student',\n        passwordMinLength: 8,\n        passwordRequireSpecialChars: true\n      };\n      // Email Settings\n      this.emailSettings = {\n        smtpHost: '',\n        smtpPort: 587,\n        smtpUsername: '',\n        smtpPassword: '',\n        smtpSecure: true,\n        fromEmail: '',\n        fromName: 'DevBridge Team'\n      };\n      // Security Settings\n      this.securitySettings = {\n        enableTwoFactor: false,\n        maxLoginAttempts: 5,\n        lockoutDuration: 15,\n        passwordExpiry: 90,\n        enableAuditLog: true,\n        enableIpWhitelist: false,\n        allowedIps: []\n      };\n      // Backup Settings\n      this.backupSettings = {\n        autoBackup: true,\n        backupFrequency: 'daily',\n        backupRetention: 30,\n        backupLocation: 'cloud',\n        lastBackup: new Date()\n      };\n      // UI State\n      this.activeTab = 'system';\n      this.loading = false;\n      this.saving = false;\n    }\n    ngOnInit() {\n      this.loadSettings();\n    }\n    loadSettings() {\n      this.loading = true;\n      // In a real app, load settings from API\n      setTimeout(() => {\n        this.loading = false;\n      }, 1000);\n    }\n    saveSettings(settingsType) {\n      this.saving = true;\n      // Simulate API call\n      setTimeout(() => {\n        this.saving = false;\n        this.toastService.showSuccess(`${settingsType} settings saved successfully!`);\n      }, 1500);\n    }\n    setActiveTab(tab) {\n      this.activeTab = tab;\n    }\n    // System Actions\n    toggleMaintenanceMode() {\n      this.systemSettings.maintenanceMode = !this.systemSettings.maintenanceMode;\n      const status = this.systemSettings.maintenanceMode ? 'enabled' : 'disabled';\n      this.toastService.showInfo(`Maintenance mode ${status}`);\n    }\n    clearCache() {\n      this.toastService.showSuccess('System cache cleared successfully!');\n    }\n    restartSystem() {\n      if (confirm('Are you sure you want to restart the system? This will temporarily interrupt service.')) {\n        this.toastService.showInfo('System restart initiated...');\n      }\n    }\n    // Backup Actions\n    createBackup() {\n      this.toastService.showInfo('Creating backup... This may take a few minutes.');\n      setTimeout(() => {\n        this.backupSettings.lastBackup = new Date();\n        this.toastService.showSuccess('Backup created successfully!');\n      }, 3000);\n    }\n    restoreBackup() {\n      if (confirm('Are you sure you want to restore from backup? This will overwrite current data.')) {\n        this.toastService.showInfo('Restoring from backup...');\n      }\n    }\n    // Security Actions\n    generateApiKey() {\n      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n      let result = '';\n      for (let i = 0; i < 32; i++) {\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n      }\n      return result;\n    }\n    addIpToWhitelist(ip) {\n      if (ip && !this.securitySettings.allowedIps.includes(ip)) {\n        this.securitySettings.allowedIps.push(ip);\n        this.toastService.showSuccess(`IP ${ip} added to whitelist`);\n      }\n    }\n    removeIpFromWhitelist(ip) {\n      const index = this.securitySettings.allowedIps.indexOf(ip);\n      if (index > -1) {\n        this.securitySettings.allowedIps.splice(index, 1);\n        this.toastService.showSuccess(`IP ${ip} removed from whitelist`);\n      }\n    }\n    // Email Actions\n    testEmailConnection() {\n      this.toastService.showInfo('Testing email connection...');\n      setTimeout(() => {\n        this.toastService.showSuccess('Email connection test successful!');\n      }, 2000);\n    }\n    sendTestEmail() {\n      this.toastService.showInfo('Sending test email...');\n      setTimeout(() => {\n        this.toastService.showSuccess('Test email sent successfully!');\n      }, 2000);\n    }\n    // Export/Import Settings\n    exportSettings() {\n      const settings = {\n        system: this.systemSettings,\n        email: this.emailSettings,\n        security: this.securitySettings,\n        backup: this.backupSettings\n      };\n      const dataStr = JSON.stringify(settings, null, 2);\n      const dataBlob = new Blob([dataStr], {\n        type: 'application/json'\n      });\n      const url = URL.createObjectURL(dataBlob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = 'admin-settings.json';\n      link.click();\n      URL.revokeObjectURL(url);\n      this.toastService.showSuccess('Settings exported successfully!');\n    }\n    importSettings(event) {\n      const file = event.target.files[0];\n      if (file) {\n        const reader = new FileReader();\n        reader.onload = e => {\n          try {\n            const settings = JSON.parse(e.target?.result);\n            if (settings.system) this.systemSettings = {\n              ...this.systemSettings,\n              ...settings.system\n            };\n            if (settings.email) this.emailSettings = {\n              ...this.emailSettings,\n              ...settings.email\n            };\n            if (settings.security) this.securitySettings = {\n              ...this.securitySettings,\n              ...settings.security\n            };\n            if (settings.backup) this.backupSettings = {\n              ...this.backupSettings,\n              ...settings.backup\n            };\n            this.toastService.showSuccess('Settings imported successfully!');\n          } catch (error) {\n            this.toastService.showError('Invalid settings file format');\n          }\n        };\n        reader.readAsText(file);\n      }\n    }\n    static {\n      this.ɵfac = function AdminSettingsComponent_Factory(t) {\n        return new (t || AdminSettingsComponent)(i0.ɵɵdirectiveInject(i1.ToastService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AdminSettingsComponent,\n        selectors: [[\"app-admin-settings\"]],\n        decls: 29,\n        vars: 14,\n        consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"min-h-screen\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"relative\", \"z-10\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"md:items-center\", \"md:justify-between\", \"mb-8\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-2\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"items-center\", \"gap-3\", \"mt-4\", \"md:mt-0\"], [1, \"inline-flex\", \"items-center\", \"px-3\", \"py-2\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:bg-[#f8fafc]\", \"dark:hover:bg-[#1a1a1a]\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-download\", \"mr-2\"], [1, \"inline-flex\", \"items-center\", \"px-3\", \"py-2\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"hover:bg-[#f8fafc]\", \"dark:hover:bg-[#1a1a1a]\", \"transition-colors\", \"cursor-pointer\"], [1, \"fas\", \"fa-upload\", \"mr-2\"], [\"type\", \"file\", \"accept\", \".json\", 1, \"hidden\", 3, \"change\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-0.5\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"border-b\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\"], [\"aria-label\", \"Tabs\", 1, \"flex\", \"space-x-8\", \"px-6\"], [\"class\", \"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors hover:text-[#4f5fad] dark:hover:text-[#6d78c9] flex items-center\", 3, \"border-[#4f5fad]\", \"text-[#4f5fad]\", \"border-transparent\", \"text-[#6d6870]\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-6\"], [\"class\", \"space-y-6\", 4, \"ngIf\"], [1, \"whitespace-nowrap\", \"py-4\", \"px-1\", \"border-b-2\", \"font-medium\", \"text-sm\", \"transition-colors\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"flex\", \"items-center\", 3, \"click\"], [1, \"space-y-6\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"space-y-4\"], [1, \"text-lg\", \"font-semibold\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-2\"], [\"type\", \"text\", 1, \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [\"rows\", \"3\", 1, \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [1, \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"student\"], [\"value\", \"teacher\"], [1, \"space-y-3\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"relative\", \"inline-flex\", \"items-center\", \"cursor-pointer\"], [\"type\", \"checkbox\", 1, \"sr-only\", \"peer\", 3, \"ngModel\", \"ngModelChange\"], [1, \"w-11\", \"h-6\", \"bg-gray-200\", \"peer-focus:outline-none\", \"peer-focus:ring-4\", \"peer-focus:ring-[#4f5fad]/20\", \"dark:peer-focus:ring-[#6d78c9]/20\", \"rounded-full\", \"peer\", \"dark:bg-gray-700\", \"peer-checked:after:translate-x-full\", \"peer-checked:after:border-white\", \"after:content-['']\", \"after:absolute\", \"after:top-[2px]\", \"after:left-[2px]\", \"after:bg-white\", \"after:border-gray-300\", \"after:border\", \"after:rounded-full\", \"after:h-5\", \"after:w-5\", \"after:transition-all\", \"dark:border-gray-600\", \"peer-checked:bg-[#4f5fad]\", \"dark:peer-checked:bg-[#6d78c9]\"], [1, \"pt-4\", \"space-y-2\"], [1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"bg-[#4f5fad]/10\", \"text-[#4f5fad]\", \"hover:bg-[#4f5fad]/20\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-broom\", \"mr-2\"], [1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"bg-[#ff6b69]/10\", \"text-[#ff6b69]\", \"hover:bg-[#ff6b69]/20\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-power-off\", \"mr-2\"], [1, \"flex\", \"justify-end\", \"pt-6\", \"border-t\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\"], [1, \"px-6\", \"py-2.5\", \"text-sm\", \"font-medium\", \"text-white\", \"rounded-lg\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#4f5fad]\", \"dark:to-[#6d78c9]\", \"hover:shadow-lg\", \"transition-all\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-save mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [1, \"fas\", \"fa-save\", \"mr-2\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"], [\"type\", \"text\", \"placeholder\", \"smtp.gmail.com\", 1, \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"number\", 1, \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"password\", 1, \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"email\", \"placeholder\", \"<EMAIL>\", 1, \"w-full\", \"px-3\", \"py-2\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1a1a1a]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [1, \"fas\", \"fa-plug\", \"mr-2\"], [1, \"w-full\", \"px-3\", \"py-2\", \"text-sm\", \"font-medium\", \"rounded-lg\", \"bg-[#00d4aa]/10\", \"text-[#00d4aa]\", \"hover:bg-[#00d4aa]/20\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\", \"mr-2\"], [1, \"text-center\", \"py-12\"], [1, \"fas\", \"fa-shield-alt\", \"text-4xl\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-4\"], [1, \"text-lg\", \"font-semibold\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-database\", \"text-4xl\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-4\"]],\n        template: function AdminSettingsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\")(7, \"h1\", 6);\n            i0.ɵɵtext(8, \" System Settings \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"p\", 7);\n            i0.ɵɵtext(10, \" Configure system-wide settings and preferences \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 8)(12, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function AdminSettingsComponent_Template_button_click_12_listener() {\n              return ctx.exportSettings();\n            });\n            i0.ɵɵelement(13, \"i\", 10);\n            i0.ɵɵtext(14, \"Export Settings \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"label\", 11);\n            i0.ɵɵelement(16, \"i\", 12);\n            i0.ɵɵtext(17, \"Import Settings \");\n            i0.ɵɵelementStart(18, \"input\", 13);\n            i0.ɵɵlistener(\"change\", function AdminSettingsComponent_Template_input_change_18_listener($event) {\n              return ctx.importSettings($event);\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(19, \"div\", 14);\n            i0.ɵɵelement(20, \"div\", 15);\n            i0.ɵɵelementStart(21, \"div\", 16)(22, \"nav\", 17);\n            i0.ɵɵtemplate(23, AdminSettingsComponent_button_23_Template, 3, 11, \"button\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"div\", 19);\n            i0.ɵɵtemplate(25, AdminSettingsComponent_div_25_Template, 55, 10, \"div\", 20);\n            i0.ɵɵtemplate(26, AdminSettingsComponent_div_26_Template, 50, 11, \"div\", 20);\n            i0.ɵɵtemplate(27, AdminSettingsComponent_div_27_Template, 7, 0, \"div\", 20);\n            i0.ɵɵtemplate(28, AdminSettingsComponent_div_28_Template, 7, 0, \"div\", 20);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(23);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction4(9, _c4, i0.ɵɵpureFunction0(5, _c0), i0.ɵɵpureFunction0(6, _c1), i0.ɵɵpureFunction0(7, _c2), i0.ɵɵpureFunction0(8, _c3)));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"system\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"email\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"security\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"backup\");\n          }\n        },\n        dependencies: [i2.NgForOf, i2.NgIf, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.CheckboxControlValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgModel],\n        styles: [\".settings-container[_ngcontent-%COMP%]{min-height:100vh;background:linear-gradient(135deg,#edf1f4 0%,#f8fafc 100%)}.dark[_ngcontent-%COMP%]   .settings-container[_ngcontent-%COMP%]{background:linear-gradient(135deg,#121212 0%,#1a1a1a 100%)}.tab-nav[_ngcontent-%COMP%]{border-bottom:1px solid #edf1f4}.dark[_ngcontent-%COMP%]   .tab-nav[_ngcontent-%COMP%]{border-bottom-color:#2a2a2a}.tab-button[_ngcontent-%COMP%]{position:relative;padding:1rem .25rem;border-bottom:2px solid transparent;font-weight:500;font-size:.875rem;transition:all .2s ease;color:#6d6870}.dark[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]{color:#a0a0a0}.tab-button[_ngcontent-%COMP%]:hover{color:#4f5fad}.dark[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]:hover{color:#6d78c9}.tab-button.active[_ngcontent-%COMP%]{color:#4f5fad;border-bottom-color:#4f5fad}.dark[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%]{color:#6d78c9;border-bottom-color:#6d78c9}.form-input[_ngcontent-%COMP%]{width:100%;padding:.75rem;border:1px solid #bdc6cc;border-radius:.5rem;background-color:#fff;color:#6d6870;transition:all .2s ease}.dark[_ngcontent-%COMP%]   .form-input[_ngcontent-%COMP%]{border-color:#2a2a2a;background-color:#1a1a1a;color:#e0e0e0}.form-input[_ngcontent-%COMP%]:focus{outline:none;border-color:#4f5fad;box-shadow:0 0 0 2px #4f5fad33}.dark[_ngcontent-%COMP%]   .form-input[_ngcontent-%COMP%]:focus{border-color:#6d78c9;box-shadow:0 0 0 2px #6d78c933}.toggle-switch[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center;cursor:pointer}.toggle-input[_ngcontent-%COMP%]{position:absolute;opacity:0;pointer-events:none}.toggle-slider[_ngcontent-%COMP%]{width:2.75rem;height:1.5rem;background-color:#e5e7eb;border-radius:9999px;position:relative;transition:all .2s ease}.dark[_ngcontent-%COMP%]   .toggle-slider[_ngcontent-%COMP%]{background-color:#374151}.toggle-slider[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:2px;left:2px;width:1.25rem;height:1.25rem;background-color:#fff;border-radius:50%;transition:all .2s ease;box-shadow:0 1px 3px #0000001a}.toggle-input[_ngcontent-%COMP%]:checked + .toggle-slider[_ngcontent-%COMP%]{background-color:#4f5fad}.dark[_ngcontent-%COMP%]   .toggle-input[_ngcontent-%COMP%]:checked + .toggle-slider[_ngcontent-%COMP%]{background-color:#6d78c9}.toggle-input[_ngcontent-%COMP%]:checked + .toggle-slider[_ngcontent-%COMP%]:after{transform:translate(1.25rem)}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3d4a85 0%,#4f5fad 100%);color:#fff;padding:.625rem 1.5rem;border-radius:.5rem;font-weight:500;font-size:.875rem;transition:all .2s ease;border:none;cursor:pointer}.dark[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad 0%,#6d78c9 100%)}.btn-primary[_ngcontent-%COMP%]:hover{box-shadow:0 4px 12px #4f5fad4d;transform:translateY(-1px)}.btn-primary[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed;transform:none}.btn-secondary[_ngcontent-%COMP%]{background-color:#4f5fad1a;color:#4f5fad;padding:.5rem .75rem;border-radius:.5rem;font-weight:500;font-size:.875rem;transition:all .2s ease;border:none;cursor:pointer}.dark[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]{background-color:#6d78c91a;color:#6d78c9}.btn-secondary[_ngcontent-%COMP%]:hover{background-color:#4f5fad33}.dark[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover{background-color:#6d78c933}.settings-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:.75rem;box-shadow:0 4px 6px #0000000d;border:1px solid rgba(237,241,244,.5);-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px);position:relative;overflow:hidden}.dark[_ngcontent-%COMP%]   .settings-card[_ngcontent-%COMP%]{background-color:#1e1e1e;box-shadow:0 4px 20px #0003;border-color:#2a2a2a}.settings-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:2px;background:linear-gradient(90deg,#3d4a85 0%,#4f5fad 100%)}.dark[_ngcontent-%COMP%]   .settings-card[_ngcontent-%COMP%]:before{background:linear-gradient(90deg,#6d78c9 0%,#4f5fad 100%)}.fade-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-in-out}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.slide-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideIn .3s ease-out}@keyframes _ngcontent-%COMP%_slideIn{0%{opacity:0;transform:translate(-20px)}to{opacity:1;transform:translate(0)}}.loading-spinner[_ngcontent-%COMP%]{display:inline-block;width:1rem;height:1rem;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.settings-container[_ngcontent-%COMP%]{padding:1rem}.tab-nav[_ngcontent-%COMP%]{overflow-x:auto;white-space:nowrap}.tab-button[_ngcontent-%COMP%]{flex-shrink:0;margin-right:2rem}}.form-input[_ngcontent-%COMP%]:focus, .btn-primary[_ngcontent-%COMP%]:focus, .btn-secondary[_ngcontent-%COMP%]:focus{outline:2px solid #4f5fad;outline-offset:2px}.dark[_ngcontent-%COMP%]   .form-input[_ngcontent-%COMP%]:focus, .dark[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:focus, .dark[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:focus{outline-color:#6d78c9}.input-success[_ngcontent-%COMP%]{border-color:#10b981;box-shadow:0 0 0 2px #10b98133}.input-error[_ngcontent-%COMP%]{border-color:#ef4444;box-shadow:0 0 0 2px #ef444433}.tooltip[_ngcontent-%COMP%]{position:relative}.tooltip[_ngcontent-%COMP%]:after{content:attr(data-tooltip);position:absolute;bottom:100%;left:50%;transform:translate(-50%);background-color:#1f2937;color:#fff;padding:.5rem;border-radius:.25rem;font-size:.75rem;white-space:nowrap;opacity:0;pointer-events:none;transition:opacity .2s ease;z-index:10}.tooltip[_ngcontent-%COMP%]:hover:after{opacity:1}\"]\n      });\n    }\n  }\n  return AdminSettingsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}