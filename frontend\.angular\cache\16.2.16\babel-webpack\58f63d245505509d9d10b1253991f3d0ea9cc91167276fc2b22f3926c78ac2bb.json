{"ast": null, "code": "import { moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';\nimport { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/task.service\";\nimport * as i2 from \"src/app/services/equipe.service\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@angular/cdk/drag-drop\";\nfunction TaskListComponent_h1_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h1\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" T\\u00E2ches: \", ctx_r0.team.name, \" \");\n  }\n}\nfunction TaskListComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"div\", 20)(3, \"span\", 21);\n    i0.ɵɵtext(4, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 22)(6, \"span\", 21);\n    i0.ɵɵtext(7, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 20)(9, \"span\", 21);\n    i0.ɵɵtext(10, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"p\", 23);\n    i0.ɵɵtext(12, \"Chargement des t\\u00E2ches...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction TaskListComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 24)(2, \"div\", 25);\n    i0.ɵɵelement(3, \"i\", 26);\n    i0.ɵɵelementStart(4, \"div\", 27);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_18_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.teamId && ctx_r7.loadTasks(ctx_r7.teamId));\n    });\n    i0.ɵɵelement(7, \"i\", 29);\n    i0.ɵɵtext(8, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nfunction TaskListComponent_div_19_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r10 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", user_r10._id || user_r10.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getUserName(user_r10._id || user_r10.id || \"\"), \" \");\n  }\n}\nfunction TaskListComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 30)(3, \"div\", 31)(4, \"h4\", 32);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 33)(7, \"form\", 34);\n    i0.ɵɵlistener(\"ngSubmit\", function TaskListComponent_div_19_Template_form_ngSubmit_7_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.editingTask ? ctx_r11.updateTask() : ctx_r11.createTask());\n    });\n    i0.ɵɵelementStart(8, \"div\", 35)(9, \"div\", 36)(10, \"label\", 37);\n    i0.ɵɵtext(11, \"Titre*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 38);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_19_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.editingTask ? ctx_r13.editingTask.title = $event : ctx_r13.newTask.title = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 39)(14, \"label\", 40);\n    i0.ɵɵtext(15, \"Priorit\\u00E9*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"select\", 41);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_19_Template_select_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.editingTask ? ctx_r14.editingTask.priority = $event : ctx_r14.newTask.priority = $event);\n    });\n    i0.ɵɵelementStart(17, \"option\", 42);\n    i0.ɵɵtext(18, \"Basse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"option\", 43);\n    i0.ɵɵtext(20, \"Moyenne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"option\", 44);\n    i0.ɵɵtext(22, \"Haute\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 39)(24, \"label\", 45);\n    i0.ɵɵtext(25, \"Statut*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"select\", 46);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_19_Template_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.editingTask ? ctx_r15.editingTask.status = $event : ctx_r15.newTask.status = $event);\n    });\n    i0.ɵɵelementStart(27, \"option\", 47);\n    i0.ɵɵtext(28, \"\\u00C0 faire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"option\", 48);\n    i0.ɵɵtext(30, \"En cours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"option\", 49);\n    i0.ɵɵtext(32, \"Termin\\u00E9e\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 36)(34, \"label\", 50);\n    i0.ɵɵtext(35, \"Assign\\u00E9e \\u00E0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"select\", 51);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_19_Template_select_ngModelChange_36_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.editingTask ? ctx_r16.editingTask.assignedTo = $event : ctx_r16.newTask.assignedTo = $event);\n    });\n    i0.ɵɵelementStart(37, \"option\", 52);\n    i0.ɵɵtext(38, \"Non assign\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(39, TaskListComponent_div_19_option_39_Template, 2, 2, \"option\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 36)(41, \"label\", 54);\n    i0.ɵɵtext(42, \"Date d'\\u00E9ch\\u00E9ance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"input\", 55);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_19_Template_input_ngModelChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.editingTask ? ctx_r17.editingTask.dueDate = $event : ctx_r17.newTask.dueDate = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 3)(45, \"label\", 56);\n    i0.ɵɵtext(46, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"textarea\", 57);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_19_Template_textarea_ngModelChange_47_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.editingTask ? ctx_r18.editingTask.description = $event : ctx_r18.newTask.description = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 58)(49, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_19_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.editingTask ? ctx_r19.cancelEdit() : ctx_r19.toggleTaskForm());\n    });\n    i0.ɵɵtext(50, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"button\", 60);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.editingTask ? \"Modifier la t\\u00E2che\" : \"Nouvelle t\\u00E2che\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.editingTask ? ctx_r3.editingTask.title : ctx_r3.newTask.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.editingTask ? ctx_r3.editingTask.priority : ctx_r3.newTask.priority);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.editingTask ? ctx_r3.editingTask.status : ctx_r3.newTask.status);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.editingTask ? ctx_r3.editingTask.assignedTo : ctx_r3.newTask.assignedTo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.users);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.editingTask ? ctx_r3.editingTask.dueDate : ctx_r3.newTask.dueDate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.editingTask ? ctx_r3.editingTask.description : ctx_r3.newTask.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.editingTask ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er\", \" \");\n  }\n}\nfunction TaskListComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 3)(2, \"div\", 30)(3, \"div\", 62)(4, \"div\", 35)(5, \"div\", 63)(6, \"div\", 64)(7, \"span\", 65);\n    i0.ɵɵelement(8, \"i\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 67);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_20_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.searchTerm = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 63)(11, \"select\", 68);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_20_Template_select_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.statusFilter = $event);\n    });\n    i0.ɵɵelementStart(12, \"option\", 69);\n    i0.ɵɵtext(13, \"Tous les statuts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 47);\n    i0.ɵɵtext(15, \"\\u00C0 faire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 48);\n    i0.ɵɵtext(17, \"En cours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 49);\n    i0.ɵɵtext(19, \"Termin\\u00E9es\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 63)(21, \"select\", 68);\n    i0.ɵɵlistener(\"ngModelChange\", function TaskListComponent_div_20_Template_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.priorityFilter = $event);\n    });\n    i0.ɵɵelementStart(22, \"option\", 69);\n    i0.ɵɵtext(23, \"Toutes les priorit\\u00E9s\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"option\", 44);\n    i0.ɵɵtext(25, \"Haute\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"option\", 43);\n    i0.ɵɵtext(27, \"Moyenne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"option\", 42);\n    i0.ɵɵtext(29, \"Basse\");\n    i0.ɵɵelementEnd()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.statusFilter);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.priorityFilter);\n  }\n}\nfunction TaskListComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 70)(2, \"div\", 71);\n    i0.ɵɵelement(3, \"i\", 72);\n    i0.ɵɵelementStart(4, \"h3\", 73);\n    i0.ɵɵtext(5, \"Aucune t\\u00E2che trouv\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 74);\n    i0.ɵɵtext(7, \" Commencez par cr\\u00E9er une nouvelle t\\u00E2che pour votre \\u00E9quipe. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_21_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.toggleTaskForm());\n    });\n    i0.ɵɵelement(9, \"i\", 11);\n    i0.ɵɵtext(10, \" Cr\\u00E9er une t\\u00E2che \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction TaskListComponent_div_22_div_12_small_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r32 = i0.ɵɵnextContext().$implicit;\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r33.getUserName(task_r32.assignedTo), \" \");\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"bg-danger\": a0,\n    \"bg-warning text-dark\": a1,\n    \"bg-info text-dark\": a2\n  };\n};\nfunction TaskListComponent_div_22_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"div\", 95)(2, \"h6\", 96);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 97)(5, \"button\", 98);\n    i0.ɵɵelement(6, \"i\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ul\", 100)(8, \"li\")(9, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_12_Template_button_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r36);\n      const task_r32 = restoredCtx.$implicit;\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.editTask(task_r32));\n    });\n    i0.ɵɵelement(10, \"i\", 102);\n    i0.ɵɵtext(11, \" Modifier \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"li\")(13, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_12_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r36);\n      const task_r32 = restoredCtx.$implicit;\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.updateTaskStatus(task_r32, \"in-progress\"));\n    });\n    i0.ɵɵelement(14, \"i\", 103);\n    i0.ɵɵtext(15, \" D\\u00E9placer vers \\\"En cours\\\" \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"li\")(17, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_12_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r36);\n      const task_r32 = restoredCtx.$implicit;\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.updateTaskStatus(task_r32, \"done\"));\n    });\n    i0.ɵɵelement(18, \"i\", 89);\n    i0.ɵɵtext(19, \" Marquer comme termin\\u00E9e \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"li\");\n    i0.ɵɵelement(21, \"hr\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"li\")(23, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_12_Template_button_click_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r36);\n      const task_r32 = restoredCtx.$implicit;\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(task_r32._id && ctx_r39.deleteTask(task_r32._id));\n    });\n    i0.ɵɵelement(24, \"i\", 106);\n    i0.ɵɵtext(25, \" Supprimer \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(26, \"p\", 107);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 108)(29, \"span\", 109);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, TaskListComponent_div_22_div_12_small_31_Template, 2, 1, \"small\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 111);\n    i0.ɵɵelement(33, \"i\", 112);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r32 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", \"priority-\" + task_r32.priority);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(task_r32.title);\n    i0.ɵɵadvance(24);\n    i0.ɵɵtextInterpolate1(\" \", task_r32.description || \"Aucune description\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, task_r32.priority === \"high\", task_r32.priority === \"medium\", task_r32.priority === \"low\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", task_r32.priority === \"high\" ? \"Haute\" : task_r32.priority === \"medium\" ? \"Moyenne\" : \"Basse\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r32.assignedTo);\n  }\n}\nfunction TaskListComponent_div_22_div_24_small_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r40 = i0.ɵɵnextContext().$implicit;\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r41.getUserName(task_r40.assignedTo), \" \");\n  }\n}\nfunction TaskListComponent_div_22_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"div\", 95)(2, \"h6\", 96);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 97)(5, \"button\", 98);\n    i0.ɵɵelement(6, \"i\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ul\", 100)(8, \"li\")(9, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_24_Template_button_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const task_r40 = restoredCtx.$implicit;\n      const ctx_r43 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r43.editTask(task_r40));\n    });\n    i0.ɵɵelement(10, \"i\", 102);\n    i0.ɵɵtext(11, \" Modifier \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"li\")(13, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_24_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const task_r40 = restoredCtx.$implicit;\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.updateTaskStatus(task_r40, \"todo\"));\n    });\n    i0.ɵɵelement(14, \"i\", 9);\n    i0.ɵɵtext(15, \" D\\u00E9placer vers \\\"\\u00C0 faire\\\" \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"li\")(17, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_24_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const task_r40 = restoredCtx.$implicit;\n      const ctx_r46 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r46.updateTaskStatus(task_r40, \"done\"));\n    });\n    i0.ɵɵelement(18, \"i\", 89);\n    i0.ɵɵtext(19, \" Marquer comme termin\\u00E9e \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"li\");\n    i0.ɵɵelement(21, \"hr\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"li\")(23, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_24_Template_button_click_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const task_r40 = restoredCtx.$implicit;\n      const ctx_r47 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(task_r40._id && ctx_r47.deleteTask(task_r40._id));\n    });\n    i0.ɵɵelement(24, \"i\", 106);\n    i0.ɵɵtext(25, \" Supprimer \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(26, \"p\", 107);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 108)(29, \"span\", 109);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, TaskListComponent_div_22_div_24_small_31_Template, 2, 1, \"small\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 111);\n    i0.ɵɵelement(33, \"i\", 112);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r40 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", \"priority-\" + task_r40.priority);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(task_r40.title);\n    i0.ɵɵadvance(24);\n    i0.ɵɵtextInterpolate1(\" \", task_r40.description || \"Aucune description\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, task_r40.priority === \"high\", task_r40.priority === \"medium\", task_r40.priority === \"low\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", task_r40.priority === \"high\" ? \"Haute\" : task_r40.priority === \"medium\" ? \"Moyenne\" : \"Basse\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r40.assignedTo);\n  }\n}\nfunction TaskListComponent_div_22_div_36_small_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 113);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const task_r48 = i0.ɵɵnextContext().$implicit;\n    const ctx_r49 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r49.getUserName(task_r48.assignedTo), \" \");\n  }\n}\nfunction TaskListComponent_div_22_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 114)(1, \"div\", 95)(2, \"h6\", 96);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 97)(5, \"button\", 98);\n    i0.ɵɵelement(6, \"i\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ul\", 100)(8, \"li\")(9, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_36_Template_button_click_9_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const task_r48 = restoredCtx.$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r51.editTask(task_r48));\n    });\n    i0.ɵɵelement(10, \"i\", 102);\n    i0.ɵɵtext(11, \" Modifier \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"li\")(13, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_36_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const task_r48 = restoredCtx.$implicit;\n      const ctx_r53 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r53.updateTaskStatus(task_r48, \"todo\"));\n    });\n    i0.ɵɵelement(14, \"i\", 9);\n    i0.ɵɵtext(15, \" D\\u00E9placer vers \\\"\\u00C0 faire\\\" \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"li\")(17, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_36_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const task_r48 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.updateTaskStatus(task_r48, \"in-progress\"));\n    });\n    i0.ɵɵelement(18, \"i\", 9);\n    i0.ɵɵtext(19, \" D\\u00E9placer vers \\\"En cours\\\" \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"li\");\n    i0.ɵɵelement(21, \"hr\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"li\")(23, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function TaskListComponent_div_22_div_36_Template_button_click_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const task_r48 = restoredCtx.$implicit;\n      const ctx_r55 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(task_r48._id && ctx_r55.deleteTask(task_r48._id));\n    });\n    i0.ɵɵelement(24, \"i\", 106);\n    i0.ɵɵtext(25, \" Supprimer \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(26, \"p\", 107);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 108)(29, \"span\", 109);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, TaskListComponent_div_22_div_36_small_31_Template, 2, 1, \"small\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 111);\n    i0.ɵɵelement(33, \"i\", 112);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r48 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", \"priority-\" + task_r48.priority);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(task_r48.title);\n    i0.ɵɵadvance(24);\n    i0.ɵɵtextInterpolate1(\" \", task_r48.description || \"Aucune description\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, task_r48.priority === \"high\", task_r48.priority === \"medium\", task_r48.priority === \"low\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", task_r48.priority === \"high\" ? \"Haute\" : task_r48.priority === \"medium\" ? \"Moyenne\" : \"Basse\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", task_r48.assignedTo);\n  }\n}\nconst _c1 = function (a0, a1) {\n  return [a0, a1];\n};\nfunction TaskListComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 63)(2, \"div\", 76)(3, \"div\", 31)(4, \"h5\", 77);\n    i0.ɵɵelement(5, \"i\", 78);\n    i0.ɵɵtext(6, \" \\u00C0 faire \");\n    i0.ɵɵelementStart(7, \"span\", 79);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 62)(10, \"div\", 80, 81);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function TaskListComponent_div_22_Template_div_cdkDropListDropped_10_listener($event) {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r56 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r56.drop($event));\n    });\n    i0.ɵɵtemplate(12, TaskListComponent_div_22_div_12_Template, 34, 10, \"div\", 82);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 63)(14, \"div\", 76)(15, \"div\", 83)(16, \"h5\", 77);\n    i0.ɵɵelement(17, \"i\", 84);\n    i0.ɵɵtext(18, \" En cours \");\n    i0.ɵɵelementStart(19, \"span\", 85);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 62)(22, \"div\", 86, 87);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function TaskListComponent_div_22_Template_div_cdkDropListDropped_22_listener($event) {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.drop($event));\n    });\n    i0.ɵɵtemplate(24, TaskListComponent_div_22_div_24_Template, 34, 10, \"div\", 82);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"div\", 63)(26, \"div\", 76)(27, \"div\", 88)(28, \"h5\", 77);\n    i0.ɵɵelement(29, \"i\", 89);\n    i0.ɵɵtext(30, \" Termin\\u00E9es \");\n    i0.ɵɵelementStart(31, \"span\", 90);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 62)(34, \"div\", 91, 92);\n    i0.ɵɵlistener(\"cdkDropListDropped\", function TaskListComponent_div_22_Template_div_cdkDropListDropped_34_listener($event) {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.drop($event));\n    });\n    i0.ɵɵtemplate(36, TaskListComponent_div_22_div_36_Template, 34, 10, \"div\", 93);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const _r26 = i0.ɵɵreference(11);\n    const _r28 = i0.ɵɵreference(23);\n    const _r30 = i0.ɵɵreference(35);\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getTodoTasksCount(), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"cdkDropListData\", ctx_r6.getTodoTasks())(\"cdkDropListConnectedTo\", i0.ɵɵpureFunction2(12, _c1, _r28, _r30));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.getTodoTasks());\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getInProgressTasksCount(), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"cdkDropListData\", ctx_r6.getInProgressTasks())(\"cdkDropListConnectedTo\", i0.ɵɵpureFunction2(15, _c1, _r26, _r30));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.getInProgressTasks());\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getDoneTasksCount(), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"cdkDropListData\", ctx_r6.getDoneTasks())(\"cdkDropListConnectedTo\", i0.ɵɵpureFunction2(18, _c1, _r26, _r28));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.getDoneTasks());\n  }\n}\nexport let TaskListComponent = /*#__PURE__*/(() => {\n  class TaskListComponent {\n    constructor(taskService, equipeService, userService, route, router, notificationService) {\n      this.taskService = taskService;\n      this.equipeService = equipeService;\n      this.userService = userService;\n      this.route = route;\n      this.router = router;\n      this.notificationService = notificationService;\n      this.tasks = [];\n      this.teamId = null;\n      this.team = null;\n      this.loading = false;\n      this.error = null;\n      this.users = [];\n      this.editingTask = null;\n      this.showTaskForm = false;\n      // Filtres\n      this.statusFilter = 'all';\n      this.priorityFilter = 'all';\n      this.searchTerm = '';\n    }\n    ngOnInit() {\n      // Initialiser la nouvelle tâche\n      this.newTask = this.initializeNewTask();\n      this.route.paramMap.subscribe(params => {\n        this.teamId = params.get('id');\n        if (this.teamId) {\n          this.loadTeamDetails(this.teamId);\n          this.loadTasks(this.teamId);\n          this.loadUsers();\n        } else {\n          this.error = \"ID d'équipe manquant\";\n          this.notificationService.showError(\"ID d'équipe manquant\");\n        }\n      });\n    }\n    loadTeamDetails(teamId) {\n      this.loading = true;\n      // Utiliser les données de test si l'API n'est pas disponible\n      const useMockData = false; // Mettre à true pour utiliser les données de test\n      if (useMockData) {\n        // Données de test pour simuler les détails de l'équipe\n        const mockTeam = {\n          _id: teamId,\n          name: 'Équipe ' + teamId,\n          description: \"Description de l'équipe \" + teamId,\n          admin: 'admin123',\n          members: []\n        };\n        setTimeout(() => {\n          this.team = mockTeam;\n          this.loading = false;\n          console.log(\"Détails de l'équipe chargés (mock):\", this.team);\n        }, 300);\n      } else {\n        // Utiliser l'API réelle\n        this.equipeService.getEquipe(teamId).pipe(finalize(() => this.loading = false)).subscribe({\n          next: data => {\n            this.team = data;\n            console.log(\"Détails de l'équipe chargés depuis l'API:\", this.team);\n          },\n          error: error => {\n            console.error(\"Erreur lors du chargement des détails de l'équipe:\", error);\n            this.error = \"Impossible de charger les détails de l'équipe\";\n            this.notificationService.showError(\"Erreur lors du chargement des détails de l'équipe\");\n            // Fallback aux données de test en cas d'erreur\n            const mockTeam = {\n              _id: teamId,\n              name: 'Équipe ' + teamId + ' (fallback)',\n              description: \"Description de l'équipe \" + teamId,\n              admin: 'admin123',\n              members: []\n            };\n            this.team = mockTeam;\n          }\n        });\n      }\n    }\n    loadTasks(teamId) {\n      this.loading = true;\n      // Utiliser les données de test si l'API n'est pas disponible\n      const useMockData = false; // Mettre à true pour utiliser les données de test\n      if (useMockData) {\n        // Données de test pour simuler les tâches\n        const mockTasks = [{\n          _id: '1',\n          title: 'Tâche 1',\n          description: 'Description de la tâche 1',\n          status: 'todo',\n          priority: 'high',\n          teamId: teamId\n        }, {\n          _id: '2',\n          title: 'Tâche 2',\n          description: 'Description de la tâche 2',\n          status: 'todo',\n          priority: 'medium',\n          teamId: teamId\n        }, {\n          _id: '3',\n          title: 'Tâche 3',\n          description: 'Description de la tâche 3',\n          status: 'in-progress',\n          priority: 'high',\n          teamId: teamId\n        }, {\n          _id: '4',\n          title: 'Tâche 4',\n          description: 'Description de la tâche 4',\n          status: 'done',\n          priority: 'low',\n          teamId: teamId\n        }];\n        setTimeout(() => {\n          this.tasks = mockTasks;\n          this.sortTasks();\n          this.loading = false;\n          console.log('Tâches chargées (mock):', this.tasks);\n        }, 500);\n      } else {\n        // Utiliser l'API réelle\n        this.taskService.getTasksByTeam(teamId).pipe(finalize(() => this.loading = false)).subscribe({\n          next: data => {\n            this.tasks = data;\n            this.sortTasks();\n            console.log(\"Tâches chargées depuis l'API:\", this.tasks);\n          },\n          error: error => {\n            console.error('Erreur lors du chargement des tâches:', error);\n            this.error = 'Impossible de charger les tâches';\n            this.notificationService.showError('Erreur lors du chargement des tâches');\n            // Fallback aux données de test en cas d'erreur\n            const mockTasks = [{\n              _id: '1',\n              title: 'Tâche 1 (fallback)',\n              description: 'Description de la tâche 1',\n              status: 'todo',\n              priority: 'high',\n              teamId: teamId\n            }, {\n              _id: '2',\n              title: 'Tâche 2 (fallback)',\n              description: 'Description de la tâche 2',\n              status: 'todo',\n              priority: 'medium',\n              teamId: teamId\n            }];\n            this.tasks = mockTasks;\n            this.sortTasks();\n            console.log('Tâches chargées (fallback):', this.tasks);\n          }\n        });\n      }\n    }\n    // Gestion du glisser-déposer\n    drop(event) {\n      if (event.previousContainer === event.container) {\n        // Déplacement dans la même liste\n        moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);\n      } else {\n        // Déplacement entre listes\n        transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);\n        // Mettre à jour le statut de la tâche\n        const task = event.container.data[event.currentIndex];\n        let newStatus;\n        if (event.container.id === 'todo-list') {\n          newStatus = 'todo';\n        } else if (event.container.id === 'in-progress-list') {\n          newStatus = 'in-progress';\n        } else {\n          newStatus = 'done';\n        }\n        if (task._id && task.status !== newStatus) {\n          task.status = newStatus;\n          this.updateTaskStatus(task, newStatus);\n        }\n      }\n    }\n    loadUsers() {\n      // Utiliser les données de test si l'API n'est pas disponible\n      const useMockData = false; // Mettre à true pour utiliser les données de test\n      if (useMockData) {\n        // Données de test pour simuler les utilisateurs\n        const mockUsers = [{\n          _id: 'user1',\n          username: 'john_doe',\n          email: '<EMAIL>',\n          role: 'admin',\n          isActive: true\n        }, {\n          _id: 'user2',\n          username: 'jane_smith',\n          email: '<EMAIL>',\n          role: 'student',\n          isActive: true\n        }];\n        setTimeout(() => {\n          this.users = mockUsers;\n          console.log('Utilisateurs chargés (mock):', this.users);\n        }, 400);\n      } else {\n        // TODO: Implémenter l'API réelle pour récupérer les utilisateurs\n        // Pour l'instant, utiliser les données mockées\n        const mockUsers = [{\n          _id: 'user1',\n          username: 'john_doe',\n          email: '<EMAIL>',\n          role: 'admin',\n          isActive: true\n        }, {\n          _id: 'user2',\n          username: 'jane_smith',\n          email: '<EMAIL>',\n          role: 'student',\n          isActive: true\n        }];\n        this.users = mockUsers;\n        console.log('Utilisateurs chargés (mock API):', this.users);\n      }\n    }\n    getUserName(userId) {\n      const user = this.users.find(u => u._id === userId || u.id === userId);\n      if (user) {\n        if (user.firstName && user.lastName) {\n          return `${user.firstName} ${user.lastName}`;\n        } else if (user.name) {\n          return user.name;\n        }\n      }\n      return 'Utilisateur inconnu';\n    }\n    createTask() {\n      if (!this.teamId) {\n        this.notificationService.showError(\"ID d'équipe manquant\");\n        return;\n      }\n      this.newTask.teamId = this.teamId;\n      this.loading = true;\n      this.taskService.createTask(this.newTask).pipe(finalize(() => this.loading = false)).subscribe({\n        next: data => {\n          this.tasks.push(data);\n          this.sortTasks();\n          this.newTask = this.initializeNewTask();\n          this.showTaskForm = false;\n          this.notificationService.showSuccess('Tâche créée avec succès');\n        },\n        error: error => {\n          console.error('Erreur lors de la création de la tâche:', error);\n          this.notificationService.showError('Erreur lors de la création de la tâche');\n        }\n      });\n    }\n    updateTask() {\n      if (!this.editingTask || !this.editingTask._id) {\n        this.notificationService.showError('Tâche invalide');\n        return;\n      }\n      this.loading = true;\n      this.taskService.updateTask(this.editingTask._id, this.editingTask).pipe(finalize(() => this.loading = false)).subscribe({\n        next: data => {\n          const index = this.tasks.findIndex(t => t._id === data._id);\n          if (index !== -1) {\n            this.tasks[index] = data;\n          }\n          this.editingTask = null;\n          this.notificationService.showSuccess('Tâche mise à jour avec succès');\n        },\n        error: error => {\n          console.error('Erreur lors de la mise à jour de la tâche:', error);\n          this.notificationService.showError('Erreur lors de la mise à jour de la tâche');\n        }\n      });\n    }\n    deleteTask(id) {\n      if (confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?')) {\n        this.loading = true;\n        this.taskService.deleteTask(id).pipe(finalize(() => this.loading = false)).subscribe({\n          next: () => {\n            this.tasks = this.tasks.filter(t => t._id !== id);\n            this.notificationService.showSuccess('Tâche supprimée avec succès');\n          },\n          error: error => {\n            console.error('Erreur lors de la suppression de la tâche:', error);\n            this.notificationService.showError('Erreur lors de la suppression de la tâche');\n          }\n        });\n      }\n    }\n    updateTaskStatus(task, status) {\n      if (!task._id) return;\n      this.loading = true;\n      this.taskService.updateTaskStatus(task._id, status).pipe(finalize(() => this.loading = false)).subscribe({\n        next: data => {\n          const index = this.tasks.findIndex(t => t._id === data._id);\n          if (index !== -1) {\n            this.tasks[index] = data;\n          }\n          this.notificationService.showSuccess('Statut de la tâche mis à jour');\n        },\n        error: error => {\n          console.error('Erreur lors de la mise à jour du statut:', error);\n          this.notificationService.showError('Erreur lors de la mise à jour du statut');\n        }\n      });\n    }\n    editTask(task) {\n      this.editingTask = {\n        ...task\n      };\n    }\n    cancelEdit() {\n      this.editingTask = null;\n    }\n    toggleTaskForm() {\n      this.showTaskForm = !this.showTaskForm;\n      if (this.showTaskForm) {\n        this.newTask = this.initializeNewTask();\n      }\n    }\n    initializeNewTask() {\n      return {\n        title: '',\n        description: '',\n        status: 'todo',\n        priority: 'medium',\n        teamId: this.teamId || '',\n        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Par défaut, une semaine à partir d'aujourd'hui\n      };\n    }\n\n    sortTasks() {\n      // Trier par priorité (high > medium > low) puis par statut (todo > in-progress > done)\n      this.tasks.sort((a, b) => {\n        const priorityOrder = {\n          high: 0,\n          medium: 1,\n          low: 2\n        };\n        const statusOrder = {\n          todo: 0,\n          'in-progress': 1,\n          done: 2\n        };\n        // D'abord par priorité\n        if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {\n          return priorityOrder[a.priority] - priorityOrder[b.priority];\n        }\n        // Ensuite par statut\n        return statusOrder[a.status] - statusOrder[b.status];\n      });\n    }\n    // Méthodes de filtrage\n    filterTasks() {\n      return this.tasks.filter(task => {\n        // Filtre par statut\n        if (this.statusFilter !== 'all' && task.status !== this.statusFilter) {\n          return false;\n        }\n        // Filtre par priorité\n        if (this.priorityFilter !== 'all' && task.priority !== this.priorityFilter) {\n          return false;\n        }\n        // Filtre par terme de recherche\n        if (this.searchTerm && !task.title.toLowerCase().includes(this.searchTerm.toLowerCase())) {\n          return false;\n        }\n        return true;\n      });\n    }\n    // Méthodes pour obtenir les tâches par statut\n    getTodoTasks() {\n      return this.tasks.filter(task => task.status === 'todo' && (this.priorityFilter === 'all' || task.priority === this.priorityFilter) && (!this.searchTerm || task.title.toLowerCase().includes(this.searchTerm.toLowerCase())));\n    }\n    getInProgressTasks() {\n      return this.tasks.filter(task => task.status === 'in-progress' && (this.priorityFilter === 'all' || task.priority === this.priorityFilter) && (!this.searchTerm || task.title.toLowerCase().includes(this.searchTerm.toLowerCase())));\n    }\n    getDoneTasks() {\n      return this.tasks.filter(task => task.status === 'done' && (this.priorityFilter === 'all' || task.priority === this.priorityFilter) && (!this.searchTerm || task.title.toLowerCase().includes(this.searchTerm.toLowerCase())));\n    }\n    // Méthodes pour compter les tâches par statut\n    getTodoTasksCount() {\n      return this.tasks.filter(task => task.status === 'todo').length;\n    }\n    getInProgressTasksCount() {\n      return this.tasks.filter(task => task.status === 'in-progress').length;\n    }\n    getDoneTasksCount() {\n      return this.tasks.filter(task => task.status === 'done').length;\n    }\n    navigateBack() {\n      this.router.navigate(['/liste']);\n    }\n    static {\n      this.ɵfac = function TaskListComponent_Factory(t) {\n        return new (t || TaskListComponent)(i0.ɵɵdirectiveInject(i1.TaskService), i0.ɵɵdirectiveInject(i2.EquipeService), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.NotificationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TaskListComponent,\n        selectors: [[\"app-task-list\"]],\n        decls: 23,\n        vars: 7,\n        consts: [[1, \"container-fluid\", \"py-5\", \"bg-light\"], [1, \"container\"], [1, \"row\", \"mb-5\"], [1, \"col-12\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"flex-wrap\"], [\"class\", \"display-4 fw-bold text-primary\", 4, \"ngIf\"], [1, \"text-muted\", \"lead\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-outline-secondary\", \"rounded-pill\", \"px-4\", \"py-2\", 3, \"click\"], [1, \"bi\", \"bi-arrow-left\", \"me-2\"], [1, \"btn\", \"btn-primary\", \"rounded-pill\", \"px-4\", \"py-2\", 3, \"click\"], [1, \"bi\", \"bi-plus-circle\", \"me-2\"], [1, \"my-4\"], [\"class\", \"row justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"row mb-5\", 4, \"ngIf\"], [\"class\", \"row mb-4\", 4, \"ngIf\"], [\"class\", \"row g-4\", 4, \"ngIf\"], [1, \"display-4\", \"fw-bold\", \"text-primary\"], [1, \"row\", \"justify-content-center\", \"my-5\"], [1, \"col-md-6\", \"text-center\"], [\"role\", \"status\", 1, \"spinner-grow\", \"text-primary\", \"mx-1\"], [1, \"visually-hidden\"], [\"role\", \"status\", 1, \"spinner-grow\", \"text-secondary\", \"mx-1\"], [1, \"mt-3\", \"text-muted\"], [1, \"col-md-8\"], [1, \"alert\", \"alert-danger\", \"shadow-sm\", \"border-0\", \"rounded-3\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"fs-3\", \"me-3\"], [1, \"flex-grow-1\"], [1, \"btn\", \"btn-danger\", \"rounded-pill\", \"ms-3\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-1\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"rounded-3\"], [1, \"card-header\", \"bg-primary\", \"text-white\", \"py-3\"], [1, \"mb-0\"], [1, \"card-body\", \"p-4\"], [3, \"ngSubmit\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"for\", \"taskTitle\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"taskTitle\", \"required\", \"\", \"name\", \"title\", \"placeholder\", \"Titre de la t\\u00E2che\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-md-3\"], [\"for\", \"taskPriority\", 1, \"form-label\"], [\"id\", \"taskPriority\", \"required\", \"\", \"name\", \"priority\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"low\"], [\"value\", \"medium\"], [\"value\", \"high\"], [\"for\", \"taskStatus\", 1, \"form-label\"], [\"id\", \"taskStatus\", \"required\", \"\", \"name\", \"status\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"todo\"], [\"value\", \"in-progress\"], [\"value\", \"done\"], [\"for\", \"taskAssignedTo\", 1, \"form-label\"], [\"id\", \"taskAssignedTo\", \"name\", \"assignedTo\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\"], [3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"taskDueDate\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"taskDueDate\", \"name\", \"dueDate\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"taskDescription\", 1, \"form-label\"], [\"id\", \"taskDescription\", \"rows\", \"3\", \"name\", \"description\", \"placeholder\", \"Description d\\u00E9taill\\u00E9e de la t\\u00E2che\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-12\", \"d-flex\", \"justify-content-end\", \"gap-2\", \"mt-4\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"rounded-pill\", \"px-4\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"rounded-pill\", \"px-4\"], [1, \"row\", \"mb-4\"], [1, \"card-body\", \"p-3\"], [1, \"col-md-4\"], [1, \"input-group\"], [1, \"input-group-text\", \"bg-white\", \"border-end-0\"], [1, \"bi\", \"bi-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher une t\\u00E2che...\", 1, \"form-control\", \"border-start-0\", 3, \"ngModel\", \"ngModelChange\"], [1, \"form-select\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"all\"], [1, \"col-md-8\", \"text-center\"], [1, \"p-5\", \"bg-white\", \"rounded-3\", \"shadow-sm\"], [1, \"bi\", \"bi-list-check\", \"fs-1\", \"text-muted\", \"mb-3\"], [1, \"mb-3\"], [1, \"text-muted\", \"mb-4\"], [1, \"row\", \"g-4\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"rounded-3\", \"h-100\"], [1, \"mb-0\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-list-task\", \"me-2\"], [1, \"badge\", \"bg-white\", \"text-primary\", \"rounded-pill\", \"ms-2\"], [\"cdkDropList\", \"\", \"id\", \"todo-list\", 1, \"task-list\", 3, \"cdkDropListData\", \"cdkDropListConnectedTo\", \"cdkDropListDropped\"], [\"todoList\", \"cdkDropList\"], [\"class\", \"task-card mb-3 p-3 rounded-3 shadow-sm\", \"cdkDrag\", \"\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"card-header\", \"bg-warning\", \"py-3\"], [1, \"bi\", \"bi-hourglass-split\", \"me-2\"], [1, \"badge\", \"bg-white\", \"text-warning\", \"rounded-pill\", \"ms-2\"], [\"cdkDropList\", \"\", \"id\", \"in-progress-list\", 1, \"task-list\", 3, \"cdkDropListData\", \"cdkDropListConnectedTo\", \"cdkDropListDropped\"], [\"inProgressList\", \"cdkDropList\"], [1, \"card-header\", \"bg-success\", \"text-white\", \"py-3\"], [1, \"bi\", \"bi-check2-all\", \"me-2\"], [1, \"badge\", \"bg-white\", \"text-success\", \"rounded-pill\", \"ms-2\"], [\"cdkDropList\", \"\", \"id\", \"done-list\", 1, \"task-list\", 3, \"cdkDropListData\", \"cdkDropListConnectedTo\", \"cdkDropListDropped\"], [\"doneList\", \"cdkDropList\"], [\"class\", \"task-card mb-3 p-3 rounded-3 shadow-sm completed-task\", \"cdkDrag\", \"\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"cdkDrag\", \"\", 1, \"task-card\", \"mb-3\", \"p-3\", \"rounded-3\", \"shadow-sm\", 3, \"ngClass\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\", \"mb-2\"], [1, \"mb-0\", \"text-truncate\"], [1, \"dropdown\"], [\"type\", \"button\", \"data-bs-toggle\", \"dropdown\", 1, \"btn\", \"btn-sm\", \"btn-link\", \"text-dark\", \"p-0\"], [1, \"bi\", \"bi-three-dots-vertical\"], [1, \"dropdown-menu\", \"dropdown-menu-end\"], [1, \"dropdown-item\", 3, \"click\"], [1, \"bi\", \"bi-pencil\", \"me-2\"], [1, \"bi\", \"bi-arrow-right\", \"me-2\"], [1, \"dropdown-divider\"], [1, \"dropdown-item\", \"text-danger\", 3, \"click\"], [1, \"bi\", \"bi-trash\", \"me-2\"], [1, \"small\", \"text-muted\", \"mb-2\", \"task-description\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"badge\", 3, \"ngClass\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"cdkDragHandle\", \"\", 1, \"task-drag-handle\"], [1, \"bi\", \"bi-grip-horizontal\"], [1, \"text-muted\"], [\"cdkDrag\", \"\", 1, \"task-card\", \"mb-3\", \"p-3\", \"rounded-3\", \"shadow-sm\", \"completed-task\", 3, \"ngClass\"]],\n        template: function TaskListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\");\n            i0.ɵɵtemplate(6, TaskListComponent_h1_6_Template, 2, 1, \"h1\", 5);\n            i0.ɵɵelementStart(7, \"p\", 6);\n            i0.ɵɵtext(8, \"G\\u00E9rez les t\\u00E2ches de votre \\u00E9quipe\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 7)(10, \"button\", 8);\n            i0.ɵɵlistener(\"click\", function TaskListComponent_Template_button_click_10_listener() {\n              return ctx.navigateBack();\n            });\n            i0.ɵɵelement(11, \"i\", 9);\n            i0.ɵɵtext(12, \" Retour \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"button\", 10);\n            i0.ɵɵlistener(\"click\", function TaskListComponent_Template_button_click_13_listener() {\n              return ctx.toggleTaskForm();\n            });\n            i0.ɵɵelement(14, \"i\", 11);\n            i0.ɵɵtext(15, \" Nouvelle t\\u00E2che \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelement(16, \"hr\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(17, TaskListComponent_div_17_Template, 13, 0, \"div\", 13);\n            i0.ɵɵtemplate(18, TaskListComponent_div_18_Template, 9, 1, \"div\", 13);\n            i0.ɵɵtemplate(19, TaskListComponent_div_19_Template, 53, 10, \"div\", 14);\n            i0.ɵɵtemplate(20, TaskListComponent_div_20_Template, 30, 3, \"div\", 15);\n            i0.ɵɵtemplate(21, TaskListComponent_div_21_Template, 11, 0, \"div\", 13);\n            i0.ɵɵtemplate(22, TaskListComponent_div_22_Template, 37, 21, \"div\", 16);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.team);\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showTaskForm || ctx.editingTask);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.tasks.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error && ctx.tasks.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.tasks.length > 0);\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i7.ɵNgNoValidate, i7.NgSelectOption, i7.ɵNgSelectMultipleOption, i7.DefaultValueAccessor, i7.SelectControlValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.RequiredValidator, i7.NgModel, i7.NgForm, i8.CdkDropList, i8.CdkDrag, i8.CdkDragHandle],\n        styles: [\".task-card[_ngcontent-%COMP%]{background-color:#fff;border-left:4px solid transparent;transition:all .2s ease;position:relative;cursor:move;margin-bottom:12px;border-radius:8px;box-shadow:0 2px 4px #0000000d}.task-description[_ngcontent-%COMP%]{max-height:3em;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;font-size:.9rem;color:#6c757d;margin-bottom:10px}.task-list[_ngcontent-%COMP%]{min-height:50px;max-height:500px;overflow-y:auto;padding:8px;border-radius:4px}.card-header.bg-primary[_ngcontent-%COMP%]{background:linear-gradient(45deg,#007bff,#6610f2)!important}.card-header.bg-warning[_ngcontent-%COMP%]{background:linear-gradient(45deg,#ffc107,#fd7e14)!important}.card-header.bg-success[_ngcontent-%COMP%]{background:linear-gradient(45deg,#28a745,#20c997)!important}.cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:8px;box-shadow:0 5px 15px #0003!important;opacity:.8}.cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:.3}.cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.task-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.task-drag-handle[_ngcontent-%COMP%]{position:absolute;bottom:5px;right:5px;color:#adb5bd;cursor:move;font-size:.8rem}.kanban-column[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;border-radius:8px;overflow:hidden;box-shadow:0 4px 6px #0000001a}.kanban-column-header[_ngcontent-%COMP%]{padding:15px;border-top-left-radius:8px;border-top-right-radius:8px}.kanban-column-content[_ngcontent-%COMP%]{flex-grow:1;background-color:#f8f9fa;padding:15px;border-bottom-left-radius:8px;border-bottom-right-radius:8px;min-height:300px;max-height:600px;overflow-y:auto}.task-card[_ngcontent-%COMP%]{background-color:#fff;border-left:4px solid transparent;transition:all .2s ease}.task-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 8px #0000001a!important}.task-card.priority-high[_ngcontent-%COMP%]{border-left-color:#dc3545}.task-card.priority-medium[_ngcontent-%COMP%]{border-left-color:#ffc107}.task-card.priority-low[_ngcontent-%COMP%]{border-left-color:#0dcaf0}.completed-task[_ngcontent-%COMP%]{opacity:.7}.task-description[_ngcontent-%COMP%]{max-height:3em;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.task-list[_ngcontent-%COMP%]{max-height:500px;overflow-y:auto}\"]\n      });\n    }\n  }\n  return TaskListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}