{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nexport const guarduserGuard = (route, state) => {\n  const authService = inject(AuthuserService);\n  const router = inject(Router);\n  if (authService.userLoggedIn() == true) {\n    return true;\n  } else {\n    router.navigate(['/loginuser'], {\n      queryParams: {\n        returnUrl: state.url\n      }\n    });\n    localStorage.removeItem('token');\n    return false;\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}