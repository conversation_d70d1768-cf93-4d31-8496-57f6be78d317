{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/projets.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"src/app/services/data.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"confirmDialog\"];\nfunction ListProjectComponent_div_83_div_1_div_34_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"div\", 88);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 61);\n    i0.ɵɵelement(4, \"path\", 89);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"span\", 90);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"a\", 91)(8, \"div\", 60);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 92);\n    i0.ɵɵelement(10, \"path\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"T\\u00E9l\\u00E9charger\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const file_r9 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getFileName(file_r9), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"href\", ctx_r8.getFileUrl(file_r9), i0.ɵɵsanitizeUrl)(\"download\", ctx_r8.getFileName(file_r9));\n  }\n}\nfunction ListProjectComponent_div_83_div_1_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 61);\n    i0.ɵɵelement(3, \"path\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h4\", 75);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 84);\n    i0.ɵɵtemplate(7, ListProjectComponent_div_83_div_1_div_34_div_7_Template, 13, 3, \"div\", 85);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const projet_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" Fichiers (\", projet_r5.fichiers.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", projet_r5.fichiers);\n  }\n}\nfunction ListProjectComponent_div_83_div_1_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 61);\n    i0.ɵɵelement(3, \"path\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h4\", 75);\n    i0.ɵɵtext(5, \" Fichiers \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 94)(7, \"div\", 95);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 96);\n    i0.ɵɵelement(9, \"path\", 83);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"p\", 64);\n    i0.ɵɵtext(11, \"Aucun fichier joint\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst _c1 = function (a1) {\n  return [\"/admin/projects/editProjet\", a1];\n};\nconst _c2 = function (a1) {\n  return [\"/admin/projects/details\", a1];\n};\nconst _c3 = function () {\n  return [\"/admin/projects/rendus\"];\n};\nconst _c4 = function (a0) {\n  return {\n    projetId: a0\n  };\n};\nfunction ListProjectComponent_div_83_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55)(2, \"div\", 56)(3, \"div\", 57)(4, \"h3\", 58);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 59)(7, \"div\", 60);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 61);\n    i0.ɵɵelement(9, \"path\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(10, \"span\", 62);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 60);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(13, \"svg\", 63);\n    i0.ɵɵelement(14, \"path\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(15, \"span\", 64);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 65)(19, \"a\", 66);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(20, \"svg\", 67);\n    i0.ɵɵelement(21, \"path\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(22, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function ListProjectComponent_div_83_div_1_Template_button_click_22_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const projet_r5 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(projet_r5._id && ctx_r11.openDeleteDialog(projet_r5._id));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(23, \"svg\", 67);\n    i0.ɵɵelement(24, \"path\", 70);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(25, \"div\", 71)(26, \"div\", 72)(27, \"div\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 61);\n    i0.ɵɵelement(29, \"path\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(30, \"h4\", 75);\n    i0.ɵɵtext(31, \" Description \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"p\", 76);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(34, ListProjectComponent_div_83_div_1_div_34_Template, 8, 2, \"div\", 77);\n    i0.ɵɵtemplate(35, ListProjectComponent_div_83_div_1_div_35_Template, 12, 0, \"div\", 77);\n    i0.ɵɵelementStart(36, \"div\", 78)(37, \"a\", 79)(38, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(39, \"svg\", 80);\n    i0.ɵɵelement(40, \"path\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(41, \"span\");\n    i0.ɵɵtext(42, \"Voir d\\u00E9tails\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(43, \"a\", 81)(44, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(45, \"svg\", 80);\n    i0.ɵɵelement(46, \"path\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(47, \"span\");\n    i0.ɵɵtext(48, \"Voir rendus\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const projet_r5 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", projet_r5.titre, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", projet_r5.groupe || \"Tous\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(17, 10, projet_r5.dateLimite, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(13, _c1, projet_r5._id));\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate1(\" \", projet_r5.description || \"Aucune description fournie\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", projet_r5.fichiers && projet_r5.fichiers.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !projet_r5.fichiers || projet_r5.fichiers.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(15, _c2, projet_r5._id));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(17, _c3))(\"queryParams\", i0.ɵɵpureFunction1(18, _c4, projet_r5._id));\n  }\n}\nfunction ListProjectComponent_div_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵtemplate(1, ListProjectComponent_div_83_div_1_Template, 49, 20, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.projets);\n  }\n}\nfunction ListProjectComponent_div_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98)(2, \"div\", 99);\n    i0.ɵɵelement(3, \"div\", 100);\n    i0.ɵɵelementStart(4, \"div\", 101);\n    i0.ɵɵelement(5, \"div\", 102);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 103);\n    i0.ɵɵtext(7, \"Chargement des projets...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 104);\n    i0.ɵɵtext(9, \"Veuillez patienter\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ListProjectComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98)(2, \"div\", 105);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 106);\n    i0.ɵɵelement(4, \"path\", 107);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"h3\", 108);\n    i0.ɵɵtext(6, \"Aucun projet disponible\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 109);\n    i0.ɵɵtext(8, \"Commencez par cr\\u00E9er votre premier projet pour organiser vos cours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"a\", 110);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(10, \"svg\", 111);\n    i0.ɵɵelement(11, \"path\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Cr\\u00E9er un projet \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ListProjectComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 112)(1, \"div\", 113)(2, \"div\", 114)(3, \"div\", 115);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 116);\n    i0.ɵɵelement(5, \"path\", 117);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"h2\", 108);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 10);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 44)(11, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function ListProjectComponent_div_86_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onDeleteCancel());\n    });\n    i0.ɵɵtext(12, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 119);\n    i0.ɵɵlistener(\"click\", function ListProjectComponent_div_86_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onDeleteConfirm());\n    });\n    i0.ɵɵtext(14, \" Supprimer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.dialogData.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.dialogData.message);\n  }\n}\nexport let ListProjectComponent = /*#__PURE__*/(() => {\n  class ListProjectComponent {\n    constructor(projetService, router, dialog, authService) {\n      this.projetService = projetService;\n      this.router = router;\n      this.dialog = dialog;\n      this.authService = authService;\n      this.projets = [];\n      this.isLoading = true;\n      this.isAdmin = false;\n      this.showDeleteDialog = false;\n      this.projectIdToDelete = null;\n      this.dialogData = {\n        title: 'Confirmer la suppression',\n        message: 'Êtes-vous sûr de vouloir supprimer ce projet?'\n      };\n    }\n    ngOnInit() {\n      console.log('Initialisation du composant ListProjectComponent');\n      this.loadProjets();\n      this.checkAdminStatus();\n    }\n    loadProjets() {\n      this.isLoading = true;\n      console.log('Chargement des projets...');\n      this.projetService.getProjets().subscribe({\n        next: projets => {\n          console.log('Projets reçus:', projets);\n          this.projets = projets;\n          this.isLoading = false;\n        },\n        error: err => {\n          console.error('Error loading projects', err);\n          this.isLoading = false;\n          // Afficher un message d'erreur à l'utilisateur\n          alert('Erreur lors du chargement des projets: ' + (err.error?.message || err.message || 'Erreur inconnue'));\n        }\n      });\n    }\n    // Alias pour loadProjets pour assurer la compatibilité avec les méthodes existantes\n    loadProjects() {\n      this.loadProjets();\n    }\n    checkAdminStatus() {\n      this.isAdmin = this.authService.isAdmin();\n      console.log('Statut admin:', this.isAdmin);\n    }\n    editProjet(projetId) {\n      if (!projetId) {\n        console.error('ID du projet non défini');\n        return;\n      }\n      console.log('Édition du projet:', projetId);\n      this.router.navigate(['/admin/projects/edit', projetId]);\n    }\n    viewProjetDetails(projetId) {\n      if (!projetId) {\n        console.error('ID du projet non défini');\n        return;\n      }\n      console.log('Affichage des détails du projet:', projetId);\n      this.router.navigate(['/admin/projects/detail', projetId]);\n    }\n    deleteProjet(projetId) {\n      if (!projetId) {\n        console.error('ID du projet non défini');\n        return;\n      }\n      if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n        console.log('Suppression du projet confirmée:', projetId);\n        this.projetService.deleteProjet(projetId).subscribe({\n          next: () => {\n            console.log('Projet supprimé avec succès');\n            alert('Projet supprimé avec succès');\n            this.loadProjets(); // Reload the projects after deletion\n          },\n\n          error: err => {\n            console.error('Error deleting project', err);\n            alert('Erreur lors de la suppression du projet: ' + (err.error?.message || err.message || 'Erreur inconnue'));\n          }\n        });\n      }\n    }\n    openDeleteDialog(id) {\n      if (!id) {\n        console.error('ID du projet non défini');\n        return;\n      }\n      console.log('Ouverture de la boîte de dialogue de confirmation pour la suppression du projet:', id);\n      this.showDeleteDialog = true; // Show confirmation dialog\n      this.projectIdToDelete = id; // Store the ID of the project to be deleted\n    }\n\n    onDeleteConfirm() {\n      if (this.projectIdToDelete) {\n        console.log('Suppression du projet confirmée:', this.projectIdToDelete);\n        this.projetService.deleteProjet(this.projectIdToDelete).subscribe({\n          next: () => {\n            console.log('Projet supprimé avec succès');\n            alert('Projet supprimé avec succès');\n            this.loadProjets(); // Reload the projects after deletion\n            this.showDeleteDialog = false; // Close the confirmation dialog\n          },\n\n          error: err => {\n            console.error('Error deleting project', err);\n            alert('Erreur lors de la suppression du projet: ' + (err.error?.message || err.message || 'Erreur inconnue'));\n            this.showDeleteDialog = false;\n          }\n        });\n      }\n    }\n    onDeleteCancel() {\n      console.log('Suppression du projet annulée');\n      this.showDeleteDialog = false; // Close the confirmation dialog without deleting\n    }\n\n    getFileUrl(filePath) {\n      if (!filePath) return '';\n      // Extraire uniquement le nom du fichier\n      let fileName = filePath;\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        fileName = parts[parts.length - 1];\n      }\n      // Utiliser la route spécifique pour le téléchargement\n      return `${environment.urlBackend}projets/telecharger/${fileName}`;\n    }\n    getFileName(filePath) {\n      if (!filePath) return 'Fichier';\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        return parts[parts.length - 1];\n      }\n      return filePath;\n    }\n    getActiveProjectsCount() {\n      if (!this.projets) return 0;\n      const now = new Date();\n      return this.projets.filter(projet => {\n        if (!projet.dateLimite) return true; // Considérer comme actif si pas de date limite\n        const dateLimit = new Date(projet.dateLimite);\n        return dateLimit >= now; // Actif si la date limite n'est pas dépassée\n      }).length;\n    }\n    getExpiredProjectsCount() {\n      if (!this.projets) return 0;\n      const now = new Date();\n      return this.projets.filter(projet => {\n        if (!projet.dateLimite) return false; // Pas expiré si pas de date limite\n        const dateLimit = new Date(projet.dateLimite);\n        return dateLimit < now; // Expiré si la date limite est dépassée\n      }).length;\n    }\n    getUniqueGroupsCount() {\n      if (!this.projets) return 0;\n      const uniqueGroups = new Set(this.projets.map(projet => projet.groupe).filter(groupe => groupe && groupe.trim() !== ''));\n      return uniqueGroups.size;\n    }\n    getCompletionPercentage() {\n      if (!this.projets || this.projets.length === 0) return 0;\n      const expiredCount = this.getExpiredProjectsCount();\n      const totalCount = this.projets.length;\n      return Math.round(expiredCount / totalCount * 100);\n    }\n    getProjectsByGroup() {\n      if (!this.projets) return {};\n      const groupCounts = {};\n      this.projets.forEach(projet => {\n        const groupe = projet.groupe || 'Non spécifié';\n        groupCounts[groupe] = (groupCounts[groupe] || 0) + 1;\n      });\n      return groupCounts;\n    }\n    getRecentProjects() {\n      if (!this.projets) return [];\n      // Comme nous n'avons pas de dateCreation, on retourne les projets récents basés sur la date limite\n      const now = new Date();\n      const oneMonthFromNow = new Date();\n      oneMonthFromNow.setMonth(oneMonthFromNow.getMonth() + 1);\n      return this.projets.filter(projet => {\n        if (!projet.dateLimite) return false;\n        const deadline = new Date(projet.dateLimite);\n        return deadline >= now && deadline <= oneMonthFromNow;\n      });\n    }\n    getUpcomingDeadlines() {\n      if (!this.projets) return [];\n      const now = new Date();\n      const oneWeekFromNow = new Date();\n      oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);\n      return this.projets.filter(projet => {\n        if (!projet.dateLimite) return false;\n        const deadline = new Date(projet.dateLimite);\n        return deadline >= now && deadline <= oneWeekFromNow;\n      }).sort((a, b) => {\n        const dateA = new Date(a.dateLimite);\n        const dateB = new Date(b.dateLimite);\n        return dateA.getTime() - dateB.getTime();\n      });\n    }\n    static {\n      this.ɵfac = function ListProjectComponent_Factory(t) {\n        return new (t || ListProjectComponent)(i0.ɵɵdirectiveInject(i1.ProjetService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MatDialog), i0.ɵɵdirectiveInject(i4.DataService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ListProjectComponent,\n        selectors: [[\"app-list-project\"]],\n        viewQuery: function ListProjectComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.confirmDialog = _t.first);\n          }\n        },\n        decls: 87,\n        vars: 13,\n        consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"mb-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-6\", \"lg:mb-0\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mb-4\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-3xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"grid\", \"grid-cols-2\", \"md:grid-cols-4\", \"gap-4\"], [1, \"bg-gradient-to-br\", \"from-primary/10\", \"to-primary/5\", \"dark:from-dark-accent-primary/20\", \"dark:to-dark-accent-primary/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-primary/20\", \"dark:border-dark-accent-primary/30\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-xs\", \"font-medium\", \"text-primary\", \"dark:text-dark-accent-primary\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-primary\", \"dark:text-dark-accent-primary\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\", \"mt-1\"], [1, \"bg-primary/20\", \"dark:bg-dark-accent-primary/30\", \"p-3\", \"rounded-xl\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-primary\", \"dark:text-dark-accent-primary\"], [1, \"bg-gradient-to-br\", \"from-success/10\", \"to-success/5\", \"dark:from-dark-accent-secondary/20\", \"dark:to-dark-accent-secondary/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-success/20\", \"dark:border-dark-accent-secondary/30\"], [1, \"text-xs\", \"font-medium\", \"text-success\", \"dark:text-dark-accent-secondary\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-success\", \"dark:text-dark-accent-secondary\"], [1, \"bg-success/20\", \"dark:bg-dark-accent-secondary/30\", \"p-3\", \"rounded-xl\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-success\", \"dark:text-dark-accent-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"bg-gradient-to-br\", \"from-warning/10\", \"to-warning/5\", \"dark:from-warning/20\", \"dark:to-warning/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-warning/20\", \"dark:border-warning/30\"], [1, \"text-xs\", \"font-medium\", \"text-warning\", \"dark:text-warning\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-warning\", \"dark:text-warning\"], [1, \"bg-warning/20\", \"dark:bg-warning/30\", \"p-3\", \"rounded-xl\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-warning\", \"dark:text-warning\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"bg-gradient-to-br\", \"from-info/10\", \"to-info/5\", \"dark:from-dark-accent-primary/20\", \"dark:to-dark-accent-primary/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-info/20\", \"dark:border-dark-accent-primary/30\"], [1, \"text-xs\", \"font-medium\", \"text-info\", \"dark:text-dark-accent-primary\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-info\", \"dark:text-dark-accent-primary\"], [1, \"bg-info/20\", \"dark:bg-dark-accent-primary/30\", \"p-3\", \"rounded-xl\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-info\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"mt-6\", \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\", \"p-4\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-2\"], [1, \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"w-full\", \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"rounded-full\", \"h-2\"], [1, \"bg-gradient-to-r\", \"from-success\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"h-2\", \"rounded-full\", \"transition-all\", \"duration-500\"], [1, \"flex\", \"justify-between\", \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\", \"mt-2\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-3\"], [\"routerLink\", \"/admin/projects/new\", 1, \"group\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 6v6m0 0v6m0-6h6m-6 0H6\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\", 4, \"ngIf\"], [\"class\", \"text-center py-16\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-3\", \"gap-6\", \"mb-8\"], [\"class\", \"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group overflow-hidden\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"hover:shadow-xl\", \"transition-all\", \"duration-300\", \"group\", \"overflow-hidden\"], [1, \"relative\", \"p-6\", \"bg-gradient-to-r\", \"from-primary/5\", \"to-secondary/5\", \"dark:from-dark-accent-primary/10\", \"dark:to-dark-accent-secondary/10\"], [1, \"flex\", \"items-start\", \"justify-between\"], [1, \"flex-1\", \"pr-4\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-2\", \"line-clamp-2\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [1, \"text-sm\", \"font-medium\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-warning\", \"dark:text-warning\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"space-x-2\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-all\", \"duration-200\"], [1, \"p-2\", \"bg-white/80\", \"dark:bg-dark-bg-tertiary/80\", \"backdrop-blur-sm\", \"rounded-lg\", \"text-primary\", \"dark:text-dark-accent-primary\", \"hover:bg-primary\", \"hover:text-white\", \"dark:hover:bg-dark-accent-primary\", \"transition-all\", \"duration-200\", \"shadow-lg\", 3, \"routerLink\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"p-2\", \"bg-white/80\", \"dark:bg-dark-bg-tertiary/80\", \"backdrop-blur-sm\", \"rounded-lg\", \"text-danger\", \"dark:text-danger-dark\", \"hover:bg-danger\", \"hover:text-white\", \"dark:hover:bg-danger-dark\", \"transition-all\", \"duration-200\", \"shadow-lg\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"p-6\"], [1, \"mb-6\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mb-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"uppercase\", \"tracking-wider\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"text-sm\", \"line-clamp-3\", \"leading-relaxed\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-3\", \"pt-4\", \"border-t\", \"border-gray-100\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex-1\", \"group\", \"px-4\", \"py-3\", \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"hover:bg-primary\", \"hover:text-white\", \"dark:hover:bg-dark-accent-primary\", \"rounded-xl\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"routerLink\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"flex-1\", \"group\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"routerLink\", \"queryParams\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"space-y-2\", \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\", \"p-4\"], [\"class\", \"flex items-center justify-between p-2 bg-white dark:bg-dark-bg-secondary rounded-lg shadow-sm\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-2\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"rounded-lg\", \"shadow-sm\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"flex-1\", \"min-w-0\"], [1, \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"p-1.5\", \"rounded-lg\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"], [1, \"text-sm\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"truncate\"], [1, \"ml-2\", \"px-3\", \"py-1.5\", \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"text-primary\", \"dark:text-dark-accent-primary\", \"hover:bg-primary\", \"hover:text-white\", \"dark:hover:bg-dark-accent-primary\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"text-xs\", \"font-medium\", 3, \"href\", \"download\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\", \"p-4\", \"text-center\"], [1, \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"p-3\", \"rounded-lg\", \"inline-flex\", \"items-center\", \"justify-center\", \"mb-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-400\", \"dark:text-dark-text-secondary\"], [1, \"text-center\", \"py-16\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-12\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"max-w-md\", \"mx-auto\"], [1, \"relative\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-primary/20\", \"border-t-primary\", \"dark:border-dark-accent-primary/20\", \"dark:border-t-dark-accent-primary\", \"mx-auto\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\"], [1, \"w-8\", \"h-8\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-full\", \"animate-pulse\"], [1, \"mt-6\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"font-medium\"], [1, \"mt-2\", \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"bg-gradient-to-br\", \"from-primary/10\", \"to-secondary/10\", \"dark:from-dark-accent-primary/20\", \"dark:to-dark-accent-secondary/20\", \"rounded-2xl\", \"p-6\", \"mb-6\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-16\", \"w-16\", \"mx-auto\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-2\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"mb-6\"], [\"routerLink\", \"/admin/projects/new\", 1, \"inline-flex\", \"items-center\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"mr-2\"], [1, \"fixed\", \"inset-0\", \"z-50\", \"flex\", \"items-center\", \"justify-center\", \"bg-black/50\", \"backdrop-blur-sm\"], [1, \"bg-white/95\", \"dark:bg-dark-bg-secondary/95\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-2xl\", \"w-full\", \"max-w-md\", \"p-8\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"mx-4\"], [1, \"text-center\", \"mb-6\"], [1, \"bg-danger/10\", \"dark:bg-danger-dark/20\", \"rounded-full\", \"p-4\", \"w-16\", \"h-16\", \"mx-auto\", \"mb-4\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-danger\", \"dark:text-danger-dark\", \"mx-auto\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"], [1, \"flex-1\", \"px-6\", \"py-3\", \"bg-gray-100\", \"dark:bg-dark-bg-tertiary\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"hover:bg-gray-200\", \"dark:hover:bg-dark-bg-tertiary/80\", \"rounded-xl\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [1, \"flex-1\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-danger\", \"to-danger-dark\", \"dark:from-danger-dark\", \"dark:to-danger\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"]],\n        template: function ListProjectComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(7, \"svg\", 7);\n            i0.ɵɵelement(8, \"path\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(9, \"div\")(10, \"h1\", 9);\n            i0.ɵɵtext(11, \" Gestion des Projets \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"p\", 10);\n            i0.ɵɵtext(13, \" Cr\\u00E9ez, g\\u00E9rez et suivez vos projets acad\\u00E9miques \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\", 12)(16, \"div\", 13)(17, \"div\")(18, \"p\", 14);\n            i0.ɵɵtext(19, \"Total\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"p\", 15);\n            i0.ɵɵtext(21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"p\", 16);\n            i0.ɵɵtext(23, \"Projets cr\\u00E9\\u00E9s\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"div\", 17);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(25, \"svg\", 18);\n            i0.ɵɵelement(26, \"path\", 8);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(27, \"div\", 19)(28, \"div\", 13)(29, \"div\")(30, \"p\", 20);\n            i0.ɵɵtext(31, \"Actifs\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"p\", 21);\n            i0.ɵɵtext(33);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"p\", 16);\n            i0.ɵɵtext(35, \"En cours\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(36, \"div\", 22);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(37, \"svg\", 23);\n            i0.ɵɵelement(38, \"path\", 24);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(39, \"div\", 25)(40, \"div\", 13)(41, \"div\")(42, \"p\", 26);\n            i0.ɵɵtext(43, \"Expir\\u00E9s\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"p\", 27);\n            i0.ɵɵtext(45);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"p\", 16);\n            i0.ɵɵtext(47, \"Date d\\u00E9pass\\u00E9e\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(48, \"div\", 28);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(49, \"svg\", 29);\n            i0.ɵɵelement(50, \"path\", 30);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(51, \"div\", 31)(52, \"div\", 13)(53, \"div\")(54, \"p\", 32);\n            i0.ɵɵtext(55, \"Groupes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"p\", 33);\n            i0.ɵɵtext(57);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"p\", 16);\n            i0.ɵɵtext(59, \"Diff\\u00E9rents\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(60, \"div\", 34);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(61, \"svg\", 35);\n            i0.ɵɵelement(62, \"path\", 36);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(63, \"div\", 37)(64, \"div\", 38)(65, \"h4\", 39);\n            i0.ɵɵtext(66, \"Progression des projets\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"span\", 40);\n            i0.ɵɵtext(68);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(69, \"div\", 41);\n            i0.ɵɵelement(70, \"div\", 42);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"div\", 43)(72, \"span\");\n            i0.ɵɵtext(73);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"span\");\n            i0.ɵɵtext(75);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(76, \"div\", 44)(77, \"a\", 45)(78, \"div\", 46);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(79, \"svg\", 47);\n            i0.ɵɵelement(80, \"path\", 48);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(81, \"span\");\n            i0.ɵɵtext(82, \"Nouveau projet\");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵtemplate(83, ListProjectComponent_div_83_Template, 2, 1, \"div\", 49);\n            i0.ɵɵtemplate(84, ListProjectComponent_div_84_Template, 10, 0, \"div\", 50);\n            i0.ɵɵtemplate(85, ListProjectComponent_div_85_Template, 13, 0, \"div\", 50);\n            i0.ɵɵtemplate(86, ListProjectComponent_div_86_Template, 15, 2, \"div\", 51);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(21);\n            i0.ɵɵtextInterpolate(ctx.projets.length || 0);\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate(ctx.getActiveProjectsCount());\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate(ctx.getExpiredProjectsCount());\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate(ctx.getUniqueGroupsCount());\n            i0.ɵɵadvance(11);\n            i0.ɵɵtextInterpolate1(\"\", ctx.getCompletionPercentage(), \"% compl\\u00E9t\\u00E9s\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵstyleProp(\"width\", ctx.getCompletionPercentage(), \"%\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\"\", ctx.getActiveProjectsCount(), \" actifs\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\"\", ctx.getExpiredProjectsCount(), \" expir\\u00E9s\");\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.projets && ctx.projets.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && (!ctx.projets || ctx.projets.length === 0));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showDeleteDialog);\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i2.RouterLink, i5.DatePipe],\n        styles: [\"@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_scaleIn{0%{opacity:0;transform:scale(.9)}to{opacity:1;transform:scale(1)}}.glass-card[_ngcontent-%COMP%]{backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2)}.dark[_ngcontent-%COMP%]   .glass-card[_ngcontent-%COMP%]{border:1px solid rgba(255,255,255,.1)}.project-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out;transition:all .3s cubic-bezier(.4,0,.2,1)}.project-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 25px 50px #00000026}.dark[_ngcontent-%COMP%]   .project-card[_ngcontent-%COMP%]:hover{box-shadow:0 25px 50px #0006}.btn-modern[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1)}.btn-modern[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.btn-modern[_ngcontent-%COMP%]:hover:before{left:100%}.badge-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad 0%,#7826b5 100%);transition:all .3s ease}.dark[_ngcontent-%COMP%]   .badge-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00f7ff 0%,#9d4edd 100%)}.icon-hover[_ngcontent-%COMP%]{transition:transform .2s ease}.icon-hover[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(5deg)}.tooltip[_ngcontent-%COMP%]{position:relative}.tooltip[_ngcontent-%COMP%]:after{content:attr(data-tooltip);position:absolute;bottom:100%;left:50%;transform:translate(-50%);background:rgba(0,0,0,.8);color:#fff;padding:.5rem;border-radius:.375rem;font-size:.75rem;white-space:nowrap;opacity:0;pointer-events:none;transition:opacity .3s;z-index:1000}.tooltip[_ngcontent-%COMP%]:hover:after{opacity:1}.loading-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.5}}.empty-state[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .8s ease-out}.focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #4f5fad;outline-offset:2px}.dark[_ngcontent-%COMP%]   .focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #00f7ff}@media (max-width: 768px){.project-card[_ngcontent-%COMP%]{margin-bottom:1rem}.btn-modern[_ngcontent-%COMP%]{width:100%;justify-content:center}}.floating-actions[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}.floating-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{transition:all .2s ease}.floating-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{transform:scale(1.1)}.file-item[_ngcontent-%COMP%]{transition:all .2s ease}.file-item[_ngcontent-%COMP%]:hover{transform:translate(4px);background-color:#4f5fad0d}.dark[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover{background-color:#00f7ff0d}.dialog-overlay[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-out}.dialog-content[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_scaleIn .3s ease-out}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}.bg-gradient-modern[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%)}.dark[_ngcontent-%COMP%]   .bg-gradient-modern[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00f7ff 0%,#9d4edd 100%)}.card-hover-effect[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}.card-hover-effect[_ngcontent-%COMP%]:hover{transform:translateY(-4px) scale(1.02);box-shadow:0 20px 40px #0000001a}.dark[_ngcontent-%COMP%]   .card-hover-effect[_ngcontent-%COMP%]:hover{box-shadow:0 20px 40px #0000004d}\"]\n      });\n    }\n  }\n  return ListProjectComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}