{"ast": null, "code": "export function isNonNullObject(obj) {\n  return obj !== null && typeof obj === \"object\";\n}\nexport function isPlainObject(obj) {\n  return obj !== null && typeof obj === \"object\" && (Object.getPrototypeOf(obj) === Object.prototype || Object.getPrototypeOf(obj) === null);\n}\n//# sourceMappingURL=objects.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}