{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@auth0/angular-jwt\";\nexport let ReunionService = /*#__PURE__*/(() => {\n  class ReunionService {\n    constructor(http, jwtHelper) {\n      this.http = http;\n      this.jwtHelper = jwtHelper;\n    }\n    getUserHeaders() {\n      const token = localStorage.getItem('token');\n      if (!token || this.jwtHelper.isTokenExpired(token)) {\n        throw new Error('Token invalide ou expiré');\n      }\n      return new HttpHeaders({\n        Authorization: `Bearer ${token || ''}`,\n        'Content-Type': 'application/json'\n      });\n    }\n    getAllReunions() {\n      return this.http.get(`${environment.urlBackend}reunions/getall`);\n    }\n    getReunionById(id) {\n      return this.http.get(`${environment.urlBackend}reunions/getone/${id}`);\n    }\n    createReunion(reunion) {\n      return this.http.post(`${environment.urlBackend}reunions/add`, reunion, {\n        headers: this.getUserHeaders()\n      });\n    }\n    updateReunion(id, reunion) {\n      return this.http.put(`${environment.urlBackend}reunions/update/${id}`, reunion, {\n        headers: this.getUserHeaders()\n      });\n    }\n    deleteReunion(id) {\n      return this.http.delete(`${environment.urlBackend}reunions/delete/${id}`, {\n        headers: this.getUserHeaders()\n      });\n    }\n    getReunionsByPlanning(planningId) {\n      return this.http.get(`${environment.urlBackend}reunions/planning/${planningId}`);\n    }\n    getProchainesReunions(userId) {\n      return this.http.get(`${environment.urlBackend}reunions/user/${userId}`);\n    }\n    static {\n      this.ɵfac = function ReunionService_Factory(t) {\n        return new (t || ReunionService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.JwtHelperService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ReunionService,\n        factory: ReunionService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ReunionService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}