{"ast": null, "code": "import { <PERSON>e } from \"@wry/trie\";\nimport { canUseWeakMap, canUseWeakSet } from \"../common/canUse.js\";\nimport { checkDocument } from \"./getFromAST.js\";\nimport { invariant } from \"../globals/index.js\";\nimport { WeakCache } from \"@wry/caches\";\nimport { wrap } from \"optimism\";\nimport { cacheSizes } from \"../caching/index.js\";\nfunction identity(document) {\n  return document;\n}\nvar DocumentTransform = /** @class */function () {\n  function DocumentTransform(transform, options) {\n    if (options === void 0) {\n      options = Object.create(null);\n    }\n    this.resultCache = canUseWeakSet ? new WeakSet() : new Set();\n    this.transform = transform;\n    if (options.getCacheKey) {\n      // Override default `getCacheKey` function, which returns [document].\n      this.getCacheKey = options.getCacheKey;\n    }\n    this.cached = options.cache !== false;\n    this.resetCache();\n  }\n  // This default implementation of getCacheKey can be overridden by providing\n  // options.getCacheKey to the DocumentTransform constructor. In general, a\n  // getCacheKey function may either return an array of keys (often including\n  // the document) to be used as a cache key, or undefined to indicate the\n  // transform for this document should not be cached.\n  DocumentTransform.prototype.getCacheKey = function (document) {\n    return [document];\n  };\n  DocumentTransform.identity = function () {\n    // No need to cache this transform since it just returns the document\n    // unchanged. This should save a bit of memory that would otherwise be\n    // needed to populate the `documentCache` of this transform.\n    return new DocumentTransform(identity, {\n      cache: false\n    });\n  };\n  DocumentTransform.split = function (predicate, left, right) {\n    if (right === void 0) {\n      right = DocumentTransform.identity();\n    }\n    return Object.assign(new DocumentTransform(function (document) {\n      var documentTransform = predicate(document) ? left : right;\n      return documentTransform.transformDocument(document);\n    },\n    // Reasonably assume both `left` and `right` transforms handle their own caching\n    {\n      cache: false\n    }), {\n      left: left,\n      right: right\n    });\n  };\n  /**\n   * Resets the internal cache of this transform, if it has one.\n   */\n  DocumentTransform.prototype.resetCache = function () {\n    var _this = this;\n    if (this.cached) {\n      var stableCacheKeys_1 = new Trie(canUseWeakMap);\n      this.performWork = wrap(DocumentTransform.prototype.performWork.bind(this), {\n        makeCacheKey: function (document) {\n          var cacheKeys = _this.getCacheKey(document);\n          if (cacheKeys) {\n            invariant(Array.isArray(cacheKeys), 77);\n            return stableCacheKeys_1.lookupArray(cacheKeys);\n          }\n        },\n        max: cacheSizes[\"documentTransform.cache\"],\n        cache: WeakCache\n      });\n    }\n  };\n  DocumentTransform.prototype.performWork = function (document) {\n    checkDocument(document);\n    return this.transform(document);\n  };\n  DocumentTransform.prototype.transformDocument = function (document) {\n    // If a user passes an already transformed result back to this function,\n    // immediately return it.\n    if (this.resultCache.has(document)) {\n      return document;\n    }\n    var transformedDocument = this.performWork(document);\n    this.resultCache.add(transformedDocument);\n    return transformedDocument;\n  };\n  DocumentTransform.prototype.concat = function (otherTransform) {\n    var _this = this;\n    return Object.assign(new DocumentTransform(function (document) {\n      return otherTransform.transformDocument(_this.transformDocument(document));\n    },\n    // Reasonably assume both transforms handle their own caching\n    {\n      cache: false\n    }), {\n      left: this,\n      right: otherTransform\n    });\n  };\n  return DocumentTransform;\n}();\nexport { DocumentTransform };\n//# sourceMappingURL=DocumentTransform.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}