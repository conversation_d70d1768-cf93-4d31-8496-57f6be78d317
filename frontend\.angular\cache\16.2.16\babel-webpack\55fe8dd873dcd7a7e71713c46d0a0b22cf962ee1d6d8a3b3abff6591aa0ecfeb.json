{"ast": null, "code": "import { addWeeks } from \"./addWeeks.js\";\nimport { millisecondsInWeek } from \"./constants.js\";\nimport { startOfISOWeekYear } from \"./startOfISOWeekYear.js\";\n\n/**\n * The {@link getISOWeeksInYear} function options.\n */\n\n/**\n * @name getISOWeeksInYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of weeks in an ISO week-numbering year of the given date.\n *\n * @description\n * Get the number of weeks in an ISO week-numbering year of the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The number of ISO weeks in a year\n *\n * @example\n * // How many weeks are in ISO week-numbering year 2015?\n * const result = getISOWeeksInYear(new Date(2015, 1, 11))\n * //=> 53\n */\nexport function getISOWeeksInYear(date, options) {\n  const thisYear = startOfISOWeekYear(date, options);\n  const nextYear = startOfISOWeekYear(addWeeks(thisYear, 60));\n  const diff = +nextYear - +thisYear;\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round(diff / millisecondsInWeek);\n}\n\n// Fallback for modularized imports:\nexport default getISOWeeksInYear;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}