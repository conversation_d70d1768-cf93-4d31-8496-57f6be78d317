{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { isNode, QueryDocumentKeys } from './ast.mjs';\nimport { Kind } from './kinds.mjs';\n/**\n * A visitor is provided to visit, it contains the collection of\n * relevant functions to be called during the visitor's traversal.\n */\n\nexport const BREAK = Object.freeze({});\n/**\n * visit() will walk through an AST using a depth-first traversal, calling\n * the visitor's enter function at each node in the traversal, and calling the\n * leave function after visiting that node and all of its child nodes.\n *\n * By returning different values from the enter and leave functions, the\n * behavior of the visitor can be altered, including skipping over a sub-tree of\n * the AST (by returning false), editing the AST by returning a value or null\n * to remove the value, or to stop the whole traversal by returning BREAK.\n *\n * When using visit() to edit an AST, the original AST will not be modified, and\n * a new version of the AST with the changes applied will be returned from the\n * visit function.\n *\n * ```ts\n * const editedAST = visit(ast, {\n *   enter(node, key, parent, path, ancestors) {\n *     // @return\n *     //   undefined: no action\n *     //   false: skip visiting this node\n *     //   visitor.BREAK: stop visiting altogether\n *     //   null: delete this node\n *     //   any value: replace this node with the returned value\n *   },\n *   leave(node, key, parent, path, ancestors) {\n *     // @return\n *     //   undefined: no action\n *     //   false: no action\n *     //   visitor.BREAK: stop visiting altogether\n *     //   null: delete this node\n *     //   any value: replace this node with the returned value\n *   }\n * });\n * ```\n *\n * Alternatively to providing enter() and leave() functions, a visitor can\n * instead provide functions named the same as the kinds of AST nodes, or\n * enter/leave visitors at a named key, leading to three permutations of the\n * visitor API:\n *\n * 1) Named visitors triggered when entering a node of a specific kind.\n *\n * ```ts\n * visit(ast, {\n *   Kind(node) {\n *     // enter the \"Kind\" node\n *   }\n * })\n * ```\n *\n * 2) Named visitors that trigger upon entering and leaving a node of a specific kind.\n *\n * ```ts\n * visit(ast, {\n *   Kind: {\n *     enter(node) {\n *       // enter the \"Kind\" node\n *     }\n *     leave(node) {\n *       // leave the \"Kind\" node\n *     }\n *   }\n * })\n * ```\n *\n * 3) Generic visitors that trigger upon entering and leaving any node.\n *\n * ```ts\n * visit(ast, {\n *   enter(node) {\n *     // enter any node\n *   },\n *   leave(node) {\n *     // leave any node\n *   }\n * })\n * ```\n */\n\nexport function visit(root, visitor, visitorKeys = QueryDocumentKeys) {\n  const enterLeaveMap = new Map();\n  for (const kind of Object.values(Kind)) {\n    enterLeaveMap.set(kind, getEnterLeaveForKind(visitor, kind));\n  }\n  /* eslint-disable no-undef-init */\n\n  let stack = undefined;\n  let inArray = Array.isArray(root);\n  let keys = [root];\n  let index = -1;\n  let edits = [];\n  let node = root;\n  let key = undefined;\n  let parent = undefined;\n  const path = [];\n  const ancestors = [];\n  /* eslint-enable no-undef-init */\n\n  do {\n    index++;\n    const isLeaving = index === keys.length;\n    const isEdited = isLeaving && edits.length !== 0;\n    if (isLeaving) {\n      key = ancestors.length === 0 ? undefined : path[path.length - 1];\n      node = parent;\n      parent = ancestors.pop();\n      if (isEdited) {\n        if (inArray) {\n          node = node.slice();\n          let editOffset = 0;\n          for (const [editKey, editValue] of edits) {\n            const arrayKey = editKey - editOffset;\n            if (editValue === null) {\n              node.splice(arrayKey, 1);\n              editOffset++;\n            } else {\n              node[arrayKey] = editValue;\n            }\n          }\n        } else {\n          node = Object.defineProperties({}, Object.getOwnPropertyDescriptors(node));\n          for (const [editKey, editValue] of edits) {\n            node[editKey] = editValue;\n          }\n        }\n      }\n      index = stack.index;\n      keys = stack.keys;\n      edits = stack.edits;\n      inArray = stack.inArray;\n      stack = stack.prev;\n    } else if (parent) {\n      key = inArray ? index : keys[index];\n      node = parent[key];\n      if (node === null || node === undefined) {\n        continue;\n      }\n      path.push(key);\n    }\n    let result;\n    if (!Array.isArray(node)) {\n      var _enterLeaveMap$get, _enterLeaveMap$get2;\n      isNode(node) || devAssert(false, `Invalid AST Node: ${inspect(node)}.`);\n      const visitFn = isLeaving ? (_enterLeaveMap$get = enterLeaveMap.get(node.kind)) === null || _enterLeaveMap$get === void 0 ? void 0 : _enterLeaveMap$get.leave : (_enterLeaveMap$get2 = enterLeaveMap.get(node.kind)) === null || _enterLeaveMap$get2 === void 0 ? void 0 : _enterLeaveMap$get2.enter;\n      result = visitFn === null || visitFn === void 0 ? void 0 : visitFn.call(visitor, node, key, parent, path, ancestors);\n      if (result === BREAK) {\n        break;\n      }\n      if (result === false) {\n        if (!isLeaving) {\n          path.pop();\n          continue;\n        }\n      } else if (result !== undefined) {\n        edits.push([key, result]);\n        if (!isLeaving) {\n          if (isNode(result)) {\n            node = result;\n          } else {\n            path.pop();\n            continue;\n          }\n        }\n      }\n    }\n    if (result === undefined && isEdited) {\n      edits.push([key, node]);\n    }\n    if (isLeaving) {\n      path.pop();\n    } else {\n      var _node$kind;\n      stack = {\n        inArray,\n        index,\n        keys,\n        edits,\n        prev: stack\n      };\n      inArray = Array.isArray(node);\n      keys = inArray ? node : (_node$kind = visitorKeys[node.kind]) !== null && _node$kind !== void 0 ? _node$kind : [];\n      index = -1;\n      edits = [];\n      if (parent) {\n        ancestors.push(parent);\n      }\n      parent = node;\n    }\n  } while (stack !== undefined);\n  if (edits.length !== 0) {\n    // New root\n    return edits[edits.length - 1][1];\n  }\n  return root;\n}\n/**\n * Creates a new visitor instance which delegates to many visitors to run in\n * parallel. Each visitor will be visited for each node before moving on.\n *\n * If a prior visitor edits a node, no following visitors will see that node.\n */\n\nexport function visitInParallel(visitors) {\n  const skipping = new Array(visitors.length).fill(null);\n  const mergedVisitor = Object.create(null);\n  for (const kind of Object.values(Kind)) {\n    let hasVisitor = false;\n    const enterList = new Array(visitors.length).fill(undefined);\n    const leaveList = new Array(visitors.length).fill(undefined);\n    for (let i = 0; i < visitors.length; ++i) {\n      const {\n        enter,\n        leave\n      } = getEnterLeaveForKind(visitors[i], kind);\n      hasVisitor || (hasVisitor = enter != null || leave != null);\n      enterList[i] = enter;\n      leaveList[i] = leave;\n    }\n    if (!hasVisitor) {\n      continue;\n    }\n    const mergedEnterLeave = {\n      enter(...args) {\n        const node = args[0];\n        for (let i = 0; i < visitors.length; i++) {\n          if (skipping[i] === null) {\n            var _enterList$i;\n            const result = (_enterList$i = enterList[i]) === null || _enterList$i === void 0 ? void 0 : _enterList$i.apply(visitors[i], args);\n            if (result === false) {\n              skipping[i] = node;\n            } else if (result === BREAK) {\n              skipping[i] = BREAK;\n            } else if (result !== undefined) {\n              return result;\n            }\n          }\n        }\n      },\n      leave(...args) {\n        const node = args[0];\n        for (let i = 0; i < visitors.length; i++) {\n          if (skipping[i] === null) {\n            var _leaveList$i;\n            const result = (_leaveList$i = leaveList[i]) === null || _leaveList$i === void 0 ? void 0 : _leaveList$i.apply(visitors[i], args);\n            if (result === BREAK) {\n              skipping[i] = BREAK;\n            } else if (result !== undefined && result !== false) {\n              return result;\n            }\n          } else if (skipping[i] === node) {\n            skipping[i] = null;\n          }\n        }\n      }\n    };\n    mergedVisitor[kind] = mergedEnterLeave;\n  }\n  return mergedVisitor;\n}\n/**\n * Given a visitor instance and a node kind, return EnterLeaveVisitor for that kind.\n */\n\nexport function getEnterLeaveForKind(visitor, kind) {\n  const kindVisitor = visitor[kind];\n  if (typeof kindVisitor === 'object') {\n    // { Kind: { enter() {}, leave() {} } }\n    return kindVisitor;\n  } else if (typeof kindVisitor === 'function') {\n    // { Kind() {} }\n    return {\n      enter: kindVisitor,\n      leave: undefined\n    };\n  } // { enter() {}, leave() {} }\n\n  return {\n    enter: visitor.enter,\n    leave: visitor.leave\n  };\n}\n/**\n * Given a visitor instance, if it is leaving or not, and a node kind, return\n * the function the visitor runtime should call.\n *\n * @deprecated Please use `getEnterLeaveForKind` instead. Will be removed in v17\n */\n\n/* c8 ignore next 8 */\n\nexport function getVisitFn(visitor, kind, isLeaving) {\n  const {\n    enter,\n    leave\n  } = getEnterLeaveForKind(visitor, kind);\n  return isLeaving ? leave : enter;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}