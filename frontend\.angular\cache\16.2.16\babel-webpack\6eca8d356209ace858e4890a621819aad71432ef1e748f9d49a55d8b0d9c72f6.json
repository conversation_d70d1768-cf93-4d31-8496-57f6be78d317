{"ast": null, "code": "import { __rest } from \"tslib\";\nimport equal from \"@wry/equality\";\nimport { createFragmentMap, getFragmentDefinitions, getFragmentFromSelection, getMainDefinition, isField, resultKeyNameFromField, shouldInclude } from \"../utilities/index.js\";\n// Returns true if aResult and bResult are deeply equal according to the fields\n// selected by the given query, ignoring any fields marked as @nonreactive.\nexport function equalByQuery(query, _a, _b, variables) {\n  var aData = _a.data,\n    aRest = __rest(_a, [\"data\"]);\n  var bData = _b.data,\n    bRest = __rest(_b, [\"data\"]);\n  return equal(aRest, bRest) && equalBySelectionSet(getMainDefinition(query).selectionSet, aData, bData, {\n    fragmentMap: createFragmentMap(getFragmentDefinitions(query)),\n    variables: variables\n  });\n}\nfunction equalBySelectionSet(selectionSet, aResult, bResult, context) {\n  if (aResult === bResult) {\n    return true;\n  }\n  var seenSelections = new Set();\n  // Returning true from this Array.prototype.every callback function skips the\n  // current field/subtree. Returning false aborts the entire traversal\n  // immediately, causing equalBySelectionSet to return false.\n  return selectionSet.selections.every(function (selection) {\n    // Avoid re-processing the same selection at the same level of recursion, in\n    // case the same field gets included via multiple indirect fragment spreads.\n    if (seenSelections.has(selection)) return true;\n    seenSelections.add(selection);\n    // Ignore @skip(if: true) and @include(if: false) fields.\n    if (!shouldInclude(selection, context.variables)) return true;\n    // If the field or (named) fragment spread has a @nonreactive directive on\n    // it, we don't care if it's different, so we pretend it's the same.\n    if (selectionHasNonreactiveDirective(selection)) return true;\n    if (isField(selection)) {\n      var resultKey = resultKeyNameFromField(selection);\n      var aResultChild = aResult && aResult[resultKey];\n      var bResultChild = bResult && bResult[resultKey];\n      var childSelectionSet = selection.selectionSet;\n      if (!childSelectionSet) {\n        // These are scalar values, so we can compare them with deep equal\n        // without redoing the main recursive work.\n        return equal(aResultChild, bResultChild);\n      }\n      var aChildIsArray = Array.isArray(aResultChild);\n      var bChildIsArray = Array.isArray(bResultChild);\n      if (aChildIsArray !== bChildIsArray) return false;\n      if (aChildIsArray && bChildIsArray) {\n        var length_1 = aResultChild.length;\n        if (bResultChild.length !== length_1) {\n          return false;\n        }\n        for (var i = 0; i < length_1; ++i) {\n          if (!equalBySelectionSet(childSelectionSet, aResultChild[i], bResultChild[i], context)) {\n            return false;\n          }\n        }\n        return true;\n      }\n      return equalBySelectionSet(childSelectionSet, aResultChild, bResultChild, context);\n    } else {\n      var fragment = getFragmentFromSelection(selection, context.fragmentMap);\n      if (fragment) {\n        // The fragment might === selection if it's an inline fragment, but\n        // could be !== if it's a named fragment ...spread.\n        if (selectionHasNonreactiveDirective(fragment)) return true;\n        return equalBySelectionSet(fragment.selectionSet,\n        // Notice that we reuse the same aResult and bResult values here,\n        // since the fragment ...spread does not specify a field name, but\n        // consists of multiple fields (within the fragment's selection set)\n        // that should be applied to the current result value(s).\n        aResult, bResult, context);\n      }\n    }\n  });\n}\nfunction selectionHasNonreactiveDirective(selection) {\n  return !!selection.directives && selection.directives.some(directiveIsNonreactive);\n}\nfunction directiveIsNonreactive(dir) {\n  return dir.name.value === \"nonreactive\";\n}\n//# sourceMappingURL=equalByQuery.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}