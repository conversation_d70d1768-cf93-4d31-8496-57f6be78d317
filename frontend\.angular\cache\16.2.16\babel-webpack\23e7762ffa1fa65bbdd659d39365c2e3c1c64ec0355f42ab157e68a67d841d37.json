{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction ForgotPasswordComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵtext(2, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36)(2, \"div\", 37);\n    i0.ɵɵelement(3, \"i\", 38)(4, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 40)(6, \"p\", 41);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction ForgotPasswordComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 36)(2, \"div\", 43);\n    i0.ɵɵelement(3, \"i\", 44)(4, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 40)(6, \"p\", 46);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.message, \" \");\n  }\n}\nexport let ForgotPasswordComponent = /*#__PURE__*/(() => {\n  class ForgotPasswordComponent {\n    constructor(fb, authService, router) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.message = '';\n      this.error = '';\n      this.forgotForm = this.fb.group({\n        email: ['', [Validators.required, Validators.email]]\n      });\n    }\n    onSubmit() {\n      if (this.forgotForm.invalid) return;\n      const email = this.forgotForm.value.email;\n      this.authService.forgotPassword(email).subscribe({\n        next: res => {\n          this.message = res.message;\n          this.error = '';\n          setTimeout(() => this.router.navigate(['/reset-password'], {\n            queryParams: {\n              email: email\n            }\n          }), 1500);\n        },\n        error: err => {\n          this.error = err.error.message || 'Something went wrong.';\n          this.message = '';\n        }\n      });\n    }\n    static {\n      this.ɵfac = function ForgotPasswordComponent_Factory(t) {\n        return new (t || ForgotPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ForgotPasswordComponent,\n        selectors: [[\"app-forgot-password\"]],\n        decls: 50,\n        vars: 5,\n        consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"min-h-screen\", \"flex\", \"items-center\", \"justify-center\", \"relative\", \"futuristic-layout\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"max-w-md\", \"relative\", \"z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"p-6\", \"text-center\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"p-6\"], [1, \"space-y-5\", 3, \"formGroup\", \"ngSubmit\"], [1, \"group\"], [\"for\", \"email\", 1, \"flex\", \"items-center\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-envelope\", \"mr-1.5\", \"text-xs\"], [1, \"relative\"], [\"id\", \"email\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\", \"required\", \"\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [\"class\", \"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1.5 flex items-center\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"w-full\", \"relative\", \"overflow-hidden\", \"group\", \"mt-6\", 3, \"disabled\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\", \"disabled:opacity-50\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\", \"disabled:opacity-0\"], [1, \"relative\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"font-medium\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"transition-all\", \"z-10\"], [1, \"fas\", \"fa-paper-plane\", \"mr-2\"], [1, \"text-center\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"space-y-2\", \"pt-4\"], [\"routerLink\", \"/login\", 1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"transition-colors\", \"font-medium\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"text-xs\", \"mt-1.5\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"flex-1\"], [1, \"text-xs\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"]],\n        template: function ForgotPasswordComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n            i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n            i0.ɵɵelementStart(21, \"div\", 11)(22, \"h1\", 12);\n            i0.ɵɵtext(23, \" Forgot Password \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"p\", 13);\n            i0.ɵɵtext(25, \" Enter your email to receive a reset code \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 14)(27, \"form\", 15);\n            i0.ɵɵlistener(\"ngSubmit\", function ForgotPasswordComponent_Template_form_ngSubmit_27_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(28, \"div\", 16)(29, \"label\", 17);\n            i0.ɵɵelement(30, \"i\", 18);\n            i0.ɵɵtext(31, \" Email \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"div\", 19);\n            i0.ɵɵelement(33, \"input\", 20);\n            i0.ɵɵelementStart(34, \"div\", 21);\n            i0.ɵɵelement(35, \"div\", 22);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(36, ForgotPasswordComponent_div_36_Template, 3, 0, \"div\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(37, ForgotPasswordComponent_div_37_Template, 8, 1, \"div\", 24);\n            i0.ɵɵtemplate(38, ForgotPasswordComponent_div_38_Template, 8, 1, \"div\", 25);\n            i0.ɵɵelementStart(39, \"button\", 26);\n            i0.ɵɵelement(40, \"div\", 27)(41, \"div\", 28);\n            i0.ɵɵelementStart(42, \"span\", 29);\n            i0.ɵɵelement(43, \"i\", 30);\n            i0.ɵɵtext(44, \" Send Reset Code \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(45, \"div\", 31)(46, \"div\");\n            i0.ɵɵtext(47, \" Remember your password? \");\n            i0.ɵɵelementStart(48, \"a\", 32);\n            i0.ɵɵtext(49, \" Sign in \");\n            i0.ɵɵelementEnd()()()()()()()();\n          }\n          if (rf & 2) {\n            let tmp_1_0;\n            i0.ɵɵadvance(27);\n            i0.ɵɵproperty(\"formGroup\", ctx.forgotForm);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.forgotForm.get(\"email\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.forgotForm.get(\"email\")) == null ? null : tmp_1_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.message);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", ctx.forgotForm.invalid);\n          }\n        },\n        dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink]\n      });\n    }\n  }\n  return ForgotPasswordComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}