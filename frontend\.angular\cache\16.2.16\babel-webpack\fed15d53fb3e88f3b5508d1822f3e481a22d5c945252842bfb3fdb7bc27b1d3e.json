{"ast": null, "code": "import { __assign, __rest as __rest_1, __spreadArray } from \"tslib\";\nimport { __rest } from \"tslib\";\nimport { mergeDeep } from \"../common/mergeDeep.js\";\n// A very basic pagination field policy that always concatenates new\n// results onto the existing array, without examining options.args.\nexport function concatPagination(keyArgs) {\n  if (keyArgs === void 0) {\n    keyArgs = false;\n  }\n  return {\n    keyArgs: keyArgs,\n    merge: function (existing, incoming) {\n      return existing ? __spreadArray(__spreadArray([], existing, true), incoming, true) : incoming;\n    }\n  };\n}\n// A basic field policy that uses options.args.{offset,limit} to splice\n// the incoming data into the existing array. If your arguments are called\n// something different (like args.{start,count}), feel free to copy/paste\n// this implementation and make the appropriate changes.\nexport function offsetLimitPagination(keyArgs) {\n  if (keyArgs === void 0) {\n    keyArgs = false;\n  }\n  return {\n    keyArgs: keyArgs,\n    merge: function (existing, incoming, _a) {\n      var args = _a.args;\n      var merged = existing ? existing.slice(0) : [];\n      if (incoming) {\n        if (args) {\n          // Assume an offset of 0 if args.offset omitted.\n          var _b = args.offset,\n            offset = _b === void 0 ? 0 : _b;\n          for (var i = 0; i < incoming.length; ++i) {\n            merged[offset + i] = incoming[i];\n          }\n        } else {\n          // It's unusual (probably a mistake) for a paginated field not\n          // to receive any arguments, so you might prefer to throw an\n          // exception here, instead of recovering by appending incoming\n          // onto the existing array.\n          merged.push.apply(merged, incoming);\n        }\n      }\n      return merged;\n    }\n  };\n}\n// As proof of the flexibility of field policies, this function generates\n// one that handles Relay-style pagination, without Apollo Client knowing\n// anything about connections, edges, cursors, or pageInfo objects.\nexport function relayStylePagination(keyArgs) {\n  if (keyArgs === void 0) {\n    keyArgs = false;\n  }\n  return {\n    keyArgs: keyArgs,\n    read: function (existing, _a) {\n      var canRead = _a.canRead,\n        readField = _a.readField;\n      if (!existing) return existing;\n      var edges = [];\n      var firstEdgeCursor = \"\";\n      var lastEdgeCursor = \"\";\n      existing.edges.forEach(function (edge) {\n        // Edges themselves could be Reference objects, so it's important\n        // to use readField to access the edge.edge.node property.\n        if (canRead(readField(\"node\", edge))) {\n          edges.push(edge);\n          if (edge.cursor) {\n            firstEdgeCursor = firstEdgeCursor || edge.cursor || \"\";\n            lastEdgeCursor = edge.cursor || lastEdgeCursor;\n          }\n        }\n      });\n      if (edges.length > 1 && firstEdgeCursor === lastEdgeCursor) {\n        firstEdgeCursor = \"\";\n      }\n      var _b = existing.pageInfo || {},\n        startCursor = _b.startCursor,\n        endCursor = _b.endCursor;\n      return __assign(__assign({}, getExtras(existing)), {\n        edges: edges,\n        pageInfo: __assign(__assign({}, existing.pageInfo), {\n          // If existing.pageInfo.{start,end}Cursor are undefined or \"\", default\n          // to firstEdgeCursor and/or lastEdgeCursor.\n          startCursor: startCursor || firstEdgeCursor,\n          endCursor: endCursor || lastEdgeCursor\n        })\n      });\n    },\n    merge: function (existing, incoming, _a) {\n      var args = _a.args,\n        isReference = _a.isReference,\n        readField = _a.readField;\n      if (!existing) {\n        existing = makeEmptyData();\n      }\n      if (!incoming) {\n        return existing;\n      }\n      var incomingEdges = incoming.edges ? incoming.edges.map(function (edge) {\n        if (isReference(edge = __assign({}, edge))) {\n          // In case edge is a Reference, we read out its cursor field and\n          // store it as an extra property of the Reference object.\n          edge.cursor = readField(\"cursor\", edge);\n        }\n        return edge;\n      }) : [];\n      if (incoming.pageInfo) {\n        var pageInfo_1 = incoming.pageInfo;\n        var startCursor = pageInfo_1.startCursor,\n          endCursor = pageInfo_1.endCursor;\n        var firstEdge = incomingEdges[0];\n        var lastEdge = incomingEdges[incomingEdges.length - 1];\n        // In case we did not request the cursor field for edges in this\n        // query, we can still infer cursors from pageInfo.\n        if (firstEdge && startCursor) {\n          firstEdge.cursor = startCursor;\n        }\n        if (lastEdge && endCursor) {\n          lastEdge.cursor = endCursor;\n        }\n        // Cursors can also come from edges, so we default\n        // pageInfo.{start,end}Cursor to {first,last}Edge.cursor.\n        var firstCursor = firstEdge && firstEdge.cursor;\n        if (firstCursor && !startCursor) {\n          incoming = mergeDeep(incoming, {\n            pageInfo: {\n              startCursor: firstCursor\n            }\n          });\n        }\n        var lastCursor = lastEdge && lastEdge.cursor;\n        if (lastCursor && !endCursor) {\n          incoming = mergeDeep(incoming, {\n            pageInfo: {\n              endCursor: lastCursor\n            }\n          });\n        }\n      }\n      var prefix = existing.edges;\n      var suffix = [];\n      if (args && args.after) {\n        // This comparison does not need to use readField(\"cursor\", edge),\n        // because we stored the cursor field of any Reference edges as an\n        // extra property of the Reference object.\n        var index = prefix.findIndex(function (edge) {\n          return edge.cursor === args.after;\n        });\n        if (index >= 0) {\n          prefix = prefix.slice(0, index + 1);\n          // suffix = []; // already true\n        }\n      } else if (args && args.before) {\n        var index = prefix.findIndex(function (edge) {\n          return edge.cursor === args.before;\n        });\n        suffix = index < 0 ? prefix : prefix.slice(index);\n        prefix = [];\n      } else if (incoming.edges) {\n        // If we have neither args.after nor args.before, the incoming\n        // edges cannot be spliced into the existing edges, so they must\n        // replace the existing edges. See #6592 for a motivating example.\n        prefix = [];\n      }\n      var edges = __spreadArray(__spreadArray(__spreadArray([], prefix, true), incomingEdges, true), suffix, true);\n      var pageInfo = __assign(__assign({}, incoming.pageInfo), existing.pageInfo);\n      if (incoming.pageInfo) {\n        var _b = incoming.pageInfo,\n          hasPreviousPage = _b.hasPreviousPage,\n          hasNextPage = _b.hasNextPage,\n          startCursor = _b.startCursor,\n          endCursor = _b.endCursor,\n          extras = __rest_1(_b, [\"hasPreviousPage\", \"hasNextPage\", \"startCursor\", \"endCursor\"]);\n        // If incoming.pageInfo had any extra non-standard properties,\n        // assume they should take precedence over any existing properties\n        // of the same name, regardless of where this page falls with\n        // respect to the existing data.\n        Object.assign(pageInfo, extras);\n        // Keep existing.pageInfo.has{Previous,Next}Page unless the\n        // placement of the incoming edges means incoming.hasPreviousPage\n        // or incoming.hasNextPage should become the new values for those\n        // properties in existing.pageInfo. Note that these updates are\n        // only permitted when the beginning or end of the incoming page\n        // coincides with the beginning or end of the existing data, as\n        // determined using prefix.length and suffix.length.\n        if (!prefix.length) {\n          if (void 0 !== hasPreviousPage) pageInfo.hasPreviousPage = hasPreviousPage;\n          if (void 0 !== startCursor) pageInfo.startCursor = startCursor;\n        }\n        if (!suffix.length) {\n          if (void 0 !== hasNextPage) pageInfo.hasNextPage = hasNextPage;\n          if (void 0 !== endCursor) pageInfo.endCursor = endCursor;\n        }\n      }\n      return __assign(__assign(__assign({}, getExtras(existing)), getExtras(incoming)), {\n        edges: edges,\n        pageInfo: pageInfo\n      });\n    }\n  };\n}\n// Returns any unrecognized properties of the given object.\nvar getExtras = function (obj) {\n  return __rest(obj, notExtras);\n};\nvar notExtras = [\"edges\", \"pageInfo\"];\nfunction makeEmptyData() {\n  return {\n    edges: [],\n    pageInfo: {\n      hasPreviousPage: false,\n      hasNextPage: true,\n      startCursor: \"\",\n      endCursor: \"\"\n    }\n  };\n}\n//# sourceMappingURL=pagination.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}