{"ast": null, "code": "import { newInvariantError } from \"../../utilities/globals/index.js\";\nexport var serializeFetchParameter = function (p, label) {\n  var serialized;\n  try {\n    serialized = JSON.stringify(p);\n  } catch (e) {\n    var parseError = newInvariantError(42, label, e.message);\n    parseError.parseError = e;\n    throw parseError;\n  }\n  return serialized;\n};\n//# sourceMappingURL=serializeFetchParameter.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}