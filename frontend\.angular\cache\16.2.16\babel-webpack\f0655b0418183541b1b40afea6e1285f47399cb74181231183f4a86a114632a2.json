{"ast": null, "code": "import { Observable } from \"./Observable.js\";\n// Like Observable.prototype.map, except that the mapping function can\n// optionally return a Promise (or be async).\nexport function asyncMap(observable, mapFn, catchFn) {\n  return new Observable(function (observer) {\n    var promiseQueue = {\n      // Normally we would initialize promiseQueue to Promise.resolve(), but\n      // in this case, for backwards compatibility, we need to be careful to\n      // invoke the first callback synchronously.\n      then: function (callback) {\n        return new Promise(function (resolve) {\n          return resolve(callback());\n        });\n      }\n    };\n    function makeCallback(examiner, key) {\n      return function (arg) {\n        if (examiner) {\n          var both = function () {\n            // If the observer is closed, we don't want to continue calling the\n            // mapping function - it's result will be swallowed anyways.\n            return observer.closed ? /* will be swallowed */0 : examiner(arg);\n          };\n          promiseQueue = promiseQueue.then(both, both).then(function (result) {\n            return observer.next(result);\n          }, function (error) {\n            return observer.error(error);\n          });\n        } else {\n          observer[key](arg);\n        }\n      };\n    }\n    var handler = {\n      next: makeCallback(mapFn, \"next\"),\n      error: makeCallback(catchFn, \"error\"),\n      complete: function () {\n        // no need to reassign `promiseQueue`, after `observer.complete`,\n        // the observer will be closed and short-circuit everything anyways\n        /*promiseQueue = */\n        promiseQueue.then(function () {\n          return observer.complete();\n        });\n      }\n    };\n    var sub = observable.subscribe(handler);\n    return function () {\n      return sub.unsubscribe();\n    };\n  });\n}\n//# sourceMappingURL=asyncMap.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}