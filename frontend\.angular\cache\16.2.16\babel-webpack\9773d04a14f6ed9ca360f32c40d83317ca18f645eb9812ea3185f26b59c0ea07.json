{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/auth.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"src/app/services/data.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nfunction ProfileComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18);\n    i0.ɵɵelement(2, \"div\", 19)(3, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"div\", 23);\n    i0.ɵɵelement(3, \"i\", 24)(4, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 26);\n    i0.ɵɵtext(7, \" Erreur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 27);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction ProfileComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 22)(2, \"div\", 29);\n    i0.ɵɵelement(3, \"i\", 30)(4, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 31);\n    i0.ɵɵtext(7, \" Succ\\u00E8s \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 27);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.message, \" \");\n  }\n}\nfunction ProfileComponent_div_26_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_26_div_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.toggleEditMode());\n    });\n    i0.ɵɵtext(2, \" Complete Profile \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 32);\n    i0.ɵɵelement(2, \"div\", 33);\n    i0.ɵɵelementStart(3, \"div\", 34)(4, \"h3\", 35);\n    i0.ɵɵtext(5, \" Profile Completion \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 36);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 37);\n    i0.ɵɵelement(9, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 27);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, ProfileComponent_div_26_div_12_Template, 3, 0, \"div\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵstyleProp(\"color\", ctx_r3.getProgressColor());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.progressPercentage, \"% \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.progressPercentage, \"%\")(\"background-color\", ctx_r3.getProgressColor());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getMotivationalMessage(), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.progressPercentage < 100);\n  }\n}\nfunction ProfileComponent_div_27_button_13_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 75);\n  }\n}\nfunction ProfileComponent_div_27_button_13_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 76);\n  }\n}\nfunction ProfileComponent_div_27_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.onUpload());\n    });\n    i0.ɵɵtemplate(1, ProfileComponent_div_27_button_13_i_1_Template, 1, 0, \"i\", 73);\n    i0.ɵɵtemplate(2, ProfileComponent_div_27_button_13_i_2_Template, 1, 0, \"i\", 74);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r9.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.uploadLoading ? \"Uploading...\" : \"Upload\", \" \");\n  }\n}\nfunction ProfileComponent_div_27_button_14_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 79);\n  }\n}\nfunction ProfileComponent_div_27_button_14_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 76);\n  }\n}\nfunction ProfileComponent_div_27_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.removeProfileImage());\n    });\n    i0.ɵɵtemplate(1, ProfileComponent_div_27_button_14_i_1_Template, 1, 0, \"i\", 78);\n    i0.ɵɵtemplate(2, ProfileComponent_div_27_button_14_i_2_Template, 1, 0, \"i\", 74);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.removeLoading ? \"Removing...\" : \"Remove\", \" \");\n  }\n}\nfunction ProfileComponent_div_27_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.user.department, \" \");\n  }\n}\nfunction ProfileComponent_div_27_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.user.position, \" \");\n  }\n}\nfunction ProfileComponent_div_27_p_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 82);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.user.bio.length > 150 ? i0.ɵɵpipeBind3(2, 1, ctx_r13.user.bio, 0, 150) + \"...\" : ctx_r13.user.bio, \" \");\n  }\n}\nfunction ProfileComponent_div_27_button_36_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r28.progressPercentage, \"% \");\n  }\n}\nfunction ProfileComponent_div_27_button_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_button_36_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r30);\n      const tab_r27 = restoredCtx.$implicit;\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.activeTab = tab_r27.id);\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, ProfileComponent_div_27_button_36_span_3_Template, 2, 1, \"span\", 84);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tab_r27 = ctx.$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bg-gradient-to-r\", ctx_r14.activeTab === tab_r27.id)(\"from-[#4f5fad]\", ctx_r14.activeTab === tab_r27.id)(\"to-[#7826b5]\", ctx_r14.activeTab === tab_r27.id)(\"text-white\", ctx_r14.activeTab === tab_r27.id)(\"text-[#6d6870]\", ctx_r14.activeTab !== tab_r27.id)(\"dark:text-[#a0a0a0]\", ctx_r14.activeTab !== tab_r27.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(tab_r27.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tab_r27.label, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", tab_r27.id === \"completion\" && ctx_r14.progressPercentage < 100);\n  }\n}\nfunction ProfileComponent_div_27_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 86)(2, \"h3\", 87);\n    i0.ɵɵelement(3, \"i\", 88);\n    i0.ɵɵtext(4, \" Personal Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_div_38_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.setEditSection(\"personal\"));\n    });\n    i0.ɵɵelement(6, \"i\", 90);\n    i0.ɵɵtext(7, \" Edit \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 91)(9, \"div\", 92)(10, \"label\", 93);\n    i0.ɵɵtext(11, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 94);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 92)(15, \"label\", 93);\n    i0.ɵɵtext(16, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 94);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 92)(20, \"label\", 93);\n    i0.ɵɵtext(21, \"Full Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\", 94);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 92)(25, \"label\", 93);\n    i0.ɵɵtext(26, \"Date of Birth\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 94);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 92)(31, \"label\", 93);\n    i0.ɵɵtext(32, \"Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 94);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 92)(36, \"label\", 93);\n    i0.ɵɵtext(37, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"p\", 94);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"div\", 92)(41, \"label\", 95);\n    i0.ɵɵtext(42, \"Bio\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"p\", 96);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r15.user.firstName || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r15.user.lastName || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r15.user.fullName || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.user.dateOfBirth ? i0.ɵɵpipeBind2(29, 7, ctx_r15.user.dateOfBirth, \"mediumDate\") : \"Not provided\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r15.user.phoneNumber || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r15.user.address || \"Not provided\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.user.bio || \"Tell us about yourself, your interests, and goals...\", \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_39_div_34_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 103);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const skill_r37 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", skill_r37, \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_39_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵtemplate(1, ProfileComponent_div_27_div_39_div_34_span_1_Template, 2, 1, \"span\", 102);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r33.user.skills);\n  }\n}\nfunction ProfileComponent_div_27_div_39_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 104);\n    i0.ɵɵtext(1, \"No skills added yet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_27_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 86)(2, \"h3\", 87);\n    i0.ɵɵelement(3, \"i\", 97);\n    i0.ɵɵtext(4, \" Professional Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_div_39_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.setEditSection(\"professional\"));\n    });\n    i0.ɵɵelement(6, \"i\", 90);\n    i0.ɵɵtext(7, \" Edit \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 91)(9, \"div\", 92)(10, \"label\", 93);\n    i0.ɵɵtext(11, \"Department\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 94);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 92)(15, \"label\", 93);\n    i0.ɵɵtext(16, \"Position\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 94);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 92)(20, \"label\", 93);\n    i0.ɵɵtext(21, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\", 94);\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 92)(26, \"label\", 93);\n    i0.ɵɵtext(27, \"Member Since\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"p\", 94);\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 92)(32, \"label\", 98);\n    i0.ɵɵtext(33, \"Skills & Expertise\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, ProfileComponent_div_27_div_39_div_34_Template, 2, 1, \"div\", 99);\n    i0.ɵɵtemplate(35, ProfileComponent_div_27_div_39_ng_template_35_Template, 2, 0, \"ng-template\", null, 100, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r34 = i0.ɵɵreference(36);\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r16.user.department || \"Not specified\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r16.user.position || \"Not specified\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(24, 6, ctx_r16.user.role));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(30, 8, ctx_r16.user.createdAt, \"mediumDate\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.user.skills && ctx_r16.user.skills.length > 0)(\"ngIfElse\", _r34);\n  }\n}\nfunction ProfileComponent_div_27_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 86)(2, \"h3\", 87);\n    i0.ɵɵelement(3, \"i\", 105);\n    i0.ɵɵtext(4, \" Account Settings \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 91)(6, \"div\", 92)(7, \"label\", 93);\n    i0.ɵɵtext(8, \"Email Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 94);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 106);\n    i0.ɵɵtext(12, \"Primary contact email\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 92)(14, \"label\", 93);\n    i0.ɵɵtext(15, \"Account Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 107)(17, \"span\", 108);\n    i0.ɵɵtext(18, \" Active \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 94);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"titlecase\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 109)(23, \"h4\", 110);\n    i0.ɵɵtext(24, \"Account Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 111)(26, \"a\", 112);\n    i0.ɵɵelement(27, \"i\", 113);\n    i0.ɵɵtext(28, \" Change Password \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_div_40_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r40.logout());\n    });\n    i0.ɵɵelement(30, \"i\", 115);\n    i0.ɵɵtext(31, \" Logout \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"a\", 116);\n    i0.ɵɵelement(33, \"i\", 67);\n    i0.ɵɵtext(34, \" Go to Dashboard \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r17.user.email);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(21, 3, ctx_r17.user.role));\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"routerLink\", ctx_r17.user.role === \"admin\" ? \"/admin/dashboard\" : \"/home\");\n  }\n}\nfunction ProfileComponent_div_27_div_41_div_37_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137)(1, \"span\", 138);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 139);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const field_r44 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(field_r44.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", field_r44.required ? \"Required\" : \"Optional\", \" \");\n  }\n}\nfunction ProfileComponent_div_27_div_41_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 131)(1, \"h4\", 132);\n    i0.ɵɵelement(2, \"i\", 133);\n    i0.ɵɵtext(3, \" Missing Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 134);\n    i0.ɵɵtemplate(5, ProfileComponent_div_27_div_41_div_37_div_5_Template, 5, 2, \"div\", 135);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 136);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_div_41_div_37_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r45 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r45.activeTab = \"personal\");\n    });\n    i0.ɵɵtext(7, \" Complete Missing Fields \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r42.getMissingFields());\n  }\n}\nfunction ProfileComponent_div_27_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 86)(2, \"h3\", 87);\n    i0.ɵɵelement(3, \"i\", 117);\n    i0.ɵɵtext(4, \" Profile Completion \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 36);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 109)(8, \"div\", 34)(9, \"h4\", 118);\n    i0.ɵɵtext(10, \"Overall Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 27);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 119);\n    i0.ɵɵelement(14, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 120)(16, \"div\", 121)(17, \"div\", 122);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 27);\n    i0.ɵɵtext(20, \"Required Fields\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 106);\n    i0.ɵɵtext(22, \"(60% weight)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 121)(24, \"div\", 123);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 27);\n    i0.ɵɵtext(27, \"Optional Fields\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 106);\n    i0.ɵɵtext(29, \"(30% weight)\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 121)(31, \"div\", 124);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 27);\n    i0.ɵɵtext(34, \"Profile Image\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 106);\n    i0.ɵɵtext(36, \"(10% weight)\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(37, ProfileComponent_div_27_div_41_div_37_Template, 8, 1, \"div\", 125);\n    i0.ɵɵelementStart(38, \"div\", 126)(39, \"h4\", 127);\n    i0.ɵɵelement(40, \"i\", 128);\n    i0.ɵɵtext(41, \" Tips for a Complete Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"ul\", 129)(43, \"li\", 22);\n    i0.ɵɵelement(44, \"i\", 130);\n    i0.ɵɵtext(45, \" Add a professional profile photo to help others recognize you \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"li\", 22);\n    i0.ɵɵelement(47, \"i\", 130);\n    i0.ɵɵtext(48, \" Write a compelling bio that showcases your interests and goals \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"li\", 22);\n    i0.ɵɵelement(50, \"i\", 130);\n    i0.ɵɵtext(51, \" List your skills to help with project matching and collaboration \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"li\", 22);\n    i0.ɵɵelement(53, \"i\", 130);\n    i0.ɵɵtext(54, \" Keep your contact information up to date \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleProp(\"color\", ctx_r18.getProgressColor());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.progressPercentage, \"% \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r18.getMotivationalMessage());\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r18.progressPercentage, \"%\")(\"background-color\", ctx_r18.getProgressColor());\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r18.getRequiredFieldsCompletion(), \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r18.getOptionalFieldsCompletion(), \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r18.hasProfileImage() ? 100 : 0, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.getMissingFields().length > 0);\n  }\n}\nfunction ProfileComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43);\n    i0.ɵɵelement(2, \"div\", 33);\n    i0.ɵɵelementStart(3, \"div\", 44)(4, \"div\", 45)(5, \"div\", 46);\n    i0.ɵɵelement(6, \"img\", 47);\n    i0.ɵɵelementStart(7, \"div\", 48)(8, \"label\", 49);\n    i0.ɵɵelement(9, \"i\", 50);\n    i0.ɵɵtext(10, \" Change Photo \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 51);\n    i0.ɵɵlistener(\"change\", function ProfileComponent_div_27_Template_input_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 52);\n    i0.ɵɵtemplate(13, ProfileComponent_div_27_button_13_Template, 4, 4, \"button\", 53);\n    i0.ɵɵtemplate(14, ProfileComponent_div_27_button_14_Template, 4, 4, \"button\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 55)(16, \"h2\", 56);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 57);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 58)(21, \"span\", 59);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, ProfileComponent_div_27_span_24_Template, 2, 1, \"span\", 60);\n    i0.ɵɵtemplate(25, ProfileComponent_div_27_span_25_Template, 2, 1, \"span\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, ProfileComponent_div_27_p_26_Template, 3, 5, \"p\", 62);\n    i0.ɵɵelementStart(27, \"div\", 63)(28, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_27_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.toggleEditMode());\n    });\n    i0.ɵɵelement(29, \"i\", 65);\n    i0.ɵɵtext(30, \" Edit Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"a\", 66);\n    i0.ɵɵelement(32, \"i\", 67);\n    i0.ɵɵtext(33, \" Dashboard \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(34, \"div\", 68)(35, \"div\", 69);\n    i0.ɵɵtemplate(36, ProfileComponent_div_27_button_36_Template, 4, 16, \"button\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 71);\n    i0.ɵɵtemplate(38, ProfileComponent_div_27_div_38_Template, 45, 10, \"div\", 15);\n    i0.ɵɵtemplate(39, ProfileComponent_div_27_div_39_Template, 37, 11, \"div\", 15);\n    i0.ɵɵtemplate(40, ProfileComponent_div_27_div_40_Template, 35, 5, \"div\", 15);\n    i0.ɵɵtemplate(41, ProfileComponent_div_27_div_41_Template, 55, 12, \"div\", 15);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r4.getProfileImageUrl(), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedImage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.profileImage && ctx_r4.user.profileImage !== \"assets/images/default-profile.png\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.user.fullName || ctx_r4.user.firstName + \" \" + ctx_r4.user.lastName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.user.email);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(23, 15, ctx_r4.user.role), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.department);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.position);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.bio);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"routerLink\", ctx_r4.user.role === \"admin\" ? \"/admin/dashboard\" : \"/home\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.profileTabs);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.activeTab === \"personal\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.activeTab === \"professional\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.activeTab === \"account\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.activeTab === \"completion\");\n  }\n}\nfunction ProfileComponent_div_28_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 175);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r50 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r50.getFieldError(\"firstName\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 175);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r51.getFieldError(\"lastName\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 175);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r52 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r52.getFieldError(\"fullName\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 175);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r53.getFieldError(\"email\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 175);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r54.getFieldError(\"phoneNumber\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_p_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 175);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r55 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r55.getFieldError(\"bio\"), \" \");\n  }\n}\nfunction ProfileComponent_div_28_span_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Save Changes\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_28_span_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 176);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 177);\n    i0.ɵɵelement(2, \"circle\", 178)(3, \"path\", 179);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Saving... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_28_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 180)(1, \"p\", 181);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r58 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r58.message);\n  }\n}\nfunction ProfileComponent_div_28_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 182)(1, \"p\", 183);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r59 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r59.error);\n  }\n}\nfunction ProfileComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 140)(1, \"div\", 141)(2, \"div\", 142)(3, \"div\", 143)(4, \"h2\", 144);\n    i0.ɵɵtext(5, \"Edit Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 145);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_28_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.cancelEdit());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 146);\n    i0.ɵɵelement(8, \"path\", 147);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"form\", 148);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_28_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r62 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r62.onEditSubmit());\n    });\n    i0.ɵɵelementStart(10, \"div\", 91)(11, \"div\")(12, \"label\", 149);\n    i0.ɵɵtext(13, \" First Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 150);\n    i0.ɵɵtemplate(15, ProfileComponent_div_28_p_15_Template, 2, 1, \"p\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\")(17, \"label\", 149);\n    i0.ɵɵtext(18, \" Last Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"input\", 152);\n    i0.ɵɵtemplate(20, ProfileComponent_div_28_p_20_Template, 2, 1, \"p\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\")(22, \"label\", 149);\n    i0.ɵɵtext(23, \" Full Name * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"input\", 153);\n    i0.ɵɵtemplate(25, ProfileComponent_div_28_p_25_Template, 2, 1, \"p\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\")(27, \"label\", 149);\n    i0.ɵɵtext(28, \" Email * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"input\", 154);\n    i0.ɵɵtemplate(30, ProfileComponent_div_28_p_30_Template, 2, 1, \"p\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\")(32, \"label\", 149);\n    i0.ɵɵtext(33, \" Date of Birth \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"input\", 155);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\")(36, \"label\", 149);\n    i0.ɵɵtext(37, \" Phone Number \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"input\", 156);\n    i0.ɵɵtemplate(39, ProfileComponent_div_28_p_39_Template, 2, 1, \"p\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\")(41, \"label\", 149);\n    i0.ɵɵtext(42, \" Department \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(43, \"input\", 157);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\")(45, \"label\", 149);\n    i0.ɵɵtext(46, \" Position \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(47, \"input\", 158);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 159)(49, \"label\", 149);\n    i0.ɵɵtext(50, \" Bio \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"textarea\", 160);\n    i0.ɵɵtemplate(52, ProfileComponent_div_28_p_52_Template, 2, 1, \"p\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 159)(54, \"label\", 149);\n    i0.ɵɵtext(55, \" Address \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(56, \"input\", 161);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\", 159)(58, \"label\", 149);\n    i0.ɵɵtext(59, \" Skills \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(60, \"input\", 162);\n    i0.ɵɵelementStart(61, \"p\", 106);\n    i0.ɵɵtext(62, \" Separate skills with commas \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 159)(64, \"label\", 149);\n    i0.ɵɵtext(65, \" Profile Picture \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"div\", 163)(67, \"div\", 164);\n    i0.ɵɵelement(68, \"img\", 165);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 166)(70, \"input\", 167);\n    i0.ɵɵlistener(\"change\", function ProfileComponent_div_28_Template_input_change_70_listener($event) {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"p\", 106);\n    i0.ɵɵtext(72, \" Upload a new profile picture (optional) \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(73, \"div\", 168)(74, \"button\", 169);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_28_Template_button_click_74_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.cancelEdit());\n    });\n    i0.ɵɵtext(75, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 170);\n    i0.ɵɵtemplate(77, ProfileComponent_div_28_span_77_Template, 2, 0, \"span\", 171);\n    i0.ɵɵtemplate(78, ProfileComponent_div_28_span_78_Template, 5, 0, \"span\", 172);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(79, ProfileComponent_div_28_div_79_Template, 3, 1, \"div\", 173);\n    i0.ɵɵtemplate(80, ProfileComponent_div_28_div_80_Template, 3, 1, \"div\", 174);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"formGroup\", ctx_r5.editForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"firstName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"firstName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"lastName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"lastName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"fullName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"fullName\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"email\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"email\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"phoneNumber\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"phoneNumber\"));\n    i0.ɵɵadvance(12);\n    i0.ɵɵclassProp(\"border-red-500\", ctx_r5.isFieldInvalid(\"bio\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.getFieldError(\"bio\"));\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"src\", ctx_r5.previewUrl || ctx_r5.getProfileImageUrl(), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r5.editLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.editLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.editLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.error);\n  }\n}\nexport let ProfileComponent = /*#__PURE__*/(() => {\n  class ProfileComponent {\n    constructor(authService, authuserService, dataService, router, fb) {\n      this.authService = authService;\n      this.authuserService = authuserService;\n      this.dataService = dataService;\n      this.router = router;\n      this.fb = fb;\n      this.selectedImage = null;\n      this.previewUrl = null;\n      this.message = '';\n      this.error = '';\n      this.uploadLoading = false;\n      this.removeLoading = false;\n      // Edit profile functionality\n      this.isEditMode = false;\n      this.editLoading = false;\n      this.progressPercentage = 0;\n      // Tab navigation\n      this.activeTab = 'personal';\n      this.editSection = '';\n      this.profileTabs = [{\n        id: 'personal',\n        label: 'Personal Info',\n        icon: 'fas fa-user'\n      }, {\n        id: 'professional',\n        label: 'Professional',\n        icon: 'fas fa-briefcase'\n      }, {\n        id: 'account',\n        label: 'Account',\n        icon: 'fas fa-cog'\n      }, {\n        id: 'completion',\n        label: 'Completion',\n        icon: 'fas fa-chart-line'\n      }];\n      this.editForm = this.fb.group({\n        firstName: ['', [Validators.required, Validators.minLength(2)]],\n        lastName: ['', [Validators.required, Validators.minLength(2)]],\n        fullName: ['', [Validators.required, Validators.minLength(2)]],\n        email: ['', [Validators.required, Validators.email]],\n        dateOfBirth: [''],\n        phoneNumber: ['', [Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\n        department: [''],\n        position: [''],\n        bio: ['', [Validators.minLength(10)]],\n        address: [''],\n        skills: ['']\n      });\n    }\n    ngOnInit() {\n      // Load user profile using DataService\n      this.dataService.getProfile().subscribe({\n        next: res => {\n          this.user = res;\n          // Ensure image properties are consistent\n          if (!this.user.profileImage && this.user.image) {\n            this.user.profileImage = this.user.image;\n          } else if (!this.user.image && this.user.profileImage) {\n            this.user.image = this.user.profileImage;\n          }\n          // If no image is available, use default\n          if (!this.user.profileImage || this.user.profileImage === 'null' || this.user.profileImage.trim() === '') {\n            this.user.profileImage = 'assets/images/default-profile.png';\n            this.user.image = 'assets/images/default-profile.png';\n          }\n          // Ensure profileImageURL is also set for backward compatibility\n          if (!this.user.profileImageURL) {\n            this.user.profileImageURL = this.user.profileImage || this.user.image;\n          }\n          // Calculate profile completion percentage\n          this.calculateProfileCompletion();\n          // Populate edit form with current user data\n          this.populateEditForm();\n        },\n        error: () => {\n          this.error = 'Failed to load profile.';\n        }\n      });\n    }\n    calculateProfileCompletion() {\n      if (!this.user) return;\n      const requiredFields = ['firstName', 'lastName', 'fullName', 'email', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\n      const optionalFields = ['position', 'address', 'skills'];\n      let completedRequired = 0;\n      let completedOptional = 0;\n      // Check required fields\n      requiredFields.forEach(field => {\n        const value = this.user[field];\n        if (value && value.toString().trim() !== '' && value !== 'uploads/default.png') {\n          completedRequired++;\n        }\n      });\n      // Check optional fields\n      optionalFields.forEach(field => {\n        const value = this.user[field];\n        if (value && value.toString().trim() !== '') {\n          completedOptional++;\n        }\n      });\n      // Check profile image\n      let hasProfileImage = 0;\n      if (this.user.profileImage && this.user.profileImage !== 'uploads/default.png' && this.user.profileImage !== 'assets/images/default-profile.png' && this.user.profileImage.trim() !== '') {\n        hasProfileImage = 1;\n      }\n      // Calculate percentage: Required fields (60%) + Optional fields (30%) + Profile Image (10%)\n      const requiredPercentage = completedRequired / requiredFields.length * 60;\n      const optionalPercentage = completedOptional / optionalFields.length * 30;\n      const imagePercentage = hasProfileImage * 10;\n      this.progressPercentage = Math.round(requiredPercentage + optionalPercentage + imagePercentage);\n    }\n    populateEditForm() {\n      if (!this.user) return;\n      this.editForm.patchValue({\n        firstName: this.user.firstName || '',\n        lastName: this.user.lastName || '',\n        fullName: this.user.fullName || '',\n        email: this.user.email || '',\n        dateOfBirth: this.user.dateOfBirth || '',\n        phoneNumber: this.user.phoneNumber || '',\n        department: this.user.department || '',\n        position: this.user.position || '',\n        bio: this.user.bio || '',\n        address: this.user.address || '',\n        skills: Array.isArray(this.user.skills) ? this.user.skills.join(', ') : this.user.skills || ''\n      });\n    }\n    /**\n     * Returns the appropriate profile image URL based on available properties\n     * Uses the same logic as in front-layout component for consistency\n     */\n    getProfileImageUrl() {\n      if (!this.user) return 'assets/images/default-profile.png';\n      // Check profileImage first\n      if (this.user.profileImage && this.user.profileImage !== 'null' && this.user.profileImage.trim() !== '') {\n        return this.user.profileImage;\n      }\n      // Then check image\n      if (this.user.image && this.user.image !== 'null' && this.user.image.trim() !== '') {\n        return this.user.image;\n      }\n      // Then check profileImageURL (for backward compatibility)\n      if (this.user.profileImageURL && this.user.profileImageURL !== 'null' && this.user.profileImageURL.trim() !== '') {\n        return this.user.profileImageURL;\n      }\n      // Default fallback\n      return 'assets/images/default-profile.png';\n    }\n    onFileSelected(event) {\n      const input = event.target;\n      if (input.files?.length) {\n        const file = input.files[0];\n        const validTypes = ['image/jpeg', 'image/png', 'image/webp'];\n        if (!validTypes.includes(file.type)) {\n          this.error = 'Seuls les JPEG, PNG et WebP sont autorisés';\n          this.resetFileInput();\n          return;\n        }\n        if (file.size > 2 * 1024 * 1024) {\n          this.error = \"L'image ne doit pas dépasser 2MB\";\n          this.resetFileInput();\n          return;\n        }\n        this.selectedImage = file;\n        this.error = '';\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.previewUrl = e.target?.result || null;\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n    onUpload() {\n      if (!this.selectedImage) return;\n      this.uploadLoading = true; // Activer l'état de chargement\n      this.message = '';\n      this.error = '';\n      console.log('Upload started, uploadLoading:', this.uploadLoading);\n      this.dataService.uploadProfileImage(this.selectedImage).pipe(finalize(() => {\n        this.uploadLoading = false;\n        console.log('Upload finished, uploadLoading:', this.uploadLoading);\n      })).subscribe({\n        next: response => {\n          this.message = response.message || 'Profile updated successfully';\n          // Update all image properties to ensure consistency across the application\n          this.user.profileImageURL = response.imageUrl;\n          this.user.profileImage = response.imageUrl;\n          this.user.image = response.imageUrl;\n          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\n          this.dataService.updateCurrentUser({\n            profileImage: response.imageUrl,\n            image: response.imageUrl\n          });\n          // Also update in AuthUserService to ensure all components are updated\n          this.authuserService.setCurrentUser({\n            ...this.user,\n            profileImage: response.imageUrl,\n            image: response.imageUrl\n          });\n          this.selectedImage = null;\n          this.previewUrl = null;\n          this.resetFileInput();\n          if (response.token) {\n            localStorage.setItem('token', response.token);\n          }\n          // Auto-hide message after 3 seconds\n          setTimeout(() => {\n            this.message = '';\n          }, 3000);\n        },\n        error: err => {\n          this.error = err.error?.message || 'Upload failed';\n          // Auto-hide error after 3 seconds\n          setTimeout(() => {\n            this.error = '';\n          }, 3000);\n        }\n      });\n    }\n    removeProfileImage() {\n      if (!confirm('Are you sure you want to remove your profile picture?')) return;\n      this.removeLoading = true;\n      this.message = '';\n      this.error = '';\n      this.dataService.removeProfileImage().pipe(finalize(() => this.removeLoading = false)).subscribe({\n        next: response => {\n          this.message = response.message || 'Profile picture removed successfully';\n          // Update all image properties to ensure consistency across the application\n          this.user.profileImageURL = null;\n          this.user.profileImage = null;\n          this.user.image = null;\n          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\n          this.dataService.updateCurrentUser({\n            profileImage: 'assets/images/default-profile.png',\n            image: 'assets/images/default-profile.png'\n          });\n          // Also update in AuthUserService to ensure all components are updated\n          this.authuserService.setCurrentUser({\n            ...this.user,\n            profileImage: 'assets/images/default-profile.png',\n            image: 'assets/images/default-profile.png'\n          });\n          if (response.token) {\n            localStorage.setItem('token', response.token);\n          }\n          // Auto-hide message after 3 seconds\n          setTimeout(() => {\n            this.message = '';\n          }, 3000);\n        },\n        error: err => {\n          this.error = err.error?.message || 'Removal failed';\n          // Auto-hide error after 3 seconds\n          setTimeout(() => {\n            this.error = '';\n          }, 3000);\n        }\n      });\n    }\n    resetFileInput() {\n      this.selectedImage = null;\n      this.previewUrl = null;\n      const fileInput = document.getElementById('profile-upload');\n      if (fileInput) fileInput.value = '';\n    }\n    navigateTo(path) {\n      this.router.navigate([path]);\n    }\n    // Edit profile methods\n    toggleEditMode() {\n      this.isEditMode = !this.isEditMode;\n      if (this.isEditMode) {\n        this.populateEditForm();\n      }\n      this.message = '';\n      this.error = '';\n    }\n    onEditSubmit() {\n      if (this.editForm.invalid) {\n        this.markFormGroupTouched();\n        return;\n      }\n      this.editLoading = true;\n      this.error = '';\n      this.message = '';\n      const formData = new FormData();\n      // Add form fields\n      Object.keys(this.editForm.value).forEach(key => {\n        const value = this.editForm.value[key];\n        if (key === 'skills' && value) {\n          // Convert skills string to array\n          const skillsArray = value.split(',').map(skill => skill.trim()).filter(skill => skill);\n          formData.append(key, JSON.stringify(skillsArray));\n        } else if (value) {\n          formData.append(key, value);\n        }\n      });\n      // Add profile image if selected\n      if (this.selectedImage) {\n        formData.append('image', this.selectedImage);\n      }\n      this.dataService.completeProfile(formData).subscribe({\n        next: response => {\n          this.editLoading = false;\n          this.message = 'Profile updated successfully!';\n          // Update current user data\n          this.user = {\n            ...this.user,\n            ...response.user\n          };\n          this.authuserService.setCurrentUser(this.user);\n          // Recalculate progress\n          this.calculateProfileCompletion();\n          // Exit edit mode\n          this.isEditMode = false;\n          // Clear selected image\n          this.selectedImage = null;\n          this.previewUrl = null;\n          this.resetFileInput();\n          // Auto-hide message after 3 seconds\n          setTimeout(() => {\n            this.message = '';\n          }, 3000);\n        },\n        error: err => {\n          this.editLoading = false;\n          this.error = err.error?.message || 'An error occurred while updating your profile.';\n          // Auto-hide error after 5 seconds\n          setTimeout(() => {\n            this.error = '';\n          }, 5000);\n        }\n      });\n    }\n    cancelEdit() {\n      this.isEditMode = false;\n      this.populateEditForm(); // Reset form to original values\n      this.selectedImage = null;\n      this.previewUrl = null;\n      this.resetFileInput();\n      this.message = '';\n      this.error = '';\n    }\n    markFormGroupTouched() {\n      Object.keys(this.editForm.controls).forEach(key => {\n        this.editForm.get(key)?.markAsTouched();\n      });\n    }\n    // Helper methods for template\n    getFieldError(fieldName) {\n      const field = this.editForm.get(fieldName);\n      if (field?.errors && field.touched) {\n        if (field.errors['required']) return `${fieldName} is required`;\n        if (field.errors['minlength']) return `${fieldName} is too short`;\n        if (field.errors['email']) return `Invalid email format`;\n        if (field.errors['pattern']) return `${fieldName} format is invalid`;\n      }\n      return '';\n    }\n    isFieldInvalid(fieldName) {\n      const field = this.editForm.get(fieldName);\n      return !!(field?.invalid && field.touched);\n    }\n    getProgressColor() {\n      if (this.progressPercentage < 25) return '#ef4444'; // red\n      if (this.progressPercentage < 50) return '#f97316'; // orange\n      if (this.progressPercentage < 75) return '#eab308'; // yellow\n      if (this.progressPercentage < 100) return '#22c55e'; // green\n      return '#10b981'; // emerald\n    }\n\n    getMotivationalMessage() {\n      if (this.progressPercentage < 25) {\n        return \"Let's complete your profile to unlock all features! 🚀\";\n      } else if (this.progressPercentage < 50) {\n        return \"You're making great progress! Keep going! 💪\";\n      } else if (this.progressPercentage < 75) {\n        return \"Excellent! You're more than halfway there! 🌟\";\n      } else if (this.progressPercentage < 100) {\n        return \"Almost perfect! Just a few more details! 🎯\";\n      } else {\n        return \"Perfect! Your profile is complete! ✨\";\n      }\n    }\n    logout() {\n      this.authuserService.logout().subscribe({\n        next: () => {\n          this.authuserService.clearAuthData();\n          setTimeout(() => {\n            this.router.navigate(['/login'], {\n              queryParams: {\n                message: 'Déconnexion réussie'\n              },\n              replaceUrl: true\n            });\n          }, 100);\n        },\n        error: err => {\n          console.error('Logout error:', err);\n          this.authuserService.clearAuthData();\n          setTimeout(() => {\n            this.router.navigate(['/login'], {});\n          }, 100);\n        }\n      });\n    }\n    // New methods for enhanced profile functionality\n    setEditSection(section) {\n      this.editSection = section;\n      this.isEditMode = true;\n      this.populateEditForm();\n    }\n    getRequiredFieldsCompletion() {\n      if (!this.user) return 0;\n      const requiredFields = ['firstName', 'lastName', 'fullName', 'email', 'dateOfBirth', 'phoneNumber', 'department', 'bio'];\n      let completed = 0;\n      requiredFields.forEach(field => {\n        const value = this.user[field];\n        if (value && value.toString().trim() !== '' && value !== 'uploads/default.png') {\n          completed++;\n        }\n      });\n      return Math.round(completed / requiredFields.length * 100);\n    }\n    getOptionalFieldsCompletion() {\n      if (!this.user) return 0;\n      const optionalFields = ['position', 'address', 'skills'];\n      let completed = 0;\n      optionalFields.forEach(field => {\n        const value = this.user[field];\n        if (field === 'skills') {\n          // Special handling for skills array\n          if (Array.isArray(value) && value.length > 0) {\n            completed++;\n          }\n        } else if (value && value.toString().trim() !== '') {\n          completed++;\n        }\n      });\n      return Math.round(completed / optionalFields.length * 100);\n    }\n    hasProfileImage() {\n      if (!this.user) return false;\n      return !!(this.user.profileImage && this.user.profileImage !== 'uploads/default.png' && this.user.profileImage !== 'assets/images/default-profile.png' && this.user.profileImage.trim() !== '');\n    }\n    getMissingFields() {\n      if (!this.user) return [];\n      const fields = [{\n        field: 'firstName',\n        label: 'First Name',\n        required: true\n      }, {\n        field: 'lastName',\n        label: 'Last Name',\n        required: true\n      }, {\n        field: 'fullName',\n        label: 'Full Name',\n        required: true\n      }, {\n        field: 'dateOfBirth',\n        label: 'Date of Birth',\n        required: true\n      }, {\n        field: 'phoneNumber',\n        label: 'Phone Number',\n        required: true\n      }, {\n        field: 'department',\n        label: 'Department',\n        required: true\n      }, {\n        field: 'bio',\n        label: 'Bio',\n        required: true\n      }, {\n        field: 'position',\n        label: 'Position',\n        required: false\n      }, {\n        field: 'address',\n        label: 'Address',\n        required: false\n      }, {\n        field: 'skills',\n        label: 'Skills',\n        required: false\n      }];\n      return fields.filter(field => {\n        const value = this.user[field.field];\n        if (field.field === 'skills') {\n          // Special handling for skills array\n          return !Array.isArray(value) || value.length === 0;\n        }\n        return !value || value.toString().trim() === '';\n      });\n    }\n    static {\n      this.ɵfac = function ProfileComponent_Factory(t) {\n        return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.DataService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.FormBuilder));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProfileComponent,\n        selectors: [[\"app-profile\"]],\n        decls: 29,\n        vars: 6,\n        consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"min-h-screen\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"relative\", \"z-10\"], [1, \"mb-8\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [\"class\", \"flex justify-center items-center py-20\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-4 mx-auto max-w-3xl my-4 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"mb-8\", 4, \"ngIf\"], [\"class\", \"space-y-6\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"items-center\", \"py-20\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-3xl\", \"my-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"font-medium\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-3xl\", \"my-4\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-1\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"text-lg\", \"font-semibold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-2xl\", \"font-bold\"], [1, \"w-full\", \"bg-[#e2e8f0]\", \"dark:bg-[#2a2a2a]\", \"rounded-full\", \"h-4\", \"overflow-hidden\", \"mb-3\"], [1, \"h-full\", \"rounded-full\", \"transition-all\", \"duration-500\", \"ease-out\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"mt-4\"], [1, \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"text-sm\", \"font-medium\", 3, \"click\"], [1, \"space-y-6\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\", \"overflow-hidden\", \"group\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"items-center\", \"md:items-start\", \"gap-6\"], [1, \"flex-shrink-0\", \"text-center\"], [1, \"relative\", \"group/avatar\"], [\"alt\", \"Profile Image\", 1, \"w-32\", \"h-32\", \"rounded-full\", \"object-cover\", \"border-4\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"shadow-lg\", \"group-hover/avatar:scale-105\", \"transition-transform\", \"duration-300\", 3, \"src\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-50\", \"rounded-full\", \"opacity-0\", \"group-hover/avatar:opacity-100\", \"transition-opacity\", \"duration-300\", \"flex\", \"items-center\", \"justify-center\"], [\"for\", \"profile-upload\", 1, \"cursor-pointer\", \"text-white\", \"text-sm\", \"font-medium\"], [1, \"fas\", \"fa-camera\", \"text-xl\", \"mb-1\", \"block\"], [\"type\", \"file\", \"id\", \"profile-upload\", \"accept\", \"image/*\", 1, \"hidden\", 3, \"change\"], [1, \"flex\", \"gap-2\", \"mt-4\"], [\"class\", \"px-3 py-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] text-white text-xs rounded-lg hover:from-[#3d4a85] hover:to-[#6a1b9a] transition-all disabled:opacity-50\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"px-3 py-1 bg-red-500 text-white text-xs rounded-lg hover:bg-red-600 transition-all disabled:opacity-50\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"flex-1\", \"text-center\", \"md:text-left\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"mb-2\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-2\"], [1, \"flex\", \"flex-wrap\", \"gap-2\", \"justify-center\", \"md:justify-start\", \"mb-4\"], [1, \"px-3\", \"py-1\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-sm\", \"rounded-full\"], [\"class\", \"px-3 py-1 bg-[#3d4a85]/10 dark:bg-[#4f5fad]/10 text-[#3d4a85] dark:text-[#4f5fad] text-sm rounded-full\", 4, \"ngIf\"], [\"class\", \"px-3 py-1 bg-[#7826b5]/10 dark:bg-[#6d78c9]/10 text-[#7826b5] dark:text-[#6d78c9] text-sm rounded-full\", 4, \"ngIf\"], [\"class\", \"text-[#6d6870] dark:text-[#a0a0a0] text-sm leading-relaxed mb-4\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"gap-2\", \"justify-center\", \"md:justify-start\"], [1, \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"text-sm\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"flex\", \"items-center\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-2\"], [1, \"px-4\", \"py-2\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-sm\", \"rounded-lg\", \"hover:bg-[#4f5fad]\", \"hover:text-white\", \"dark:hover:bg-[#6d78c9]\", \"dark:hover:text-white\", \"transition-all\", \"flex\", \"items-center\", 3, \"routerLink\"], [1, \"fas\", \"fa-home\", \"mr-2\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"flex\", \"flex-wrap\", \"border-b\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\"], [\"class\", \"flex-1 px-4 py-3 text-sm font-medium transition-all hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 flex items-center justify-center gap-2\", 3, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-6\"], [1, \"px-3\", \"py-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"text-xs\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-upload mr-1\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-1\", 4, \"ngIf\"], [1, \"fas\", \"fa-upload\", \"mr-1\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-1\"], [1, \"px-3\", \"py-1\", \"bg-red-500\", \"text-white\", \"text-xs\", \"rounded-lg\", \"hover:bg-red-600\", \"transition-all\", \"disabled:opacity-50\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-trash mr-1\", 4, \"ngIf\"], [1, \"fas\", \"fa-trash\", \"mr-1\"], [1, \"px-3\", \"py-1\", \"bg-[#3d4a85]/10\", \"dark:bg-[#4f5fad]/10\", \"text-[#3d4a85]\", \"dark:text-[#4f5fad]\", \"text-sm\", \"rounded-full\"], [1, \"px-3\", \"py-1\", \"bg-[#7826b5]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#7826b5]\", \"dark:text-[#6d78c9]\", \"text-sm\", \"rounded-full\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"text-sm\", \"leading-relaxed\", \"mb-4\"], [1, \"flex-1\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"transition-all\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"flex\", \"items-center\", \"justify-center\", \"gap-2\", 3, \"click\"], [\"class\", \"px-2 py-1 bg-orange-500 text-white text-xs rounded-full\", 4, \"ngIf\"], [1, \"px-2\", \"py-1\", \"bg-orange-500\", \"text-white\", \"text-xs\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-6\"], [1, \"text-xl\", \"font-semibold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-user\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"px-3\", \"py-1\", \"text-sm\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"rounded-lg\", \"hover:bg-[#4f5fad]/20\", \"dark:hover:bg-[#6d78c9]/20\", \"transition-all\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"bg-[#f8fafc]\", \"dark:bg-[#2a2a2a]\", \"p-4\", \"rounded-lg\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-2\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"leading-relaxed\"], [1, \"fas\", \"fa-briefcase\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-3\"], [\"class\", \"flex flex-wrap gap-2\", 4, \"ngIf\", \"ngIfElse\"], [\"noSkills\", \"\"], [1, \"flex\", \"flex-wrap\", \"gap-2\"], [\"class\", \"px-3 py-1 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] text-sm rounded-full border border-[#4f5fad]/20 dark:border-[#6d78c9]/20\", 4, \"ngFor\", \"ngForOf\"], [1, \"px-3\", \"py-1\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-sm\", \"rounded-full\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"italic\"], [1, \"fas\", \"fa-cog\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-1\"], [1, \"flex\", \"items-center\", \"gap-2\"], [1, \"px-2\", \"py-1\", \"bg-green-100\", \"dark:bg-green-900/20\", \"text-green-800\", \"dark:text-green-200\", \"text-xs\", \"rounded-full\"], [1, \"bg-[#f8fafc]\", \"dark:bg-[#2a2a2a]\", \"p-6\", \"rounded-lg\"], [1, \"text-lg\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-4\"], [1, \"flex\", \"flex-wrap\", \"gap-3\"], [\"routerLink\", \"/change-password\", 1, \"px-4\", \"py-2\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"rounded-lg\", \"hover:bg-[#4f5fad]/20\", \"dark:hover:bg-[#6d78c9]/20\", \"transition-all\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-key\", \"mr-2\"], [1, \"px-4\", \"py-2\", \"bg-red-100\", \"dark:bg-red-900/20\", \"text-red-600\", \"dark:text-red-400\", \"rounded-lg\", \"hover:bg-red-200\", \"dark:hover:bg-red-900/40\", \"transition-all\", \"flex\", \"items-center\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\", \"mr-2\"], [1, \"px-4\", \"py-2\", \"bg-[#6d6870]/10\", \"dark:bg-[#a0a0a0]/10\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"rounded-lg\", \"hover:bg-[#6d6870]/20\", \"dark:hover:bg-[#a0a0a0]/20\", \"transition-all\", \"flex\", \"items-center\", 3, \"routerLink\"], [1, \"fas\", \"fa-chart-line\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"text-lg\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"w-full\", \"bg-[#e2e8f0]\", \"dark:bg-[#2a2a2a]\", \"rounded-full\", \"h-4\", \"overflow-hidden\", \"mb-4\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-4\", \"mt-6\"], [1, \"text-center\", \"p-4\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"text-2xl\", \"font-bold\", \"text-[#7826b5]\", \"dark:text-[#6d78c9]\"], [1, \"text-2xl\", \"font-bold\", \"text-[#3d4a85]\", \"dark:text-[#4f5fad]\"], [\"class\", \"bg-orange-50 dark:bg-orange-900/20 p-6 rounded-lg border border-orange-200 dark:border-orange-800\", 4, \"ngIf\"], [1, \"bg-blue-50\", \"dark:bg-blue-900/20\", \"p-6\", \"rounded-lg\", \"border\", \"border-blue-200\", \"dark:border-blue-800\"], [1, \"text-lg\", \"font-medium\", \"text-blue-800\", \"dark:text-blue-200\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-lightbulb\", \"mr-2\"], [1, \"space-y-2\", \"text-blue-700\", \"dark:text-blue-300\"], [1, \"fas\", \"fa-check-circle\", \"mr-2\", \"mt-1\", \"text-green-500\"], [1, \"bg-orange-50\", \"dark:bg-orange-900/20\", \"p-6\", \"rounded-lg\", \"border\", \"border-orange-200\", \"dark:border-orange-800\"], [1, \"text-lg\", \"font-medium\", \"text-orange-800\", \"dark:text-orange-200\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-2\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-3\"], [\"class\", \"flex items-center justify-between p-3 bg-white dark:bg-[#1e1e1e] rounded-lg\", 4, \"ngFor\", \"ngForOf\"], [1, \"mt-4\", \"px-4\", \"py-2\", \"bg-orange-500\", \"text-white\", \"rounded-lg\", \"hover:bg-orange-600\", \"transition-all\", 3, \"click\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-3\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"px-2\", \"py-1\", \"bg-orange-100\", \"dark:bg-orange-900/40\", \"text-orange-800\", \"dark:text-orange-200\", \"text-xs\", \"rounded-full\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-50\", \"flex\", \"items-center\", \"justify-center\", \"z-50\", \"p-4\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-2xl\", \"shadow-2xl\", \"max-w-4xl\", \"w-full\", \"max-h-[90vh]\", \"overflow-y-auto\"], [1, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"p-6\", \"rounded-t-2xl\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"text-2xl\", \"font-bold\", \"text-white\"], [1, \"text-white\", \"hover:text-gray-200\", \"transition-colors\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"p-6\", 3, \"formGroup\", \"ngSubmit\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [\"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"class\", \"text-red-500 text-sm mt-1\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"fullName\", \"placeholder\", \"Enter your full name\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"date\", \"formControlName\", \"dateOfBirth\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"tel\", \"formControlName\", \"phoneNumber\", \"placeholder\", \"Enter your phone number\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"department\", \"placeholder\", \"e.g., Computer Science, Marketing\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"position\", \"placeholder\", \"e.g., Student, Professor, Developer\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"mt-6\"], [\"formControlName\", \"bio\", \"rows\", \"4\", \"placeholder\", \"Tell us about yourself, your interests, and goals...\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", \"resize-none\"], [\"type\", \"text\", \"formControlName\", \"address\", \"placeholder\", \"Enter your address\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"type\", \"text\", \"formControlName\", \"skills\", \"placeholder\", \"e.g., JavaScript, Python, Project Management (comma separated)\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"flex-shrink-0\"], [\"alt\", \"Profile preview\", 1, \"w-20\", \"h-20\", \"rounded-full\", \"object-cover\", \"border-2\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", 3, \"src\"], [1, \"flex-1\"], [\"type\", \"file\", \"accept\", \"image/*\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"change\"], [1, \"flex\", \"justify-end\", \"space-x-4\", \"mt-8\", \"pt-6\", \"border-t\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"border\", \"border-[#6d6870]\", \"dark:border-[#a0a0a0]\", \"rounded-lg\", \"hover:bg-[#6d6870]\", \"hover:text-white\", \"dark:hover:bg-[#a0a0a0]\", \"dark:hover:text-black\", \"transition-all\", 3, \"click\"], [\"type\", \"submit\", 1, \"px-8\", \"py-3\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-lg\", \"hover:from-[#3d4a85]\", \"hover:to-[#6a1b9a]\", \"transition-all\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"disabled\"], [4, \"ngIf\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [\"class\", \"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\", 4, \"ngIf\"], [\"class\", \"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\", 4, \"ngIf\"], [1, \"text-red-500\", \"text-sm\", \"mt-1\"], [1, \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"-ml-1\", \"mr-3\", \"h-5\", \"w-5\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"], [1, \"mt-4\", \"p-4\", \"bg-green-50\", \"dark:bg-green-900/20\", \"border\", \"border-green-200\", \"dark:border-green-800\", \"rounded-lg\"], [1, \"text-green-800\", \"dark:text-green-200\"], [1, \"mt-4\", \"p-4\", \"bg-red-50\", \"dark:bg-red-900/20\", \"border\", \"border-red-200\", \"dark:border-red-800\", \"rounded-lg\"], [1, \"text-red-800\", \"dark:text-red-200\"]],\n        template: function ProfileComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"h1\", 9);\n            i0.ɵɵtext(20, \" Mon Profil \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"p\", 10);\n            i0.ɵɵtext(22, \" G\\u00E9rez vos informations personnelles et vos pr\\u00E9f\\u00E9rences \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(23, ProfileComponent_div_23_Template, 4, 0, \"div\", 11);\n            i0.ɵɵtemplate(24, ProfileComponent_div_24_Template, 10, 1, \"div\", 12);\n            i0.ɵɵtemplate(25, ProfileComponent_div_25_Template, 10, 1, \"div\", 13);\n            i0.ɵɵtemplate(26, ProfileComponent_div_26_Template, 13, 9, \"div\", 14);\n            i0.ɵɵtemplate(27, ProfileComponent_div_27_Template, 42, 17, \"div\", 15);\n            i0.ɵɵtemplate(28, ProfileComponent_div_28_Template, 81, 25, \"div\", 16);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(23);\n            i0.ɵɵproperty(\"ngIf\", !ctx.user);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.message);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.user);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.user);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i4.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, i6.SlicePipe, i6.TitleCasePipe, i6.DatePipe],\n        styles: [\".loading-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:rgba(255,255,255,.8);display:flex;flex-direction:column;align-items:center;justify-content:center;z-index:1000}.spinner[_ngcontent-%COMP%]{border:4px solid rgba(0,0,0,.1);width:36px;height:36px;border-radius:50%;border-left-color:#09f;animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.form-loading[_ngcontent-%COMP%]{min-height:300px;display:flex;align-items:center;justify-content:center}\"]\n      });\n    }\n  }\n  return ProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}