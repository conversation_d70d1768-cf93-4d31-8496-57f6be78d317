{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { equal } from \"@wry/equality\";\nimport { DeepMerger } from \"../utilities/index.js\";\nimport { mergeIncrementalData } from \"../utilities/index.js\";\nimport { isNonEmptyArray, graphQLResultHasError, canUseWeakMap } from \"../utilities/index.js\";\nimport { NetworkStatus } from \"./networkStatus.js\";\nvar destructiveMethodCounts = new (canUseWeakMap ? WeakMap : Map)();\nfunction wrapDestructiveCacheMethod(cache, methodName) {\n  var original = cache[methodName];\n  if (typeof original === \"function\") {\n    // @ts-expect-error this is just too generic to be typed correctly\n    cache[methodName] = function () {\n      destructiveMethodCounts.set(cache,\n      // The %1e15 allows the count to wrap around to 0 safely every\n      // quadrillion evictions, so there's no risk of overflow. To be\n      // clear, this is more of a pedantic principle than something\n      // that matters in any conceivable practical scenario.\n      (destructiveMethodCounts.get(cache) + 1) % 1e15);\n      // @ts-expect-error this is just too generic to be typed correctly\n      return original.apply(this, arguments);\n    };\n  }\n}\n// A QueryInfo object represents a single query managed by the\n// QueryManager, which tracks all QueryInfo objects by queryId in its\n// this.queries Map. QueryInfo objects store the latest results and errors\n// for the given query, and are responsible for reporting those results to\n// the corresponding ObservableQuery, via the QueryInfo.notify method.\n// Results are reported asynchronously whenever setDiff marks the\n// QueryInfo object as dirty, though a call to the QueryManager's\n// broadcastQueries method may trigger the notification before it happens\n// automatically. This class used to be a simple interface type without\n// any field privacy or meaningful methods, which is why it still has so\n// many public fields. The effort to lock down and simplify the QueryInfo\n// interface is ongoing, and further improvements are welcome.\nvar QueryInfo = /** @class */function () {\n  function QueryInfo(queryManager, queryId) {\n    if (queryId === void 0) {\n      queryId = queryManager.generateQueryId();\n    }\n    this.queryId = queryId;\n    this.document = null;\n    this.lastRequestId = 1;\n    this.stopped = false;\n    this.observableQuery = null;\n    var cache = this.cache = queryManager.cache;\n    // Track how often cache.evict is called, since we want eviction to\n    // override the feud-stopping logic in the markResult method, by\n    // causing shouldWrite to return true. Wrapping the cache.evict method\n    // is a bit of a hack, but it saves us from having to make eviction\n    // counting an official part of the ApolloCache API.\n    if (!destructiveMethodCounts.has(cache)) {\n      destructiveMethodCounts.set(cache, 0);\n      wrapDestructiveCacheMethod(cache, \"evict\");\n      wrapDestructiveCacheMethod(cache, \"modify\");\n      wrapDestructiveCacheMethod(cache, \"reset\");\n    }\n  }\n  QueryInfo.prototype.init = function (query) {\n    var networkStatus = query.networkStatus || NetworkStatus.loading;\n    if (this.variables && this.networkStatus !== NetworkStatus.loading && !equal(this.variables, query.variables)) {\n      networkStatus = NetworkStatus.setVariables;\n    }\n    if (!equal(query.variables, this.variables)) {\n      this.lastDiff = void 0;\n      // Ensure we don't continue to receive cache updates for old variables\n      this.cancel();\n    }\n    Object.assign(this, {\n      document: query.document,\n      variables: query.variables,\n      networkError: null,\n      graphQLErrors: this.graphQLErrors || [],\n      networkStatus: networkStatus\n    });\n    if (query.observableQuery) {\n      this.setObservableQuery(query.observableQuery);\n    }\n    if (query.lastRequestId) {\n      this.lastRequestId = query.lastRequestId;\n    }\n    return this;\n  };\n  QueryInfo.prototype.resetDiff = function () {\n    this.lastDiff = void 0;\n  };\n  QueryInfo.prototype.getDiff = function () {\n    var options = this.getDiffOptions();\n    if (this.lastDiff && equal(options, this.lastDiff.options)) {\n      return this.lastDiff.diff;\n    }\n    this.updateWatch(this.variables);\n    var oq = this.observableQuery;\n    if (oq && oq.options.fetchPolicy === \"no-cache\") {\n      return {\n        complete: false\n      };\n    }\n    var diff = this.cache.diff(options);\n    this.updateLastDiff(diff, options);\n    return diff;\n  };\n  QueryInfo.prototype.updateLastDiff = function (diff, options) {\n    this.lastDiff = diff ? {\n      diff: diff,\n      options: options || this.getDiffOptions()\n    } : void 0;\n  };\n  QueryInfo.prototype.getDiffOptions = function (variables) {\n    var _a;\n    if (variables === void 0) {\n      variables = this.variables;\n    }\n    return {\n      query: this.document,\n      variables: variables,\n      returnPartialData: true,\n      optimistic: true,\n      canonizeResults: (_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a.options.canonizeResults\n    };\n  };\n  QueryInfo.prototype.setDiff = function (diff) {\n    var _a, _b;\n    var oldDiff = this.lastDiff && this.lastDiff.diff;\n    // If we are trying to deliver an incomplete cache result, we avoid\n    // reporting it if the query has errored, otherwise we let the broadcast try\n    // and repair the partial result by refetching the query. This check avoids\n    // a situation where a query that errors and another succeeds with\n    // overlapping data does not report the partial data result to the errored\n    // query.\n    //\n    // See https://github.com/apollographql/apollo-client/issues/11400 for more\n    // information on this issue.\n    if (diff && !diff.complete && ((_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a.getLastError())) {\n      return;\n    }\n    this.updateLastDiff(diff);\n    if (!equal(oldDiff && oldDiff.result, diff && diff.result)) {\n      (_b = this.observableQuery) === null || _b === void 0 ? void 0 : _b[\"scheduleNotify\"]();\n    }\n  };\n  QueryInfo.prototype.setObservableQuery = function (oq) {\n    if (oq === this.observableQuery) return;\n    this.observableQuery = oq;\n    if (oq) {\n      oq[\"queryInfo\"] = this;\n    }\n  };\n  QueryInfo.prototype.stop = function () {\n    var _a;\n    if (!this.stopped) {\n      this.stopped = true;\n      // Cancel the pending notify timeout\n      (_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a[\"resetNotifications\"]();\n      this.cancel();\n      var oq = this.observableQuery;\n      if (oq) oq.stopPolling();\n    }\n  };\n  QueryInfo.prototype.cancel = function () {\n    var _a;\n    (_a = this.cancelWatch) === null || _a === void 0 ? void 0 : _a.call(this);\n    this.cancelWatch = void 0;\n  };\n  QueryInfo.prototype.updateWatch = function (variables) {\n    var _this = this;\n    if (variables === void 0) {\n      variables = this.variables;\n    }\n    var oq = this.observableQuery;\n    if (oq && oq.options.fetchPolicy === \"no-cache\") {\n      return;\n    }\n    var watchOptions = __assign(__assign({}, this.getDiffOptions(variables)), {\n      watcher: this,\n      callback: function (diff) {\n        return _this.setDiff(diff);\n      }\n    });\n    if (!this.lastWatch || !equal(watchOptions, this.lastWatch)) {\n      this.cancel();\n      this.cancelWatch = this.cache.watch(this.lastWatch = watchOptions);\n    }\n  };\n  QueryInfo.prototype.resetLastWrite = function () {\n    this.lastWrite = void 0;\n  };\n  QueryInfo.prototype.shouldWrite = function (result, variables) {\n    var lastWrite = this.lastWrite;\n    return !(lastWrite &&\n    // If cache.evict has been called since the last time we wrote this\n    // data into the cache, there's a chance writing this result into\n    // the cache will repair what was evicted.\n    lastWrite.dmCount === destructiveMethodCounts.get(this.cache) && equal(variables, lastWrite.variables) && equal(result.data, lastWrite.result.data));\n  };\n  QueryInfo.prototype.markResult = function (result, document, options, cacheWriteBehavior) {\n    var _this = this;\n    var _a;\n    var merger = new DeepMerger();\n    var graphQLErrors = isNonEmptyArray(result.errors) ? result.errors.slice(0) : [];\n    // Cancel the pending notify timeout (if it exists) to prevent extraneous network\n    // requests. To allow future notify timeouts, diff and dirty are reset as well.\n    (_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a[\"resetNotifications\"]();\n    if (\"incremental\" in result && isNonEmptyArray(result.incremental)) {\n      var mergedData = mergeIncrementalData(this.getDiff().result, result);\n      result.data = mergedData;\n      // Detect the first chunk of a deferred query and merge it with existing\n      // cache data. This ensures a `cache-first` fetch policy that returns\n      // partial cache data or a `cache-and-network` fetch policy that already\n      // has full data in the cache does not complain when trying to merge the\n      // initial deferred server data with existing cache data.\n    } else if (\"hasNext\" in result && result.hasNext) {\n      var diff = this.getDiff();\n      result.data = merger.merge(diff.result, result.data);\n    }\n    this.graphQLErrors = graphQLErrors;\n    if (options.fetchPolicy === \"no-cache\") {\n      this.updateLastDiff({\n        result: result.data,\n        complete: true\n      }, this.getDiffOptions(options.variables));\n    } else if (cacheWriteBehavior !== 0 /* CacheWriteBehavior.FORBID */) {\n      if (shouldWriteResult(result, options.errorPolicy)) {\n        // Using a transaction here so we have a chance to read the result\n        // back from the cache before the watch callback fires as a result\n        // of writeQuery, so we can store the new diff quietly and ignore\n        // it when we receive it redundantly from the watch callback.\n        this.cache.performTransaction(function (cache) {\n          if (_this.shouldWrite(result, options.variables)) {\n            cache.writeQuery({\n              query: document,\n              data: result.data,\n              variables: options.variables,\n              overwrite: cacheWriteBehavior === 1 /* CacheWriteBehavior.OVERWRITE */\n            });\n\n            _this.lastWrite = {\n              result: result,\n              variables: options.variables,\n              dmCount: destructiveMethodCounts.get(_this.cache)\n            };\n          } else {\n            // If result is the same as the last result we received from\n            // the network (and the variables match too), avoid writing\n            // result into the cache again. The wisdom of skipping this\n            // cache write is far from obvious, since any cache write\n            // could be the one that puts the cache back into a desired\n            // state, fixing corruption or missing data. However, if we\n            // always write every network result into the cache, we enable\n            // feuds between queries competing to update the same data in\n            // incompatible ways, which can lead to an endless cycle of\n            // cache broadcasts and useless network requests. As with any\n            // feud, eventually one side must step back from the brink,\n            // letting the other side(s) have the last word(s). There may\n            // be other points where we could break this cycle, such as\n            // silencing the broadcast for cache.writeQuery (not a good\n            // idea, since it just delays the feud a bit) or somehow\n            // avoiding the network request that just happened (also bad,\n            // because the server could return useful new data). All\n            // options considered, skipping this cache write seems to be\n            // the least damaging place to break the cycle, because it\n            // reflects the intuition that we recently wrote this exact\n            // result into the cache, so the cache *should* already/still\n            // contain this data. If some other query has clobbered that\n            // data in the meantime, that's too bad, but there will be no\n            // winners if every query blindly reverts to its own version\n            // of the data. This approach also gives the network a chance\n            // to return new data, which will be written into the cache as\n            // usual, notifying only those queries that are directly\n            // affected by the cache updates, as usual. In the future, an\n            // even more sophisticated cache could perhaps prevent or\n            // mitigate the clobbering somehow, but that would make this\n            // particular cache write even less important, and thus\n            // skipping it would be even safer than it is today.\n            if (_this.lastDiff && _this.lastDiff.diff.complete) {\n              // Reuse data from the last good (complete) diff that we\n              // received, when possible.\n              result.data = _this.lastDiff.diff.result;\n              return;\n            }\n            // If the previous this.diff was incomplete, fall through to\n            // re-reading the latest data with cache.diff, below.\n          }\n\n          var diffOptions = _this.getDiffOptions(options.variables);\n          var diff = cache.diff(diffOptions);\n          // In case the QueryManager stops this QueryInfo before its\n          // results are delivered, it's important to avoid restarting the\n          // cache watch when markResult is called. We also avoid updating\n          // the watch if we are writing a result that doesn't match the current\n          // variables to avoid race conditions from broadcasting the wrong\n          // result.\n          if (!_this.stopped && equal(_this.variables, options.variables)) {\n            // Any time we're about to update this.diff, we need to make\n            // sure we've started watching the cache.\n            _this.updateWatch(options.variables);\n          }\n          // If we're allowed to write to the cache, and we can read a\n          // complete result from the cache, update result.data to be the\n          // result from the cache, rather than the raw network result.\n          // Set without setDiff to avoid triggering a notify call, since\n          // we have other ways of notifying for this result.\n          _this.updateLastDiff(diff, diffOptions);\n          if (diff.complete) {\n            result.data = diff.result;\n          }\n        });\n      } else {\n        this.lastWrite = void 0;\n      }\n    }\n  };\n  QueryInfo.prototype.markReady = function () {\n    this.networkError = null;\n    return this.networkStatus = NetworkStatus.ready;\n  };\n  QueryInfo.prototype.markError = function (error) {\n    var _a;\n    this.networkStatus = NetworkStatus.error;\n    this.lastWrite = void 0;\n    (_a = this.observableQuery) === null || _a === void 0 ? void 0 : _a[\"resetNotifications\"]();\n    if (error.graphQLErrors) {\n      this.graphQLErrors = error.graphQLErrors;\n    }\n    if (error.networkError) {\n      this.networkError = error.networkError;\n    }\n    return error;\n  };\n  return QueryInfo;\n}();\nexport { QueryInfo };\nexport function shouldWriteResult(result, errorPolicy) {\n  if (errorPolicy === void 0) {\n    errorPolicy = \"none\";\n  }\n  var ignoreErrors = errorPolicy === \"ignore\" || errorPolicy === \"all\";\n  var writeWithErrors = !graphQLResultHasError(result);\n  if (!writeWithErrors && ignoreErrors && result.data) {\n    writeWithErrors = true;\n  }\n  return writeWithErrors;\n}\n//# sourceMappingURL=QueryInfo.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}