{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { MessageType, CallType } from 'src/app/models/message.model';\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/message.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"src/app/services/user-status.service\";\nimport * as i6 from \"src/app/services/toast.service\";\nimport * as i7 from \"src/app/services/logger.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../../components/voice-recorder/voice-recorder.component\";\nimport * as i10 from \"../../../../components/voice-message-player/voice-message-player.component\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 50);\n  }\n}\nfunction MessageChatComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"span\", 52);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 53);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.otherParticipant.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.otherParticipant.isOnline ? \"En ligne\" : ctx_r1.formatLastActive(ctx_r1.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtext(2, \" Choisir un th\\u00E8me \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 56)(4, \"a\", 57);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_Template_a_click_4_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.changeTheme(\"theme-default\"));\n    });\n    i0.ɵɵelementStart(5, \"div\", 58);\n    i0.ɵɵelement(6, \"div\", 59);\n    i0.ɵɵelementStart(7, \"div\");\n    i0.ɵɵtext(8, \"Par d\\u00E9faut\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"a\", 60);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.changeTheme(\"theme-feminine\"));\n    });\n    i0.ɵɵelementStart(10, \"div\", 58);\n    i0.ɵɵelement(11, \"div\", 61);\n    i0.ɵɵelementStart(12, \"div\");\n    i0.ɵɵtext(13, \"Rose\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"a\", 62);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_Template_a_click_14_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.changeTheme(\"theme-masculine\"));\n    });\n    i0.ɵɵelementStart(15, \"div\", 58);\n    i0.ɵɵelement(16, \"div\", 63);\n    i0.ɵɵelementStart(17, \"div\");\n    i0.ɵɵtext(18, \"Bleu\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"a\", 64);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_Template_a_click_19_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.changeTheme(\"theme-neutral\"));\n    });\n    i0.ɵɵelementStart(20, \"div\", 58);\n    i0.ɵɵelement(21, \"div\", 65);\n    i0.ɵɵelementStart(22, \"div\");\n    i0.ɵɵtext(23, \"Vert\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"div\", 68)(4, \"div\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 70);\n    i0.ɵɵtext(6, \" Initializing communication... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72)(2, \"div\", 73);\n    i0.ɵɵelement(3, \"div\", 74)(4, \"div\", 75)(5, \"div\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 77);\n    i0.ɵɵtext(7, \" Retrieving data... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"div\", 80);\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵtext(4, \" Communication Initialized \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 80);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"div\", 83)(2, \"div\", 84);\n    i0.ɵɵelement(3, \"i\", 85)(4, \"div\", 86);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 87);\n    i0.ɵɵtext(7, \" System Error: Communication Failure \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 88);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r7.error);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵelement(1, \"div\", 99);\n    i0.ɵɵelementStart(2, \"div\", 100);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 99);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext().$implicit;\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r28.formatMessageDate(message_r26 == null ? null : message_r26.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵelement(1, \"img\", 102);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_5_span_6_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 111);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_5_span_6_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 112);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_5_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 108);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_54_div_1_div_5_span_6_i_1_Template, 1, 0, \"i\", 109);\n    i0.ɵɵtemplate(2, MessageChatComponent_ng_container_54_div_1_div_5_span_6_i_2_Template, 1, 0, \"i\", 110);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r26 == null ? null : message_r26.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(message_r26 == null ? null : message_r26.isRead));\n  }\n}\nconst _c2 = function (a0, a1, a2) {\n  return {\n    \"futuristic-message-pending\": a0,\n    \"futuristic-message-sending\": a1,\n    \"futuristic-message-error\": a2\n  };\n};\nfunction MessageChatComponent_ng_container_54_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"div\", 104);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 105)(4, \"span\", 106);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, MessageChatComponent_ng_container_54_div_1_div_5_span_6_Template, 3, 2, \"span\", 107);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext().$implicit;\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(4, _c2, message_r26.isPending, message_r26.isPending && !message_r26.isError, message_r26.isError));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", message_r26.content, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r30.formatMessageTime(message_r26 == null ? null : message_r26.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender.id) === ctx_r30.currentUserId || (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender._id) === ctx_r30.currentUserId || (message_r26 == null ? null : message_r26.senderId) === ctx_r30.currentUserId);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_6_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 120);\n  }\n  if (rf & 2) {\n    const i_r42 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"height\", 5 + (i_r42 % 3 === 0 ? 20 : i_r42 % 3 === 1 ? 15 : 10), \"px\");\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_6_span_10_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 111);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_6_span_10_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 112);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_6_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 108);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_54_div_1_div_6_span_10_i_1_Template, 1, 0, \"i\", 109);\n    i0.ɵɵtemplate(2, MessageChatComponent_ng_container_54_div_1_div_6_span_10_i_2_Template, 1, 0, \"i\", 110);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r26 == null ? null : message_r26.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(message_r26 == null ? null : message_r26.isRead));\n  }\n}\nconst _c3 = function () {\n  return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14];\n};\nfunction MessageChatComponent_ng_container_54_div_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"div\", 114)(2, \"div\", 115);\n    i0.ɵɵelement(3, \"i\", 116);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 117);\n    i0.ɵɵtemplate(5, MessageChatComponent_ng_container_54_div_1_div_6_div_5_Template, 1, 2, \"div\", 118);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"app-voice-message-player\", 119);\n    i0.ɵɵelementStart(7, \"div\", 105)(8, \"span\", 106);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, MessageChatComponent_ng_container_54_div_1_div_6_span_10_Template, 3, 2, \"span\", 107);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext().$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c2, message_r26.isPending, message_r26.isPending && !message_r26.isError, message_r26.isError));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(10, _c3));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"audioUrl\", ctx_r31.getVoiceMessageUrl(message_r26))(\"duration\", ctx_r31.getVoiceMessageDuration(message_r26));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r31.formatMessageTime(message_r26 == null ? null : message_r26.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender.id) === ctx_r31.currentUserId || (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender._id) === ctx_r31.currentUserId || (message_r26 == null ? null : message_r26.senderId) === ctx_r31.currentUserId);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_7_span_9_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 111);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_7_span_9_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 112);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_7_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 108);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_54_div_1_div_7_span_9_i_1_Template, 1, 0, \"i\", 109);\n    i0.ɵɵtemplate(2, MessageChatComponent_ng_container_54_div_1_div_7_span_9_i_2_Template, 1, 0, \"i\", 110);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r26 == null ? null : message_r26.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(message_r26 == null ? null : message_r26.isRead));\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 121)(1, \"div\", 122)(2, \"a\", 123);\n    i0.ɵɵelement(3, \"img\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 125);\n    i0.ɵɵelement(5, \"i\", 126);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 105)(7, \"span\", 106);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, MessageChatComponent_ng_container_54_div_1_div_7_span_9_Template, 3, 2, \"span\", 107);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext().$implicit;\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(5, _c2, message_r26.isPending, message_r26.isPending && !message_r26.isError, message_r26.isError));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"href\", ctx_r32.getImageUrl(message_r26), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r32.getImageUrl(message_r26), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r32.formatMessageTime(message_r26 == null ? null : message_r26.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender.id) === ctx_r32.currentUserId || (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender._id) === ctx_r32.currentUserId || (message_r26 == null ? null : message_r26.senderId) === ctx_r32.currentUserId);\n  }\n}\nconst _c4 = function (a0, a1) {\n  return {\n    \"futuristic-message-current-user\": a0,\n    \"futuristic-message-other-user\": a1\n  };\n};\nfunction MessageChatComponent_ng_container_54_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_54_div_1_div_1_Template, 5, 1, \"div\", 91);\n    i0.ɵɵelementStart(2, \"div\", 92);\n    i0.ɵɵtemplate(3, MessageChatComponent_ng_container_54_div_1_div_3_Template, 2, 1, \"div\", 93);\n    i0.ɵɵelementStart(4, \"div\", 94);\n    i0.ɵɵtemplate(5, MessageChatComponent_ng_container_54_div_1_div_5_Template, 7, 8, \"div\", 95);\n    i0.ɵɵtemplate(6, MessageChatComponent_ng_container_54_div_1_div_6_Template, 11, 11, \"div\", 96);\n    i0.ɵɵtemplate(7, MessageChatComponent_ng_container_54_div_1_div_7_Template, 10, 9, \"div\", 97);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r26 = ctx.$implicit;\n    const i_r27 = ctx.index;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-message-id\", message_r26.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.shouldShowDateHeader(i_r27));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c4, (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender.id) === ctx_r25.currentUserId || (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender._id) === ctx_r25.currentUserId || (message_r26 == null ? null : message_r26.senderId) === ctx_r25.currentUserId, !((message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender.id) === ctx_r25.currentUserId || (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender._id) === ctx_r25.currentUserId || (message_r26 == null ? null : message_r26.senderId) === ctx_r25.currentUserId)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !((message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender.id) === ctx_r25.currentUserId || (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender._id) === ctx_r25.currentUserId || (message_r26 == null ? null : message_r26.senderId) === ctx_r25.currentUserId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (message_r26 == null ? null : message_r26.content) && !ctx_r25.hasImage(message_r26) && !ctx_r25.isVoiceMessage(message_r26));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.isVoiceMessage(message_r26));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.hasImage(message_r26));\n  }\n}\nfunction MessageChatComponent_ng_container_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_54_div_1_Template, 8, 10, \"div\", 89);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.messages);\n  }\n}\nfunction MessageChatComponent_ng_template_55_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 128)(1, \"div\", 129);\n    i0.ɵɵelement(2, \"i\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 131);\n    i0.ɵɵtext(4, \" Aucun message dans cette conversation. \");\n    i0.ɵɵelement(5, \"br\");\n    i0.ɵɵtext(6, \"\\u00C9tablissez le premier contact pour commencer. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_template_55_div_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r53 = i0.ɵɵnextContext(2);\n      let tmp_b_0;\n      return i0.ɵɵresetView((tmp_b_0 = ctx_r53.messageForm.get(\"content\")) == null ? null : tmp_b_0.setValue(\"Bonjour!\"));\n    });\n    i0.ɵɵelement(8, \"i\", 133);\n    i0.ɵɵtext(9, \" Initialiser la communication \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_ng_template_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MessageChatComponent_ng_template_55_div_0_Template, 10, 0, \"div\", 127);\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.loading && !ctx_r10.error);\n  }\n}\nfunction MessageChatComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 134)(1, \"div\", 101);\n    i0.ɵɵelement(2, \"img\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 135)(4, \"div\", 136);\n    i0.ɵɵelement(5, \"div\", 137)(6, \"div\", 138)(7, \"div\", 139);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (ctx_r11.otherParticipant == null ? null : ctx_r11.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MessageChatComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 140);\n    i0.ɵɵelement(1, \"img\", 141);\n    i0.ɵɵelementStart(2, \"button\", 142);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r55 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r55.removeAttachment());\n    });\n    i0.ɵɵelement(3, \"i\", 143);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r12.previewUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MessageChatComponent_div_60_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 155);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_60_button_15_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r60);\n      const emoji_r58 = restoredCtx.$implicit;\n      const ctx_r59 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r59.insertEmoji(emoji_r58));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r58 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r58, \" \");\n  }\n}\nfunction MessageChatComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144)(1, \"div\", 145)(2, \"button\", 146);\n    i0.ɵɵelement(3, \"i\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 147);\n    i0.ɵɵelement(5, \"i\", 148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 147);\n    i0.ɵɵelement(7, \"i\", 149);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 147);\n    i0.ɵɵelement(9, \"i\", 150);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 147);\n    i0.ɵɵelement(11, \"i\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 147);\n    i0.ɵɵelement(13, \"i\", 152);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 153);\n    i0.ɵɵtemplate(15, MessageChatComponent_div_60_button_15_Template, 2, 1, \"button\", 154);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.commonEmojis);\n  }\n}\nfunction MessageChatComponent_app_voice_recorder_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-voice-recorder\", 156);\n    i0.ɵɵlistener(\"recordingComplete\", function MessageChatComponent_app_voice_recorder_69_Template_app_voice_recorder_recordingComplete_0_listener($event) {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r61 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r61.onVoiceRecordingComplete($event));\n    })(\"recordingCancelled\", function MessageChatComponent_app_voice_recorder_69_Template_app_voice_recorder_recordingCancelled_0_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.onVoiceRecordingCancelled());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"maxDuration\", 60);\n  }\n}\nfunction MessageChatComponent_input_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 157);\n    i0.ɵɵlistener(\"input\", function MessageChatComponent_input_70_Template_input_input_0_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.onTyping());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_button_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_button_71_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r66.toggleVoiceRecording());\n    });\n    i0.ɵɵelement(1, \"i\", 159);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_button_72_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 133);\n  }\n}\nfunction MessageChatComponent_button_72_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 163);\n  }\n}\nfunction MessageChatComponent_button_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 160);\n    i0.ɵɵtemplate(1, MessageChatComponent_button_72_i_1_Template, 1, 0, \"i\", 161);\n    i0.ɵɵtemplate(2, MessageChatComponent_button_72_i_2_Template, 1, 0, \"i\", 162);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r18.isUploading || ctx_r18.messageForm.invalid && !ctx_r18.selectedFile);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r18.isUploading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.isUploading);\n  }\n}\nfunction MessageChatComponent_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 164)(1, \"div\", 165)(2, \"div\", 166)(3, \"div\", 167);\n    i0.ɵɵelement(4, \"img\", 168);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\", 169);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 170);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 171)(10, \"button\", 172);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_73_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r70 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r70.rejectCall());\n    });\n    i0.ɵɵelement(11, \"i\", 173);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Rejeter\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 174);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_73_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.acceptCall());\n    });\n    i0.ɵɵelement(15, \"i\", 175);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"Accepter\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", (ctx_r19.incomingCall.caller == null ? null : ctx_r19.incomingCall.caller.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r19.incomingCall.caller == null ? null : ctx_r19.incomingCall.caller.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.incomingCall.type === \"AUDIO\" ? \"Appel audio entrant\" : \"Appel vid\\u00E9o entrant\", \" \");\n  }\n}\nconst _c5 = function (a0) {\n  return {\n    active: a0\n  };\n};\nexport let MessageChatComponent = /*#__PURE__*/(() => {\n  class MessageChatComponent {\n    constructor(MessageService, route, authService, fb, statusService, router, toastService, logger, cdr) {\n      this.MessageService = MessageService;\n      this.route = route;\n      this.authService = authService;\n      this.fb = fb;\n      this.statusService = statusService;\n      this.router = router;\n      this.toastService = toastService;\n      this.logger = logger;\n      this.cdr = cdr;\n      this.messages = [];\n      this.conversation = null;\n      this.loading = true;\n      this.currentUserId = null;\n      this.currentUsername = 'You';\n      this.otherParticipant = null;\n      this.selectedFile = null;\n      this.previewUrl = null;\n      this.isUploading = false;\n      this.isTyping = false;\n      this.isRecordingVoice = false;\n      this.voiceRecordingDuration = 0;\n      this.MAX_MESSAGES_PER_SIDE = 5; // Nombre maximum de messages à afficher par côté (expéditeur/destinataire)\n      this.MAX_MESSAGES_TO_LOAD = 10; // Nombre maximum de messages à charger à la fois (pagination)\n      this.MAX_TOTAL_MESSAGES = 100; // Limite totale de messages à conserver en mémoire\n      this.currentPage = 1; // Page actuelle pour la pagination\n      this.isLoadingMore = false; // Indicateur de chargement en cours (public pour le template)\n      this.hasMoreMessages = true; // Indique s'il y a plus de messages à charger (public pour le template)\n      this.subscriptions = new Subscription();\n      // Variables pour le sélecteur de thème\n      this.selectedTheme = 'theme-default'; // Thème par défaut\n      this.showThemeSelector = false; // Affichage du sélecteur de thème\n      // Variables pour le sélecteur d'émojis\n      this.showEmojiPicker = false;\n      // Variables pour les appels\n      this.incomingCall = null;\n      this.showCallModal = false;\n      this.commonEmojis = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '👍', '👎', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✌️', '🤞', '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '💔', '💯', '💢'];\n      this.isCurrentlyTyping = false;\n      this.TYPING_DELAY = 500; // Délai en ms avant d'envoyer l'événement de frappe\n      this.TYPING_TIMEOUT = 3000; // Délai en ms avant d'arrêter l'indicateur de frappe\n      this.messageForm = this.fb.group({\n        content: ['', [Validators.maxLength(1000)]]\n      });\n    }\n    ngOnInit() {\n      this.currentUserId = this.authService.getCurrentUserId();\n      // Charger le thème sauvegardé\n      const savedTheme = localStorage.getItem('chat-theme');\n      if (savedTheme) {\n        this.selectedTheme = savedTheme;\n        this.logger.debug('MessageChat', `Loaded saved theme: ${savedTheme}`);\n      }\n      // Récupérer les messages vocaux pour assurer leur persistance\n      this.loadVoiceMessages();\n      // S'abonner aux notifications en temps réel\n      this.subscribeToNotifications();\n      const routeSub = this.route.params.pipe(filter(params => params['id']), distinctUntilChanged(), switchMap(params => {\n        this.loading = true;\n        this.messages = [];\n        this.currentPage = 1; // Réinitialiser à la page 1\n        this.hasMoreMessages = true; // Réinitialiser l'indicateur de messages supplémentaires\n        this.logger.debug('MessageChat', `Loading conversation with pagination: page=${this.currentPage}, limit=${this.MAX_MESSAGES_TO_LOAD}`);\n        // Charger la conversation avec pagination (page 1, limit 10)\n        return this.MessageService.getConversation(params['id'], this.MAX_MESSAGES_TO_LOAD, this.currentPage // Utiliser la page au lieu de l'offset\n        );\n      })).subscribe({\n        next: conversation => {\n          this.handleConversationLoaded(conversation);\n        },\n        error: error => {\n          this.handleError('Failed to load conversation', error);\n        }\n      });\n      this.subscriptions.add(routeSub);\n    }\n    /**\n     * Charge les messages vocaux pour assurer leur persistance\n     */\n    loadVoiceMessages() {\n      this.logger.debug('MessageChat', 'Loading voice messages for persistence');\n      const sub = this.MessageService.getVoiceMessages().subscribe({\n        next: voiceMessages => {\n          this.logger.info('MessageChat', `Retrieved ${voiceMessages.length} voice messages`);\n          // Les messages vocaux sont maintenant chargés et disponibles dans le service\n          // Ils seront automatiquement associés aux conversations correspondantes\n          if (voiceMessages.length > 0) {\n            this.logger.debug('MessageChat', 'Voice messages loaded successfully');\n            // Forcer le rafraîchissement de la vue après le chargement des messages vocaux\n            setTimeout(() => {\n              this.cdr.detectChanges();\n              this.logger.debug('MessageChat', 'View refreshed after loading voice messages');\n            }, 100);\n          }\n        },\n        error: error => {\n          this.logger.error('MessageChat', 'Error loading voice messages:', error);\n          // Ne pas bloquer l'expérience utilisateur si le chargement des messages vocaux échoue\n        }\n      });\n\n      this.subscriptions.add(sub);\n    }\n    /**\n     * Gère les erreurs et les affiche à l'utilisateur\n     * @param message Message d'erreur à afficher\n     * @param error Objet d'erreur\n     */\n    handleError(message, error) {\n      this.logger.error('MessageChat', message, error);\n      this.loading = false;\n      this.error = error;\n      this.toastService.showError(message);\n    }\n    // logique FileService\n    getFileIcon(mimeType) {\n      if (!mimeType) return 'fa-file';\n      if (mimeType.startsWith('image/')) return 'fa-image';\n      if (mimeType.includes('pdf')) return 'fa-file-pdf';\n      if (mimeType.includes('word') || mimeType.includes('msword')) return 'fa-file-word';\n      if (mimeType.includes('excel')) return 'fa-file-excel';\n      if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n      if (mimeType.includes('audio')) return 'fa-file-audio';\n      if (mimeType.includes('video')) return 'fa-file-video';\n      if (mimeType.includes('zip') || mimeType.includes('compressed')) return 'fa-file-archive';\n      return 'fa-file';\n    }\n    getFileType(mimeType) {\n      if (!mimeType) return 'File';\n      const typeMap = {\n        'image/': 'Image',\n        'application/pdf': 'PDF',\n        'application/msword': 'Word Doc',\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Doc',\n        'application/vnd.ms-excel': 'Excel',\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',\n        'application/vnd.ms-powerpoint': 'PowerPoint',\n        'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint',\n        'audio/': 'Audio',\n        'video/': 'Video',\n        'application/zip': 'ZIP Archive',\n        'application/x-rar-compressed': 'RAR Archive'\n      };\n      for (const [key, value] of Object.entries(typeMap)) {\n        if (mimeType.includes(key)) return value;\n      }\n      return 'File';\n    }\n    handleConversationLoaded(conversation) {\n      this.logger.info('MessageChat', `Handling loaded conversation: ${conversation.id}`);\n      this.logger.debug('MessageChat', `Conversation has ${conversation?.messages?.length || 0} messages and ${conversation?.participants?.length || 0} participants`);\n      // Log détaillé des messages pour le débogage\n      if (conversation?.messages && conversation.messages.length > 0) {\n        this.logger.debug('MessageChat', `First message details: id=${conversation.messages[0].id}, content=${conversation.messages[0].content?.substring(0, 20)}, sender=${conversation.messages[0].sender?.username}`);\n      }\n      this.conversation = conversation;\n      // Si la conversation n'a pas de messages, initialiser un tableau vide\n      if (!conversation?.messages || conversation.messages.length === 0) {\n        this.logger.debug('MessageChat', 'No messages found in conversation');\n        // Récupérer les participants\n        this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n        // Initialiser un tableau vide pour les messages\n        this.messages = [];\n        this.logger.debug('MessageChat', 'Initialized empty messages array');\n      } else {\n        // Récupérer les messages de la conversation\n        const conversationMessages = [...(conversation?.messages || [])];\n        // Trier les messages par date (du plus ancien au plus récent)\n        conversationMessages.sort((a, b) => {\n          const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n          const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n          return timeA - timeB;\n        });\n        // Log détaillé pour comprendre la structure des messages\n        if (conversationMessages.length > 0) {\n          const firstMessage = conversationMessages[0];\n          this.logger.debug('MessageChat', `Message structure: sender.id=${firstMessage.sender?.id}, sender._id=${firstMessage.sender?._id}, senderId=${firstMessage.senderId}, receiver.id=${firstMessage.receiver?.id}, receiver._id=${firstMessage.receiver?._id}, receiverId=${firstMessage.receiverId}`);\n        }\n        // Utiliser directement tous les messages triés sans filtrage supplémentaire\n        this.messages = conversationMessages;\n        this.logger.debug('MessageChat', `Using all ${this.messages.length} messages from conversation`);\n        this.logger.debug('MessageChat', `Using ${conversationMessages.length} messages from conversation, showing last ${this.messages.length}`);\n      }\n      this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n      this.logger.debug('MessageChat', `Other participant identified: ${this.otherParticipant?.username || 'Unknown'}`);\n      this.loading = false;\n      setTimeout(() => this.scrollToBottom(), 100);\n      this.logger.debug('MessageChat', `Marking unread messages as read`);\n      this.markMessagesAsRead();\n      if (this.conversation?.id) {\n        this.logger.debug('MessageChat', `Setting up subscriptions for conversation: ${this.conversation.id}`);\n        this.subscribeToConversationUpdates(this.conversation.id);\n        this.subscribeToNewMessages(this.conversation.id);\n        this.subscribeToTypingIndicators(this.conversation.id);\n      }\n      this.logger.info('MessageChat', `Conversation loaded successfully`);\n    }\n    subscribeToConversationUpdates(conversationId) {\n      const sub = this.MessageService.subscribeToConversationUpdates(conversationId).subscribe({\n        next: updatedConversation => {\n          this.conversation = updatedConversation;\n          this.messages = updatedConversation.messages ? [...updatedConversation.messages] : [];\n          this.scrollToBottom();\n        },\n        error: error => {\n          this.toastService.showError('Connection to conversation updates lost');\n        }\n      });\n      this.subscriptions.add(sub);\n    }\n    subscribeToNewMessages(conversationId) {\n      const sub = this.MessageService.subscribeToNewMessages(conversationId).subscribe({\n        next: newMessage => {\n          if (newMessage?.conversationId === this.conversation?.id) {\n            // Ajouter le nouveau message à la liste complète\n            this.messages = [...this.messages, newMessage].sort((a, b) => {\n              const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n              const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n              return timeA - timeB; // Tri par ordre croissant pour l'affichage\n            });\n\n            this.logger.debug('MessageChat', `Added new message, now showing ${this.messages.length} messages`);\n            setTimeout(() => this.scrollToBottom(), 100);\n            // Marquer le message comme lu s'il vient d'un autre utilisateur\n            if (newMessage.sender?.id !== this.currentUserId && newMessage.sender?._id !== this.currentUserId) {\n              if (newMessage.id) {\n                this.MessageService.markMessageAsRead(newMessage.id).subscribe();\n              }\n            }\n          }\n        },\n        error: error => {\n          this.toastService.showError('Connection to new messages lost');\n        }\n      });\n      this.subscriptions.add(sub);\n    }\n    subscribeToTypingIndicators(conversationId) {\n      const sub = this.MessageService.subscribeToTypingIndicator(conversationId).subscribe({\n        next: event => {\n          if (event.userId !== this.currentUserId) {\n            this.isTyping = event.isTyping;\n            if (this.isTyping) {\n              clearTimeout(this.typingTimeout);\n              this.typingTimeout = setTimeout(() => {\n                this.isTyping = false;\n              }, 2000);\n            }\n          }\n        }\n      });\n      this.subscriptions.add(sub);\n    }\n    markMessagesAsRead() {\n      const unreadMessages = this.messages.filter(msg => !msg.isRead && (msg.receiver?.id === this.currentUserId || msg.receiver?._id === this.currentUserId));\n      unreadMessages.forEach(msg => {\n        if (msg.id) {\n          const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\n            error: error => {\n              this.logger.error('MessageChat', 'Error marking message as read:', error);\n            }\n          });\n          this.subscriptions.add(sub);\n        }\n      });\n    }\n    onFileSelected(event) {\n      const file = event.target.files[0];\n      if (!file) return;\n      // Validate file size (e.g., 5MB max)\n      if (file.size > 5 * 1024 * 1024) {\n        this.toastService.showError('File size should be less than 5MB');\n        return;\n      }\n      // Validate file type\n      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];\n      if (!validTypes.includes(file.type)) {\n        this.toastService.showError('Invalid file type. Only images, PDFs and Word docs are allowed');\n        return;\n      }\n      this.selectedFile = file;\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.previewUrl = reader.result;\n      };\n      reader.readAsDataURL(file);\n    }\n    removeAttachment() {\n      this.selectedFile = null;\n      this.previewUrl = null;\n      if (this.fileInput?.nativeElement) {\n        this.fileInput.nativeElement.value = '';\n      }\n    }\n    /**\n     * Gère l'événement de frappe de l'utilisateur\n     * Envoie un indicateur de frappe avec un délai pour éviter trop de requêtes\n     */\n    onTyping() {\n      if (!this.conversation?.id || !this.currentUserId) {\n        return;\n      }\n      // Stocker l'ID de conversation pour éviter les erreurs TypeScript\n      const conversationId = this.conversation.id;\n      // Annuler le timer précédent\n      clearTimeout(this.typingTimer);\n      // Si l'utilisateur n'est pas déjà en train de taper, envoyer l'événement immédiatement\n      if (!this.isCurrentlyTyping) {\n        this.isCurrentlyTyping = true;\n        this.logger.debug('MessageChat', 'Starting typing indicator');\n        this.MessageService.startTyping(conversationId).subscribe({\n          next: () => {\n            this.logger.debug('MessageChat', 'Typing indicator started successfully');\n          },\n          error: error => {\n            this.logger.error('MessageChat', 'Error starting typing indicator:', error);\n          }\n        });\n      }\n      // Définir un timer pour arrêter l'indicateur de frappe après un délai d'inactivité\n      this.typingTimer = setTimeout(() => {\n        if (this.isCurrentlyTyping) {\n          this.isCurrentlyTyping = false;\n          this.logger.debug('MessageChat', 'Stopping typing indicator due to inactivity');\n          this.MessageService.stopTyping(conversationId).subscribe({\n            next: () => {\n              this.logger.debug('MessageChat', 'Typing indicator stopped successfully');\n            },\n            error: error => {\n              this.logger.error('MessageChat', 'Error stopping typing indicator:', error);\n            }\n          });\n        }\n      }, this.TYPING_TIMEOUT);\n    }\n    /**\n     * Affiche ou masque le sélecteur de thème\n     */\n    toggleThemeSelector() {\n      this.showThemeSelector = !this.showThemeSelector;\n      // Fermer le sélecteur de thème lorsqu'on clique ailleurs\n      if (this.showThemeSelector) {\n        setTimeout(() => {\n          const clickHandler = event => {\n            const target = event.target;\n            if (!target.closest('.theme-selector')) {\n              this.showThemeSelector = false;\n              document.removeEventListener('click', clickHandler);\n            }\n          };\n          document.addEventListener('click', clickHandler);\n        }, 0);\n      }\n    }\n    /**\n     * Change le thème de la conversation\n     * @param theme Nom du thème à appliquer\n     */\n    changeTheme(theme) {\n      this.selectedTheme = theme;\n      this.showThemeSelector = false;\n      // Sauvegarder le thème dans le localStorage pour le conserver entre les sessions\n      localStorage.setItem('chat-theme', theme);\n      this.logger.debug('MessageChat', `Theme changed to: ${theme}`);\n    }\n    sendMessage() {\n      this.logger.info('MessageChat', `Attempting to send message`);\n      // Vérifier l'authentification\n      const token = localStorage.getItem('token');\n      this.logger.debug('MessageChat', `Authentication check: token=${!!token}, userId=${this.currentUserId}`);\n      if (this.messageForm.invalid && !this.selectedFile || !this.currentUserId || !this.otherParticipant?.id) {\n        this.logger.warn('MessageChat', `Cannot send message: form invalid or missing user IDs`);\n        return;\n      }\n      // Arrêter l'indicateur de frappe lorsqu'un message est envoyé\n      this.stopTypingIndicator();\n      const content = this.messageForm.get('content')?.value;\n      // Créer un message temporaire pour l'affichage immédiat (comme dans Facebook Messenger)\n      const tempMessage = {\n        id: 'temp-' + new Date().getTime(),\n        content: content || '',\n        sender: {\n          id: this.currentUserId || '',\n          username: this.currentUsername\n        },\n        receiver: {\n          id: this.otherParticipant.id,\n          username: this.otherParticipant.username || 'Recipient'\n        },\n        timestamp: new Date(),\n        isRead: false,\n        isPending: true // Marquer comme en attente\n      };\n      // Si un fichier est sélectionné, ajouter l'aperçu au message temporaire\n      if (this.selectedFile) {\n        // Déterminer le type de fichier\n        let fileType = 'file';\n        if (this.selectedFile.type.startsWith('image/')) {\n          fileType = 'image';\n          // Pour les images, ajouter un aperçu immédiat\n          if (this.previewUrl) {\n            tempMessage.attachments = [{\n              id: 'temp-attachment',\n              url: this.previewUrl ? this.previewUrl.toString() : '',\n              type: MessageType.IMAGE,\n              name: this.selectedFile.name,\n              size: this.selectedFile.size\n            }];\n          }\n        }\n        // Définir le type de message en fonction du type de fichier\n        if (fileType === 'image') {\n          tempMessage.type = MessageType.IMAGE;\n        } else if (fileType === 'file') {\n          tempMessage.type = MessageType.FILE;\n        }\n      }\n      // Ajouter immédiatement le message temporaire à la liste\n      this.messages = [...this.messages, tempMessage];\n      // Réinitialiser le formulaire immédiatement pour une meilleure expérience utilisateur\n      const fileToSend = this.selectedFile; // Sauvegarder une référence\n      this.messageForm.reset();\n      this.removeAttachment();\n      // Forcer le défilement vers le bas immédiatement\n      setTimeout(() => this.scrollToBottom(true), 50);\n      // Maintenant, envoyer le message au serveur\n      this.isUploading = true;\n      const sendSub = this.MessageService.sendMessage(this.otherParticipant.id, content, fileToSend || undefined, MessageType.TEXT).subscribe({\n        next: message => {\n          this.logger.info('MessageChat', `Message sent successfully: ${message?.id || 'unknown'}`);\n          // Remplacer le message temporaire par le message réel\n          this.messages = this.messages.map(msg => msg.id === tempMessage.id ? message : msg);\n          this.isUploading = false;\n        },\n        error: error => {\n          this.logger.error('MessageChat', `Error sending message:`, error);\n          // Marquer le message temporaire comme échoué\n          this.messages = this.messages.map(msg => {\n            if (msg.id === tempMessage.id) {\n              return {\n                ...msg,\n                isPending: false,\n                isError: true\n              };\n            }\n            return msg;\n          });\n          this.isUploading = false;\n          this.toastService.showError('Failed to send message');\n        }\n      });\n      this.subscriptions.add(sendSub);\n    }\n    formatMessageTime(timestamp) {\n      if (!timestamp) {\n        return 'Unknown time';\n      }\n      try {\n        const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n        // Format heure:minute sans les secondes, comme dans l'image de référence\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit',\n          hour12: false\n        });\n      } catch (error) {\n        this.logger.error('MessageChat', 'Error formatting message time:', error);\n        return 'Invalid time';\n      }\n    }\n    formatLastActive(lastActive) {\n      if (!lastActive) return 'Offline';\n      const lastActiveDate = lastActive instanceof Date ? lastActive : new Date(lastActive);\n      const now = new Date();\n      const diffHours = Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n      if (diffHours < 24) {\n        return `Active ${lastActiveDate.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        })}`;\n      }\n      return `Active ${lastActiveDate.toLocaleDateString()}`;\n    }\n    formatMessageDate(timestamp) {\n      if (!timestamp) {\n        return 'Unknown date';\n      }\n      try {\n        const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n        const today = new Date();\n        // Format pour l'affichage comme dans l'image de référence\n        const options = {\n          weekday: 'short',\n          hour: '2-digit',\n          minute: '2-digit'\n        };\n        if (date.toDateString() === today.toDateString()) {\n          return date.toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n          });\n        }\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        if (date.toDateString() === yesterday.toDateString()) {\n          return `LUN., ${date.toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n          })}`;\n        }\n        // Format pour les autres jours (comme dans l'image)\n        const day = date.toLocaleDateString('fr-FR', {\n          weekday: 'short'\n        }).toUpperCase();\n        return `${day}., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        })}`;\n      } catch (error) {\n        this.logger.error('MessageChat', 'Error formatting message date:', error);\n        return 'Invalid date';\n      }\n    }\n    shouldShowDateHeader(index) {\n      if (index === 0) return true;\n      try {\n        const currentMsg = this.messages[index];\n        const prevMsg = this.messages[index - 1];\n        if (!currentMsg?.timestamp || !prevMsg?.timestamp) {\n          return true;\n        }\n        const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n        const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n        return currentDate !== prevDate;\n      } catch (error) {\n        this.logger.error('MessageChat', 'Error checking date header:', error);\n        return false;\n      }\n    }\n    getDateFromTimestamp(timestamp) {\n      if (!timestamp) {\n        return 'unknown-date';\n      }\n      try {\n        return (timestamp instanceof Date ? timestamp : new Date(timestamp)).toDateString();\n      } catch (error) {\n        this.logger.error('MessageChat', 'Error getting date from timestamp:', error);\n        return 'invalid-date';\n      }\n    }\n    getMessageType(message) {\n      if (!message) {\n        return MessageType.TEXT;\n      }\n      try {\n        // Vérifier d'abord le type de message explicite\n        if (message.type) {\n          // Convertir les types en minuscules en leurs équivalents en majuscules\n          const msgType = message.type.toString();\n          if (msgType === 'text' || msgType === 'TEXT') {\n            return MessageType.TEXT;\n          } else if (msgType === 'image' || msgType === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (msgType === 'file' || msgType === 'FILE') {\n            return MessageType.FILE;\n          } else if (msgType === 'audio' || msgType === 'AUDIO') {\n            return MessageType.AUDIO;\n          } else if (msgType === 'video' || msgType === 'VIDEO') {\n            return MessageType.VIDEO;\n          } else if (msgType === 'system' || msgType === 'SYSTEM') {\n            return MessageType.SYSTEM;\n          }\n        }\n        // Ensuite, vérifier les pièces jointes\n        if (message.attachments?.length) {\n          const attachment = message.attachments[0];\n          if (attachment && attachment.type) {\n            const attachmentTypeStr = attachment.type.toString();\n            // Gérer les différentes formes de types d'attachements\n            if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n              return MessageType.IMAGE;\n            } else if (attachmentTypeStr === 'file' || attachmentTypeStr === 'FILE') {\n              return MessageType.FILE;\n            } else if (attachmentTypeStr === 'audio' || attachmentTypeStr === 'AUDIO') {\n              return MessageType.AUDIO;\n            } else if (attachmentTypeStr === 'video' || attachmentTypeStr === 'VIDEO') {\n              return MessageType.VIDEO;\n            }\n          }\n          // Type par défaut pour les pièces jointes\n          return MessageType.FILE;\n        }\n        // Type par défaut\n        return MessageType.TEXT;\n      } catch (error) {\n        this.logger.error('MessageChat', 'Error getting message type:', error);\n        return MessageType.TEXT;\n      }\n    }\n    // Méthode auxiliaire pour vérifier si un message contient une image\n    hasImage(message) {\n      if (!message || !message.attachments || message.attachments.length === 0) {\n        return false;\n      }\n      const attachment = message.attachments[0];\n      if (!attachment || !attachment.type) {\n        return false;\n      }\n      const type = attachment.type.toString();\n      return type === 'IMAGE' || type === 'image';\n    }\n    /**\n     * Vérifie si le message est un message vocal\n     */\n    isVoiceMessage(message) {\n      if (!message) {\n        return false;\n      }\n      // Vérifier le type du message\n      if (message.type === MessageType.VOICE_MESSAGE || message.type === MessageType.VOICE_MESSAGE_LOWER) {\n        return true;\n      }\n      // Vérifier les pièces jointes\n      if (message.attachments && message.attachments.length > 0) {\n        return message.attachments.some(att => {\n          const type = att.type?.toString();\n          return type === 'VOICE_MESSAGE' || type === 'voice_message' || message.metadata?.isVoiceMessage && (type === 'AUDIO' || type === 'audio');\n        });\n      }\n      // Vérifier les métadonnées\n      return !!message.metadata?.isVoiceMessage;\n    }\n    /**\n     * Récupère l'URL du message vocal\n     */\n    getVoiceMessageUrl(message) {\n      if (!message || !message.attachments || message.attachments.length === 0) {\n        return '';\n      }\n      // Chercher une pièce jointe de type message vocal ou audio\n      const voiceAttachment = message.attachments.find(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n      });\n      return voiceAttachment?.url || '';\n    }\n    /**\n     * Récupère la durée du message vocal\n     */\n    getVoiceMessageDuration(message) {\n      if (!message) {\n        return 0;\n      }\n      // Essayer d'abord de récupérer la durée depuis les métadonnées\n      if (message.metadata?.duration) {\n        return message.metadata.duration;\n      }\n      // Sinon, essayer de récupérer depuis les pièces jointes\n      if (message.attachments && message.attachments.length > 0) {\n        const voiceAttachment = message.attachments.find(att => {\n          const type = att.type?.toString();\n          return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n        });\n        if (voiceAttachment && voiceAttachment.duration) {\n          return voiceAttachment.duration;\n        }\n      }\n      return 0;\n    }\n    // Méthode pour obtenir l'URL de l'image en toute sécurité\n    getImageUrl(message) {\n      if (!message || !message.attachments || message.attachments.length === 0) {\n        return '';\n      }\n      const attachment = message.attachments[0];\n      return attachment?.url || '';\n    }\n    getMessageTypeClass(message) {\n      if (!message) {\n        return 'bg-gray-100 rounded-lg px-4 py-2';\n      }\n      try {\n        const isCurrentUser = message.sender?.id === this.currentUserId || message.sender?._id === this.currentUserId || message.senderId === this.currentUserId;\n        // Utiliser une couleur plus foncée pour les messages de l'utilisateur actuel (à droite)\n        // et une couleur plus claire pour les messages des autres utilisateurs (à gauche)\n        // Couleurs et forme adaptées exactement à l'image de référence mobile\n        const baseClass = isCurrentUser ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm' : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n        const messageType = this.getMessageType(message);\n        // Vérifier si le message contient une image\n        if (message.attachments && message.attachments.length > 0) {\n          const attachment = message.attachments[0];\n          if (attachment && attachment.type) {\n            const attachmentTypeStr = attachment.type.toString();\n            if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n              // Pour les images, on utilise un style sans bordure\n              return `p-1 max-w-xs`;\n            } else if (attachmentTypeStr === 'FILE' || attachmentTypeStr === 'file') {\n              return `${baseClass} p-3`;\n            }\n          }\n        }\n        // Vérifier le type de message\n        if (messageType === MessageType.IMAGE || messageType === MessageType.IMAGE_LOWER) {\n          // Pour les images, on utilise un style sans bordure\n          return `p-1 max-w-xs`;\n        } else if (messageType === MessageType.FILE || messageType === MessageType.FILE_LOWER) {\n          return `${baseClass} p-3`;\n        }\n        // Type par défaut (texte)\n        return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n      } catch (error) {\n        this.logger.error('MessageChat', 'Error getting message type class:', error);\n        return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n      }\n    }\n    // La méthode ngAfterViewChecked est implémentée plus bas dans le fichier\n    // Méthode pour détecter le défilement vers le haut et charger plus de messages\n    onScroll(event) {\n      const container = event.target;\n      const scrollTop = container.scrollTop;\n      // Si on est proche du haut de la liste et qu'on n'est pas déjà en train de charger\n      if (scrollTop < 50 && !this.isLoadingMore && this.conversation?.id && this.hasMoreMessages) {\n        // Afficher un indicateur de chargement en haut de la liste\n        this.showLoadingIndicator();\n        // Sauvegarder la hauteur actuelle et la position des messages\n        const oldScrollHeight = container.scrollHeight;\n        const firstVisibleMessage = this.getFirstVisibleMessage();\n        // Marquer comme chargement en cours\n        this.isLoadingMore = true;\n        // Charger plus de messages avec un délai réduit\n        this.loadMoreMessages();\n        // Maintenir la position de défilement pour que l'utilisateur reste au même endroit\n        // en utilisant le premier message visible comme ancre\n        requestAnimationFrame(() => {\n          const preserveScrollPosition = () => {\n            if (firstVisibleMessage) {\n              const messageElement = this.findMessageElement(firstVisibleMessage.id);\n              if (messageElement) {\n                // Faire défiler jusqu'à l'élément qui était visible avant\n                messageElement.scrollIntoView({\n                  block: 'center'\n                });\n              } else {\n                // Fallback: utiliser la différence de hauteur\n                const newScrollHeight = container.scrollHeight;\n                const scrollDiff = newScrollHeight - oldScrollHeight;\n                container.scrollTop = scrollTop + scrollDiff;\n              }\n            }\n            // Masquer l'indicateur de chargement\n            this.hideLoadingIndicator();\n          };\n          // Attendre que le DOM soit mis à jour\n          setTimeout(preserveScrollPosition, 100);\n        });\n      }\n    }\n    // Méthode pour trouver le premier message visible dans la vue\n    getFirstVisibleMessage() {\n      if (!this.messagesContainer?.nativeElement || !this.messages.length) return null;\n      const container = this.messagesContainer.nativeElement;\n      const messageElements = container.querySelectorAll('.message-item');\n      for (let i = 0; i < messageElements.length; i++) {\n        const element = messageElements[i];\n        const rect = element.getBoundingClientRect();\n        // Si l'élément est visible dans la vue\n        if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\n          const messageId = element.getAttribute('data-message-id');\n          return this.messages.find(m => m.id === messageId) || null;\n        }\n      }\n      return null;\n    }\n    // Méthode pour trouver un élément de message par ID\n    findMessageElement(messageId) {\n      if (!this.messagesContainer?.nativeElement || !messageId) return null;\n      return this.messagesContainer.nativeElement.querySelector(`[data-message-id=\"${messageId}\"]`);\n    }\n    // Afficher un indicateur de chargement en haut de la liste\n    showLoadingIndicator() {\n      // Créer l'indicateur s'il n'existe pas déjà\n      if (!document.getElementById('message-loading-indicator')) {\n        const indicator = document.createElement('div');\n        indicator.id = 'message-loading-indicator';\n        indicator.className = 'text-center py-2 text-gray-500 text-sm';\n        indicator.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\n        if (this.messagesContainer?.nativeElement) {\n          this.messagesContainer.nativeElement.prepend(indicator);\n        }\n      }\n    }\n    // Masquer l'indicateur de chargement\n    hideLoadingIndicator() {\n      const indicator = document.getElementById('message-loading-indicator');\n      if (indicator && indicator.parentNode) {\n        indicator.parentNode.removeChild(indicator);\n      }\n    }\n    // Méthode pour charger plus de messages (style Facebook Messenger)\n    loadMoreMessages() {\n      if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages) return;\n      // Marquer comme chargement en cours\n      this.isLoadingMore = true;\n      // Augmenter la page pour charger les messages plus anciens\n      this.currentPage++;\n      // Charger plus de messages depuis le serveur avec pagination\n      this.MessageService.getConversation(this.conversation.id, this.MAX_MESSAGES_TO_LOAD, this.currentPage).subscribe({\n        next: conversation => {\n          if (conversation && conversation.messages && conversation.messages.length > 0) {\n            // Sauvegarder les messages actuels\n            const oldMessages = [...this.messages];\n            // Créer un Set des IDs existants pour une recherche de doublons plus rapide\n            const existingIds = new Set(oldMessages.map(msg => msg.id));\n            // Filtrer et trier les nouveaux messages plus efficacement\n            const newMessages = conversation.messages.filter(msg => !existingIds.has(msg.id)).sort((a, b) => {\n              const timeA = new Date(a.timestamp).getTime();\n              const timeB = new Date(b.timestamp).getTime();\n              return timeA - timeB;\n            });\n            if (newMessages.length > 0) {\n              // Ajouter les nouveaux messages au début de la liste\n              this.messages = [...newMessages, ...oldMessages];\n              // Limiter le nombre total de messages pour éviter les problèmes de performance\n              if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\n                this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\n              }\n              // Vérifier s'il y a plus de messages à charger\n              this.hasMoreMessages = newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\n            } else {\n              // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\n              this.hasMoreMessages = false;\n            }\n          } else {\n            this.hasMoreMessages = false;\n          }\n          // Désactiver le flag de chargement après un court délai\n          // pour permettre au DOM de se mettre à jour\n          setTimeout(() => {\n            this.isLoadingMore = false;\n          }, 200);\n        },\n        error: error => {\n          this.logger.error('MessageChat', 'Error loading more messages:', error);\n          this.isLoadingMore = false;\n          this.hideLoadingIndicator();\n          this.toastService.showError('Failed to load more messages');\n        }\n      });\n    }\n    // Méthode utilitaire pour comparer les timestamps\n    isSameTimestamp(timestamp1, timestamp2) {\n      if (!timestamp1 || !timestamp2) return false;\n      try {\n        const time1 = timestamp1 instanceof Date ? timestamp1.getTime() : new Date(timestamp1).getTime();\n        const time2 = timestamp2 instanceof Date ? timestamp2.getTime() : new Date(timestamp2).getTime();\n        return Math.abs(time1 - time2) < 1000; // Tolérance d'une seconde\n      } catch (error) {\n        return false;\n      }\n    }\n    scrollToBottom(force = false) {\n      try {\n        if (!this.messagesContainer?.nativeElement) return;\n        // Utiliser requestAnimationFrame pour s'assurer que le DOM est prêt\n        requestAnimationFrame(() => {\n          const container = this.messagesContainer.nativeElement;\n          const isScrolledToBottom = container.scrollHeight - container.clientHeight <= container.scrollTop + 150;\n          // Faire défiler vers le bas si:\n          // - force est true (pour les nouveaux messages envoyés par l'utilisateur)\n          // - ou si l'utilisateur est déjà proche du bas\n          if (force || isScrolledToBottom) {\n            // Utiliser une animation fluide pour le défilement (comme dans Messenger)\n            container.scrollTo({\n              top: container.scrollHeight,\n              behavior: 'smooth'\n            });\n          }\n        });\n      } catch (err) {\n        this.logger.error('MessageChat', 'Error scrolling to bottom:', err);\n      }\n    }\n    // Méthode pour ouvrir l'image en plein écran (style Messenger)\n    /**\n     * Active/désactive l'enregistrement vocal\n     */\n    toggleVoiceRecording() {\n      this.isRecordingVoice = !this.isRecordingVoice;\n      if (!this.isRecordingVoice) {\n        // Si on désactive l'enregistrement, réinitialiser la durée\n        this.voiceRecordingDuration = 0;\n      }\n    }\n    /**\n     * Gère la fin de l'enregistrement vocal\n     * @param audioBlob Blob audio enregistré\n     */\n    onVoiceRecordingComplete(audioBlob) {\n      this.logger.debug('MessageChat', 'Voice recording complete, size:', audioBlob.size);\n      if (!this.conversation?.id && !this.otherParticipant?.id) {\n        this.toastService.showError('No conversation or recipient selected');\n        this.isRecordingVoice = false;\n        return;\n      }\n      // Récupérer l'ID du destinataire\n      const receiverId = this.otherParticipant?.id || '';\n      // Envoyer le message vocal\n      this.MessageService.sendVoiceMessage(receiverId, audioBlob, this.conversation?.id, this.voiceRecordingDuration).subscribe({\n        next: message => {\n          this.logger.debug('MessageChat', 'Voice message sent:', message);\n          this.isRecordingVoice = false;\n          this.voiceRecordingDuration = 0;\n          this.scrollToBottom(true);\n        },\n        error: error => {\n          this.logger.error('MessageChat', 'Error sending voice message:', error);\n          this.toastService.showError('Failed to send voice message');\n          this.isRecordingVoice = false;\n        }\n      });\n    }\n    /**\n     * Gère l'annulation de l'enregistrement vocal\n     */\n    onVoiceRecordingCancelled() {\n      this.logger.debug('MessageChat', 'Voice recording cancelled');\n      this.isRecordingVoice = false;\n      this.voiceRecordingDuration = 0;\n    }\n    /**\n     * Ouvre une image en plein écran (méthode conservée pour compatibilité)\n     * @param imageUrl URL de l'image à afficher\n     */\n    openImageFullscreen(imageUrl) {\n      // Ouvrir l'image dans un nouvel onglet\n      window.open(imageUrl, '_blank');\n      this.logger.debug('MessageChat', `Image opened in new tab: ${imageUrl}`);\n    }\n    /**\n     * Détecte les changements après chaque vérification de la vue\n     * Cela permet de s'assurer que les messages vocaux sont correctement affichés\n     * et que le défilement est maintenu\n     */\n    ngAfterViewChecked() {\n      // Faire défiler vers le bas si nécessaire\n      this.scrollToBottom();\n      // Forcer la détection des changements pour les messages vocaux\n      // Cela garantit que les messages vocaux sont correctement affichés même après avoir quitté la conversation\n      if (this.messages.some(msg => msg.type === MessageType.VOICE_MESSAGE)) {\n        // Utiliser setTimeout pour éviter l'erreur ExpressionChangedAfterItHasBeenCheckedError\n        setTimeout(() => {\n          this.cdr.detectChanges();\n        }, 0);\n      }\n    }\n    /**\n     * Arrête l'indicateur de frappe\n     */\n    stopTypingIndicator() {\n      if (this.isCurrentlyTyping && this.conversation?.id) {\n        this.isCurrentlyTyping = false;\n        clearTimeout(this.typingTimer);\n        this.logger.debug('MessageChat', 'Stopping typing indicator');\n        // Utiliser l'opérateur de chaînage optionnel pour éviter les erreurs TypeScript\n        const conversationId = this.conversation?.id;\n        if (conversationId) {\n          this.MessageService.stopTyping(conversationId).subscribe({\n            next: () => {\n              this.logger.debug('MessageChat', 'Typing indicator stopped successfully');\n            },\n            error: error => {\n              this.logger.error('MessageChat', 'Error stopping typing indicator:', error);\n            }\n          });\n        }\n      }\n    }\n    ngOnDestroy() {\n      // Arrêter l'indicateur de frappe lorsque l'utilisateur quitte la conversation\n      this.stopTypingIndicator();\n      this.subscriptions.unsubscribe();\n      clearTimeout(this.typingTimeout);\n    }\n    /**\n     * Navigue vers la liste des conversations\n     */\n    goBackToConversations() {\n      this.router.navigate(['/messages/conversations']);\n    }\n    /**\n     * Bascule l'affichage du sélecteur d'émojis\n     */\n    toggleEmojiPicker() {\n      this.showEmojiPicker = !this.showEmojiPicker;\n      if (this.showEmojiPicker) {\n        this.showThemeSelector = false;\n      }\n    }\n    /**\n     * Insère un emoji dans le champ de message\n     * @param emoji Emoji à insérer\n     */\n    insertEmoji(emoji) {\n      const control = this.messageForm.get('content');\n      if (control) {\n        const currentValue = control.value || '';\n        control.setValue(currentValue + emoji);\n        control.markAsDirty();\n        // Garder le focus sur le champ de saisie\n        setTimeout(() => {\n          const inputElement = document.querySelector('.whatsapp-input-field');\n          if (inputElement) {\n            inputElement.focus();\n          }\n        }, 0);\n      }\n    }\n    /**\n     * S'abonne aux notifications en temps réel\n     */\n    subscribeToNotifications() {\n      // S'abonner aux nouvelles notifications\n      const notificationSub = this.MessageService.subscribeToNewNotifications().subscribe({\n        next: notification => {\n          this.logger.debug('MessageChat', `Nouvelle notification reçue: ${notification.type}`);\n          // Si c'est une notification de message et que nous sommes dans la conversation concernée\n          if (notification.type === 'NEW_MESSAGE' && notification.conversationId === this.conversation?.id) {\n            // Marquer automatiquement comme lue\n            if (notification.id) {\n              this.MessageService.markAsRead([notification.id]).subscribe();\n            }\n          }\n        },\n        error: error => {\n          this.logger.error('MessageChat', 'Erreur lors de la réception des notifications:', error);\n        }\n      });\n      this.subscriptions.add(notificationSub);\n      // S'abonner aux appels entrants\n      const callSub = this.MessageService.incomingCall$.subscribe({\n        next: call => {\n          if (call) {\n            this.logger.debug('MessageChat', `Appel entrant de: ${call.caller.username}`);\n            this.incomingCall = call;\n            this.showCallModal = true;\n            // Jouer la sonnerie\n            this.MessageService.play('ringtone');\n          } else {\n            this.showCallModal = false;\n            this.incomingCall = null;\n          }\n        }\n      });\n      this.subscriptions.add(callSub);\n    }\n    /**\n     * Initie un appel audio ou vidéo avec l'autre participant\n     * @param type Type d'appel (AUDIO ou VIDEO)\n     */\n    initiateCall(type) {\n      if (!this.otherParticipant || !this.otherParticipant.id) {\n        console.error(\"Impossible d'initier un appel: participant invalide\");\n        return;\n      }\n      this.logger.info('MessageChat', `Initiation d'un appel ${type} avec ${this.otherParticipant.username}`);\n      // Utiliser le service d'appel pour initier l'appel\n      this.MessageService.initiateCall(this.otherParticipant.id, type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO, this.conversation?.id).subscribe({\n        next: call => {\n          this.logger.info('MessageChat', 'Appel initié avec succès:', call);\n          // Ici, vous pourriez ouvrir une fenêtre d'appel ou rediriger vers une page d'appel\n        },\n\n        error: error => {\n          this.logger.error('MessageChat', \"Erreur lors de l'initiation de l'appel:\", error);\n          this.toastService.showError(\"Impossible d'initier l'appel. Veuillez réessayer.\");\n        }\n      });\n    }\n    /**\n     * Accepte un appel entrant\n     */\n    acceptCall() {\n      if (!this.incomingCall) {\n        this.logger.error('MessageChat', 'Aucun appel entrant à accepter');\n        return;\n      }\n      this.logger.info('MessageChat', `Acceptation de l'appel de ${this.incomingCall.caller.username}`);\n      this.MessageService.acceptCall(this.incomingCall.id).subscribe({\n        next: call => {\n          this.logger.info('MessageChat', 'Appel accepté avec succès:', call);\n          this.showCallModal = false;\n          // Ici, vous pourriez ouvrir une fenêtre d'appel ou rediriger vers une page d'appel\n        },\n\n        error: error => {\n          this.logger.error('MessageChat', \"Erreur lors de l'acceptation de l'appel:\", error);\n          this.toastService.showError(\"Impossible d'accepter l'appel. Veuillez réessayer.\");\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      });\n    }\n    /**\n     * Rejette un appel entrant\n     */\n    rejectCall() {\n      if (!this.incomingCall) {\n        this.logger.error('MessageChat', 'Aucun appel entrant à rejeter');\n        return;\n      }\n      this.logger.info('MessageChat', `Rejet de l'appel de ${this.incomingCall.caller.username}`);\n      this.MessageService.rejectCall(this.incomingCall.id).subscribe({\n        next: call => {\n          this.logger.info('MessageChat', 'Appel rejeté avec succès:', call);\n          this.showCallModal = false;\n          this.incomingCall = null;\n        },\n        error: error => {\n          this.logger.error('MessageChat', \"Erreur lors du rejet de l'appel:\", error);\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      });\n    }\n    /**\n     * Termine un appel en cours\n     */\n    endCall() {\n      // Utiliser une variable pour stocker la dernière valeur de l'observable\n      let activeCall = null;\n      // S'abonner à l'observable pour obtenir la valeur actuelle\n      const sub = this.MessageService.activeCall$.subscribe(call => {\n        activeCall = call;\n        if (!activeCall) {\n          this.logger.error('MessageChat', 'Aucun appel actif à terminer');\n          return;\n        }\n        this.logger.info('MessageChat', `Fin de l'appel`);\n        this.MessageService.endCall(activeCall.id).subscribe({\n          next: call => {\n            this.logger.info('MessageChat', 'Appel terminé avec succès:', call);\n          },\n          error: error => {\n            this.logger.error('MessageChat', \"Erreur lors de la fin de l'appel:\", error);\n          }\n        });\n      });\n      // Se désabonner immédiatement après avoir obtenu la valeur\n      sub.unsubscribe();\n    }\n    static {\n      this.ɵfac = function MessageChatComponent_Factory(t) {\n        return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.UserStatusService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i7.LoggerService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: MessageChatComponent,\n        selectors: [[\"app-message-chat\"]],\n        viewQuery: function MessageChatComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n            i0.ɵɵviewQuery(_c1, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          }\n        },\n        decls: 74,\n        vars: 23,\n        consts: [[1, \"flex\", \"flex-col\", \"h-full\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"relative\", \"overflow-hidden\", \"futuristic-chat-container\", \"dark\", 3, \"ngClass\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"grid\", \"grid-rows-12\"], [1, \"border-b\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"absolute\", \"inset-0\", \"opacity-0\", \"dark:opacity-100\", \"overflow-hidden\"], [1, \"h-px\", \"w-full\", \"bg-[#00f7ff]/20\", \"absolute\", \"animate-scan\"], [1, \"whatsapp-chat-header\"], [1, \"whatsapp-action-button\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"whatsapp-user-info\"], [1, \"whatsapp-avatar\"], [\"alt\", \"User avatar\", 3, \"src\"], [\"class\", \"whatsapp-online-indicator\", 4, \"ngIf\"], [\"class\", \"whatsapp-user-details\", 4, \"ngIf\"], [1, \"whatsapp-actions\"], [1, \"fas\", \"fa-phone-alt\"], [1, \"fas\", \"fa-video\"], [1, \"relative\"], [1, \"fas\", \"fa-palette\"], [\"class\", \"absolute right-0 mt-2 w-48 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\", 4, \"ngIf\"], [1, \"whatsapp-action-button\"], [1, \"fas\", \"fa-ellipsis-v\"], [1, \"futuristic-messages-container\", 3, \"scroll\"], [\"messagesContainer\", \"\"], [\"class\", \"flex justify-center items-center h-full\", 4, \"ngIf\"], [\"class\", \"flex justify-center py-2 sticky top-0 z-10\", 4, \"ngIf\"], [\"class\", \"flex justify-center py-2 mb-2\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-md my-4 backdrop-blur-sm\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [\"noMessages\", \"\"], [\"class\", \"futuristic-typing-indicator\", 4, \"ngIf\"], [1, \"whatsapp-input-container\"], [\"class\", \"whatsapp-file-preview\", 4, \"ngIf\"], [\"class\", \"whatsapp-emoji-picker\", 4, \"ngIf\"], [1, \"whatsapp-input-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"whatsapp-input-tools\"], [\"type\", \"button\", 1, \"whatsapp-tool-button\", 3, \"ngClass\", \"click\"], [1, \"far\", \"fa-smile\"], [\"type\", \"button\", 1, \"whatsapp-tool-button\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [\"type\", \"file\", \"accept\", \"image/*\", 1, \"hidden\", 3, \"change\"], [\"fileInput\", \"\"], [3, \"maxDuration\", \"recordingComplete\", \"recordingCancelled\", 4, \"ngIf\"], [\"formControlName\", \"content\", \"type\", \"text\", \"placeholder\", \"Message\", \"class\", \"whatsapp-input-field\", 3, \"input\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"whatsapp-voice-button\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"submit\", \"class\", \"whatsapp-send-button\", 3, \"disabled\", 4, \"ngIf\"], [\"class\", \"whatsapp-call-modal\", 4, \"ngIf\"], [1, \"whatsapp-online-indicator\"], [1, \"whatsapp-user-details\"], [1, \"whatsapp-username\"], [1, \"whatsapp-status\"], [1, \"absolute\", \"right-0\", \"mt-2\", \"w-48\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\", \"shadow-lg\", \"z-50\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"p-2\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"p-1\"], [\"href\", \"javascript:void(0)\", 1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-center\"], [1, \"w-4\", \"h-4\", \"rounded-full\", \"bg-[#4f5fad]\", \"mr-2\"], [\"href\", \"javascript:void(0)\", 1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-[#ff6b9d]/10\", \"dark:hover:bg-[#ff6b9d]/10\", \"transition-colors\", 3, \"click\"], [1, \"w-4\", \"h-4\", \"rounded-full\", \"bg-[#ff6b9d]\", \"mr-2\"], [\"href\", \"javascript:void(0)\", 1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-[#3d85c6]/10\", \"dark:hover:bg-[#3d85c6]/10\", \"transition-colors\", 3, \"click\"], [1, \"w-4\", \"h-4\", \"rounded-full\", \"bg-[#3d85c6]\", \"mr-2\"], [\"href\", \"javascript:void(0)\", 1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-[#6aa84f]/10\", \"dark:hover:bg-[#6aa84f]/10\", \"transition-colors\", 3, \"click\"], [1, \"w-4\", \"h-4\", \"rounded-full\", \"bg-[#6aa84f]\", \"mr-2\"], [1, \"flex\", \"justify-center\", \"items-center\", \"h-full\"], [1, \"flex\", \"flex-col\", \"items-center\"], [1, \"w-12\", \"h-12\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\", \"mb-3\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-3\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"font-medium\"], [1, \"flex\", \"justify-center\", \"py-2\", \"sticky\", \"top-0\", \"z-10\"], [1, \"flex\", \"flex-col\", \"items-center\", \"backdrop-blur-sm\", \"bg-white/50\", \"dark:bg-[#1e1e1e]/50\", \"px-4\", \"py-2\", \"rounded-full\", \"shadow-sm\"], [1, \"flex\", \"space-x-2\", \"mb-1\"], [1, \"w-2\", \"h-2\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"rounded-full\", \"animate-pulse\", \"shadow-[0_0_5px_rgba(79,95,173,0.5)]\", \"dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\"], [1, \"w-2\", \"h-2\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"rounded-full\", \"animate-pulse\", \"shadow-[0_0_5px_rgba(79,95,173,0.5)]\", \"dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\", 2, \"animation-delay\", \"0.2s\"], [1, \"w-2\", \"h-2\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"rounded-full\", \"animate-pulse\", \"shadow-[0_0_5px_rgba(79,95,173,0.5)]\", \"dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\", 2, \"animation-delay\", \"0.4s\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"justify-center\", \"py-2\", \"mb-2\"], [1, \"flex\", \"items-center\", \"w-full\", \"max-w-xs\"], [1, \"flex-1\", \"h-px\", \"bg-gradient-to-r\", \"from-transparent\", \"via-[#4f5fad]/20\", \"dark:via-[#6d78c9]/20\", \"to-transparent\"], [1, \"px-3\", \"text-xs\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"font-medium\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-md\", \"my-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"font-medium\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"class\", \"futuristic-message-wrapper\", 4, \"ngFor\", \"ngForOf\"], [1, \"futuristic-message-wrapper\"], [\"class\", \"futuristic-date-separator\", 4, \"ngIf\"], [1, \"futuristic-message\", 3, \"ngClass\"], [\"class\", \"futuristic-avatar\", 4, \"ngIf\"], [1, \"futuristic-message-content\"], [\"class\", \"futuristic-message-bubble\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"futuristic-message-bubble futuristic-voice-message-container\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"futuristic-message-image-container\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"futuristic-date-separator\"], [1, \"futuristic-date-line\"], [1, \"futuristic-date-text\"], [1, \"futuristic-avatar\"], [\"alt\", \"User avatar\", \"onerror\", \"this.src='assets/images/default-avatar.png'\", 3, \"src\"], [1, \"futuristic-message-bubble\", 3, \"ngClass\"], [1, \"futuristic-message-text\"], [1, \"futuristic-message-info\"], [1, \"futuristic-message-time\"], [\"class\", \"futuristic-message-status\", 4, \"ngIf\"], [1, \"futuristic-message-status\"], [\"class\", \"fas fa-check-double\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", 4, \"ngIf\"], [1, \"fas\", \"fa-check-double\"], [1, \"fas\", \"fa-check\"], [1, \"futuristic-message-bubble\", \"futuristic-voice-message-container\", 3, \"ngClass\"], [1, \"futuristic-voice-message\"], [1, \"futuristic-voice-play-button\"], [1, \"fas\", \"fa-play\"], [1, \"futuristic-voice-waveform\"], [\"class\", \"futuristic-voice-bar\", 3, \"height\", 4, \"ngFor\", \"ngForOf\"], [1, \"messenger-style-voice-message\", 2, \"display\", \"none\", 3, \"audioUrl\", \"duration\"], [1, \"futuristic-voice-bar\"], [1, \"futuristic-message-image-container\", 3, \"ngClass\"], [1, \"futuristic-image-wrapper\"], [\"target\", \"_blank\", 1, \"futuristic-message-image-link\", 3, \"href\"], [\"alt\", \"Image\", 1, \"futuristic-message-image\", 3, \"src\"], [1, \"futuristic-image-overlay\"], [1, \"fas\", \"fa-expand\"], [\"class\", \"futuristic-no-messages\", 4, \"ngIf\"], [1, \"futuristic-no-messages\"], [1, \"futuristic-no-messages-icon\"], [1, \"fas\", \"fa-satellite-dish\"], [1, \"futuristic-no-messages-text\"], [1, \"futuristic-start-button\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"futuristic-typing-indicator\"], [1, \"futuristic-typing-bubble\"], [1, \"futuristic-typing-dots\"], [1, \"futuristic-typing-dot\"], [1, \"futuristic-typing-dot\", 2, \"animation-delay\", \"0.2s\"], [1, \"futuristic-typing-dot\", 2, \"animation-delay\", \"0.4s\"], [1, \"whatsapp-file-preview\"], [1, \"whatsapp-preview-image\", 3, \"src\"], [1, \"whatsapp-remove-button\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"whatsapp-emoji-picker\"], [1, \"whatsapp-emoji-categories\"], [1, \"whatsapp-emoji-category\", \"active\"], [1, \"whatsapp-emoji-category\"], [1, \"fas\", \"fa-cat\"], [1, \"fas\", \"fa-hamburger\"], [1, \"fas\", \"fa-futbol\"], [1, \"fas\", \"fa-car\"], [1, \"fas\", \"fa-lightbulb\"], [1, \"whatsapp-emoji-list\"], [\"class\", \"whatsapp-emoji-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"whatsapp-emoji-item\", 3, \"click\"], [3, \"maxDuration\", \"recordingComplete\", \"recordingCancelled\"], [\"formControlName\", \"content\", \"type\", \"text\", \"placeholder\", \"Message\", 1, \"whatsapp-input-field\", 3, \"input\"], [\"type\", \"button\", 1, \"whatsapp-voice-button\", 3, \"click\"], [1, \"fas\", \"fa-microphone\"], [\"type\", \"submit\", 1, \"whatsapp-send-button\", 3, \"disabled\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"whatsapp-call-modal\"], [1, \"whatsapp-call-modal-content\"], [1, \"whatsapp-call-header\"], [1, \"whatsapp-call-avatar\"], [\"alt\", \"Caller avatar\", 3, \"src\"], [1, \"whatsapp-call-name\"], [1, \"whatsapp-call-status\"], [1, \"whatsapp-call-actions\"], [1, \"whatsapp-call-reject\", 3, \"click\"], [1, \"fas\", \"fa-phone-slash\"], [1, \"whatsapp-call-accept\", 3, \"click\"], [1, \"fas\", \"fa-phone\"]],\n        template: function MessageChatComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r73 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"div\", 4)(5, \"div\", 4)(6, \"div\", 4)(7, \"div\", 4)(8, \"div\", 4)(9, \"div\", 4)(10, \"div\", 4)(11, \"div\", 4)(12, \"div\", 4)(13, \"div\", 4)(14, \"div\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"div\", 5);\n            i0.ɵɵelement(16, \"div\", 6)(17, \"div\", 6)(18, \"div\", 6)(19, \"div\", 6)(20, \"div\", 6)(21, \"div\", 6)(22, \"div\", 6)(23, \"div\", 6)(24, \"div\", 6)(25, \"div\", 6)(26, \"div\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(27, \"div\", 7);\n            i0.ɵɵelement(28, \"div\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(29, \"div\", 9)(30, \"button\", 10);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_30_listener() {\n              return ctx.goBackToConversations();\n            });\n            i0.ɵɵelement(31, \"i\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"div\", 12)(33, \"div\", 13);\n            i0.ɵɵelement(34, \"img\", 14);\n            i0.ɵɵtemplate(35, MessageChatComponent_span_35_Template, 1, 0, \"span\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(36, MessageChatComponent_div_36_Template, 5, 2, \"div\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"div\", 17)(38, \"button\", 10);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_38_listener() {\n              return ctx.initiateCall(\"AUDIO\");\n            });\n            i0.ɵɵelement(39, \"i\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"button\", 10);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_40_listener() {\n              return ctx.initiateCall(\"VIDEO\");\n            });\n            i0.ɵɵelement(41, \"i\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"div\", 20)(43, \"button\", 10);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_43_listener() {\n              return ctx.toggleThemeSelector();\n            });\n            i0.ɵɵelement(44, \"i\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(45, MessageChatComponent_div_45_Template, 24, 0, \"div\", 22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"button\", 23);\n            i0.ɵɵelement(47, \"i\", 24);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(48, \"div\", 25, 26);\n            i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_div_scroll_48_listener($event) {\n              return ctx.onScroll($event);\n            });\n            i0.ɵɵtemplate(50, MessageChatComponent_div_50_Template, 7, 0, \"div\", 27);\n            i0.ɵɵtemplate(51, MessageChatComponent_div_51_Template, 8, 0, \"div\", 28);\n            i0.ɵɵtemplate(52, MessageChatComponent_div_52_Template, 6, 0, \"div\", 29);\n            i0.ɵɵtemplate(53, MessageChatComponent_div_53_Template, 10, 1, \"div\", 30);\n            i0.ɵɵtemplate(54, MessageChatComponent_ng_container_54_Template, 2, 1, \"ng-container\", 31);\n            i0.ɵɵtemplate(55, MessageChatComponent_ng_template_55_Template, 1, 1, \"ng-template\", null, 32, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵtemplate(57, MessageChatComponent_div_57_Template, 8, 1, \"div\", 33);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"div\", 34);\n            i0.ɵɵtemplate(59, MessageChatComponent_div_59_Template, 4, 1, \"div\", 35);\n            i0.ɵɵtemplate(60, MessageChatComponent_div_60_Template, 16, 1, \"div\", 36);\n            i0.ɵɵelementStart(61, \"form\", 37);\n            i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_61_listener() {\n              return ctx.sendMessage();\n            });\n            i0.ɵɵelementStart(62, \"div\", 38)(63, \"button\", 39);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_63_listener() {\n              return ctx.toggleEmojiPicker();\n            });\n            i0.ɵɵelement(64, \"i\", 40);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"button\", 41);\n            i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_65_listener() {\n              i0.ɵɵrestoreView(_r73);\n              const _r14 = i0.ɵɵreference(68);\n              return i0.ɵɵresetView(_r14.click());\n            });\n            i0.ɵɵelement(66, \"i\", 42);\n            i0.ɵɵelementStart(67, \"input\", 43, 44);\n            i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_67_listener($event) {\n              return ctx.onFileSelected($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(69, MessageChatComponent_app_voice_recorder_69_Template, 1, 1, \"app-voice-recorder\", 45);\n            i0.ɵɵtemplate(70, MessageChatComponent_input_70_Template, 1, 0, \"input\", 46);\n            i0.ɵɵtemplate(71, MessageChatComponent_button_71_Template, 2, 0, \"button\", 47);\n            i0.ɵɵtemplate(72, MessageChatComponent_button_72_Template, 3, 3, \"button\", 48);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(73, MessageChatComponent_div_73_Template, 18, 3, \"div\", 49);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            const _r9 = i0.ɵɵreference(56);\n            let tmp_18_0;\n            let tmp_19_0;\n            i0.ɵɵproperty(\"ngClass\", ctx.selectedTheme);\n            i0.ɵɵadvance(34);\n            i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.showThemeSelector);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoadingMore);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.hasMoreMessages && ctx.messages.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.messages && ctx.messages.length > 0)(\"ngIfElse\", _r9);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isTyping);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.previewUrl);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c5, ctx.showEmojiPicker));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.isRecordingVoice);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isRecordingVoice);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isRecordingVoice && ((tmp_18_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_18_0.value) === \"\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isRecordingVoice && ((tmp_19_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_19_0.value) !== \"\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showCallModal && ctx.incomingCall);\n          }\n        },\n        dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, i9.VoiceRecorderComponent, i10.VoiceMessagePlayerComponent],\n        styles: [\".futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(0,247,255,.9),rgba(131,56,236,.9));color:#fff;border:1px solid rgba(255,255,255,.3);box-shadow:0 4px 20px #00f7ff66;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);transform:perspective(800px) rotateX(0);transition:all .3s ease}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover{transform:perspective(800px) rotateX(2deg) translateY(-2px);box-shadow:0 6px 25px #00f7ff80}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:before{border-left:8px solid rgba(131,56,236,.8)}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]{background:rgba(30,30,40,.7);border-color:#00f7ff26}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover{border-color:#00f7ff40;box-shadow:0 2px 15px #00f7ff33}.whatsapp-chat-header[_ngcontent-%COMP%]{display:flex;align-items:center;padding:6px 10px;background-color:#f0f2f5;border-bottom:1px solid #e0e0e0;height:50px}.dark[_nghost-%COMP%]   .whatsapp-chat-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-chat-header[_ngcontent-%COMP%]{background-color:#2a2a2a;border-bottom:1px solid #3a3a3a}.whatsapp-user-info[_ngcontent-%COMP%]{display:flex;align-items:center;flex:1;margin-left:12px}.whatsapp-avatar[_ngcontent-%COMP%]{position:relative;width:36px;height:36px;border-radius:50%;overflow:hidden;margin-right:8px}.whatsapp-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.whatsapp-online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:2px;right:2px;width:8px;height:8px;border-radius:50%;background:#25d366;border:2px solid #f0f2f5}.dark[_nghost-%COMP%]   .whatsapp-online-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-online-indicator[_ngcontent-%COMP%]{border-color:#2a2a2a}.whatsapp-user-details[_ngcontent-%COMP%]{margin-left:8px;display:flex;flex-direction:column}.whatsapp-username[_ngcontent-%COMP%]{font-size:.9375rem;font-weight:600;color:#333}.dark[_nghost-%COMP%]   .whatsapp-username[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-username[_ngcontent-%COMP%]{color:#e0e0e0}.whatsapp-status[_ngcontent-%COMP%]{font-size:.75rem;color:#666}.dark[_nghost-%COMP%]   .whatsapp-status[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-status[_ngcontent-%COMP%]{color:#aaa}.whatsapp-actions[_ngcontent-%COMP%]{display:flex;gap:16px}.whatsapp-action-button[_ngcontent-%COMP%]{background:transparent;border:none;color:#54656f;width:32px;height:32px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:background-color .2s}.dark[_nghost-%COMP%]   .whatsapp-action-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-action-button[_ngcontent-%COMP%]{color:#aaa}.whatsapp-action-button[_ngcontent-%COMP%]:hover{background-color:#0000000d}.dark[_nghost-%COMP%]   .whatsapp-action-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .whatsapp-action-button[_ngcontent-%COMP%]:hover{background-color:#ffffff1a}.futuristic-messages-container[_ngcontent-%COMP%]{flex:1;padding:.75rem;background-color:#f0f4f8;overflow-y:auto;scroll-behavior:smooth;-webkit-overflow-scrolling:touch;scrollbar-width:thin;scrollbar-color:var(--accent-color) transparent;position:relative;max-height:calc(100vh - 180px)}.dark[_nghost-%COMP%]   .futuristic-messages-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-messages-container[_ngcontent-%COMP%]{background-color:var(--dark-bg)}@keyframes _ngcontent-%COMP%_scan{0%{top:0}to{top:100%}}.futuristic-messages-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.futuristic-messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:rgba(0,247,255,.05)}.futuristic-messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:var(--accent-color);border-radius:10px;border:transparent}.futuristic-message-wrapper[_ngcontent-%COMP%]{margin-bottom:4px;position:relative;z-index:1}.futuristic-message[_ngcontent-%COMP%]{display:flex;align-items:flex-end;margin-bottom:1px;position:relative;width:100%}.futuristic-message-bubble[_ngcontent-%COMP%]{margin-bottom:.25rem;max-width:70%;min-width:40px;padding:.6rem .8rem;font-size:.9rem;line-height:1.4;word-wrap:break-word;word-break:normal;display:inline-block;width:auto;white-space:normal;overflow-wrap:break-word;text-align:left;direction:ltr;position:relative;transition:all var(--transition-fast);animation:_ngcontent-%COMP%_fadeIn .3s ease-out;border-radius:12px;letter-spacing:.02em;font-weight:400;-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);box-shadow:0 2px 10px #00000026}.futuristic-message-content[_ngcontent-%COMP%]{max-width:80%}.futuristic-message-text[_ngcontent-%COMP%]{white-space:normal;word-break:break-word;overflow-wrap:break-word;text-align:left;direction:ltr;min-width:80px;font-weight:400;letter-spacing:.01em;line-height:1.5}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-text[_ngcontent-%COMP%]{text-shadow:0 1px 2px rgba(0,0,0,.1)}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-text[_ngcontent-%COMP%]{text-shadow:0 1px 1px rgba(0,0,0,.05)}.futuristic-date-separator[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;margin:1.5rem 0;color:var(--text-dim);font-size:.75rem;text-transform:uppercase;letter-spacing:1px}.futuristic-date-line[_ngcontent-%COMP%]{flex:1;height:1px;background:linear-gradient(to right,transparent,var(--accent-color),transparent);opacity:.3}.futuristic-date-text[_ngcontent-%COMP%]{margin:0 10px;padding:2px 8px;background-color:#00f7ff0d;border-radius:var(--border-radius-sm)}.futuristic-message-time[_ngcontent-%COMP%]{font-size:.7rem;margin-top:.2rem;opacity:.7;color:var(--text-dim)}.futuristic-message-info[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;gap:6px;margin-top:4px;font-size:.75rem;letter-spacing:.02em;font-weight:300}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-info[_ngcontent-%COMP%]{color:#fffc}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-info[_ngcontent-%COMP%]{color:#00f7ffb3}.futuristic-message-status[_ngcontent-%COMP%]{color:#00f7ffe6}.futuristic-message-current-user[_ngcontent-%COMP%]{justify-content:flex-end;width:100%;display:flex;margin-left:auto;animation:_ngcontent-%COMP%_slideInRight .3s ease-out}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;max-width:80%;margin-left:auto}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(0,247,255,.8),rgba(131,56,236,.8));color:#fff;border-radius:12px;margin-left:auto;box-shadow:0 2px 15px #00f7ff4d;position:relative;border:1px solid rgba(255,255,255,.2);text-shadow:0 1px 2px rgba(0,0,0,.2)}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;right:-8px;width:0;height:0;border-top:8px solid transparent;border-left:8px solid rgba(131,56,236,.8);border-bottom:8px solid transparent;z-index:1;filter:drop-shadow(2px 0px 2px rgba(0,0,0,.1))}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover{box-shadow:var(--glow-effect)}.futuristic-message-other-user[_ngcontent-%COMP%]{justify-content:flex-start;width:100%;display:flex;margin-right:auto;animation:_ngcontent-%COMP%_slideInLeft .3s ease-out}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-start;max-width:80%;margin-right:auto}:not(.dark)[_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]{background:rgba(220,225,235,.8);color:#333;border-radius:12px;margin-right:auto;box-shadow:0 4px 15px #0000001a;border:1px solid rgba(79,95,173,.2);position:relative;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);transform:perspective(800px) rotateX(0);transition:all .3s ease}.dark[_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]{background:rgba(30,30,40,.8);color:#fffffff2;border-radius:12px;margin-right:auto;box-shadow:0 4px 15px #00000040;border:1px solid rgba(0,247,255,.2);position:relative;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);transform:perspective(800px) rotateX(0);transition:all .3s ease}:not(.dark)[_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-8px;width:0;height:0;border-top:8px solid transparent;border-right:8px solid rgba(220,225,235,.8);border-bottom:8px solid transparent;z-index:1;filter:drop-shadow(-2px 0px 2px rgba(0,0,0,.05))}.dark[_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-8px;width:0;height:0;border-top:8px solid transparent;border-right:8px solid rgba(30,30,40,.7);border-bottom:8px solid transparent;z-index:1;filter:drop-shadow(-2px 0px 2px rgba(0,0,0,.1))}:not(.dark)[_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover{background:rgba(230,235,245,.9);box-shadow:0 6px 20px #4f5fad33;border-color:#4f5fad59;transform:perspective(800px) rotateX(2deg) translateY(-2px)}.dark[_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover{background:rgba(40,40,50,.9);box-shadow:0 6px 20px #00f7ff4d;border-color:#00f7ff59;transform:perspective(800px) rotateX(2deg) translateY(-2px)}.futuristic-input-container[_ngcontent-%COMP%]{padding:6px 10px;background-color:var(--medium-bg);min-height:50px;position:relative;z-index:10;overflow:hidden}.futuristic-input-container[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:1px;background:linear-gradient(90deg,transparent 0%,rgba(0,247,255,.2) 20%,rgba(0,247,255,.8) 50%,rgba(0,247,255,.2) 80%,transparent 100%);background-size:200% 100%;animation:_ngcontent-%COMP%_borderFlow 3s infinite linear;box-shadow:0 0 15px #00f7ffb3;z-index:1}.futuristic-input-container[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;background:radial-gradient(ellipse at center,rgba(0,247,255,.05) 0%,transparent 70%);opacity:0;animation:_ngcontent-%COMP%_ambientGlow 4s infinite alternate;pointer-events:none;z-index:-1}@keyframes _ngcontent-%COMP%_borderFlow{0%{background-position:0% 0%}to{background-position:200% 0%}}@keyframes _ngcontent-%COMP%_ambientGlow{0%{opacity:0;transform:scale(.95)}to{opacity:.5;transform:scale(1.05)}}.futuristic-input-form[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;position:relative;z-index:2}.futuristic-input-tools[_ngcontent-%COMP%]{display:flex;gap:8px}.futuristic-tool-button[_ngcontent-%COMP%]{background-color:#00f7ff1a;color:var(--accent-color);border:none;border-radius:50%;width:32px;height:32px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all var(--transition-fast)}.futuristic-tool-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:translateY(-2px);box-shadow:var(--glow-effect)}.futuristic-tool-button.active[_ngcontent-%COMP%]{background-color:var(--accent-color);color:var(--dark-bg);animation:_ngcontent-%COMP%_pulse 1.5s infinite}.futuristic-input-field[_ngcontent-%COMP%]{flex:1;font-size:.9rem;padding:10px 16px;height:44px;border-radius:22px;border:1px solid rgba(0,247,255,.2);background-color:#00f7ff0d;color:var(--text-light);transition:all .3s ease}.futuristic-send-button[_ngcontent-%COMP%]{position:relative;background:linear-gradient(135deg,#00f7ff,#0066ff);color:#fff;border:none;border-radius:50%;width:44px;height:44px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s cubic-bezier(.175,.885,.32,1.275);box-shadow:0 0 10px #00f7ff80,0 0 20px #00f7ff33,inset 0 0 5px #ffffff4d;overflow:hidden;z-index:2}.futuristic-send-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:-2px;background:conic-gradient(from 0deg,transparent 0%,rgba(0,247,255,.8) 25%,rgba(131,56,236,.8) 50%,rgba(0,247,255,.8) 75%,transparent 100%);border-radius:50%;animation:_ngcontent-%COMP%_rotateHalo 3s linear infinite;z-index:-1;opacity:0;transition:opacity .3s ease}.futuristic-send-button[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;background:radial-gradient(circle at center,rgba(255,255,255,.9) 0%,rgba(0,247,255,.5) 30%,transparent 70%);border-radius:50%;opacity:0;transform:scale(.5);transition:all .3s ease;z-index:-1}@keyframes _ngcontent-%COMP%_rotateHalo{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes _ngcontent-%COMP%_pulseButton{0%{box-shadow:0 0 10px #00f7ff80,0 0 20px #00f7ff33;transform:scale(1)}50%{box-shadow:0 0 15px #00f7ffb3,0 0 30px #00f7ff66;transform:scale(1.05)}to{box-shadow:0 0 10px #00f7ff80,0 0 20px #00f7ff33;transform:scale(1)}}.futuristic-send-button[_ngcontent-%COMP%]:hover{transform:translateY(-3px) scale(1.1);box-shadow:0 0 15px #00f7ffb3,0 0 30px #00f7ff66,inset 0 0 10px #ffffff80;animation:_ngcontent-%COMP%_pulseButton 1.5s infinite}.futuristic-send-button[_ngcontent-%COMP%]:hover:before{opacity:1}.futuristic-send-button[_ngcontent-%COMP%]:hover:after{opacity:.8;transform:scale(1.2)}.futuristic-send-button[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed;transform:none;box-shadow:none;animation:none}.futuristic-send-button[_ngcontent-%COMP%]:disabled:before, .futuristic-send-button[_ngcontent-%COMP%]:disabled:after{opacity:0}.message-header[_ngcontent-%COMP%]{padding:.25rem .5rem;border-bottom:1px solid #e9ecef;height:40px}.message-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-size:.8rem;margin-bottom:0}.futuristic-chat-container[_ngcontent-%COMP%]{width:100%;margin:0 auto;border:1px solid rgba(0,247,255,.2);border-radius:var(--border-radius-lg);overflow:hidden;box-shadow:0 0 20px #0000004d;height:100%;display:flex;flex-direction:column;background-color:var(--medium-bg);position:relative}.message-date-divider[_ngcontent-%COMP%]{font-size:.75rem;color:#6c757d;margin:.5rem 0;text-align:center}.futuristic-message-image-container[_ngcontent-%COMP%]{margin:2px 0;overflow:hidden;background-color:transparent!important;border:none!important;box-shadow:none!important;padding:0!important;position:relative;max-width:220px;transform:perspective(800px) rotateX(0);transition:all .3s ease}.futuristic-message-image-container[_ngcontent-%COMP%]:hover{transform:perspective(800px) rotateX(2deg) translateY(-3px)}.futuristic-image-wrapper[_ngcontent-%COMP%]{position:relative;overflow:hidden;border-radius:12px;box-shadow:0 4px 15px #0000004d;transition:all var(--transition-fast);border:1px solid rgba(255,255,255,.1);-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);transform:perspective(800px) rotateX(0)}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-image-wrapper[_ngcontent-%COMP%]{position:relative}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-image-wrapper[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;right:-8px;width:0;height:0;border-top:8px solid transparent;border-left:8px solid var(--secondary-color);border-bottom:8px solid transparent;z-index:1}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-image-wrapper[_ngcontent-%COMP%]{position:relative}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-image-wrapper[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-8px;width:0;height:0;border-top:8px solid transparent;border-right:8px solid rgba(255,255,255,.1);border-bottom:8px solid transparent;z-index:1}.futuristic-message-image-link[_ngcontent-%COMP%]{display:block;width:100%;height:100%;text-decoration:none;cursor:pointer}.futuristic-message-image[_ngcontent-%COMP%]{width:100%;display:block;transition:transform .3s ease;cursor:pointer}.futuristic-image-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:linear-gradient(to top,rgba(0,0,0,.5),transparent);display:flex;align-items:center;justify-content:center;opacity:0;transition:opacity .3s ease;color:#fff;font-size:1.5rem}.futuristic-image-wrapper[_ngcontent-%COMP%]:hover   .futuristic-message-image[_ngcontent-%COMP%]{transform:scale(1.05)}.futuristic-image-wrapper[_ngcontent-%COMP%]:hover{box-shadow:0 12px 30px #00f7ff66;transform:perspective(800px) rotateX(3deg) translateY(-5px);border-color:#00f7ff66}.futuristic-image-wrapper[_ngcontent-%COMP%]:hover   .futuristic-image-overlay[_ngcontent-%COMP%]{opacity:1}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-image-wrapper[_ngcontent-%COMP%]{border:2px solid transparent;background:linear-gradient(135deg,rgba(0,247,255,.8),rgba(131,56,236,.8));padding:2px;box-shadow:0 5px 20px #00f7ff4d}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-image-wrapper[_ngcontent-%COMP%]{border:2px solid rgba(0,247,255,.15);background-color:#1e1e28b3;padding:2px;box-shadow:0 5px 15px #0000004d}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_scaleIn{0%{transform:scale(.9);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(20px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_slideInLeft{0%{opacity:0;transform:translate(-20px)}to{opacity:1;transform:translate(0)}}.fullscreen-image-container[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#000000f2;z-index:2147483647;display:flex;align-items:center;justify-content:center;animation:_ngcontent-%COMP%_fadeIn .3s ease-in-out}.fullscreen-image-wrapper[_ngcontent-%COMP%]{position:relative;max-width:90%;max-height:90%}.fullscreen-image-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;max-height:90vh;object-fit:contain;border-radius:8px;box-shadow:0 0 30px #00f7ff4d}.whatsapp-input-container[_ngcontent-%COMP%]{padding:8px 10px;background-color:#f0f2f5;min-height:50px;position:relative;z-index:10;border-top:1px solid #e0e0e0}.dark[_nghost-%COMP%]   .whatsapp-input-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-input-container[_ngcontent-%COMP%]{background-color:#2a2a2a;border-top:1px solid #3a3a3a}.whatsapp-input-form[_ngcontent-%COMP%]{display:flex;align-items:center;position:relative;z-index:2}.whatsapp-input-tools[_ngcontent-%COMP%]{display:flex;gap:8px;margin-right:8px}.whatsapp-tool-button[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;background-color:transparent;border:none;color:#54656f;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}.dark[_nghost-%COMP%]   .whatsapp-tool-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-tool-button[_ngcontent-%COMP%]{color:#aaa}.whatsapp-tool-button[_ngcontent-%COMP%]:hover, .whatsapp-tool-button.active[_ngcontent-%COMP%]{background-color:#0000000d;color:#128c7e}.dark[_nghost-%COMP%]   .whatsapp-tool-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .whatsapp-tool-button[_ngcontent-%COMP%]:hover, .dark[_nghost-%COMP%]   .whatsapp-tool-button.active[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-tool-button.active[_ngcontent-%COMP%]{background-color:#ffffff1a;color:#25d366}.whatsapp-input-field[_ngcontent-%COMP%]{flex:1;background-color:#fff;border:none;border-radius:20px;padding:8px 16px;color:#333;font-size:.9375rem;transition:all .2s ease;outline:none;box-shadow:0 1px 2px #0000001a}.dark[_nghost-%COMP%]   .whatsapp-input-field[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-input-field[_ngcontent-%COMP%]{background-color:#3a3a3a;color:#e0e0e0;box-shadow:0 1px 2px #0000004d}.whatsapp-input-field[_ngcontent-%COMP%]:focus{box-shadow:0 1px 3px #0003}.whatsapp-send-button[_ngcontent-%COMP%]{width:36px;height:36px;border-radius:50%;background-color:#25d366;border:none;color:#fff;display:flex;align-items:center;justify-content:center;cursor:pointer;margin-left:8px;transition:all .2s ease}.whatsapp-send-button[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#128c7e}.whatsapp-send-button[_ngcontent-%COMP%]:disabled{background-color:#b3b3b3;cursor:not-allowed}.whatsapp-voice-button[_ngcontent-%COMP%]{width:36px;height:36px;border-radius:50%;background-color:#25d366;border:none;color:#fff;display:flex;align-items:center;justify-content:center;cursor:pointer;margin-left:8px;transition:all .2s ease}.whatsapp-voice-button[_ngcontent-%COMP%]:hover{background-color:#128c7e}.whatsapp-file-preview[_ngcontent-%COMP%]{position:relative;margin-bottom:8px;border-radius:8px;overflow:hidden;box-shadow:0 1px 3px #0003;max-width:200px;max-height:200px}.whatsapp-preview-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.whatsapp-remove-button[_ngcontent-%COMP%]{position:absolute;top:4px;right:4px;width:24px;height:24px;border-radius:50%;background-color:#00000080;border:none;color:#fff;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}.whatsapp-remove-button[_ngcontent-%COMP%]:hover{background-color:#ff0000b3}.whatsapp-emoji-picker[_ngcontent-%COMP%]{position:absolute;bottom:60px;left:0;right:0;background-color:#fff;border-radius:8px 8px 0 0;box-shadow:0 -2px 10px #0000001a;z-index:100;display:flex;flex-direction:column;height:250px;overflow:hidden}.dark[_nghost-%COMP%]   .whatsapp-emoji-picker[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-emoji-picker[_ngcontent-%COMP%]{background-color:#2a2a2a;box-shadow:0 -2px 10px #0000004d}.whatsapp-emoji-categories[_ngcontent-%COMP%]{display:flex;padding:8px;border-bottom:1px solid #e0e0e0;overflow-x:auto;scrollbar-width:none}.dark[_nghost-%COMP%]   .whatsapp-emoji-categories[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-emoji-categories[_ngcontent-%COMP%]{border-bottom:1px solid #3a3a3a}.whatsapp-emoji-categories[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.whatsapp-emoji-category[_ngcontent-%COMP%]{width:36px;height:36px;border-radius:50%;background-color:transparent;border:none;color:#54656f;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease;flex-shrink:0}.dark[_nghost-%COMP%]   .whatsapp-emoji-category[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-emoji-category[_ngcontent-%COMP%]{color:#aaa}.whatsapp-emoji-category[_ngcontent-%COMP%]:hover, .whatsapp-emoji-category.active[_ngcontent-%COMP%]{background-color:#0000000d;color:#128c7e}.dark[_nghost-%COMP%]   .whatsapp-emoji-category[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .whatsapp-emoji-category[_ngcontent-%COMP%]:hover, .dark[_nghost-%COMP%]   .whatsapp-emoji-category.active[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-emoji-category.active[_ngcontent-%COMP%]{background-color:#ffffff1a;color:#25d366}.whatsapp-emoji-list[_ngcontent-%COMP%]{flex:1;padding:8px;display:grid;grid-template-columns:repeat(8,1fr);gap:4px;overflow-y:auto}.whatsapp-emoji-item[_ngcontent-%COMP%]{width:36px;height:36px;border-radius:4px;background-color:transparent;border:none;font-size:20px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}.whatsapp-emoji-item[_ngcontent-%COMP%]:hover{background-color:#0000000d}.dark[_nghost-%COMP%]   .whatsapp-emoji-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .whatsapp-emoji-item[_ngcontent-%COMP%]:hover{background-color:#ffffff1a}.whatsapp-call-modal[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#000c;display:flex;align-items:center;justify-content:center;z-index:9999;animation:_ngcontent-%COMP%_fadeIn .3s ease-in-out}.whatsapp-call-modal-content[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px;width:300px;max-width:90%;overflow:hidden;box-shadow:0 10px 25px #0000004d}.dark[_nghost-%COMP%]   .whatsapp-call-modal-content[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-call-modal-content[_ngcontent-%COMP%]{background-color:#2a2a2a;box-shadow:0 10px 25px #00000080}.whatsapp-call-header[_ngcontent-%COMP%]{padding:20px;text-align:center}.whatsapp-call-avatar[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;overflow:hidden;margin:0 auto 15px;border:3px solid #25d366;animation:_ngcontent-%COMP%_pulse 1.5s infinite}.whatsapp-call-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.whatsapp-call-name[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;margin-bottom:5px;color:#333}.dark[_nghost-%COMP%]   .whatsapp-call-name[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-call-name[_ngcontent-%COMP%]{color:#e0e0e0}.whatsapp-call-status[_ngcontent-%COMP%]{font-size:.9rem;color:#666;margin-bottom:0}.dark[_nghost-%COMP%]   .whatsapp-call-status[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-call-status[_ngcontent-%COMP%]{color:#aaa}.whatsapp-call-actions[_ngcontent-%COMP%]{display:flex;border-top:1px solid #e0e0e0}.dark[_nghost-%COMP%]   .whatsapp-call-actions[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-call-actions[_ngcontent-%COMP%]{border-top:1px solid #3a3a3a}.whatsapp-call-reject[_ngcontent-%COMP%], .whatsapp-call-accept[_ngcontent-%COMP%]{flex:1;padding:15px;border:none;display:flex;flex-direction:column;align-items:center;justify-content:center;cursor:pointer;transition:background-color .2s}.whatsapp-call-reject[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}.whatsapp-call-accept[_ngcontent-%COMP%]{background-color:#25d366;color:#fff}.whatsapp-call-reject[_ngcontent-%COMP%]:hover{background-color:#d32f2f}.whatsapp-call-accept[_ngcontent-%COMP%]:hover{background-color:#1da856}.whatsapp-call-reject[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .whatsapp-call-accept[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:5px}@keyframes _ngcontent-%COMP%_pulse{0%{box-shadow:0 0 #25d36680}70%{box-shadow:0 0 0 10px #25d36600}to{box-shadow:0 0 #25d36600}}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}.close-fullscreen-btn[_ngcontent-%COMP%]{position:absolute;top:-40px;right:-40px;width:40px;height:40px;background-color:#000000b3;color:#fff;border:none;border-radius:50%;font-size:24px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}.close-fullscreen-btn[_ngcontent-%COMP%]:hover{background-color:#00f7ff80;transform:scale(1.1)}.image-fullscreen-container[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;justify-content:center;align-items:center;position:relative}.image-fullscreen-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:85%;max-height:85%;object-fit:contain;border-radius:12px;box-shadow:0 0 50px #00f7ff4d;animation:_ngcontent-%COMP%_scaleIn .4s cubic-bezier(.34,1.56,.64,1);border:1px solid rgba(0,247,255,.2)}.image-fullscreen-dialog[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;background:rgba(0,0,0,.5);color:#00f7ffe6;border:2px solid rgba(0,247,255,.3);border-radius:50%;width:50px;height:50px;font-size:30px;display:flex;justify-content:center;align-items:center;cursor:pointer;transition:all .3s cubic-bezier(.175,.885,.32,1.275);z-index:10000000;box-shadow:0 0 20px #00f7ff33}.image-fullscreen-dialog[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]:hover{background:rgba(0,247,255,.3);transform:scale(1.15) rotate(90deg);box-shadow:0 0 30px #00f7ff66}.futuristic-loading[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px}.futuristic-loading-circle[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:50%;border:3px solid rgba(0,247,255,.1);border-top-color:var(--accent-color);animation:spin 1.5s linear infinite;margin-bottom:15px}.futuristic-loading-text[_ngcontent-%COMP%]{color:var(--accent-color);font-size:.875rem;letter-spacing:.5px}.futuristic-loading-more[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:10px;background-color:#00f7ff0d;border-radius:var(--border-radius-md)}.futuristic-loading-dots[_ngcontent-%COMP%]{display:flex;gap:5px;margin-bottom:5px}.futuristic-loading-dot[_ngcontent-%COMP%]{width:8px;height:8px;background-color:var(--accent-color);border-radius:50%;animation:_ngcontent-%COMP%_pulse 1.5s infinite ease-in-out}.futuristic-conversation-start[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:100%;margin:20px 0}.futuristic-conversation-start-line[_ngcontent-%COMP%]{flex:1;height:1px;background:linear-gradient(to right,transparent,var(--accent-color),transparent);opacity:.3}.futuristic-conversation-start-text[_ngcontent-%COMP%]{margin:0 10px;padding:4px 12px;background-color:#00f7ff1a;border-radius:var(--border-radius-md);color:var(--accent-color);font-size:.75rem;letter-spacing:1px;text-transform:uppercase}.futuristic-error-container[_ngcontent-%COMP%]{margin:15px;padding:15px;background:rgba(255,0,0,.1);border-left:3px solid #ff3b30;border-radius:5px;display:flex;align-items:flex-start}.futuristic-error-icon[_ngcontent-%COMP%]{color:#ff3b30;font-size:1.25rem;margin-right:15px}.futuristic-error-title[_ngcontent-%COMP%]{color:#ff3b30;font-size:.875rem;font-weight:600;margin-bottom:5px}.futuristic-error-message[_ngcontent-%COMP%]{color:var(--text-dim);font-size:.8125rem}.futuristic-message-pending[_ngcontent-%COMP%]{opacity:.7}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:.6}50%{opacity:.9}to{opacity:.6}}.futuristic-message-sending[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s infinite ease-in-out}.futuristic-message-error[_ngcontent-%COMP%]{border:1px solid rgba(239,68,68,.5)}.futuristic-voice-message-container[_ngcontent-%COMP%]{padding:8px!important;min-width:200px;border-radius:14px;-webkit-backdrop-filter:blur(12px);backdrop-filter:blur(12px);border:1px solid rgba(255,255,255,.15);box-shadow:0 8px 25px #0003;transform:perspective(800px) rotateX(0);transition:all .3s ease}.futuristic-voice-message-container[_ngcontent-%COMP%]:hover{transform:perspective(800px) rotateX(2deg) translateY(-3px);box-shadow:0 12px 30px #00000040}.futuristic-voice-message[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;position:relative;z-index:1}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-voice-message-container[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(0,247,255,.2),rgba(131,56,236,.2));border-color:#fff3}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-voice-message-container[_ngcontent-%COMP%]{background:rgba(30,30,40,.4);border-color:#00f7ff26}.futuristic-voice-play-button[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s cubic-bezier(.175,.885,.32,1.275);box-shadow:0 0 20px #0000004d;position:relative;overflow:hidden;z-index:2;animation:pulse-slow 4s infinite}.futuristic-voice-play-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:radial-gradient(circle at center,rgba(0,247,255,.2) 0%,transparent 70%);opacity:0;transition:opacity .3s ease;z-index:-1}.futuristic-voice-play-button[_ngcontent-%COMP%]:hover:before{opacity:1}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-voice-play-button[_ngcontent-%COMP%]{background-color:#ffffffe6;color:var(--accent-color);border:2px solid rgba(0,247,255,.5)}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-voice-play-button[_ngcontent-%COMP%]{background-color:#00f7ffe6;color:#0a0a14e6;border:2px solid rgba(0,247,255,.2)}.futuristic-voice-play-button[_ngcontent-%COMP%]:hover{transform:scale(1.2) translateY(-3px);box-shadow:0 8px 25px #00f7ff80;animation:none}.futuristic-voice-play-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:16px;transition:all .3s ease}.futuristic-voice-play-button[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:scale(1.1)}.futuristic-voice-waveform[_ngcontent-%COMP%]{display:flex;align-items:center;gap:3px;height:36px;padding:0 5px;position:relative}.futuristic-voice-waveform[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(90deg,transparent,rgba(0,247,255,.1),transparent);opacity:0;transition:opacity .5s ease;animation:_ngcontent-%COMP%_waveformPulse 2s infinite;border-radius:8px;pointer-events:none}@keyframes _ngcontent-%COMP%_waveformPulse{0%,to{opacity:0}50%{opacity:1}}.futuristic-voice-bar[_ngcontent-%COMP%]{width:3px;background-color:currentColor;border-radius:4px;transition:height .3s cubic-bezier(.34,1.56,.64,1);box-shadow:0 0 5px #00f7ff4d}.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-voice-bar[_ngcontent-%COMP%]{background-color:#fffc}.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-voice-bar[_ngcontent-%COMP%]{background-color:#00f7ffcc}.futuristic-voice-waveform[_ngcontent-%COMP%]:hover   .futuristic-voice-bar[_ngcontent-%COMP%]{transform:scaleY(1.1);box-shadow:0 0 8px #00f7ff80}audio[_ngcontent-%COMP%]:focus, audio[_ngcontent-%COMP%]:active, audio[_ngcontent-%COMP%]:hover, .voice-message-player[_ngcontent-%COMP%]:focus, .voice-message-player[_ngcontent-%COMP%]:active, .voice-message-player[_ngcontent-%COMP%]:hover, app-voice-message-player[_ngcontent-%COMP%]{outline:none!important;border:none!important;box-shadow:none!important;background-color:transparent!important}.futuristic-message-current-user[_ngcontent-%COMP%]   app-voice-message-player[_ngcontent-%COMP%]   .waveform-bar[_ngcontent-%COMP%]{background-color:#ffffffb3!important}.futuristic-message-current-user[_ngcontent-%COMP%]   app-voice-message-player[_ngcontent-%COMP%]   .waveform-bar.active[_ngcontent-%COMP%]{background-color:#fff!important;box-shadow:0 0 5px #fffc!important}.futuristic-message-other-user[_ngcontent-%COMP%]   app-voice-message-player[_ngcontent-%COMP%]   .waveform-bar[_ngcontent-%COMP%]{background-color:#00f7ff80!important}.futuristic-message-other-user[_ngcontent-%COMP%]   app-voice-message-player[_ngcontent-%COMP%]   .waveform-bar.active[_ngcontent-%COMP%]{background-color:var(--accent-color)!important;box-shadow:0 0 5px var(--accent-color)!important}.futuristic-message-current-user[_ngcontent-%COMP%]   app-voice-message-player[_ngcontent-%COMP%]   .play-button[_ngcontent-%COMP%]{background-color:#ffffffe6!important;color:var(--accent-color)!important;box-shadow:var(--glow-effect)!important}.futuristic-message-other-user[_ngcontent-%COMP%]   app-voice-message-player[_ngcontent-%COMP%]   .play-button[_ngcontent-%COMP%]{background-color:var(--accent-color)!important;color:var(--dark-bg)!important;box-shadow:var(--glow-effect)!important}.image-modal[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_scaleIn .3s ease-in-out;transition:transform .2s ease}.futuristic-typing-indicator[_ngcontent-%COMP%]{display:flex;align-items:flex-end;margin:10px 0;animation:_ngcontent-%COMP%_fadeIn .3s ease-out}.futuristic-typing-bubble[_ngcontent-%COMP%]{background-color:#ffffff1a;border-radius:16px;padding:10px 15px;margin-left:10px;box-shadow:0 2px 5px #0000001a;border:1px solid rgba(255,255,255,.1)}.futuristic-typing-dots[_ngcontent-%COMP%]{display:flex;gap:4px}.futuristic-typing-dot[_ngcontent-%COMP%]{width:8px;height:8px;background-color:var(--accent-color);border-radius:50%;animation:_ngcontent-%COMP%_typingPulse 1.5s infinite ease-in-out;box-shadow:0 0 5px var(--accent-color)}@keyframes _ngcontent-%COMP%_typingPulse{0%,to{transform:translateY(0);opacity:.5}50%{transform:translateY(-5px);opacity:1;box-shadow:0 0 8px var(--accent-color)}}.futuristic-no-messages[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:20px;text-align:center}.futuristic-no-messages-icon[_ngcontent-%COMP%]{font-size:3rem;color:var(--accent-color);margin-bottom:20px;opacity:.7}.futuristic-no-messages-text[_ngcontent-%COMP%]{color:var(--text-dim);font-size:1rem;margin-bottom:30px;line-height:1.5}.futuristic-file-preview[_ngcontent-%COMP%]{position:relative;margin-bottom:10px;border-radius:var(--border-radius-md);overflow:hidden;max-width:200px;border:2px solid rgba(0,247,255,.3);box-shadow:var(--glow-effect)}.futuristic-preview-image[_ngcontent-%COMP%]{width:100%;display:block}.futuristic-remove-button[_ngcontent-%COMP%]{position:absolute;top:5px;right:5px;background:rgba(0,0,0,.6);border:none;border-radius:50%;width:24px;height:24px;display:flex;align-items:center;justify-content:center;color:#fff;font-size:12px;cursor:pointer;transition:all var(--transition-fast)}.futuristic-remove-button[_ngcontent-%COMP%]:hover{background:rgba(255,0,0,.7);transform:scale(1.1)}\", \"@keyframes _ngcontent-%COMP%_borderFlow{0%{background-position:0% 0%}to{background-position:200% 0%}}@keyframes _ngcontent-%COMP%_ambientGlow{0%{opacity:.3;transform:scale(.95)}to{opacity:.7;transform:scale(1.05)}}@keyframes _ngcontent-%COMP%_rotateHalo{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes _ngcontent-%COMP%_flameBorder{0%{filter:brightness(1) blur(0px);transform:scaleY(.95)}50%{filter:brightness(1.2) blur(1px);transform:scaleY(1.05)}to{filter:brightness(1) blur(0px);transform:scaleY(.95)}}@keyframes _ngcontent-%COMP%_sparkle{0%{opacity:0;transform:translate(0)}25%{opacity:.7;transform:translate(1px)}50%{opacity:0;transform:translate(0)}75%{opacity:.5;transform:translate(2px)}to{opacity:0;transform:translate(0)}}.magic-input-container[_ngcontent-%COMP%]{padding:8px 12px;background-color:#121212b3;min-height:60px;position:relative;z-index:10;overflow:hidden;border-top:1px solid rgba(0,247,255,.1)}.magic-input-container[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:1px;background:linear-gradient(90deg,transparent 0%,rgba(0,247,255,.2) 20%,rgba(0,247,255,.8) 50%,rgba(0,247,255,.2) 80%,transparent 100%);background-size:200% 100%;animation:_ngcontent-%COMP%_borderFlow 3s infinite linear;box-shadow:0 0 15px #00f7ffb3;z-index:1}.magic-input-container[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;background:radial-gradient(ellipse at center,rgba(0,247,255,.05) 0%,transparent 70%);opacity:0;animation:_ngcontent-%COMP%_ambientGlow 4s infinite alternate;pointer-events:none;z-index:-1}.magic-input-field[_ngcontent-%COMP%]{flex:1;font-size:.9rem;padding:10px 16px;height:44px;border-radius:22px;border:1px solid rgba(0,247,255,.2);background-color:#00f7ff0d;color:#fff;transition:all .3s ease;position:relative;overflow:hidden;-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);box-shadow:inset 0 0 10px #00f7ff1a}.magic-input-field[_ngcontent-%COMP%]:focus{border-color:transparent;outline:none;box-shadow:0 0 0 1px #00f7ff80,0 0 15px #00f7ff80,inset 0 0 10px #00f7ff33;background-color:#00f7ff14}.magic-send-button[_ngcontent-%COMP%]{position:relative;background:linear-gradient(135deg,#00f7ff,#0066ff);color:#fff;border:none;border-radius:50%;width:44px;height:44px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s cubic-bezier(.175,.885,.32,1.275);box-shadow:0 0 10px #00f7ff80,0 0 20px #00f7ff33,inset 0 0 5px #ffffff4d;overflow:hidden;z-index:2}.magic-send-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:-2px;background:conic-gradient(from 0deg,transparent 0%,rgba(0,247,255,.8) 25%,rgba(131,56,236,.8) 50%,rgba(0,247,255,.8) 75%,transparent 100%);border-radius:50%;animation:_ngcontent-%COMP%_rotateHalo 3s linear infinite;z-index:-1;opacity:.5}.magic-send-button[_ngcontent-%COMP%]:hover{transform:translateY(-3px) scale(1.1);box-shadow:0 0 15px #00f7ffb3,0 0 30px #00f7ff66,inset 0 0 10px #ffffff80}.magic-send-button[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed;transform:none;box-shadow:none}.magic-notification-unread[_ngcontent-%COMP%]{position:relative;overflow:hidden;border-left:5px solid transparent;background-color:#00f7ff0d}.magic-notification-unread[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:5px;height:100%;background:linear-gradient(to bottom,#00f7ff,#0066ff,#00f7ff);box-shadow:0 0 15px #00f7ffcc,0 0 30px #06f6;animation:_ngcontent-%COMP%_flameBorder 3s ease-in-out infinite;z-index:1}.magic-notification-unread[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:0;left:0;width:15px;height:100%;background-image:radial-gradient(circle at 30% 20%,rgba(0,247,255,.9) 0%,rgba(0,247,255,0) 3%),radial-gradient(circle at 40% 40%,rgba(0,247,255,.9) 0%,rgba(0,247,255,0) 4%),radial-gradient(circle at 20% 60%,rgba(0,247,255,.9) 0%,rgba(0,247,255,0) 3%),radial-gradient(circle at 40% 80%,rgba(0,247,255,.9) 0%,rgba(0,247,255,0) 4%),radial-gradient(circle at 10% 30%,rgba(0,247,255,.9) 0%,rgba(0,247,255,0) 3%);opacity:0;z-index:2;animation:_ngcontent-%COMP%_sparkle 4s ease-in-out infinite}.magic-notification-unread[_ngcontent-%COMP%]:hover:before{animation-duration:1.5s;box-shadow:0 0 20px #00f7ffe6,0 0 40px #0066ff80}\", \"@keyframes _ngcontent-%COMP%_borderFlow {\\n      0% {\\n        background-position: 0% 0%;\\n      }\\n      100% {\\n        background-position: 200% 0%;\\n      }\\n    }\\n\\n    @keyframes _ngcontent-%COMP%_ambientGlow {\\n      0% {\\n        opacity: 0;\\n        transform: scale(0.95);\\n      }\\n      100% {\\n        opacity: 0.5;\\n        transform: scale(1.05);\\n      }\\n    }\\n\\n    @keyframes _ngcontent-%COMP%_rotateHalo {\\n      0% {\\n        transform: rotate(0deg);\\n      }\\n      100% {\\n        transform: rotate(360deg);\\n      }\\n    }\\n\\n    @keyframes _ngcontent-%COMP%_pulseButton {\\n      0% {\\n        box-shadow: 0 0 10px rgba(0, 247, 255, 0.5),\\n          0 0 20px rgba(0, 247, 255, 0.2);\\n        transform: scale(1);\\n      }\\n      50% {\\n        box-shadow: 0 0 15px rgba(0, 247, 255, 0.7),\\n          0 0 30px rgba(0, 247, 255, 0.4);\\n        transform: scale(1.05);\\n      }\\n      100% {\\n        box-shadow: 0 0 10px rgba(0, 247, 255, 0.5),\\n          0 0 20px rgba(0, 247, 255, 0.2);\\n        transform: scale(1);\\n      }\\n    }\"]\n      });\n    }\n  }\n  return MessageChatComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}