{"ast": null, "code": "import { previousDay } from \"./previousDay.js\";\n\n/**\n * The {@link previousMonday} function options.\n */\n\n/**\n * @name previousMonday\n * @category Weekday Helpers\n * @summary When is the previous Monday?\n *\n * @description\n * When is the previous Monday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The previous Monday\n *\n * @example\n * // When is the previous Monday before Jun, 18, 2021?\n * const result = previousMonday(new Date(2021, 5, 18))\n * //=> Mon June 14 2021 00:00:00\n */\nexport function previousMonday(date, options) {\n  return previousDay(date, 1, options);\n}\n\n// Fallback for modularized imports:\nexport default previousMonday;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}