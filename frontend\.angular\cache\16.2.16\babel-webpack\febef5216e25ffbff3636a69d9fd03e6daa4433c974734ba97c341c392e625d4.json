{"ast": null, "code": "import { newInvariantError, invariant } from \"../../utilities/globals/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport { validateOperation, createOperation, transformOperation } from \"../utils/index.js\";\nfunction passthrough(op, forward) {\n  return forward ? forward(op) : Observable.of();\n}\nfunction toLink(handler) {\n  return typeof handler === \"function\" ? new ApolloLink(handler) : handler;\n}\nfunction isTerminating(link) {\n  return link.request.length <= 1;\n}\nvar ApolloLink = /** @class */function () {\n  function ApolloLink(request) {\n    if (request) this.request = request;\n  }\n  ApolloLink.empty = function () {\n    return new ApolloLink(function () {\n      return Observable.of();\n    });\n  };\n  ApolloLink.from = function (links) {\n    if (links.length === 0) return ApolloLink.empty();\n    return links.map(toLink).reduce(function (x, y) {\n      return x.concat(y);\n    });\n  };\n  ApolloLink.split = function (test, left, right) {\n    var leftLink = toLink(left);\n    var rightLink = toLink(right || new ApolloLink(passthrough));\n    var ret;\n    if (isTerminating(leftLink) && isTerminating(rightLink)) {\n      ret = new ApolloLink(function (operation) {\n        return test(operation) ? leftLink.request(operation) || Observable.of() : rightLink.request(operation) || Observable.of();\n      });\n    } else {\n      ret = new ApolloLink(function (operation, forward) {\n        return test(operation) ? leftLink.request(operation, forward) || Observable.of() : rightLink.request(operation, forward) || Observable.of();\n      });\n    }\n    return Object.assign(ret, {\n      left: leftLink,\n      right: rightLink\n    });\n  };\n  ApolloLink.execute = function (link, operation) {\n    return link.request(createOperation(operation.context, transformOperation(validateOperation(operation)))) || Observable.of();\n  };\n  ApolloLink.concat = function (first, second) {\n    var firstLink = toLink(first);\n    if (isTerminating(firstLink)) {\n      globalThis.__DEV__ !== false && invariant.warn(38, firstLink);\n      return firstLink;\n    }\n    var nextLink = toLink(second);\n    var ret;\n    if (isTerminating(nextLink)) {\n      ret = new ApolloLink(function (operation) {\n        return firstLink.request(operation, function (op) {\n          return nextLink.request(op) || Observable.of();\n        }) || Observable.of();\n      });\n    } else {\n      ret = new ApolloLink(function (operation, forward) {\n        return firstLink.request(operation, function (op) {\n          return nextLink.request(op, forward) || Observable.of();\n        }) || Observable.of();\n      });\n    }\n    return Object.assign(ret, {\n      left: firstLink,\n      right: nextLink\n    });\n  };\n  ApolloLink.prototype.split = function (test, left, right) {\n    return this.concat(ApolloLink.split(test, left, right || new ApolloLink(passthrough)));\n  };\n  ApolloLink.prototype.concat = function (next) {\n    return ApolloLink.concat(this, next);\n  };\n  ApolloLink.prototype.request = function (operation, forward) {\n    throw newInvariantError(39);\n  };\n  ApolloLink.prototype.onError = function (error, observer) {\n    if (observer && observer.error) {\n      observer.error(error);\n      // Returning false indicates that observer.error does not need to be\n      // called again, since it was already called (on the previous line).\n      // Calling observer.error again would not cause any real problems,\n      // since only the first call matters, but custom onError functions\n      // might have other reasons for wanting to prevent the default\n      // behavior by returning false.\n      return false;\n    }\n    // Throw errors will be passed to observer.error.\n    throw error;\n  };\n  ApolloLink.prototype.setOnError = function (fn) {\n    this.onError = fn;\n    return this;\n  };\n  return ApolloLink;\n}();\nexport { ApolloLink };\n//# sourceMappingURL=ApolloLink.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}