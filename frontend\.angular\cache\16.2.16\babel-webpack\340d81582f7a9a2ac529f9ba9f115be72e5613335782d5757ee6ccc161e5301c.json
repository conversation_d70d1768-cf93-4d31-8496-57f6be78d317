{"ast": null, "code": "import { <PERSON><PERSON> } from \"@wry/trie\";\nimport { StrongCache } from \"@wry/caches\";\nimport { Entry } from \"./entry.js\";\nimport { parentEntrySlot } from \"./context.js\";\n// These helper functions are important for making optimism work with\n// asynchronous code. In order to register parent-child dependencies,\n// optimism needs to know about any currently active parent computations.\n// In ordinary synchronous code, the parent context is implicit in the\n// execution stack, but asynchronous code requires some extra guidance in\n// order to propagate context from one async task segment to the next.\nexport { bindContext, noContext, nonReactive, setTimeout, asyncFromGen, Slot } from \"./context.js\";\n// A lighter-weight dependency, similar to OptimisticWrapperFunction, except\n// with only one argument, no makeCacheKey, no wrapped function to recompute,\n// and no result value. Useful for representing dependency leaves in the graph\n// of computation. Subscriptions are supported.\nexport { dep } from \"./dep.js\";\n// The defaultMakeCacheKey function is remarkably powerful, because it gives\n// a unique object for any shallow-identical list of arguments. If you need\n// to implement a custom makeCacheKey function, you may find it helpful to\n// delegate the final work to defaultMakeCacheKey, which is why we export it\n// here. However, you may want to avoid defaultMakeCacheKey if your runtime\n// does not support WeakMap, or you have the ability to return a string key.\n// In those cases, just write your own custom makeCacheKey functions.\nlet defaultKeyTrie;\nexport function defaultMakeCacheKey(...args) {\n  const trie = defaultKeyTrie || (defaultKeyTrie = new Trie(typeof WeakMap === \"function\"));\n  return trie.lookupArray(args);\n}\n// If you're paranoid about memory leaks, or you want to avoid using WeakMap\n// under the hood, but you still need the behavior of defaultMakeCacheKey,\n// import this constructor to create your own tries.\nexport { Trie as KeyTrie };\n;\nconst caches = new Set();\nexport function wrap(originalFunction, {\n  max = Math.pow(2, 16),\n  keyArgs,\n  makeCacheKey = defaultMakeCacheKey,\n  normalizeResult,\n  subscribe,\n  cache: cacheOption = StrongCache\n} = Object.create(null)) {\n  const cache = typeof cacheOption === \"function\" ? new cacheOption(max, entry => entry.dispose()) : cacheOption;\n  const optimistic = function () {\n    const key = makeCacheKey.apply(null, keyArgs ? keyArgs.apply(null, arguments) : arguments);\n    if (key === void 0) {\n      return originalFunction.apply(null, arguments);\n    }\n    let entry = cache.get(key);\n    if (!entry) {\n      cache.set(key, entry = new Entry(originalFunction));\n      entry.normalizeResult = normalizeResult;\n      entry.subscribe = subscribe;\n      // Give the Entry the ability to trigger cache.delete(key), even though\n      // the Entry itself does not know about key or cache.\n      entry.forget = () => cache.delete(key);\n    }\n    const value = entry.recompute(Array.prototype.slice.call(arguments));\n    // Move this entry to the front of the least-recently used queue,\n    // since we just finished computing its value.\n    cache.set(key, entry);\n    caches.add(cache);\n    // Clean up any excess entries in the cache, but only if there is no\n    // active parent entry, meaning we're not in the middle of a larger\n    // computation that might be flummoxed by the cleaning.\n    if (!parentEntrySlot.hasValue()) {\n      caches.forEach(cache => cache.clean());\n      caches.clear();\n    }\n    return value;\n  };\n  Object.defineProperty(optimistic, \"size\", {\n    get: () => cache.size,\n    configurable: false,\n    enumerable: false\n  });\n  Object.freeze(optimistic.options = {\n    max,\n    keyArgs,\n    makeCacheKey,\n    normalizeResult,\n    subscribe,\n    cache\n  });\n  function dirtyKey(key) {\n    const entry = key && cache.get(key);\n    if (entry) {\n      entry.setDirty();\n    }\n  }\n  optimistic.dirtyKey = dirtyKey;\n  optimistic.dirty = function dirty() {\n    dirtyKey(makeCacheKey.apply(null, arguments));\n  };\n  function peekKey(key) {\n    const entry = key && cache.get(key);\n    if (entry) {\n      return entry.peek();\n    }\n  }\n  optimistic.peekKey = peekKey;\n  optimistic.peek = function peek() {\n    return peekKey(makeCacheKey.apply(null, arguments));\n  };\n  function forgetKey(key) {\n    return key ? cache.delete(key) : false;\n  }\n  optimistic.forgetKey = forgetKey;\n  optimistic.forget = function forget() {\n    return forgetKey(makeCacheKey.apply(null, arguments));\n  };\n  optimistic.makeCacheKey = makeCacheKey;\n  optimistic.getKey = keyArgs ? function getKey() {\n    return makeCacheKey.apply(null, keyArgs.apply(null, arguments));\n  } : makeCacheKey;\n  return Object.freeze(optimistic);\n}\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}