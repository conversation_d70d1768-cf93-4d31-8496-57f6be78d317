{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { invariant, newInvariantError } from \"../utilities/globals/index.js\";\nimport { ApolloLink, execute } from \"../link/core/index.js\";\nimport { version } from \"../version.js\";\nimport { HttpLink } from \"../link/http/index.js\";\nimport { QueryManager } from \"./QueryManager.js\";\nimport { LocalState } from \"./LocalState.js\";\nvar hasSuggestedDevtools = false;\n// Though mergeOptions now resides in @apollo/client/utilities, it was\n// previously declared and exported from this module, and then reexported from\n// @apollo/client/core. Since we need to preserve that API anyway, the easiest\n// solution is to reexport mergeOptions where it was previously declared (here).\nimport { mergeOptions } from \"../utilities/index.js\";\nimport { getApolloClientMemoryInternals } from \"../utilities/caching/getMemoryInternals.js\";\nexport { mergeOptions };\n/**\n * This is the primary Apollo Client class. It is used to send GraphQL documents (i.e. queries\n * and mutations) to a GraphQL spec-compliant server over an `ApolloLink` instance,\n * receive results from the server and cache the results in a store. It also delivers updates\n * to GraphQL queries through `Observable` instances.\n */\nvar ApolloClient = /** @class */function () {\n  /**\n   * Constructs an instance of `ApolloClient`.\n   *\n   * @example\n   * ```js\n   * import { ApolloClient, InMemoryCache } from '@apollo/client';\n   *\n   * const cache = new InMemoryCache();\n   *\n   * const client = new ApolloClient({\n   *   // Provide required constructor fields\n   *   cache: cache,\n   *   uri: 'http://localhost:4000/',\n   *\n   *   // Provide some optional constructor fields\n   *   name: 'react-web-client',\n   *   version: '1.3',\n   *   queryDeduplication: false,\n   *   defaultOptions: {\n   *     watchQuery: {\n   *       fetchPolicy: 'cache-and-network',\n   *     },\n   *   },\n   * });\n   * ```\n   */\n  function ApolloClient(options) {\n    var _this = this;\n    var _a;\n    this.resetStoreCallbacks = [];\n    this.clearStoreCallbacks = [];\n    if (!options.cache) {\n      throw newInvariantError(16);\n    }\n    var uri = options.uri,\n      credentials = options.credentials,\n      headers = options.headers,\n      cache = options.cache,\n      documentTransform = options.documentTransform,\n      _b = options.ssrMode,\n      ssrMode = _b === void 0 ? false : _b,\n      _c = options.ssrForceFetchDelay,\n      ssrForceFetchDelay = _c === void 0 ? 0 : _c,\n      // Expose the client instance as window.__APOLLO_CLIENT__ and call\n      // onBroadcast in queryManager.broadcastQueries to enable browser\n      // devtools, but disable them by default in production.\n      connectToDevTools = options.connectToDevTools,\n      _d = options.queryDeduplication,\n      queryDeduplication = _d === void 0 ? true : _d,\n      defaultOptions = options.defaultOptions,\n      defaultContext = options.defaultContext,\n      _e = options.assumeImmutableResults,\n      assumeImmutableResults = _e === void 0 ? cache.assumeImmutableResults : _e,\n      resolvers = options.resolvers,\n      typeDefs = options.typeDefs,\n      fragmentMatcher = options.fragmentMatcher,\n      clientAwarenessName = options.name,\n      clientAwarenessVersion = options.version,\n      devtools = options.devtools,\n      dataMasking = options.dataMasking;\n    var link = options.link;\n    if (!link) {\n      link = uri ? new HttpLink({\n        uri: uri,\n        credentials: credentials,\n        headers: headers\n      }) : ApolloLink.empty();\n    }\n    this.link = link;\n    this.cache = cache;\n    this.disableNetworkFetches = ssrMode || ssrForceFetchDelay > 0;\n    this.queryDeduplication = queryDeduplication;\n    this.defaultOptions = defaultOptions || Object.create(null);\n    this.typeDefs = typeDefs;\n    this.devtoolsConfig = __assign(__assign({}, devtools), {\n      enabled: (_a = devtools === null || devtools === void 0 ? void 0 : devtools.enabled) !== null && _a !== void 0 ? _a : connectToDevTools\n    });\n    if (this.devtoolsConfig.enabled === undefined) {\n      this.devtoolsConfig.enabled = globalThis.__DEV__ !== false;\n    }\n    if (ssrForceFetchDelay) {\n      setTimeout(function () {\n        return _this.disableNetworkFetches = false;\n      }, ssrForceFetchDelay);\n    }\n    this.watchQuery = this.watchQuery.bind(this);\n    this.query = this.query.bind(this);\n    this.mutate = this.mutate.bind(this);\n    this.watchFragment = this.watchFragment.bind(this);\n    this.resetStore = this.resetStore.bind(this);\n    this.reFetchObservableQueries = this.reFetchObservableQueries.bind(this);\n    this.version = version;\n    this.localState = new LocalState({\n      cache: cache,\n      client: this,\n      resolvers: resolvers,\n      fragmentMatcher: fragmentMatcher\n    });\n    this.queryManager = new QueryManager({\n      cache: this.cache,\n      link: this.link,\n      defaultOptions: this.defaultOptions,\n      defaultContext: defaultContext,\n      documentTransform: documentTransform,\n      queryDeduplication: queryDeduplication,\n      ssrMode: ssrMode,\n      dataMasking: !!dataMasking,\n      clientAwareness: {\n        name: clientAwarenessName,\n        version: clientAwarenessVersion\n      },\n      localState: this.localState,\n      assumeImmutableResults: assumeImmutableResults,\n      onBroadcast: this.devtoolsConfig.enabled ? function () {\n        if (_this.devToolsHookCb) {\n          _this.devToolsHookCb({\n            action: {},\n            state: {\n              queries: _this.queryManager.getQueryStore(),\n              mutations: _this.queryManager.mutationStore || {}\n            },\n            dataWithOptimisticResults: _this.cache.extract(true)\n          });\n        }\n      } : void 0\n    });\n    if (this.devtoolsConfig.enabled) this.connectToDevTools();\n  }\n  ApolloClient.prototype.connectToDevTools = function () {\n    if (typeof window === \"undefined\") {\n      return;\n    }\n    var windowWithDevTools = window;\n    var devtoolsSymbol = Symbol.for(\"apollo.devtools\");\n    (windowWithDevTools[devtoolsSymbol] = windowWithDevTools[devtoolsSymbol] || []).push(this);\n    windowWithDevTools.__APOLLO_CLIENT__ = this;\n    /**\n     * Suggest installing the devtools for developers who don't have them\n     */\n    if (!hasSuggestedDevtools && globalThis.__DEV__ !== false) {\n      hasSuggestedDevtools = true;\n      if (window.document && window.top === window.self && /^(https?|file):$/.test(window.location.protocol)) {\n        setTimeout(function () {\n          if (!window.__APOLLO_DEVTOOLS_GLOBAL_HOOK__) {\n            var nav = window.navigator;\n            var ua = nav && nav.userAgent;\n            var url = void 0;\n            if (typeof ua === \"string\") {\n              if (ua.indexOf(\"Chrome/\") > -1) {\n                url = \"https://chrome.google.com/webstore/detail/\" + \"apollo-client-developer-t/jdkknkkbebbapilgoeccciglkfbmbnfm\";\n              } else if (ua.indexOf(\"Firefox/\") > -1) {\n                url = \"https://addons.mozilla.org/en-US/firefox/addon/apollo-developer-tools/\";\n              }\n            }\n            if (url) {\n              globalThis.__DEV__ !== false && invariant.log(\"Download the Apollo DevTools for a better development \" + \"experience: %s\", url);\n            }\n          }\n        }, 10000);\n      }\n    }\n  };\n  Object.defineProperty(ApolloClient.prototype, \"documentTransform\", {\n    /**\n     * The `DocumentTransform` used to modify GraphQL documents before a request\n     * is made. If a custom `DocumentTransform` is not provided, this will be the\n     * default document transform.\n     */\n    get: function () {\n      return this.queryManager.documentTransform;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  /**\n   * Call this method to terminate any active client processes, making it safe\n   * to dispose of this `ApolloClient` instance.\n   */\n  ApolloClient.prototype.stop = function () {\n    this.queryManager.stop();\n  };\n  /**\n   * This watches the cache store of the query according to the options specified and\n   * returns an `ObservableQuery`. We can subscribe to this `ObservableQuery` and\n   * receive updated results through an observer when the cache store changes.\n   *\n   * Note that this method is not an implementation of GraphQL subscriptions. Rather,\n   * it uses Apollo's store in order to reactively deliver updates to your query results.\n   *\n   * For example, suppose you call watchQuery on a GraphQL query that fetches a person's\n   * first and last name and this person has a particular object identifier, provided by\n   * dataIdFromObject. Later, a different query fetches that same person's\n   * first and last name and the first name has now changed. Then, any observers associated\n   * with the results of the first query will be updated with a new result object.\n   *\n   * Note that if the cache does not change, the subscriber will *not* be notified.\n   *\n   * See [here](https://medium.com/apollo-stack/the-concepts-of-graphql-bc68bd819be3#.3mb0cbcmc) for\n   * a description of store reactivity.\n   */\n  ApolloClient.prototype.watchQuery = function (options) {\n    if (this.defaultOptions.watchQuery) {\n      options = mergeOptions(this.defaultOptions.watchQuery, options);\n    }\n    // XXX Overwriting options is probably not the best way to do this long term...\n    if (this.disableNetworkFetches && (options.fetchPolicy === \"network-only\" || options.fetchPolicy === \"cache-and-network\")) {\n      options = __assign(__assign({}, options), {\n        fetchPolicy: \"cache-first\"\n      });\n    }\n    return this.queryManager.watchQuery(options);\n  };\n  /**\n   * This resolves a single query according to the options specified and\n   * returns a `Promise` which is either resolved with the resulting data\n   * or rejected with an error.\n   *\n   * @param options - An object of type `QueryOptions` that allows us to\n   * describe how this query should be treated e.g. whether it should hit the\n   * server at all or just resolve from the cache, etc.\n   */\n  ApolloClient.prototype.query = function (options) {\n    if (this.defaultOptions.query) {\n      options = mergeOptions(this.defaultOptions.query, options);\n    }\n    invariant(options.fetchPolicy !== \"cache-and-network\", 17);\n    if (this.disableNetworkFetches && options.fetchPolicy === \"network-only\") {\n      options = __assign(__assign({}, options), {\n        fetchPolicy: \"cache-first\"\n      });\n    }\n    return this.queryManager.query(options);\n  };\n  /**\n   * This resolves a single mutation according to the options specified and returns a\n   * Promise which is either resolved with the resulting data or rejected with an\n   * error. In some cases both `data` and `errors` might be undefined, for example\n   * when `errorPolicy` is set to `'ignore'`.\n   *\n   * It takes options as an object with the following keys and values:\n   */\n  ApolloClient.prototype.mutate = function (options) {\n    if (this.defaultOptions.mutate) {\n      options = mergeOptions(this.defaultOptions.mutate, options);\n    }\n    return this.queryManager.mutate(options);\n  };\n  /**\n   * This subscribes to a graphql subscription according to the options specified and returns an\n   * `Observable` which either emits received data or an error.\n   */\n  ApolloClient.prototype.subscribe = function (options) {\n    var _this = this;\n    var id = this.queryManager.generateQueryId();\n    return this.queryManager.startGraphQLSubscription(options).map(function (result) {\n      return __assign(__assign({}, result), {\n        data: _this.queryManager.maskOperation({\n          document: options.query,\n          data: result.data,\n          fetchPolicy: options.fetchPolicy,\n          id: id\n        })\n      });\n    });\n  };\n  /**\n   * Tries to read some data from the store in the shape of the provided\n   * GraphQL query without making a network request. This method will start at\n   * the root query. To start at a specific id returned by `dataIdFromObject`\n   * use `readFragment`.\n   *\n   * @param optimistic - Set to `true` to allow `readQuery` to return\n   * optimistic results. Is `false` by default.\n   */\n  ApolloClient.prototype.readQuery = function (options, optimistic) {\n    if (optimistic === void 0) {\n      optimistic = false;\n    }\n    return this.cache.readQuery(options, optimistic);\n  };\n  /**\n   * Watches the cache store of the fragment according to the options specified\n   * and returns an `Observable`. We can subscribe to this\n   * `Observable` and receive updated results through an\n   * observer when the cache store changes.\n   *\n   * You must pass in a GraphQL document with a single fragment or a document\n   * with multiple fragments that represent what you are reading. If you pass\n   * in a document with multiple fragments then you must also specify a\n   * `fragmentName`.\n   *\n   * @since 3.10.0\n   * @param options - An object of type `WatchFragmentOptions` that allows\n   * the cache to identify the fragment and optionally specify whether to react\n   * to optimistic updates.\n   */\n  ApolloClient.prototype.watchFragment = function (options) {\n    var _a;\n    return this.cache.watchFragment(__assign(__assign({}, options), (_a = {}, _a[Symbol.for(\"apollo.dataMasking\")] = this.queryManager.dataMasking, _a)));\n  };\n  /**\n   * Tries to read some data from the store in the shape of the provided\n   * GraphQL fragment without making a network request. This method will read a\n   * GraphQL fragment from any arbitrary id that is currently cached, unlike\n   * `readQuery` which will only read from the root query.\n   *\n   * You must pass in a GraphQL document with a single fragment or a document\n   * with multiple fragments that represent what you are reading. If you pass\n   * in a document with multiple fragments then you must also specify a\n   * `fragmentName`.\n   *\n   * @param optimistic - Set to `true` to allow `readFragment` to return\n   * optimistic results. Is `false` by default.\n   */\n  ApolloClient.prototype.readFragment = function (options, optimistic) {\n    if (optimistic === void 0) {\n      optimistic = false;\n    }\n    return this.cache.readFragment(options, optimistic);\n  };\n  /**\n   * Writes some data in the shape of the provided GraphQL query directly to\n   * the store. This method will start at the root query. To start at a\n   * specific id returned by `dataIdFromObject` then use `writeFragment`.\n   */\n  ApolloClient.prototype.writeQuery = function (options) {\n    var ref = this.cache.writeQuery(options);\n    if (options.broadcast !== false) {\n      this.queryManager.broadcastQueries();\n    }\n    return ref;\n  };\n  /**\n   * Writes some data in the shape of the provided GraphQL fragment directly to\n   * the store. This method will write to a GraphQL fragment from any arbitrary\n   * id that is currently cached, unlike `writeQuery` which will only write\n   * from the root query.\n   *\n   * You must pass in a GraphQL document with a single fragment or a document\n   * with multiple fragments that represent what you are writing. If you pass\n   * in a document with multiple fragments then you must also specify a\n   * `fragmentName`.\n   */\n  ApolloClient.prototype.writeFragment = function (options) {\n    var ref = this.cache.writeFragment(options);\n    if (options.broadcast !== false) {\n      this.queryManager.broadcastQueries();\n    }\n    return ref;\n  };\n  ApolloClient.prototype.__actionHookForDevTools = function (cb) {\n    this.devToolsHookCb = cb;\n  };\n  ApolloClient.prototype.__requestRaw = function (payload) {\n    return execute(this.link, payload);\n  };\n  /**\n   * Resets your entire store by clearing out your cache and then re-executing\n   * all of your active queries. This makes it so that you may guarantee that\n   * there is no data left in your store from a time before you called this\n   * method.\n   *\n   * `resetStore()` is useful when your user just logged out. You’ve removed the\n   * user session, and you now want to make sure that any references to data you\n   * might have fetched while the user session was active is gone.\n   *\n   * It is important to remember that `resetStore()` *will* refetch any active\n   * queries. This means that any components that might be mounted will execute\n   * their queries again using your network interface. If you do not want to\n   * re-execute any queries then you should make sure to stop watching any\n   * active queries.\n   */\n  ApolloClient.prototype.resetStore = function () {\n    var _this = this;\n    return Promise.resolve().then(function () {\n      return _this.queryManager.clearStore({\n        discardWatches: false\n      });\n    }).then(function () {\n      return Promise.all(_this.resetStoreCallbacks.map(function (fn) {\n        return fn();\n      }));\n    }).then(function () {\n      return _this.reFetchObservableQueries();\n    });\n  };\n  /**\n   * Remove all data from the store. Unlike `resetStore`, `clearStore` will\n   * not refetch any active queries.\n   */\n  ApolloClient.prototype.clearStore = function () {\n    var _this = this;\n    return Promise.resolve().then(function () {\n      return _this.queryManager.clearStore({\n        discardWatches: true\n      });\n    }).then(function () {\n      return Promise.all(_this.clearStoreCallbacks.map(function (fn) {\n        return fn();\n      }));\n    });\n  };\n  /**\n   * Allows callbacks to be registered that are executed when the store is\n   * reset. `onResetStore` returns an unsubscribe function that can be used\n   * to remove registered callbacks.\n   */\n  ApolloClient.prototype.onResetStore = function (cb) {\n    var _this = this;\n    this.resetStoreCallbacks.push(cb);\n    return function () {\n      _this.resetStoreCallbacks = _this.resetStoreCallbacks.filter(function (c) {\n        return c !== cb;\n      });\n    };\n  };\n  /**\n   * Allows callbacks to be registered that are executed when the store is\n   * cleared. `onClearStore` returns an unsubscribe function that can be used\n   * to remove registered callbacks.\n   */\n  ApolloClient.prototype.onClearStore = function (cb) {\n    var _this = this;\n    this.clearStoreCallbacks.push(cb);\n    return function () {\n      _this.clearStoreCallbacks = _this.clearStoreCallbacks.filter(function (c) {\n        return c !== cb;\n      });\n    };\n  };\n  /**\n   * Refetches all of your active queries.\n   *\n   * `reFetchObservableQueries()` is useful if you want to bring the client back to proper state in case of a network outage\n   *\n   * It is important to remember that `reFetchObservableQueries()` *will* refetch any active\n   * queries. This means that any components that might be mounted will execute\n   * their queries again using your network interface. If you do not want to\n   * re-execute any queries then you should make sure to stop watching any\n   * active queries.\n   * Takes optional parameter `includeStandby` which will include queries in standby-mode when refetching.\n   */\n  ApolloClient.prototype.reFetchObservableQueries = function (includeStandby) {\n    return this.queryManager.reFetchObservableQueries(includeStandby);\n  };\n  /**\n   * Refetches specified active queries. Similar to \"reFetchObservableQueries()\" but with a specific list of queries.\n   *\n   * `refetchQueries()` is useful for use cases to imperatively refresh a selection of queries.\n   *\n   * It is important to remember that `refetchQueries()` *will* refetch specified active\n   * queries. This means that any components that might be mounted will execute\n   * their queries again using your network interface. If you do not want to\n   * re-execute any queries then you should make sure to stop watching any\n   * active queries.\n   */\n  ApolloClient.prototype.refetchQueries = function (options) {\n    var map = this.queryManager.refetchQueries(options);\n    var queries = [];\n    var results = [];\n    map.forEach(function (result, obsQuery) {\n      queries.push(obsQuery);\n      results.push(result);\n    });\n    var result = Promise.all(results);\n    // In case you need the raw results immediately, without awaiting\n    // Promise.all(results):\n    result.queries = queries;\n    result.results = results;\n    // If you decide to ignore the result Promise because you're using\n    // result.queries and result.results instead, you shouldn't have to worry\n    // about preventing uncaught rejections for the Promise.all result.\n    result.catch(function (error) {\n      globalThis.__DEV__ !== false && invariant.debug(18, error);\n    });\n    return result;\n  };\n  /**\n   * Get all currently active `ObservableQuery` objects, in a `Map` keyed by\n   * query ID strings.\n   *\n   * An \"active\" query is one that has observers and a `fetchPolicy` other than\n   * \"standby\" or \"cache-only\".\n   *\n   * You can include all `ObservableQuery` objects (including the inactive ones)\n   * by passing \"all\" instead of \"active\", or you can include just a subset of\n   * active queries by passing an array of query names or DocumentNode objects.\n   */\n  ApolloClient.prototype.getObservableQueries = function (include) {\n    if (include === void 0) {\n      include = \"active\";\n    }\n    return this.queryManager.getObservableQueries(include);\n  };\n  /**\n   * Exposes the cache's complete state, in a serializable format for later restoration.\n   */\n  ApolloClient.prototype.extract = function (optimistic) {\n    return this.cache.extract(optimistic);\n  };\n  /**\n   * Replaces existing state in the cache (if any) with the values expressed by\n   * `serializedState`.\n   *\n   * Called when hydrating a cache (server side rendering, or offline storage),\n   * and also (potentially) during hot reloads.\n   */\n  ApolloClient.prototype.restore = function (serializedState) {\n    return this.cache.restore(serializedState);\n  };\n  /**\n   * Add additional local resolvers.\n   */\n  ApolloClient.prototype.addResolvers = function (resolvers) {\n    this.localState.addResolvers(resolvers);\n  };\n  /**\n   * Set (override existing) local resolvers.\n   */\n  ApolloClient.prototype.setResolvers = function (resolvers) {\n    this.localState.setResolvers(resolvers);\n  };\n  /**\n   * Get all registered local resolvers.\n   */\n  ApolloClient.prototype.getResolvers = function () {\n    return this.localState.getResolvers();\n  };\n  /**\n   * Set a custom local state fragment matcher.\n   */\n  ApolloClient.prototype.setLocalStateFragmentMatcher = function (fragmentMatcher) {\n    this.localState.setFragmentMatcher(fragmentMatcher);\n  };\n  /**\n   * Define a new ApolloLink (or link chain) that Apollo Client will use.\n   */\n  ApolloClient.prototype.setLink = function (newLink) {\n    this.link = this.queryManager.link = newLink;\n  };\n  Object.defineProperty(ApolloClient.prototype, \"defaultContext\", {\n    get: function () {\n      return this.queryManager.defaultContext;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  return ApolloClient;\n}();\nexport { ApolloClient };\nif (globalThis.__DEV__ !== false) {\n  ApolloClient.prototype.getMemoryInternals = getApolloClientMemoryInternals;\n}\n//# sourceMappingURL=ApolloClient.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}