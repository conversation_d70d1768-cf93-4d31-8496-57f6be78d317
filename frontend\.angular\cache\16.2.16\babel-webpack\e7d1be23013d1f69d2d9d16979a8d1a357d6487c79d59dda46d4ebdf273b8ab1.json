{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { startOfQuarter } from \"./startOfQuarter.js\";\n\n/**\n * The {@link isSameQuarter} function options.\n */\n\n/**\n * @name isSameQuarter\n * @category Quarter Helpers\n * @summary Are the given dates in the same quarter (and year)?\n *\n * @description\n * Are the given dates in the same quarter (and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same quarter (and year)\n *\n * @example\n * // Are 1 January 2014 and 8 March 2014 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2014, 2, 8))\n * //=> true\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */\nexport function isSameQuarter(laterDate, earlierDate, options) {\n  const [dateLeft_, dateRight_] = normalizeDates(options?.in, laterDate, earlierDate);\n  return +startOfQuarter(dateLeft_) === +startOfQuarter(dateRight_);\n}\n\n// Fallback for modularized imports:\nexport default isSameQuarter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}