{"ast": null, "code": "import { __extends, __spreadArray } from \"tslib\";\nimport \"../utilities/globals/index.js\";\nimport { isNonNullObject } from \"../utilities/index.js\";\n// This Symbol allows us to pass transport-specific errors from the link chain\n// into QueryManager/client internals without risking a naming collision within\n// extensions (which implementers can use as they see fit).\nexport var PROTOCOL_ERRORS_SYMBOL = Symbol();\nexport function graphQLResultHasProtocolErrors(result) {\n  if (result.extensions) {\n    return Array.isArray(result.extensions[PROTOCOL_ERRORS_SYMBOL]);\n  }\n  return false;\n}\nexport function isApolloError(err) {\n  return err.hasOwnProperty(\"graphQLErrors\");\n}\n// Sets the error message on this error according to the\n// the GraphQL and network errors that are present.\n// If the error message has already been set through the\n// constructor or otherwise, this function is a nop.\nvar generateErrorMessage = function (err) {\n  var errors = __spreadArray(__spreadArray(__spreadArray([], err.graphQLErrors, true), err.clientErrors, true), err.protocolErrors, true);\n  if (err.networkError) errors.push(err.networkError);\n  return errors\n  // The rest of the code sometimes unsafely types non-Error objects as GraphQLErrors\n  .map(function (err) {\n    return isNonNullObject(err) && err.message || \"Error message not found.\";\n  }).join(\"\\n\");\n};\nvar ApolloError = /** @class */function (_super) {\n  __extends(ApolloError, _super);\n  // Constructs an instance of ApolloError given serialized GraphQL errors,\n  // client errors, protocol errors or network errors.\n  // Note that one of these has to be a valid\n  // value or the constructed error will be meaningless.\n  function ApolloError(_a) {\n    var graphQLErrors = _a.graphQLErrors,\n      protocolErrors = _a.protocolErrors,\n      clientErrors = _a.clientErrors,\n      networkError = _a.networkError,\n      errorMessage = _a.errorMessage,\n      extraInfo = _a.extraInfo;\n    var _this = _super.call(this, errorMessage) || this;\n    _this.name = \"ApolloError\";\n    _this.graphQLErrors = graphQLErrors || [];\n    _this.protocolErrors = protocolErrors || [];\n    _this.clientErrors = clientErrors || [];\n    _this.networkError = networkError || null;\n    _this.message = errorMessage || generateErrorMessage(_this);\n    _this.extraInfo = extraInfo;\n    _this.cause = __spreadArray(__spreadArray(__spreadArray([networkError], graphQLErrors || [], true), protocolErrors || [], true), clientErrors || [], true).find(function (e) {\n      return !!e;\n    }) || null;\n    // We're not using `Object.setPrototypeOf` here as it isn't fully\n    // supported on Android (see issue #3236).\n    _this.__proto__ = ApolloError.prototype;\n    return _this;\n  }\n  return ApolloError;\n}(Error);\nexport { ApolloError };\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}