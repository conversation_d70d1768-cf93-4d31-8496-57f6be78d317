{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { assertName } from '../type/assertName.mjs';\n/* c8 ignore start */\n\n/**\n * Upholds the spec rules about naming.\n * @deprecated Please use `assertName` instead. Will be removed in v17\n */\n\nexport function assertValidName(name) {\n  const error = isValidNameError(name);\n  if (error) {\n    throw error;\n  }\n  return name;\n}\n/**\n * Returns an Error if a name is invalid.\n * @deprecated Please use `assertName` instead. Will be removed in v17\n */\n\nexport function isValidNameError(name) {\n  typeof name === 'string' || devAssert(false, 'Expected name to be a string.');\n  if (name.startsWith('__')) {\n    return new GraphQLError(`Name \"${name}\" must not begin with \"__\", which is reserved by GraphQL introspection.`);\n  }\n  try {\n    assertName(name);\n  } catch (error) {\n    return error;\n  }\n}\n/* c8 ignore stop */", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}