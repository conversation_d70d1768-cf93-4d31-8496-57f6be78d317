{"ast": null, "code": "import { previousDay } from \"./previousDay.js\";\n\n/**\n * The {@link previousWednesday} function options.\n */\n\n/**\n * @name previousWednesday\n * @category Weekday Helpers\n * @summary When is the previous Wednesday?\n *\n * @description\n * When is the previous Wednesday?\n *\n * @typeParam DateType - The Date type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [UTCDate](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to start counting from\n * @param options - An object with options\n *\n * @returns The previous Wednesday\n *\n * @example\n * // When is the previous Wednesday before Jun, 18, 2021?\n * const result = previousWednesday(new Date(2021, 5, 18))\n * //=> Wed June 16 2021 00:00:00\n */\nexport function previousWednesday(date, options) {\n  return previousDay(date, 3, options);\n}\n\n// Fallback for modularized imports:\nexport default previousWednesday;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}