{"ast": null, "code": "\"use strict\";\n\n/**\n * Used to mark\n * [React Native `File` substitutes]{@link ReactNativeFileSubstitute} as it’s\n * too risky to assume all objects with `uri`, `type` and `name` properties are\n * extractable files.\n * @kind class\n * @name ReactNativeFile\n * @param {ReactNativeFileSubstitute} file A [React Native](https://reactnative.dev) [`File`](https://developer.mozilla.org/en-US/docs/Web/API/File) substitute.\n * @see [`extract-files` `ReactNativeFile` docs](https://github.com/jaydenseric/extract-files#class-reactnativefile).\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { ReactNativeFile } from \"apollo-upload-client\";\n * ```\n *\n * ```js\n * import ReactNativeFile from \"apollo-upload-client/public/ReactNativeFile.js\";\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { ReactNativeFile } = require(\"apollo-upload-client\");\n * ```\n *\n * ```js\n * const ReactNativeFile = require(\"apollo-upload-client/public/ReactNativeFile.js\");\n * ```\n * @example <caption>A file in [React Native](https://reactnative.dev) that can be used in query or mutation variables.</caption>\n * ```js\n * const file = new ReactNativeFile({\n *   uri: uriFromCameraRoll,\n *   name: \"a.jpg\",\n *   type: \"image/jpeg\",\n * });\n * ```\n */\nmodule.exports = require(\"extract-files/public/ReactNativeFile.js\");", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}