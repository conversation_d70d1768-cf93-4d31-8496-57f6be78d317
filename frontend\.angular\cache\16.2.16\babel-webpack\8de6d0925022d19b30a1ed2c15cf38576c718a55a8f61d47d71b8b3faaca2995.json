{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let PlanningCalendarComponent = /*#__PURE__*/(() => {\n  class PlanningCalendarComponent {\n    static {\n      this.ɵfac = function PlanningCalendarComponent_Factory(t) {\n        return new (t || PlanningCalendarComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PlanningCalendarComponent,\n        selectors: [[\"app-planning-calendar\"]],\n        decls: 2,\n        vars: 0,\n        template: function PlanningCalendarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"p\");\n            i0.ɵɵtext(1, \"planning-calendar works!\");\n            i0.ɵɵelementEnd();\n          }\n        }\n      });\n    }\n  }\n  return PlanningCalendarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}