{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { EquipeListComponent } from './equipe-list/equipe-list.component';\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\nimport { TaskListComponent } from './task-list/task-list.component';\nimport { EquipeComponent } from './equipe/equipe.component';\nimport { EquipeLayoutComponent } from './equipe-layout/equipe-layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: EquipeLayoutComponent,\n  children: [\n  // Liste des équipes\n  {\n    path: '',\n    component: EquipeComponent\n  }, {\n    path: 'liste',\n    component: EquipeListComponent\n  }, {\n    path: 'mes-equipes',\n    component: EquipeListComponent\n  },\n  // Formulaire pour ajouter une nouvelle équipe\n  {\n    path: 'ajouter',\n    component: EquipeFormComponent\n  }, {\n    path: 'nouveau',\n    component: EquipeFormComponent\n  },\n  // Formulaire pour modifier une équipe existante\n  {\n    path: 'modifier/:id',\n    component: EquipeFormComponent\n  },\n  // Détails d'une équipe spécifique\n  {\n    path: 'detail/:id',\n    component: EquipeDetailComponent\n  },\n  // Gestion des tâches d'une équipe\n  {\n    path: 'tasks/:id',\n    component: TaskListComponent\n  }]\n}];\nexport let EquipesRoutingModule = /*#__PURE__*/(() => {\n  class EquipesRoutingModule {\n    static {\n      this.ɵfac = function EquipesRoutingModule_Factory(t) {\n        return new (t || EquipesRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: EquipesRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return EquipesRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}