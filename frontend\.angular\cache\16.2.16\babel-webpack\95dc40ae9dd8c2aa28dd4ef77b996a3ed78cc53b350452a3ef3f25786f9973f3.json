{"ast": null, "code": "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { addWeeks } from \"./addWeeks.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\n\n/**\n * The {@link eachWeekOfInterval} function options.\n */\n\n/**\n * The {@link eachWeekOfInterval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the interval start date,\n * then the end interval date. If a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name eachWeekOfInterval\n * @category Interval Helpers\n * @summary Return the array of weeks within the specified time interval.\n *\n * @description\n * Return the array of weeks within the specified time interval.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of weeks from the week of the interval start to the week of the interval end\n *\n * @example\n * // Each week within interval 6 October 2014 - 23 November 2014:\n * const result = eachWeekOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 10, 23)\n * })\n * //=> [\n * //   Sun Oct 05 2014 00:00:00,\n * //   Sun Oct 12 2014 00:00:00,\n * //   Sun Oct 19 2014 00:00:00,\n * //   Sun Oct 26 2014 00:00:00,\n * //   Sun Nov 02 2014 00:00:00,\n * //   Sun Nov 09 2014 00:00:00,\n * //   Sun Nov 16 2014 00:00:00,\n * //   Sun Nov 23 2014 00:00:00\n * // ]\n */\nexport function eachWeekOfInterval(interval, options) {\n  const {\n    start,\n    end\n  } = normalizeInterval(options?.in, interval);\n  let reversed = +start > +end;\n  const startDateWeek = reversed ? startOfWeek(end, options) : startOfWeek(start, options);\n  const endDateWeek = reversed ? startOfWeek(start, options) : startOfWeek(end, options);\n  startDateWeek.setHours(15);\n  endDateWeek.setHours(15);\n  const endTime = +endDateWeek.getTime();\n  let currentDate = startDateWeek;\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n  const dates = [];\n  while (+currentDate <= endTime) {\n    currentDate.setHours(0);\n    dates.push(constructFrom(start, currentDate));\n    currentDate = addWeeks(currentDate, step);\n    currentDate.setHours(15);\n  }\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachWeekOfInterval;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}