{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isWednesday} function options.\n */\n\n/**\n * @name isWednesday\n * @category Weekday Helpers\n * @summary Is the given date Wednesday?\n *\n * @description\n * Is the given date Wednesday?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is Wednesday\n *\n * @example\n * // Is 24 September 2014 Wednesday?\n * const result = isWednesday(new Date(2014, 8, 24))\n * //=> true\n */\nexport function isWednesday(date, options) {\n  return toDate(date, options?.in).getDay() === 3;\n}\n\n// Fallback for modularized imports:\nexport default isWednesday;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}