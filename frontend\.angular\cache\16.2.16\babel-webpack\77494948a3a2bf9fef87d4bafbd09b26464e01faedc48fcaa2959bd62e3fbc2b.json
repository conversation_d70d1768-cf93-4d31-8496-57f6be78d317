{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link lastDayOfQuarter} function options.\n */\n\n/**\n * @name lastDayOfQuarter\n * @category Quarter Helpers\n * @summary Return the last day of a year quarter for the given date.\n *\n * @description\n * Return the last day of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The last day of a quarter\n *\n * @example\n * // The last day of a quarter for 2 September 2014 11:55:00:\n * const result = lastDayOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport function lastDayOfQuarter(date, options) {\n  const date_ = toDate(date, options?.in);\n  const currentMonth = date_.getMonth();\n  const month = currentMonth - currentMonth % 3 + 3;\n  date_.setMonth(month, 0);\n  date_.setHours(0, 0, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfQuarter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}