{"ast": null, "code": "import { addLeadingZeros } from \"./_lib/addLeadingZeros.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link formatRFC3339} function options.\n */\n\n/**\n * @name formatRFC3339\n * @category Common Helpers\n * @summary Format the date according to the RFC 3339 standard (https://tools.ietf.org/html/rfc3339#section-5.6).\n *\n * @description\n * Return the formatted date string in RFC 3339 format. Options may be passed to control the parts and notations of the date.\n *\n * @param date - The original date\n * @param options - An object with options.\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format:\n * formatRFC3339(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18T19:00:52Z'\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format, 3 digits of second fraction\n * formatRFC3339(new Date(2019, 8, 18, 19, 0, 52, 234), {\n *   fractionDigits: 3\n * })\n * //=> '2019-09-18T19:00:52.234Z'\n */\nexport function formatRFC3339(date, options) {\n  const date_ = toDate(date, options?.in);\n  if (!isValid(date_)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n  const fractionDigits = options?.fractionDigits ?? 0;\n  const day = addLeadingZeros(date_.getDate(), 2);\n  const month = addLeadingZeros(date_.getMonth() + 1, 2);\n  const year = date_.getFullYear();\n  const hour = addLeadingZeros(date_.getHours(), 2);\n  const minute = addLeadingZeros(date_.getMinutes(), 2);\n  const second = addLeadingZeros(date_.getSeconds(), 2);\n  let fractionalSecond = \"\";\n  if (fractionDigits > 0) {\n    const milliseconds = date_.getMilliseconds();\n    const fractionalSeconds = Math.trunc(milliseconds * Math.pow(10, fractionDigits - 3));\n    fractionalSecond = \".\" + addLeadingZeros(fractionalSeconds, fractionDigits);\n  }\n  let offset = \"\";\n  const tzOffset = date_.getTimezoneOffset();\n  if (tzOffset !== 0) {\n    const absoluteOffset = Math.abs(tzOffset);\n    const hourOffset = addLeadingZeros(Math.trunc(absoluteOffset / 60), 2);\n    const minuteOffset = addLeadingZeros(absoluteOffset % 60, 2);\n    // If less than 0, the sign is +, because it is ahead of time.\n    const sign = tzOffset < 0 ? \"+\" : \"-\";\n    offset = `${sign}${hourOffset}:${minuteOffset}`;\n  } else {\n    offset = \"Z\";\n  }\n  return `${year}-${month}-${day}T${hour}:${minute}:${second}${fractionalSecond}${offset}`;\n}\n\n// Fallback for modularized imports:\nexport default formatRFC3339;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}