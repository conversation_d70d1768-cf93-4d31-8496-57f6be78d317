{"ast": null, "code": "import { quartersInYear } from \"./constants.js\";\n\n/**\n * @name yearsToQuarters\n * @category Conversion Helpers\n * @summary Convert years to quarters.\n *\n * @description\n * Convert a number of years to a full number of quarters.\n *\n * @param years - The number of years to be converted\n *\n * @returns The number of years converted in quarters\n *\n * @example\n * // Convert 2 years to quarters\n * const result = yearsToQuarters(2)\n * //=> 8\n */\nexport function yearsToQuarters(years) {\n  return Math.trunc(years * quartersInYear);\n}\n\n// Fallback for modularized imports:\nexport default yearsToQuarters;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}