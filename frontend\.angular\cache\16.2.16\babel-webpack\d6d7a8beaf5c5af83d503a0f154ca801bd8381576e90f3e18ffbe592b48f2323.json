{"ast": null, "code": "export var MessageType = /*#__PURE__*/function (MessageType) {\n  MessageType[\"TEXT\"] = \"TEXT\";\n  MessageType[\"IMAGE\"] = \"IMAGE\";\n  MessageType[\"FILE\"] = \"FILE\";\n  MessageType[\"AUDIO\"] = \"AUDIO\";\n  MessageType[\"VIDEO\"] = \"VIDEO\";\n  MessageType[\"SYSTEM\"] = \"SYSTEM\";\n  MessageType[\"VOICE_MESSAGE\"] = \"VOICE_MESSAGE\";\n  MessageType[\"TEXT_LOWER\"] = \"text\";\n  MessageType[\"IMAGE_LOWER\"] = \"image\";\n  MessageType[\"FILE_LOWER\"] = \"file\";\n  MessageType[\"AUDIO_LOWER\"] = \"audio\";\n  MessageType[\"VIDEO_LOWER\"] = \"video\";\n  MessageType[\"SYSTEM_LOWER\"] = \"system\";\n  MessageType[\"VOICE_MESSAGE_LOWER\"] = \"voice_message\";\n  return MessageType;\n}(MessageType || {});\nexport var MessageStatus = /*#__PURE__*/function (MessageStatus) {\n  MessageStatus[\"SENDING\"] = \"SENDING\";\n  MessageStatus[\"SENT\"] = \"SENT\";\n  MessageStatus[\"DELIVERED\"] = \"DELIVERED\";\n  MessageStatus[\"READ\"] = \"READ\";\n  MessageStatus[\"FAILED\"] = \"FAILED\";\n  return MessageStatus;\n}(MessageStatus || {});\n// --------------------------------------------------------------------------\n// Types et interfaces pour les appels\n// --------------------------------------------------------------------------\n/**\n * Types d'appels possibles\n */\nexport var CallType = /*#__PURE__*/function (CallType) {\n  CallType[\"AUDIO\"] = \"AUDIO\";\n  CallType[\"VIDEO\"] = \"VIDEO\";\n  CallType[\"VIDEO_ONLY\"] = \"VIDEO_ONLY\";\n  return CallType;\n}(CallType || {});\n/**\n * États possibles d'un appel\n */\nexport var CallStatus = /*#__PURE__*/function (CallStatus) {\n  CallStatus[\"RINGING\"] = \"RINGING\";\n  CallStatus[\"CONNECTED\"] = \"CONNECTED\";\n  CallStatus[\"ENDED\"] = \"ENDED\";\n  CallStatus[\"MISSED\"] = \"MISSED\";\n  CallStatus[\"REJECTED\"] = \"REJECTED\";\n  CallStatus[\"FAILED\"] = \"FAILED\";\n  return CallStatus;\n}(CallStatus || {});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}