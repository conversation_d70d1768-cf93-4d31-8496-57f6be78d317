{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { isPrintableAsBlockString } from '../language/blockString.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { print } from '../language/printer.mjs';\nimport { isEnumType, isInputObjectType, isInterfaceType, isObjectType, isScalarType, isUnionType } from '../type/definition.mjs';\nimport { DEFAULT_DEPRECATION_REASON, isSpecifiedDirective } from '../type/directives.mjs';\nimport { isIntrospectionType } from '../type/introspection.mjs';\nimport { isSpecifiedScalarType } from '../type/scalars.mjs';\nimport { astFromValue } from './astFromValue.mjs';\nexport function printSchema(schema) {\n  return printFilteredSchema(schema, n => !isSpecifiedDirective(n), isDefinedType);\n}\nexport function printIntrospectionSchema(schema) {\n  return printFilteredSchema(schema, isSpecifiedDirective, isIntrospectionType);\n}\nfunction isDefinedType(type) {\n  return !isSpecifiedScalarType(type) && !isIntrospectionType(type);\n}\nfunction printFilteredSchema(schema, directiveFilter, typeFilter) {\n  const directives = schema.getDirectives().filter(directiveFilter);\n  const types = Object.values(schema.getTypeMap()).filter(typeFilter);\n  return [printSchemaDefinition(schema), ...directives.map(directive => printDirective(directive)), ...types.map(type => printType(type))].filter(Boolean).join('\\n\\n');\n}\nfunction printSchemaDefinition(schema) {\n  if (schema.description == null && isSchemaOfCommonNames(schema)) {\n    return;\n  }\n  const operationTypes = [];\n  const queryType = schema.getQueryType();\n  if (queryType) {\n    operationTypes.push(`  query: ${queryType.name}`);\n  }\n  const mutationType = schema.getMutationType();\n  if (mutationType) {\n    operationTypes.push(`  mutation: ${mutationType.name}`);\n  }\n  const subscriptionType = schema.getSubscriptionType();\n  if (subscriptionType) {\n    operationTypes.push(`  subscription: ${subscriptionType.name}`);\n  }\n  return printDescription(schema) + `schema {\\n${operationTypes.join('\\n')}\\n}`;\n}\n/**\n * GraphQL schema define root types for each type of operation. These types are\n * the same as any other type and can be named in any manner, however there is\n * a common naming convention:\n *\n * ```graphql\n *   schema {\n *     query: Query\n *     mutation: Mutation\n *     subscription: Subscription\n *   }\n * ```\n *\n * When using this naming convention, the schema description can be omitted.\n */\n\nfunction isSchemaOfCommonNames(schema) {\n  const queryType = schema.getQueryType();\n  if (queryType && queryType.name !== 'Query') {\n    return false;\n  }\n  const mutationType = schema.getMutationType();\n  if (mutationType && mutationType.name !== 'Mutation') {\n    return false;\n  }\n  const subscriptionType = schema.getSubscriptionType();\n  if (subscriptionType && subscriptionType.name !== 'Subscription') {\n    return false;\n  }\n  return true;\n}\nexport function printType(type) {\n  if (isScalarType(type)) {\n    return printScalar(type);\n  }\n  if (isObjectType(type)) {\n    return printObject(type);\n  }\n  if (isInterfaceType(type)) {\n    return printInterface(type);\n  }\n  if (isUnionType(type)) {\n    return printUnion(type);\n  }\n  if (isEnumType(type)) {\n    return printEnum(type);\n  }\n  if (isInputObjectType(type)) {\n    return printInputObject(type);\n  }\n  /* c8 ignore next 3 */\n  // Not reachable, all possible types have been considered.\n\n  false || invariant(false, 'Unexpected type: ' + inspect(type));\n}\nfunction printScalar(type) {\n  return printDescription(type) + `scalar ${type.name}` + printSpecifiedByURL(type);\n}\nfunction printImplementedInterfaces(type) {\n  const interfaces = type.getInterfaces();\n  return interfaces.length ? ' implements ' + interfaces.map(i => i.name).join(' & ') : '';\n}\nfunction printObject(type) {\n  return printDescription(type) + `type ${type.name}` + printImplementedInterfaces(type) + printFields(type);\n}\nfunction printInterface(type) {\n  return printDescription(type) + `interface ${type.name}` + printImplementedInterfaces(type) + printFields(type);\n}\nfunction printUnion(type) {\n  const types = type.getTypes();\n  const possibleTypes = types.length ? ' = ' + types.join(' | ') : '';\n  return printDescription(type) + 'union ' + type.name + possibleTypes;\n}\nfunction printEnum(type) {\n  const values = type.getValues().map((value, i) => printDescription(value, '  ', !i) + '  ' + value.name + printDeprecated(value.deprecationReason));\n  return printDescription(type) + `enum ${type.name}` + printBlock(values);\n}\nfunction printInputObject(type) {\n  const fields = Object.values(type.getFields()).map((f, i) => printDescription(f, '  ', !i) + '  ' + printInputValue(f));\n  return printDescription(type) + `input ${type.name}` + printBlock(fields);\n}\nfunction printFields(type) {\n  const fields = Object.values(type.getFields()).map((f, i) => printDescription(f, '  ', !i) + '  ' + f.name + printArgs(f.args, '  ') + ': ' + String(f.type) + printDeprecated(f.deprecationReason));\n  return printBlock(fields);\n}\nfunction printBlock(items) {\n  return items.length !== 0 ? ' {\\n' + items.join('\\n') + '\\n}' : '';\n}\nfunction printArgs(args, indentation = '') {\n  if (args.length === 0) {\n    return '';\n  } // If every arg does not have a description, print them on one line.\n\n  if (args.every(arg => !arg.description)) {\n    return '(' + args.map(printInputValue).join(', ') + ')';\n  }\n  return '(\\n' + args.map((arg, i) => printDescription(arg, '  ' + indentation, !i) + '  ' + indentation + printInputValue(arg)).join('\\n') + '\\n' + indentation + ')';\n}\nfunction printInputValue(arg) {\n  const defaultAST = astFromValue(arg.defaultValue, arg.type);\n  let argDecl = arg.name + ': ' + String(arg.type);\n  if (defaultAST) {\n    argDecl += ` = ${print(defaultAST)}`;\n  }\n  return argDecl + printDeprecated(arg.deprecationReason);\n}\nfunction printDirective(directive) {\n  return printDescription(directive) + 'directive @' + directive.name + printArgs(directive.args) + (directive.isRepeatable ? ' repeatable' : '') + ' on ' + directive.locations.join(' | ');\n}\nfunction printDeprecated(reason) {\n  if (reason == null) {\n    return '';\n  }\n  if (reason !== DEFAULT_DEPRECATION_REASON) {\n    const astValue = print({\n      kind: Kind.STRING,\n      value: reason\n    });\n    return ` @deprecated(reason: ${astValue})`;\n  }\n  return ' @deprecated';\n}\nfunction printSpecifiedByURL(scalar) {\n  if (scalar.specifiedByURL == null) {\n    return '';\n  }\n  const astValue = print({\n    kind: Kind.STRING,\n    value: scalar.specifiedByURL\n  });\n  return ` @specifiedBy(url: ${astValue})`;\n}\nfunction printDescription(def, indentation = '', firstInBlock = true) {\n  const {\n    description\n  } = def;\n  if (description == null) {\n    return '';\n  }\n  const blockString = print({\n    kind: Kind.STRING,\n    value: description,\n    block: isPrintableAsBlockString(description)\n  });\n  const prefix = indentation && !firstInBlock ? '\\n' + indentation : indentation;\n  return prefix + blockString.replace(/\\n/g, '\\n' + indentation) + '\\n';\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}