{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { print } from '../../language/printer.mjs';\nimport { isInputType } from '../../type/definition.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\n/**\n * Variables are input types\n *\n * A GraphQL operation is only valid if all the variables it defines are of\n * input types (scalar, enum, or input object).\n *\n * See https://spec.graphql.org/draft/#sec-Variables-Are-Input-Types\n */\nexport function VariablesAreInputTypesRule(context) {\n  return {\n    VariableDefinition(node) {\n      const type = typeFromAST(context.getSchema(), node.type);\n      if (type !== undefined && !isInputType(type)) {\n        const variableName = node.variable.name.value;\n        const typeName = print(node.type);\n        context.reportError(new GraphQLError(`Variable \"$${variableName}\" cannot be non-input type \"${typeName}\".`, {\n          nodes: node.type\n        }));\n      }\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}