{"ast": null, "code": "import { __assign, __rest } from \"tslib\";\nimport { wrap } from \"optimism\";\nimport { Observable, cacheSizes, getFragmentDefinition, getFragmentQueryDocument, mergeDeepArray } from \"../../utilities/index.js\";\nimport { WeakCache } from \"@wry/caches\";\nimport { getApolloCacheMemoryInternals } from \"../../utilities/caching/getMemoryInternals.js\";\nimport { equalByQuery } from \"../../core/equalByQuery.js\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { maskFragment } from \"../../masking/index.js\";\nvar ApolloCache = /** @class */function () {\n  function ApolloCache() {\n    this.assumeImmutableResults = false;\n    // Make sure we compute the same (===) fragment query document every\n    // time we receive the same fragment in readFragment.\n    this.getFragmentDoc = wrap(getFragmentQueryDocument, {\n      max: cacheSizes[\"cache.fragmentQueryDocuments\"] || 1000 /* defaultCacheSizes[\"cache.fragmentQueryDocuments\"] */,\n      cache: WeakCache\n    });\n  }\n  // Function used to lookup a fragment when a fragment definition is not part\n  // of the GraphQL document. This is useful for caches, such as InMemoryCache,\n  // that register fragments ahead of time so they can be referenced by name.\n  ApolloCache.prototype.lookupFragment = function (fragmentName) {\n    return null;\n  };\n  // Transactional API\n  // The batch method is intended to replace/subsume both performTransaction\n  // and recordOptimisticTransaction, but performTransaction came first, so we\n  // provide a default batch implementation that's just another way of calling\n  // performTransaction. Subclasses of ApolloCache (such as InMemoryCache) can\n  // override the batch method to do more interesting things with its options.\n  ApolloCache.prototype.batch = function (options) {\n    var _this = this;\n    var optimisticId = typeof options.optimistic === \"string\" ? options.optimistic : options.optimistic === false ? null : void 0;\n    var updateResult;\n    this.performTransaction(function () {\n      return updateResult = options.update(_this);\n    }, optimisticId);\n    return updateResult;\n  };\n  ApolloCache.prototype.recordOptimisticTransaction = function (transaction, optimisticId) {\n    this.performTransaction(transaction, optimisticId);\n  };\n  // Optional API\n  // Called once per input document, allowing the cache to make static changes\n  // to the query, such as adding __typename fields.\n  ApolloCache.prototype.transformDocument = function (document) {\n    return document;\n  };\n  // Called before each ApolloLink request, allowing the cache to make dynamic\n  // changes to the query, such as filling in missing fragment definitions.\n  ApolloCache.prototype.transformForLink = function (document) {\n    return document;\n  };\n  ApolloCache.prototype.identify = function (object) {\n    return;\n  };\n  ApolloCache.prototype.gc = function () {\n    return [];\n  };\n  ApolloCache.prototype.modify = function (options) {\n    return false;\n  };\n  // DataProxy API\n  ApolloCache.prototype.readQuery = function (options, optimistic) {\n    if (optimistic === void 0) {\n      optimistic = !!options.optimistic;\n    }\n    return this.read(__assign(__assign({}, options), {\n      rootId: options.id || \"ROOT_QUERY\",\n      optimistic: optimistic\n    }));\n  };\n  /** {@inheritDoc @apollo/client!ApolloClient#watchFragment:member(1)} */\n  ApolloCache.prototype.watchFragment = function (options) {\n    var _this = this;\n    var fragment = options.fragment,\n      fragmentName = options.fragmentName,\n      from = options.from,\n      _a = options.optimistic,\n      optimistic = _a === void 0 ? true : _a,\n      otherOptions = __rest(options, [\"fragment\", \"fragmentName\", \"from\", \"optimistic\"]);\n    var query = this.getFragmentDoc(fragment, fragmentName);\n    // While our TypeScript types do not allow for `undefined` as a valid\n    // `from`, its possible `useFragment` gives us an `undefined` since it\n    // calls` cache.identify` and provides that value to `from`. We are\n    // adding this fix here however to ensure those using plain JavaScript\n    // and using `cache.identify` themselves will avoid seeing the obscure\n    // warning.\n    var id = typeof from === \"undefined\" || typeof from === \"string\" ? from : this.identify(from);\n    var dataMasking = !!options[Symbol.for(\"apollo.dataMasking\")];\n    if (globalThis.__DEV__ !== false) {\n      var actualFragmentName = fragmentName || getFragmentDefinition(fragment).name.value;\n      if (!id) {\n        globalThis.__DEV__ !== false && invariant.warn(1, actualFragmentName);\n      }\n    }\n    var diffOptions = __assign(__assign({}, otherOptions), {\n      returnPartialData: true,\n      id: id,\n      query: query,\n      optimistic: optimistic\n    });\n    var latestDiff;\n    return new Observable(function (observer) {\n      return _this.watch(__assign(__assign({}, diffOptions), {\n        immediate: true,\n        callback: function (diff) {\n          var data = dataMasking ? maskFragment(diff.result, fragment, _this, fragmentName) : diff.result;\n          if (\n          // Always ensure we deliver the first result\n          latestDiff && equalByQuery(query, {\n            data: latestDiff.result\n          }, {\n            data: data\n          },\n          // TODO: Fix the type on WatchFragmentOptions so that TVars\n          // extends OperationVariables\n          options.variables)) {\n            return;\n          }\n          var result = {\n            data: data,\n            complete: !!diff.complete\n          };\n          if (diff.missing) {\n            result.missing = mergeDeepArray(diff.missing.map(function (error) {\n              return error.missing;\n            }));\n          }\n          latestDiff = __assign(__assign({}, diff), {\n            result: data\n          });\n          observer.next(result);\n        }\n      }));\n    });\n  };\n  ApolloCache.prototype.readFragment = function (options, optimistic) {\n    if (optimistic === void 0) {\n      optimistic = !!options.optimistic;\n    }\n    return this.read(__assign(__assign({}, options), {\n      query: this.getFragmentDoc(options.fragment, options.fragmentName),\n      rootId: options.id,\n      optimistic: optimistic\n    }));\n  };\n  ApolloCache.prototype.writeQuery = function (_a) {\n    var id = _a.id,\n      data = _a.data,\n      options = __rest(_a, [\"id\", \"data\"]);\n    return this.write(Object.assign(options, {\n      dataId: id || \"ROOT_QUERY\",\n      result: data\n    }));\n  };\n  ApolloCache.prototype.writeFragment = function (_a) {\n    var id = _a.id,\n      data = _a.data,\n      fragment = _a.fragment,\n      fragmentName = _a.fragmentName,\n      options = __rest(_a, [\"id\", \"data\", \"fragment\", \"fragmentName\"]);\n    return this.write(Object.assign(options, {\n      query: this.getFragmentDoc(fragment, fragmentName),\n      dataId: id,\n      result: data\n    }));\n  };\n  ApolloCache.prototype.updateQuery = function (options, update) {\n    return this.batch({\n      update: function (cache) {\n        var value = cache.readQuery(options);\n        var data = update(value);\n        if (data === void 0 || data === null) return value;\n        cache.writeQuery(__assign(__assign({}, options), {\n          data: data\n        }));\n        return data;\n      }\n    });\n  };\n  ApolloCache.prototype.updateFragment = function (options, update) {\n    return this.batch({\n      update: function (cache) {\n        var value = cache.readFragment(options);\n        var data = update(value);\n        if (data === void 0 || data === null) return value;\n        cache.writeFragment(__assign(__assign({}, options), {\n          data: data\n        }));\n        return data;\n      }\n    });\n  };\n  return ApolloCache;\n}();\nexport { ApolloCache };\nif (globalThis.__DEV__ !== false) {\n  ApolloCache.prototype.getMemoryInternals = getApolloCacheMemoryInternals;\n}\n//# sourceMappingURL=cache.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}