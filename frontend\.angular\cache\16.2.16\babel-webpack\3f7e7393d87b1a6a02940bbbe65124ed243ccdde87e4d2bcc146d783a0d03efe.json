{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { isTypeDefinitionNode, isTypeExtensionNode } from '../../language/predicates.mjs';\nimport { specifiedDirectives } from '../../type/directives.mjs';\n\n/**\n * Unique directive names per location\n *\n * A GraphQL document is only valid if all non-repeatable directives at\n * a given location are uniquely named.\n *\n * See https://spec.graphql.org/draft/#sec-Directives-Are-Unique-Per-Location\n */\nexport function UniqueDirectivesPerLocationRule(context) {\n  const uniqueDirectiveMap = Object.create(null);\n  const schema = context.getSchema();\n  const definedDirectives = schema ? schema.getDirectives() : specifiedDirectives;\n  for (const directive of definedDirectives) {\n    uniqueDirectiveMap[directive.name] = !directive.isRepeatable;\n  }\n  const astDefinitions = context.getDocument().definitions;\n  for (const def of astDefinitions) {\n    if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      uniqueDirectiveMap[def.name.value] = !def.repeatable;\n    }\n  }\n  const schemaDirectives = Object.create(null);\n  const typeDirectivesMap = Object.create(null);\n  return {\n    // Many different AST nodes may contain directives. Rather than listing\n    // them all, just listen for entering any node, and check to see if it\n    // defines any directives.\n    enter(node) {\n      if (!('directives' in node) || !node.directives) {\n        return;\n      }\n      let seenDirectives;\n      if (node.kind === Kind.SCHEMA_DEFINITION || node.kind === Kind.SCHEMA_EXTENSION) {\n        seenDirectives = schemaDirectives;\n      } else if (isTypeDefinitionNode(node) || isTypeExtensionNode(node)) {\n        const typeName = node.name.value;\n        seenDirectives = typeDirectivesMap[typeName];\n        if (seenDirectives === undefined) {\n          typeDirectivesMap[typeName] = seenDirectives = Object.create(null);\n        }\n      } else {\n        seenDirectives = Object.create(null);\n      }\n      for (const directive of node.directives) {\n        const directiveName = directive.name.value;\n        if (uniqueDirectiveMap[directiveName]) {\n          if (seenDirectives[directiveName]) {\n            context.reportError(new GraphQLError(`The directive \"@${directiveName}\" can only be used once at this location.`, {\n              nodes: [seenDirectives[directiveName], directive]\n            }));\n          } else {\n            seenDirectives[directiveName] = directive;\n          }\n        }\n      }\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}