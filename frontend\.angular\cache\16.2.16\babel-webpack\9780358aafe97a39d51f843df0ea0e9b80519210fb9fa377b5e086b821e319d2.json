{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/services/data.service\";\nimport * as i3 from \"@angular/common\";\nfunction UserdetailsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"div\", 6);\n    i0.ɵɵelementStart(2, \"p\", 7);\n    i0.ɵɵtext(3, \"Chargement des donn\\u00E9es utilisateur...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserdetailsComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function UserdetailsComponent_div_2_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.messageSuccess = \"\");\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 10);\n    i0.ɵɵelement(5, \"path\", 11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.messageSuccess);\n  }\n}\nfunction UserdetailsComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.messageErr, \" \");\n  }\n}\nfunction UserdetailsComponent_div_4_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"img\", 60);\n    i0.ɵɵelementStart(2, \"div\", 61)(3, \"span\", 62);\n    i0.ɵɵtext(4, \"Voir\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r6.userObject.image, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UserdetailsComponent_div_4_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r7.userObject.fullName ? ctx_r7.userObject.fullName.charAt(0) : \"\") || (ctx_r7.userObject.username ? ctx_r7.userObject.username.charAt(0) : \"\") || \"U\", \" \");\n  }\n}\nfunction UserdetailsComponent_div_4_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵelement(1, \"span\", 65);\n    i0.ɵɵtext(2, \" En ligne \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"bg-[#dac4ea]/30 text-[#7826b5]\": a0,\n    \"bg-[#afcf75]/20 text-[#2a5a03]\": a1,\n    \"bg-[#4a89ce]/20 text-[#4f5fad]\": a2\n  };\n};\nfunction UserdetailsComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 16)(3, \"div\")(4, \"h1\", 17);\n    i0.ɵɵtext(5, \" D\\u00E9tails de l'utilisateur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 18);\n    i0.ɵɵtext(7, \" Informations compl\\u00E8tes sur le profil \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 19)(9, \"span\", 20);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 21)(12, \"div\", 22);\n    i0.ɵɵtemplate(13, UserdetailsComponent_div_4_div_13_Template, 5, 1, \"div\", 23);\n    i0.ɵɵtemplate(14, UserdetailsComponent_div_4_div_14_Template, 2, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 25)(16, \"h2\", 26);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 27)(19, \"span\", 28);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 29);\n    i0.ɵɵelement(23, \"span\", 30);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, UserdetailsComponent_div_4_span_25_Template, 3, 0, \"span\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 32)(27, \"a\", 33);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 34);\n    i0.ɵɵelement(29, \"path\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(31, \"div\", 36)(32, \"div\", 37)(33, \"h3\", 38);\n    i0.ɵɵtext(34, \" Informations de base \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 16)(36, \"span\", 39);\n    i0.ɵɵtext(37, \"Nom d'utilisateur :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\", 40);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 16)(41, \"span\", 39);\n    i0.ɵɵtext(42, \"Nom complet :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\", 40);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 16)(46, \"span\", 39);\n    i0.ɵɵtext(47, \"Email :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"span\", 40);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 16)(51, \"span\", 39);\n    i0.ɵɵtext(52, \"R\\u00F4le :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"span\", 40);\n    i0.ɵɵtext(54);\n    i0.ɵɵpipe(55, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 16)(57, \"span\", 39);\n    i0.ɵɵtext(58, \"Groupe :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"span\", 40);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 16)(62, \"span\", 39);\n    i0.ɵɵtext(63, \"V\\u00E9rifi\\u00E9 :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"span\", 41);\n    i0.ɵɵtext(65);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(66, \"div\", 37)(67, \"h3\", 38);\n    i0.ɵɵtext(68, \" Statut et dates \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 16)(70, \"span\", 39);\n    i0.ɵɵtext(71, \"Statut :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"span\", 41);\n    i0.ɵɵtext(73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(74, \"div\", 16)(75, \"span\", 39);\n    i0.ɵɵtext(76, \"En ligne :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"span\", 41);\n    i0.ɵɵtext(78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(79, \"div\", 16)(80, \"span\", 39);\n    i0.ɵɵtext(81, \"Derni\\u00E8re activit\\u00E9 :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(82, \"span\", 40);\n    i0.ɵɵtext(83);\n    i0.ɵɵpipe(84, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"div\", 16)(86, \"span\", 39);\n    i0.ɵɵtext(87, \"Cr\\u00E9\\u00E9 le :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"span\", 40);\n    i0.ɵɵtext(89);\n    i0.ɵɵpipe(90, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(91, \"div\", 16)(92, \"span\", 39);\n    i0.ɵɵtext(93, \"Mis \\u00E0 jour le :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(94, \"span\", 40);\n    i0.ɵɵtext(95);\n    i0.ɵɵpipe(96, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(97, \"div\", 42)(98, \"details\", 43)(99, \"summary\", 44)(100, \"span\");\n    i0.ɵɵtext(101, \"D\\u00E9tails techniques\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(102, \"svg\", 45);\n    i0.ɵɵelement(103, \"path\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(104, \"div\", 47)(105, \"div\", 48)(106, \"div\")(107, \"p\", 49);\n    i0.ɵɵtext(108, \" ID: \");\n    i0.ɵɵelementStart(109, \"span\", 40);\n    i0.ɵɵtext(110);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(111, \"p\", 49);\n    i0.ɵɵtext(112, \" Version: \");\n    i0.ɵɵelementStart(113, \"span\", 40);\n    i0.ɵɵtext(114);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(115, \"div\")(116, \"p\", 49);\n    i0.ɵɵtext(117, \" Image URL: \");\n    i0.ɵɵelementStart(118, \"span\", 50);\n    i0.ɵɵtext(119);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(120, \"p\", 49);\n    i0.ɵɵtext(121, \" Profile Image URL: \");\n    i0.ɵɵelementStart(122, \"span\", 50);\n    i0.ɵɵtext(123);\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(124, \"div\", 51)(125, \"div\", 52)(126, \"span\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(127, \"svg\", 54);\n    i0.ɵɵelement(128, \"path\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(129);\n    i0.ɵɵpipe(130, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(131, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function UserdetailsComponent_div_4_Template_button_click_131_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.goBack());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(132, \"svg\", 57);\n    i0.ɵɵelement(133, \"path\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(134, \" Retour au tableau de bord \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" ID: \", ctx_r3.userObject._id, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.userObject.image);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.userObject.image);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.userObject.fullName || ctx_r3.userObject.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(47, _c0, ctx_r3.userObject.role === \"admin\", ctx_r3.userObject.role === \"teacher\", ctx_r3.userObject.role === \"student\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(21, 31, ctx_r3.userObject.role), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.userObject.isActive ? \"bg-[#afcf75]/20 text-[#2a5a03]\" : \"bg-[#ff6b69]/20 text-[#ff6b69]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.userObject.isActive ? \"bg-[#2a5a03]\" : \"bg-[#ff6b69]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.userObject.isActive ? \"Actif\" : \"Inactif\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.userObject.isOnline);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"href\", \"mailto:\", ctx_r3.userObject.email, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.userObject.email, \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject.username);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject.fullName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject.email);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(55, 33, ctx_r3.userObject.role));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject.group ? ctx_r3.userObject.group : \"Aucun groupe\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.userObject.verified ? \"text-[#2a5a03]\" : \"text-[#ff6b69]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.userObject.verified ? \"Oui\" : \"Non\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.userObject.isActive ? \"text-[#2a5a03]\" : \"text-[#ff6b69]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.userObject.isActive ? \"Actif\" : \"Inactif\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.userObject.isOnline ? \"text-[#4a89ce]\" : \"text-[#6d6870]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.userObject.isOnline ? \"Oui\" : \"Non\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(84, 35, ctx_r3.userObject.lastActive, \"dd/MM/yyyy HH:mm\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(90, 38, ctx_r3.userObject.createdAt, \"dd/MM/yyyy HH:mm\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(96, 41, ctx_r3.userObject.updatedAt, \"dd/MM/yyyy HH:mm\"));\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject._id);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject.__v);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject.image);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.userObject.profileImage);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Derni\\u00E8re activit\\u00E9: \", i0.ɵɵpipeBind2(130, 44, ctx_r3.userObject.lastActive, \"dd/MM/yyyy HH:mm\"), \" \");\n  }\n}\nexport let UserdetailsComponent = /*#__PURE__*/(() => {\n  class UserdetailsComponent {\n    constructor(route, ds, router) {\n      this.route = route;\n      this.ds = ds;\n      this.router = router;\n      this.messageErr = '';\n      this.messageSuccess = '';\n      this.isLoading = true;\n    }\n    ngOnInit() {\n      const userId = this.route.snapshot.paramMap.get('id');\n      if (userId) {\n        this.loadUser(userId);\n      } else {\n        this.messageErr = 'ID utilisateur non valide';\n        this.isLoading = false;\n      }\n    }\n    loadUser(id) {\n      this.isLoading = true;\n      this.messageErr = '';\n      this.subscription = this.ds.getOneUser(id).subscribe({\n        next: response => {\n          this.userObject = response;\n          this.isLoading = false;\n        },\n        error: err => {\n          this.messageErr = err.status === 404 ? 'Utilisateur non trouvé' : 'Erreur lors du chargement des données';\n          this.isLoading = false;\n        }\n      });\n    }\n    goBack() {\n      this.router.navigate(['/admin/dashboard']);\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static {\n      this.ɵfac = function UserdetailsComponent_Factory(t) {\n        return new (t || UserdetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.DataService), i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: UserdetailsComponent,\n        selectors: [[\"app-userdetails\"]],\n        decls: 5,\n        vars: 4,\n        consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"min-h-screen\"], [\"class\", \"flex flex-col items-center justify-center py-12\", 4, \"ngIf\"], [\"class\", \"bg-[#afcf75]/20 border border-[#afcf75] text-[#2a5a03] p-4 rounded-lg mb-6 flex justify-between items-center\", 4, \"ngIf\"], [\"class\", \"flex justify-center mb-6\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-xl shadow-md overflow-hidden max-w-5xl mx-auto\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-12\"], [1, \"animate-spin\", \"rounded-full\", \"h-10\", \"w-10\", \"border-t-2\", \"border-b-2\", \"border-[#4f5fad]\"], [1, \"mt-4\", \"text-[#6d6870]\"], [1, \"bg-[#afcf75]/20\", \"border\", \"border-[#afcf75]\", \"text-[#2a5a03]\", \"p-4\", \"rounded-lg\", \"mb-6\", \"flex\", \"justify-between\", \"items-center\"], [1, \"text-[#2a5a03]\", \"hover:text-[#1a3a01]\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\"], [\"fill-rule\", \"evenodd\", \"d\", \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\", \"clip-rule\", \"evenodd\"], [1, \"flex\", \"justify-center\", \"mb-6\"], [1, \"bg-[#ff6b69]/20\", \"border\", \"border-[#ff6b69]\", \"text-[#ff6b69]\", \"p-4\", \"rounded-lg\", \"max-w-md\", \"w-full\", \"text-center\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-md\", \"overflow-hidden\", \"max-w-5xl\", \"mx-auto\"], [1, \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"p-6\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"text-2xl\", \"font-bold\", \"text-white\"], [1, \"text-white/80\", \"text-sm\"], [1, \"flex\", \"space-x-2\"], [1, \"px-3\", \"py-1\", \"text-xs\", \"rounded-full\", \"backdrop-blur-sm\", \"bg-white/20\", \"text-white\", \"font-medium\"], [1, \"px-6\", \"py-6\", \"flex\", \"items-center\", \"border-b\", \"border-[#edf1f4]\"], [1, \"flex-shrink-0\"], [\"class\", \"relative group\", 4, \"ngIf\"], [\"class\", \"h-24 w-24 rounded-full bg-gradient-to-br from-[#4f5fad] to-[#3d4a85] flex items-center justify-center text-white text-3xl font-bold\", 4, \"ngIf\"], [1, \"ml-6\"], [1, \"text-2xl\", \"font-bold\", \"text-[#3d4a85]\"], [1, \"flex\", \"items-center\", \"mt-2\", \"space-x-3\"], [1, \"px-3\", \"py-1\", \"text-sm\", \"rounded-full\", \"font-medium\", 3, \"ngClass\"], [1, \"px-3\", \"py-1\", \"text-sm\", \"rounded-full\", \"font-medium\", \"flex\", \"items-center\", 3, \"ngClass\"], [1, \"w-2\", \"h-2\", \"rounded-full\", \"mr-2\", 3, \"ngClass\"], [\"class\", \"px-3 py-1 text-sm rounded-full bg-[#4a89ce]/20 text-[#4a89ce] font-medium flex items-center\", 4, \"ngIf\"], [1, \"mt-2\"], [1, \"text-[#4a89ce]\", \"hover:text-[#7826b5]\", \"transition-colors\", \"flex\", \"items-center\", 3, \"href\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"], [1, \"p-6\", \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-x-8\", \"gap-y-4\"], [1, \"space-y-4\"], [1, \"text-lg\", \"font-semibold\", \"text-[#3d4a85]\", \"mb-4\", \"pb-2\", \"border-b\", \"border-[#edf1f4]\"], [1, \"font-medium\", \"text-[#6d6870]\"], [1, \"text-[#4f5fad]\"], [3, \"ngClass\"], [1, \"px-6\", \"pb-6\"], [1, \"group\"], [1, \"flex\", \"justify-between\", \"items-center\", \"cursor-pointer\", \"text-[#3d4a85]\", \"font-medium\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"transform\", \"group-open:rotate-180\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 9l-7 7-7-7\"], [1, \"mt-4\", \"p-4\", \"bg-[#f8f9fa]\", \"rounded-lg\", \"text-sm\", \"font-mono\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [1, \"text-[#6d6870]\"], [1, \"text-[#4f5fad]\", \"break-all\"], [1, \"p-6\", \"border-t\", \"border-[#edf1f4]\", \"flex\", \"justify-between\", \"items-center\", \"bg-[#f8f9fa]\"], [1, \"flex\", \"items-center\"], [1, \"text-sm\", \"text-[#6d6870]\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"inline\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"px-4\", \"py-2\", \"text-sm\", \"rounded-lg\", \"bg-[#4f5fad]\", \"text-white\", \"hover:bg-[#3d4a85]\", \"font-medium\", \"flex\", \"items-center\", \"transition-colors\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\"], [\"fill-rule\", \"evenodd\", \"d\", \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\", \"clip-rule\", \"evenodd\"], [1, \"relative\", \"group\"], [\"alt\", \"Profile\", 1, \"h-24\", \"w-24\", \"rounded-full\", \"object-cover\", \"border-2\", \"border-[#4f5fad]\", 3, \"src\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"bg-black/40\", \"opacity-0\", \"group-hover:opacity-100\", \"flex\", \"items-center\", \"justify-center\", \"transition-opacity\"], [1, \"text-white\", \"text-xs\"], [1, \"h-24\", \"w-24\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]\", \"to-[#3d4a85]\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-3xl\", \"font-bold\"], [1, \"px-3\", \"py-1\", \"text-sm\", \"rounded-full\", \"bg-[#4a89ce]/20\", \"text-[#4a89ce]\", \"font-medium\", \"flex\", \"items-center\"], [1, \"w-2\", \"h-2\", \"rounded-full\", \"bg-[#4a89ce]\", \"mr-2\", \"animate-pulse\"]],\n        template: function UserdetailsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, UserdetailsComponent_div_1_Template, 4, 0, \"div\", 1);\n            i0.ɵɵtemplate(2, UserdetailsComponent_div_2_Template, 6, 1, \"div\", 2);\n            i0.ɵɵtemplate(3, UserdetailsComponent_div_3_Template, 3, 1, \"div\", 3);\n            i0.ɵɵtemplate(4, UserdetailsComponent_div_4_Template, 135, 51, \"div\", 4);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.messageSuccess);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.messageErr && !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.userObject && !ctx.isLoading);\n          }\n        },\n        dependencies: [i3.NgClass, i3.NgIf, i3.TitleCasePipe, i3.DatePipe],\n        styles: [\".loading-spinner[_ngcontent-%COMP%]{position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);z-index:9999}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}\"]\n      });\n    }\n  }\n  return UserdetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}