{"ast": null, "code": "import { printBlockString } from './blockString.mjs';\nimport { printString } from './printString.mjs';\nimport { visit } from './visitor.mjs';\n/**\n * Converts an AST into a string, using one set of reasonable\n * formatting rules.\n */\n\nexport function print(ast) {\n  return visit(ast, printDocASTReducer);\n}\nconst MAX_LINE_LENGTH = 80;\nconst printDocASTReducer = {\n  Name: {\n    leave: node => node.value\n  },\n  Variable: {\n    leave: node => '$' + node.name\n  },\n  // Document\n  Document: {\n    leave: node => join(node.definitions, '\\n\\n')\n  },\n  OperationDefinition: {\n    leave(node) {\n      const varDefs = wrap('(', join(node.variableDefinitions, ', '), ')');\n      const prefix = join([node.operation, join([node.name, varDefs]), join(node.directives, ' ')], ' '); // Anonymous queries with no directives or variable definitions can use\n      // the query short form.\n\n      return (prefix === 'query' ? '' : prefix + ' ') + node.selectionSet;\n    }\n  },\n  VariableDefinition: {\n    leave: ({\n      variable,\n      type,\n      defaultValue,\n      directives\n    }) => variable + ': ' + type + wrap(' = ', defaultValue) + wrap(' ', join(directives, ' '))\n  },\n  SelectionSet: {\n    leave: ({\n      selections\n    }) => block(selections)\n  },\n  Field: {\n    leave({\n      alias,\n      name,\n      arguments: args,\n      directives,\n      selectionSet\n    }) {\n      const prefix = wrap('', alias, ': ') + name;\n      let argsLine = prefix + wrap('(', join(args, ', '), ')');\n      if (argsLine.length > MAX_LINE_LENGTH) {\n        argsLine = prefix + wrap('(\\n', indent(join(args, '\\n')), '\\n)');\n      }\n      return join([argsLine, join(directives, ' '), selectionSet], ' ');\n    }\n  },\n  Argument: {\n    leave: ({\n      name,\n      value\n    }) => name + ': ' + value\n  },\n  // Fragments\n  FragmentSpread: {\n    leave: ({\n      name,\n      directives\n    }) => '...' + name + wrap(' ', join(directives, ' '))\n  },\n  InlineFragment: {\n    leave: ({\n      typeCondition,\n      directives,\n      selectionSet\n    }) => join(['...', wrap('on ', typeCondition), join(directives, ' '), selectionSet], ' ')\n  },\n  FragmentDefinition: {\n    leave: ({\n      name,\n      typeCondition,\n      variableDefinitions,\n      directives,\n      selectionSet\n    } // Note: fragment variable definitions are experimental and may be changed\n    ) =>\n    // or removed in the future.\n    `fragment ${name}${wrap('(', join(variableDefinitions, ', '), ')')} ` + `on ${typeCondition} ${wrap('', join(directives, ' '), ' ')}` + selectionSet\n  },\n  // Value\n  IntValue: {\n    leave: ({\n      value\n    }) => value\n  },\n  FloatValue: {\n    leave: ({\n      value\n    }) => value\n  },\n  StringValue: {\n    leave: ({\n      value,\n      block: isBlockString\n    }) => isBlockString ? printBlockString(value) : printString(value)\n  },\n  BooleanValue: {\n    leave: ({\n      value\n    }) => value ? 'true' : 'false'\n  },\n  NullValue: {\n    leave: () => 'null'\n  },\n  EnumValue: {\n    leave: ({\n      value\n    }) => value\n  },\n  ListValue: {\n    leave: ({\n      values\n    }) => '[' + join(values, ', ') + ']'\n  },\n  ObjectValue: {\n    leave: ({\n      fields\n    }) => '{' + join(fields, ', ') + '}'\n  },\n  ObjectField: {\n    leave: ({\n      name,\n      value\n    }) => name + ': ' + value\n  },\n  // Directive\n  Directive: {\n    leave: ({\n      name,\n      arguments: args\n    }) => '@' + name + wrap('(', join(args, ', '), ')')\n  },\n  // Type\n  NamedType: {\n    leave: ({\n      name\n    }) => name\n  },\n  ListType: {\n    leave: ({\n      type\n    }) => '[' + type + ']'\n  },\n  NonNullType: {\n    leave: ({\n      type\n    }) => type + '!'\n  },\n  // Type System Definitions\n  SchemaDefinition: {\n    leave: ({\n      description,\n      directives,\n      operationTypes\n    }) => wrap('', description, '\\n') + join(['schema', join(directives, ' '), block(operationTypes)], ' ')\n  },\n  OperationTypeDefinition: {\n    leave: ({\n      operation,\n      type\n    }) => operation + ': ' + type\n  },\n  ScalarTypeDefinition: {\n    leave: ({\n      description,\n      name,\n      directives\n    }) => wrap('', description, '\\n') + join(['scalar', name, join(directives, ' ')], ' ')\n  },\n  ObjectTypeDefinition: {\n    leave: ({\n      description,\n      name,\n      interfaces,\n      directives,\n      fields\n    }) => wrap('', description, '\\n') + join(['type', name, wrap('implements ', join(interfaces, ' & ')), join(directives, ' '), block(fields)], ' ')\n  },\n  FieldDefinition: {\n    leave: ({\n      description,\n      name,\n      arguments: args,\n      type,\n      directives\n    }) => wrap('', description, '\\n') + name + (hasMultilineItems(args) ? wrap('(\\n', indent(join(args, '\\n')), '\\n)') : wrap('(', join(args, ', '), ')')) + ': ' + type + wrap(' ', join(directives, ' '))\n  },\n  InputValueDefinition: {\n    leave: ({\n      description,\n      name,\n      type,\n      defaultValue,\n      directives\n    }) => wrap('', description, '\\n') + join([name + ': ' + type, wrap('= ', defaultValue), join(directives, ' ')], ' ')\n  },\n  InterfaceTypeDefinition: {\n    leave: ({\n      description,\n      name,\n      interfaces,\n      directives,\n      fields\n    }) => wrap('', description, '\\n') + join(['interface', name, wrap('implements ', join(interfaces, ' & ')), join(directives, ' '), block(fields)], ' ')\n  },\n  UnionTypeDefinition: {\n    leave: ({\n      description,\n      name,\n      directives,\n      types\n    }) => wrap('', description, '\\n') + join(['union', name, join(directives, ' '), wrap('= ', join(types, ' | '))], ' ')\n  },\n  EnumTypeDefinition: {\n    leave: ({\n      description,\n      name,\n      directives,\n      values\n    }) => wrap('', description, '\\n') + join(['enum', name, join(directives, ' '), block(values)], ' ')\n  },\n  EnumValueDefinition: {\n    leave: ({\n      description,\n      name,\n      directives\n    }) => wrap('', description, '\\n') + join([name, join(directives, ' ')], ' ')\n  },\n  InputObjectTypeDefinition: {\n    leave: ({\n      description,\n      name,\n      directives,\n      fields\n    }) => wrap('', description, '\\n') + join(['input', name, join(directives, ' '), block(fields)], ' ')\n  },\n  DirectiveDefinition: {\n    leave: ({\n      description,\n      name,\n      arguments: args,\n      repeatable,\n      locations\n    }) => wrap('', description, '\\n') + 'directive @' + name + (hasMultilineItems(args) ? wrap('(\\n', indent(join(args, '\\n')), '\\n)') : wrap('(', join(args, ', '), ')')) + (repeatable ? ' repeatable' : '') + ' on ' + join(locations, ' | ')\n  },\n  SchemaExtension: {\n    leave: ({\n      directives,\n      operationTypes\n    }) => join(['extend schema', join(directives, ' '), block(operationTypes)], ' ')\n  },\n  ScalarTypeExtension: {\n    leave: ({\n      name,\n      directives\n    }) => join(['extend scalar', name, join(directives, ' ')], ' ')\n  },\n  ObjectTypeExtension: {\n    leave: ({\n      name,\n      interfaces,\n      directives,\n      fields\n    }) => join(['extend type', name, wrap('implements ', join(interfaces, ' & ')), join(directives, ' '), block(fields)], ' ')\n  },\n  InterfaceTypeExtension: {\n    leave: ({\n      name,\n      interfaces,\n      directives,\n      fields\n    }) => join(['extend interface', name, wrap('implements ', join(interfaces, ' & ')), join(directives, ' '), block(fields)], ' ')\n  },\n  UnionTypeExtension: {\n    leave: ({\n      name,\n      directives,\n      types\n    }) => join(['extend union', name, join(directives, ' '), wrap('= ', join(types, ' | '))], ' ')\n  },\n  EnumTypeExtension: {\n    leave: ({\n      name,\n      directives,\n      values\n    }) => join(['extend enum', name, join(directives, ' '), block(values)], ' ')\n  },\n  InputObjectTypeExtension: {\n    leave: ({\n      name,\n      directives,\n      fields\n    }) => join(['extend input', name, join(directives, ' '), block(fields)], ' ')\n  }\n};\n/**\n * Given maybeArray, print an empty string if it is null or empty, otherwise\n * print all items together separated by separator if provided\n */\n\nfunction join(maybeArray, separator = '') {\n  var _maybeArray$filter$jo;\n  return (_maybeArray$filter$jo = maybeArray === null || maybeArray === void 0 ? void 0 : maybeArray.filter(x => x).join(separator)) !== null && _maybeArray$filter$jo !== void 0 ? _maybeArray$filter$jo : '';\n}\n/**\n * Given array, print each item on its own line, wrapped in an indented `{ }` block.\n */\n\nfunction block(array) {\n  return wrap('{\\n', indent(join(array, '\\n')), '\\n}');\n}\n/**\n * If maybeString is not null or empty, then wrap with start and end, otherwise print an empty string.\n */\n\nfunction wrap(start, maybeString, end = '') {\n  return maybeString != null && maybeString !== '' ? start + maybeString + end : '';\n}\nfunction indent(str) {\n  return wrap('  ', str.replace(/\\n/g, '\\n  '));\n}\nfunction hasMultilineItems(maybeArray) {\n  var _maybeArray$some;\n\n  // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  /* c8 ignore next */\n  return (_maybeArray$some = maybeArray === null || maybeArray === void 0 ? void 0 : maybeArray.some(str => str.includes('\\n'))) !== null && _maybeArray$some !== void 0 ? _maybeArray$some : false;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}