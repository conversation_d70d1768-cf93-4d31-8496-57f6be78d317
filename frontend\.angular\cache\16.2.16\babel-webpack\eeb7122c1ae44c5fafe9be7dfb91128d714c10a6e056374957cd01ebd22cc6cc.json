{"ast": null, "code": "import { maybe } from \"../globals/index.js\";\nvar isReactNative = maybe(function () {\n  return navigator.product;\n}) == \"ReactNative\";\nexport var canUseWeakMap = typeof WeakMap === \"function\" && !(isReactNative && !global.HermesInternal);\nexport var canUseWeakSet = typeof WeakSet === \"function\";\nexport var canUseSymbol = typeof Symbol === \"function\" && typeof Symbol.for === \"function\";\nexport var canUseAsyncIteratorSymbol = canUseSymbol && Symbol.asyncIterator;\nexport var canUseDOM = typeof maybe(function () {\n  return window.document.createElement;\n}) === \"function\";\nvar usingJSDOM =\n// Following advice found in this comment from @domenic (maintainer of jsdom):\n// https://github.com/jsdom/jsdom/issues/1537#issuecomment-229405327\n//\n// Since we control the version of Jest and jsdom used when running Apollo\n// Client tests, and that version is recent enought to include \" jsdom/x.y.z\"\n// at the end of the user agent string, I believe this case is all we need to\n// check. Testing for \"Node.js\" was recommended for backwards compatibility\n// with older version of jsdom, but we don't have that problem.\nmaybe(function () {\n  return navigator.userAgent.indexOf(\"jsdom\") >= 0;\n}) || false;\n// Our tests should all continue to pass if we remove this !usingJSDOM\n// condition, thereby allowing useLayoutEffect when using jsdom. Unfortunately,\n// if we allow useLayoutEffect, then useSyncExternalStore generates many\n// warnings about useLayoutEffect doing nothing on the server. While these\n// warnings are harmless, this !usingJSDOM condition seems to be the best way to\n// prevent them (i.e. skipping useLayoutEffect when using jsdom).\nexport var canUseLayoutEffect = (canUseDOM || isReactNative) && !usingJSDOM;\n//# sourceMappingURL=canUse.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}