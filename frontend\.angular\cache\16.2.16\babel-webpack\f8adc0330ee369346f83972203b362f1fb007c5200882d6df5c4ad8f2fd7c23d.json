{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { visit, visitInParallel } from '../language/visitor.mjs';\nimport { assertValidSchema } from '../type/validate.mjs';\nimport { TypeInfo, visitWithTypeInfo } from '../utilities/TypeInfo.mjs';\nimport { specifiedRules, specifiedSDLRules } from './specifiedRules.mjs';\nimport { SDLValidationContext, ValidationContext } from './ValidationContext.mjs';\n/**\n * Implements the \"Validation\" section of the spec.\n *\n * Validation runs synchronously, returning an array of encountered errors, or\n * an empty array if no errors were encountered and the document is valid.\n *\n * A list of specific validation rules may be provided. If not provided, the\n * default list of rules defined by the GraphQL specification will be used.\n *\n * Each validation rules is a function which returns a visitor\n * (see the language/visitor API). Visitor methods are expected to return\n * GraphQLErrors, or Arrays of GraphQLErrors when invalid.\n *\n * Validate will stop validation after a `maxErrors` limit has been reached.\n * Attackers can send pathologically invalid queries to induce a DoS attack,\n * so by default `maxErrors` set to 100 errors.\n *\n * Optionally a custom TypeInfo instance may be provided. If not provided, one\n * will be created from the provided schema.\n */\n\nexport function validate(schema, documentAST, rules = specifiedRules, options, /** @deprecated will be removed in 17.0.0 */\ntypeInfo = new TypeInfo(schema)) {\n  var _options$maxErrors;\n  const maxErrors = (_options$maxErrors = options === null || options === void 0 ? void 0 : options.maxErrors) !== null && _options$maxErrors !== void 0 ? _options$maxErrors : 100;\n  documentAST || devAssert(false, 'Must provide document.'); // If the schema used for validation is invalid, throw an error.\n\n  assertValidSchema(schema);\n  const abortObj = Object.freeze({});\n  const errors = [];\n  const context = new ValidationContext(schema, documentAST, typeInfo, error => {\n    if (errors.length >= maxErrors) {\n      errors.push(new GraphQLError('Too many validation errors, error limit reached. Validation aborted.')); // eslint-disable-next-line @typescript-eslint/no-throw-literal\n\n      throw abortObj;\n    }\n    errors.push(error);\n  }); // This uses a specialized visitor which runs multiple visitors in parallel,\n  // while maintaining the visitor skip and break API.\n\n  const visitor = visitInParallel(rules.map(rule => rule(context))); // Visit the whole document with each instance of all provided rules.\n\n  try {\n    visit(documentAST, visitWithTypeInfo(typeInfo, visitor));\n  } catch (e) {\n    if (e !== abortObj) {\n      throw e;\n    }\n  }\n  return errors;\n}\n/**\n * @internal\n */\n\nexport function validateSDL(documentAST, schemaToExtend, rules = specifiedSDLRules) {\n  const errors = [];\n  const context = new SDLValidationContext(documentAST, schemaToExtend, error => {\n    errors.push(error);\n  });\n  const visitors = rules.map(rule => rule(context));\n  visit(documentAST, visitInParallel(visitors));\n  return errors;\n}\n/**\n * Utility function which asserts a SDL document is valid by throwing an error\n * if it is invalid.\n *\n * @internal\n */\n\nexport function assertValidSDL(documentAST) {\n  const errors = validateSDL(documentAST);\n  if (errors.length !== 0) {\n    throw new Error(errors.map(error => error.message).join('\\n\\n'));\n  }\n}\n/**\n * Utility function which asserts a SDL document is valid by throwing an error\n * if it is invalid.\n *\n * @internal\n */\n\nexport function assertValidSDLExtension(documentAST, schema) {\n  const errors = validateSDL(documentAST, schema);\n  if (errors.length !== 0) {\n    throw new Error(errors.map(error => error.message).join('\\n\\n'));\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}