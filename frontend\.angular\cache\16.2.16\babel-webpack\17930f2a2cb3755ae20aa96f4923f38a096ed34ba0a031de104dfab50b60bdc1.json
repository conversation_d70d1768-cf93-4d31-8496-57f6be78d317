{"ast": null, "code": "import { Subject, of, BehaviorSubject } from 'rxjs';\nimport { MessageType } from 'src/app/models/message.model';\nimport { catchError, map, takeUntil, take, debounceTime, distinctUntilChanged, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/message.service\";\nimport * as i2 from \"@app/services/theme.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"notificationContainer\"];\nfunction NotificationListComponent_div_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"label\", 32)(2, \"input\", 33);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_div_3_Template_input_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.toggleSelectAll($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r9.allSelected);\n  }\n}\nfunction NotificationListComponent_div_8_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.markAllAsRead());\n    });\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵtext(2, \" Tout marquer comme lu \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_8_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.deleteAllNotifications());\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2, \" Tout supprimer \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.loadNotifications());\n    });\n    i0.ɵɵelement(2, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, NotificationListComponent_div_8_div_3_Template, 4, 1, \"div\", 24);\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵelementStart(5, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.toggleUnreadFilter());\n    });\n    i0.ɵɵelement(6, \"i\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.toggleSound());\n    });\n    i0.ɵɵelement(8, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_8_button_9_Template, 3, 0, \"button\", 29);\n    i0.ɵɵpipe(10, \"async\");\n    i0.ɵɵtemplate(11, NotificationListComponent_div_8_button_11_Template, 3, 0, \"button\", 30);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(4, 9, ctx_r0.hasNotifications()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r0.showOnlyUnread);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", !ctx_r0.isSoundMuted);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r0.isSoundMuted ? \"Activer le son\" : \"D\\u00E9sactiver le son\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isSoundMuted ? \"fa-volume-mute\" : \"fa-volume-up\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(10, 11, ctx_r0.unreadCount$) || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(12, 13, ctx_r0.hasNotifications()));\n  }\n}\nfunction NotificationListComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"span\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.markSelectedAsRead());\n    });\n    i0.ɵɵelement(4, \"i\", 41);\n    i0.ɵɵtext(5, \" Marquer comme lu \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.deleteSelectedNotifications());\n    });\n    i0.ɵɵelement(7, \"i\", 38);\n    i0.ɵɵtext(8, \" Supprimer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r25 = i0.ɵɵnextContext();\n      ctx_r25.selectedNotifications.clear();\n      ctx_r25.showSelectionBar = false;\n      return i0.ɵɵresetView(ctx_r25.allSelected = false);\n    });\n    i0.ɵɵelement(10, \"i\", 44);\n    i0.ɵɵtext(11, \" Annuler \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.selectedNotifications.size, \" s\\u00E9lectionn\\u00E9(s)\");\n  }\n}\nfunction NotificationListComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"div\", 46);\n    i0.ɵɵelementStart(2, \"p\", 47);\n    i0.ɵɵtext(3, \"Chargement des notifications...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵelementStart(3, \"div\")(4, \"h3\", 51);\n    i0.ɵɵtext(5, \"Erreur de chargement\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 52);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_11_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.loadNotifications());\n    });\n    i0.ɵɵtext(9, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.getErrorMessage());\n  }\n}\nfunction NotificationListComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵelement(2, \"i\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 57);\n    i0.ɵɵtext(4, \"Aucune notification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 58);\n    i0.ɵɵtext(6, \"Vous \\u00EAtes \\u00E0 jour !\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_12_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.loadNotifications());\n    });\n    i0.ɵɵtext(8, \" V\\u00E9rifier les nouvelles notifications \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", notification_r33.message == null ? null : notification_r33.message.content, \" \");\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵelement(1, \"i\", 89);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r33 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", notification_r33.message == null ? null : notification_r33.message.attachments == null ? null : notification_r33.message.attachments.length, \" pi\\u00E8ce(s) jointe(s) \");\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 90);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_24_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const notification_r33 = i0.ɵɵnextContext().$implicit;\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      ctx_r42.getNotificationAttachments(notification_r33.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 89);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_25_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const notification_r33 = i0.ɵɵnextContext().$implicit;\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      ctx_r45.joinConversation(notification_r33);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 93);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_28_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const notification_r33 = i0.ɵɵnextContext().$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      ctx_r48.markAsRead(notification_r33.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 95);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 64)(2, \"div\", 65)(3, \"label\", 32)(4, \"input\", 33);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_input_click_4_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const notification_r33 = restoredCtx.$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r51.toggleSelection(notification_r33.id, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"span\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 66);\n    i0.ɵɵelement(7, \"img\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 68)(9, \"div\", 69)(10, \"div\", 70)(11, \"div\", 71)(12, \"span\", 72);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 73);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 74)(18, \"span\", 75);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, NotificationListComponent_div_14_ng_container_2_div_20_Template, 2, 1, \"div\", 76);\n    i0.ɵɵtemplate(21, NotificationListComponent_div_14_ng_container_2_div_21_Template, 3, 1, \"div\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, NotificationListComponent_div_14_ng_container_2_div_22_Template, 1, 0, \"div\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 79);\n    i0.ɵɵtemplate(24, NotificationListComponent_div_14_ng_container_2_button_24_Template, 2, 0, \"button\", 80);\n    i0.ɵɵtemplate(25, NotificationListComponent_div_14_ng_container_2_button_25_Template, 2, 0, \"button\", 81);\n    i0.ɵɵelementStart(26, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_button_click_26_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const notification_r33 = restoredCtx.$implicit;\n      const ctx_r53 = i0.ɵɵnextContext(2);\n      ctx_r53.openNotificationDetails(notification_r33);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(27, \"i\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, NotificationListComponent_div_14_ng_container_2_button_28_Template, 2, 0, \"button\", 84);\n    i0.ɵɵelementStart(29, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_button_click_29_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r52);\n      const notification_r33 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      ctx_r54.deleteNotification(notification_r33.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(30, \"i\", 86);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notification_r33 = ctx.$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"futuristic-notification-unread\", !notification_r33.isRead)(\"futuristic-notification-read\", notification_r33.isRead)(\"futuristic-notification-selected\", ctx_r31.isSelected(notification_r33.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r31.isSelected(notification_r33.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", (notification_r33.senderId == null ? null : notification_r33.senderId.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate((notification_r33.senderId == null ? null : notification_r33.senderId.username) || \"Syst\\u00E8me\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(16, 17, notification_r33.timestamp, \"shortTime\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(notification_r33.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r33.message == null ? null : notification_r33.message.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r33.message == null ? null : notification_r33.message.attachments == null ? null : notification_r33.message.attachments.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !notification_r33.isRead);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", notification_r33.message == null ? null : notification_r33.message.attachments == null ? null : notification_r33.message.attachments.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r33.type === \"NEW_MESSAGE\" || notification_r33.type === \"GROUP_INVITE\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !notification_r33.isRead);\n  }\n}\nfunction NotificationListComponent_div_14_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelement(1, \"div\", 97);\n    i0.ɵɵelementStart(2, \"p\", 98);\n    i0.ɵɵtext(3, \" Chargement des notifications plus anciennes... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60, 61);\n    i0.ɵɵlistener(\"scroll\", function NotificationListComponent_div_14_Template_div_scroll_0_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const _r30 = i0.ɵɵreference(1);\n      const ctx_r55 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r55.onScroll(_r30));\n    });\n    i0.ɵɵtemplate(2, NotificationListComponent_div_14_ng_container_2_Template, 31, 20, \"ng-container\", 62);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵtemplate(4, NotificationListComponent_div_14_div_4_Template, 4, 0, \"div\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(3, 2, ctx_r5.filteredNotifications$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.loadingMore);\n  }\n}\nfunction NotificationListComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"div\", 46);\n    i0.ɵɵelementStart(2, \"p\", 47);\n    i0.ɵɵtext(3, \"Chargement des pi\\u00E8ces jointes...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵelement(2, \"i\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 57);\n    i0.ɵɵtext(4, \"Aucune pi\\u00E8ce jointe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 58);\n    i0.ɵɵtext(6, \" Aucune pi\\u00E8ce jointe n'a \\u00E9t\\u00E9 trouv\\u00E9e pour cette notification. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_27_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 115)(1, \"img\", 116);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const attachment_r58 = i0.ɵɵnextContext().$implicit;\n      const ctx_r62 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r62.openAttachment(attachment_r58.url));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const attachment_r58 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", attachment_r58.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NotificationListComponent_div_27_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r58 = i0.ɵɵnextContext().$implicit;\n    const ctx_r60 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r60.getFileIcon(attachment_r58.type));\n  }\n}\nfunction NotificationListComponent_div_27_div_1_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 118);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r58 = i0.ɵɵnextContext().$implicit;\n    const ctx_r61 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r61.formatFileSize(attachment_r58.size));\n  }\n}\nfunction NotificationListComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r69 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_27_div_1_div_1_Template, 2, 1, \"div\", 103);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_27_div_1_div_2_Template, 2, 2, \"div\", 104);\n    i0.ɵɵelementStart(3, \"div\", 105)(4, \"div\", 106);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 107)(7, \"span\", 108);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_27_div_1_span_9_Template, 2, 1, \"span\", 109);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 110)(11, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r69);\n      const attachment_r58 = restoredCtx.$implicit;\n      const ctx_r68 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r68.openAttachment(attachment_r58.url));\n    });\n    i0.ɵɵelement(12, \"i\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r69);\n      const attachment_r58 = restoredCtx.$implicit;\n      const ctx_r70 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r70.downloadAttachment(attachment_r58));\n    });\n    i0.ɵɵelement(14, \"i\", 114);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const attachment_r58 = ctx.$implicit;\n    const ctx_r57 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r57.isImage(attachment_r58.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r57.isImage(attachment_r58.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", attachment_r58.name || \"Pi\\u00E8ce jointe\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r57.getFileTypeLabel(attachment_r58.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", attachment_r58.size);\n  }\n}\nfunction NotificationListComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_27_div_1_Template, 15, 5, \"div\", 101);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.currentAttachments);\n  }\n}\nexport let NotificationListComponent = /*#__PURE__*/(() => {\n  class NotificationListComponent {\n    constructor(messageService, themeService, router) {\n      this.messageService = messageService;\n      this.themeService = themeService;\n      this.router = router;\n      this.loading = true;\n      this.loadingMore = false;\n      this.hasMoreNotifications = true;\n      this.error = null;\n      this.showOnlyUnread = false;\n      this.isSoundMuted = false;\n      // Propriétés pour la sélection multiple\n      this.selectedNotifications = new Set();\n      this.allSelected = false;\n      this.showSelectionBar = false;\n      // Propriétés pour le modal des pièces jointes\n      this.showAttachmentsModal = false;\n      this.loadingAttachments = false;\n      this.currentAttachments = [];\n      // Propriétés pour le modal des détails de notification\n      this.showNotificationDetailsModal = false;\n      this.currentNotification = null;\n      this.destroy$ = new Subject();\n      this.scrollPosition$ = new BehaviorSubject(0);\n      this.notifications$ = this.messageService.notifications$;\n      this.filteredNotifications$ = this.notifications$; // Par défaut, afficher toutes les notifications\n      this.unreadCount$ = this.messageService.notificationCount$;\n      this.isDarkMode$ = this.themeService.darkMode$;\n      // Vérifier l'état du son\n      this.isSoundMuted = this.messageService.isMuted();\n    }\n    /**\n     * Rejoint une conversation ou un groupe à partir d'une notification\n     * @param notification Notification contenant les informations de la conversation ou du groupe\n     */\n    joinConversation(notification) {\n      console.log('Rejoindre la conversation:', notification);\n      // Marquer la notification comme lue\n      this.markAsRead(notification.id);\n      // Extraire les informations pertinentes de la notification\n      const conversationId = notification.conversationId || notification.metadata && notification.metadata['conversationId'] || (notification.relatedEntity && notification.relatedEntity.includes('conversation') ? notification.relatedEntity : null);\n      const groupId = notification.groupId || notification.metadata && notification.metadata['groupId'] || (notification.relatedEntity && notification.relatedEntity.includes('group') ? notification.relatedEntity : null);\n      // Déterminer où rediriger l'utilisateur\n      if (conversationId) {\n        // Rediriger vers la conversation existante\n        console.log('Redirection vers la conversation existante:', conversationId);\n        // Utiliser le format exact de l'URL fournie avec l'ID\n        window.location.href = `/messages/conversations/chat/${conversationId}`;\n      } else if (groupId) {\n        // Rediriger vers le groupe\n        console.log('Redirection vers le groupe:', groupId);\n        window.location.href = `/messages/group/${groupId}`;\n      } else if (notification.senderId && notification.senderId.id) {\n        // Si aucun ID de conversation n'est trouvé, mais qu'il y a un expéditeur,\n        // utiliser getOrCreateConversation pour obtenir ou créer une conversation\n        console.log(\"Création/récupération d'une conversation avec l'utilisateur:\", notification.senderId.id);\n        // Afficher un indicateur de chargement si nécessaire\n        // this.loading = true;\n        this.messageService.getOrCreateConversation(notification.senderId.id).subscribe({\n          next: conversation => {\n            console.log('Conversation obtenue:', conversation);\n            // this.loading = false;\n            if (conversation && conversation.id) {\n              // Rediriger vers la conversation nouvellement créée ou récupérée\n              // Utiliser le format exact de l'URL fournie avec l'ID\n              window.location.href = `/messages/conversations/chat/${conversation.id}`;\n            } else {\n              console.error('Conversation invalide reçue:', conversation);\n              window.location.href = '/messages';\n            }\n          },\n          error: error => {\n            console.error('Erreur lors de la création/récupération de la conversation:', error);\n            // this.loading = false;\n            // En cas d'erreur, rediriger vers la liste des messages\n            window.location.href = '/messages';\n          }\n        });\n      } else {\n        // Si aucune information n'est trouvée, rediriger vers la liste des messages\n        console.log('Redirection vers la liste des messages');\n        window.location.href = '/messages';\n      }\n    }\n    onScroll(target) {\n      if (!target) return;\n      const scrollPosition = target.scrollTop;\n      const scrollHeight = target.scrollHeight;\n      const clientHeight = target.clientHeight;\n      // Si on est proche du bas (à 200px du bas)\n      if (scrollHeight - scrollPosition - clientHeight < 200) {\n        this.scrollPosition$.next(scrollPosition);\n      }\n    }\n    ngOnInit() {\n      // Charger la préférence de son depuis le localStorage\n      const savedMutePreference = localStorage.getItem('notificationSoundMuted');\n      if (savedMutePreference !== null) {\n        this.isSoundMuted = savedMutePreference === 'true';\n        console.log(`Préférence de son chargée: ${this.isSoundMuted ? 'désactivé' : 'activé'}`);\n        this.messageService.setMuted(this.isSoundMuted);\n      }\n      this.loadNotifications();\n      this.setupSubscriptions();\n      this.setupInfiniteScroll();\n      this.filterDeletedNotifications();\n    }\n    /**\n     * Filtre les notifications supprimées lors du chargement initial\n     */\n    filterDeletedNotifications() {\n      // Récupérer les IDs des notifications supprimées du localStorage\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      if (deletedNotificationIds.size > 0) {\n        console.log(`Filtrage de ${deletedNotificationIds.size} notifications supprimées`);\n        // Filtrer les notifications pour exclure celles qui ont été supprimées\n        this.notifications$.pipe(take(1)).subscribe(notifications => {\n          const filteredNotifications = notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n          // Mettre à jour l'interface utilisateur\n          this.messageService.notifications.next(filteredNotifications);\n          // Mettre à jour le compteur de notifications non lues\n          const unreadCount = filteredNotifications.filter(n => !n.isRead).length;\n          this.messageService.notificationCount.next(unreadCount);\n          // Mettre à jour le cache de notifications dans le service\n          this.updateNotificationCache(filteredNotifications);\n        });\n      }\n    }\n    setupInfiniteScroll() {\n      // Configurer le chargement des anciennes notifications lors du défilement\n      this.scrollPosition$.pipe(takeUntil(this.destroy$), debounceTime(200),\n      // Attendre 200ms après le dernier événement de défilement\n      distinctUntilChanged(),\n      // Ne déclencher que si la position de défilement a changé\n      filter(() => !this.loadingMore && this.hasMoreNotifications) // Ne charger que s'il y a plus de notifications et qu'on n'est pas déjà en train de charger\n      ).subscribe(() => {\n        this.loadMoreNotifications();\n      });\n    }\n    loadNotifications() {\n      console.log('NotificationListComponent: Loading notifications');\n      this.loading = true;\n      this.loadingMore = false;\n      this.error = null;\n      this.hasMoreNotifications = true;\n      // Récupérer les IDs des notifications supprimées du localStorage\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      console.log(`${deletedNotificationIds.size} notifications supprimées trouvées dans le localStorage`);\n      this.messageService.getNotifications(true).pipe(takeUntil(this.destroy$), map(notifications => {\n        // Filtrer les notifications supprimées\n        if (deletedNotificationIds.size > 0) {\n          return notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n        }\n        return notifications;\n      })).subscribe({\n        next: notifications => {\n          console.log('NotificationListComponent: Notifications loaded successfully', notifications);\n          // Mettre à jour l'interface utilisateur avec les notifications filtrées\n          this.messageService.notifications.next(notifications);\n          // Mettre à jour le compteur de notifications non lues\n          const unreadCount = notifications.filter(n => !n.isRead).length;\n          this.messageService.notificationCount.next(unreadCount);\n          this.loading = false;\n          this.hasMoreNotifications = this.messageService.hasMoreNotifications();\n        },\n        error: err => {\n          console.error('NotificationListComponent: Error loading notifications', err);\n          this.error = err;\n          this.loading = false;\n          this.hasMoreNotifications = false;\n        }\n      });\n    }\n    loadMoreNotifications() {\n      if (this.loadingMore || !this.hasMoreNotifications) return;\n      console.log('NotificationListComponent: Loading more notifications');\n      this.loadingMore = true;\n      // Récupérer les IDs des notifications supprimées du localStorage\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      console.log(`${deletedNotificationIds.size} notifications supprimées trouvées dans le localStorage`);\n      this.messageService.loadMoreNotifications().pipe(takeUntil(this.destroy$), map(notifications => {\n        // Filtrer les notifications supprimées\n        if (deletedNotificationIds.size > 0) {\n          const filteredNotifications = notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n          console.log(`Filtré ${notifications.length - filteredNotifications.length} notifications supprimées`);\n          return filteredNotifications;\n        }\n        return notifications;\n      })).subscribe({\n        next: notifications => {\n          console.log('NotificationListComponent: More notifications loaded successfully', notifications);\n          // Mettre à jour l'interface utilisateur avec les notifications filtrées\n          this.notifications$.pipe(take(1)).subscribe(existingNotifications => {\n            const allNotifications = [...existingNotifications, ...notifications];\n            this.messageService.notifications.next(allNotifications);\n            // Mettre à jour le compteur de notifications non lues\n            const unreadCount = allNotifications.filter(n => !n.isRead).length;\n            this.messageService.notificationCount.next(unreadCount);\n            // Mettre à jour le cache de notifications dans le service\n            this.updateNotificationCache(allNotifications);\n          });\n          this.loadingMore = false;\n          this.hasMoreNotifications = this.messageService.hasMoreNotifications();\n        },\n        error: err => {\n          console.error('NotificationListComponent: Error loading more notifications', err);\n          this.loadingMore = false;\n          this.hasMoreNotifications = false;\n        }\n      });\n    }\n    setupSubscriptions() {\n      this.messageService.subscribeToNewNotifications().pipe(takeUntil(this.destroy$), catchError(error => {\n        console.error('Notification stream error:', error);\n        return of(null);\n      })).subscribe();\n      this.messageService.subscribeToNotificationsRead().pipe(takeUntil(this.destroy$), catchError(error => {\n        console.error('Notifications read stream error:', error);\n        return of(null);\n      })).subscribe();\n    }\n    markAsRead(notificationId) {\n      console.log('Marking notification as read:', notificationId);\n      if (!notificationId) {\n        console.error('Invalid notification ID:', notificationId);\n        this.error = new Error('Invalid notification ID');\n        return;\n      }\n      // Afficher des informations de débogage sur la notification\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        const notification = notifications.find(n => n.id === notificationId);\n        if (notification) {\n          console.log('Found notification to mark as read:', {\n            id: notification.id,\n            type: notification.type,\n            isRead: notification.isRead\n          });\n          // Mettre à jour localement la notification\n          const updatedNotifications = notifications.map(n => n.id === notificationId ? {\n            ...n,\n            isRead: true,\n            readAt: new Date().toISOString()\n          } : n);\n          // Mettre à jour l'interface utilisateur immédiatement\n          this.messageService.notifications.next(updatedNotifications);\n          // Mettre à jour le compteur de notifications non lues\n          const unreadCount = updatedNotifications.filter(n => !n.isRead).length;\n          this.messageService.notificationCount.next(unreadCount);\n          // Mettre à jour le cache de notifications dans le service\n          this.updateNotificationCache(updatedNotifications);\n          // Appeler le service pour marquer la notification comme lue\n          this.messageService.markAsRead([notificationId]).pipe(takeUntil(this.destroy$)).subscribe({\n            next: result => {\n              console.log('Mark as read result:', result);\n              if (result && result.success) {\n                console.log('Notification marked as read successfully');\n                // Si l'erreur était liée à cette opération, la réinitialiser\n                if (this.error && this.error.message.includes('mark')) {\n                  this.error = null;\n                }\n              }\n            },\n            error: err => {\n              console.error('Error marking notification as read:', err);\n              console.error('Error details:', {\n                message: err.message,\n                stack: err.stack,\n                notificationId\n              });\n              // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur\n              // this.error = err;\n            }\n          });\n        } else {\n          console.warn('Notification not found in local cache:', notificationId);\n          // Forcer le rechargement des notifications\n          this.loadNotifications();\n        }\n      });\n    }\n    // Méthode pour mettre à jour le cache de notifications dans le service\n    updateNotificationCache(notifications) {\n      // Mettre à jour le cache de notifications dans le service\n      const notificationCache = this.messageService.notificationCache;\n      if (notificationCache) {\n        notifications.forEach(notification => {\n          notificationCache.set(notification.id, notification);\n        });\n        // Forcer la mise à jour du compteur\n        const unreadCount = notifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        console.log('Notification cache updated, new unread count:', unreadCount);\n      }\n    }\n    markAllAsRead() {\n      console.log('Marking all notifications as read');\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        console.log('All notifications:', notifications);\n        const unreadIds = notifications.filter(n => !n.isRead).map(n => n.id);\n        console.log('Unread notification IDs to mark as read:', unreadIds);\n        if (unreadIds.length === 0) {\n          console.log('No unread notifications to mark as read');\n          return;\n        }\n        // Vérifier que tous les IDs sont valides\n        const validIds = unreadIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n        if (validIds.length !== unreadIds.length) {\n          console.error('Some notification IDs are invalid:', unreadIds);\n          this.error = new Error('Invalid notification IDs');\n          return;\n        }\n        console.log('Marking all notifications as read:', validIds);\n        // Mettre à jour localement toutes les notifications\n        const updatedNotifications = notifications.map(n => validIds.includes(n.id) ? {\n          ...n,\n          isRead: true,\n          readAt: new Date().toISOString()\n        } : n);\n        // Mettre à jour l'interface utilisateur immédiatement\n        this.messageService.notifications.next(updatedNotifications);\n        // Mettre à jour le compteur de notifications non lues\n        const unreadCount = updatedNotifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        // Mettre à jour le cache de notifications dans le service\n        this.updateNotificationCache(updatedNotifications);\n        // Appeler le service pour marquer toutes les notifications comme lues\n        this.messageService.markAsRead(validIds).pipe(takeUntil(this.destroy$)).subscribe({\n          next: result => {\n            console.log('Mark all as read result:', result);\n            if (result && result.success) {\n              console.log('All notifications marked as read successfully');\n              // Si l'erreur était liée à cette opération, la réinitialiser\n              if (this.error && this.error.message.includes('mark')) {\n                this.error = null;\n              }\n            }\n          },\n          error: err => {\n            console.error('Error marking all notifications as read:', err);\n            console.error('Error details:', {\n              message: err.message,\n              stack: err.stack\n            });\n            // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur\n            // this.error = err;\n          }\n        });\n      });\n    }\n\n    hasNotifications() {\n      return this.notifications$.pipe(map(notifications => notifications?.length > 0));\n    }\n    hasUnreadNotifications() {\n      return this.unreadCount$.pipe(map(count => count > 0));\n    }\n    /**\n     * Active/désactive le filtre pour n'afficher que les notifications non lues\n     */\n    toggleUnreadFilter() {\n      this.showOnlyUnread = !this.showOnlyUnread;\n      console.log(`Filtre des notifications non lues ${this.showOnlyUnread ? 'activé' : 'désactivé'}`);\n      if (this.showOnlyUnread) {\n        // Utiliser la méthode du service pour obtenir uniquement les notifications non lues\n        this.filteredNotifications$ = this.messageService.getUnreadNotifications();\n      } else {\n        // Afficher toutes les notifications\n        this.filteredNotifications$ = this.notifications$;\n      }\n    }\n    /**\n     * Active/désactive le son des notifications\n     */\n    toggleSound() {\n      this.isSoundMuted = !this.isSoundMuted;\n      console.log(`Son des notifications ${this.isSoundMuted ? 'désactivé' : 'activé'}`);\n      // Utiliser la méthode du service pour activer/désactiver le son\n      this.messageService.setMuted(this.isSoundMuted);\n      // Tester le son si activé\n      if (!this.isSoundMuted) {\n        console.log('Test du son de notification...');\n        // Jouer le son après un court délai pour s'assurer que le navigateur est prêt\n        setTimeout(() => {\n          // Jouer le son deux fois pour s'assurer qu'il est audible\n          this.messageService.playNotificationSound();\n          // Jouer une deuxième fois après 1 seconde\n          setTimeout(() => {\n            this.messageService.playNotificationSound();\n          }, 1000);\n        }, 100);\n      }\n      // Sauvegarder la préférence dans le localStorage\n      localStorage.setItem('notificationSoundMuted', this.isSoundMuted.toString());\n    }\n    /**\n     * Récupère les pièces jointes d'une notification et ouvre le modal\n     * @param notificationId ID de la notification\n     */\n    getNotificationAttachments(notificationId) {\n      if (!notificationId) {\n        console.error('ID de notification invalide');\n        return;\n      }\n      console.log(`Récupération des pièces jointes pour la notification ${notificationId}`);\n      // Réinitialiser les pièces jointes et afficher le modal\n      this.currentAttachments = [];\n      this.loadingAttachments = true;\n      this.showAttachmentsModal = true;\n      // Vérifier d'abord si la notification existe dans le cache local\n      let notification;\n      // Utiliser pipe(take(1)) pour obtenir la valeur actuelle de l'Observable\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        notification = notifications.find(n => n.id === notificationId);\n      });\n      if (notification && notification.message && notification.message.attachments && notification.message.attachments.length > 0) {\n        console.log('Pièces jointes trouvées dans le cache local:', notification.message.attachments);\n        this.loadingAttachments = false;\n        // Conversion des pièces jointes au format attendu\n        this.currentAttachments = notification.message.attachments.map(attachment => ({\n          id: '',\n          url: attachment.url || '',\n          type: this.convertAttachmentType(attachment.type),\n          name: attachment.name || '',\n          size: attachment.size || 0,\n          duration: 0 // NotificationAttachment n'a pas de durée\n        }));\n\n        return;\n      }\n      // Si aucune pièce jointe n'est trouvée localement, essayer de les récupérer du serveur\n      this.messageService.getNotificationAttachments(notificationId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: attachments => {\n          console.log(`${attachments.length} pièces jointes récupérées du serveur`, attachments);\n          this.loadingAttachments = false;\n          this.currentAttachments = attachments;\n        },\n        error: err => {\n          console.error('Erreur lors de la récupération des pièces jointes', err);\n          this.loadingAttachments = false;\n        }\n      });\n    }\n    /**\n     * Ferme le modal des pièces jointes\n     */\n    closeAttachmentsModal() {\n      this.showAttachmentsModal = false;\n    }\n    /**\n     * Ouvre le modal des détails de notification\n     * @param notification Notification à afficher\n     */\n    openNotificationDetails(notification) {\n      console.log('Ouverture des détails de la notification:', notification);\n      this.currentNotification = notification;\n      this.showNotificationDetailsModal = true;\n      // Marquer la notification comme lue\n      if (!notification.isRead) {\n        this.markAsRead(notification.id);\n      }\n    }\n    /**\n     * Ferme le modal des détails de notification\n     */\n    closeNotificationDetailsModal() {\n      this.showNotificationDetailsModal = false;\n      this.currentNotification = null;\n    }\n    /**\n     * Vérifie si le type de fichier est une image\n     * @param type Type MIME du fichier\n     * @returns true si c'est une image, false sinon\n     */\n    isImage(type) {\n      return type?.startsWith('image/') || false;\n    }\n    /**\n     * Obtient l'icône FontAwesome correspondant au type de fichier\n     * @param type Type MIME du fichier\n     * @returns Classe CSS de l'icône\n     */\n    getFileIcon(type) {\n      if (!type) return 'fas fa-file';\n      if (type.startsWith('image/')) return 'fas fa-file-image';\n      if (type.startsWith('video/')) return 'fas fa-file-video';\n      if (type.startsWith('audio/')) return 'fas fa-file-audio';\n      if (type.startsWith('text/')) return 'fas fa-file-alt';\n      if (type.includes('pdf')) return 'fas fa-file-pdf';\n      if (type.includes('word') || type.includes('document')) return 'fas fa-file-word';\n      if (type.includes('excel') || type.includes('sheet')) return 'fas fa-file-excel';\n      if (type.includes('powerpoint') || type.includes('presentation')) return 'fas fa-file-powerpoint';\n      if (type.includes('zip') || type.includes('compressed')) return 'fas fa-file-archive';\n      return 'fas fa-file';\n    }\n    /**\n     * Obtient le libellé du type de fichier\n     * @param type Type MIME du fichier\n     * @returns Libellé du type de fichier\n     */\n    getFileTypeLabel(type) {\n      if (!type) return 'Fichier';\n      if (type.startsWith('image/')) return 'Image';\n      if (type.startsWith('video/')) return 'Vidéo';\n      if (type.startsWith('audio/')) return 'Audio';\n      if (type.startsWith('text/')) return 'Texte';\n      if (type.includes('pdf')) return 'PDF';\n      if (type.includes('word') || type.includes('document')) return 'Document';\n      if (type.includes('excel') || type.includes('sheet')) return 'Feuille de calcul';\n      if (type.includes('powerpoint') || type.includes('presentation')) return 'Présentation';\n      if (type.includes('zip') || type.includes('compressed')) return 'Archive';\n      return 'Fichier';\n    }\n    /**\n     * Formate la taille du fichier en unités lisibles\n     * @param size Taille en octets\n     * @returns Taille formatée (ex: \"1.5 MB\")\n     */\n    formatFileSize(size) {\n      if (!size) return '';\n      const units = ['B', 'KB', 'MB', 'GB', 'TB'];\n      let i = 0;\n      let formattedSize = size;\n      while (formattedSize >= 1024 && i < units.length - 1) {\n        formattedSize /= 1024;\n        i++;\n      }\n      return `${formattedSize.toFixed(1)} ${units[i]}`;\n    }\n    /**\n     * Ouvre une pièce jointe dans un nouvel onglet\n     * @param url URL de la pièce jointe\n     */\n    openAttachment(url) {\n      if (!url) return;\n      window.open(url, '_blank');\n    }\n    /**\n     * Télécharge une pièce jointe\n     * @param attachment Pièce jointe à télécharger\n     */\n    downloadAttachment(attachment) {\n      if (!attachment?.url) return;\n      const link = document.createElement('a');\n      link.href = attachment.url;\n      link.download = attachment.name || 'attachment';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n    acceptFriendRequest(notification) {\n      this.markAsRead(notification.id);\n    }\n    /**\n     * Supprime une notification et la stocke dans le localStorage\n     * @param notificationId ID de la notification à supprimer\n     */\n    deleteNotification(notificationId) {\n      console.log('Suppression de la notification:', notificationId);\n      if (!notificationId) {\n        console.error('ID de notification invalide');\n        this.error = new Error('ID de notification invalide');\n        return;\n      }\n      // Récupérer les IDs des notifications supprimées du localStorage\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      // Ajouter l'ID de la notification à supprimer\n      deletedNotificationIds.add(notificationId);\n      // Sauvegarder les IDs dans le localStorage\n      this.saveDeletedNotificationIds(deletedNotificationIds);\n      // Appeler le service pour supprimer la notification\n      this.messageService.deleteNotification(notificationId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          console.log('Résultat de la suppression:', result);\n          if (result && result.success) {\n            console.log('Notification supprimée avec succès');\n            // Si l'erreur était liée à cette opération, la réinitialiser\n            if (this.error && this.error.message.includes('suppression')) {\n              this.error = null;\n            }\n          }\n        },\n        error: err => {\n          console.error('Erreur lors de la suppression de la notification:', err);\n          // Même en cas d'erreur, conserver l'ID dans le localStorage\n          this.error = err;\n        }\n      });\n    }\n    /**\n     * Supprime toutes les notifications et les stocke dans le localStorage\n     */\n    deleteAllNotifications() {\n      console.log('Suppression de toutes les notifications');\n      // Récupérer toutes les notifications actuelles\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        // Récupérer les IDs des notifications supprimées du localStorage\n        const deletedNotificationIds = this.getDeletedNotificationIds();\n        // Ajouter tous les IDs des notifications actuelles\n        notifications.forEach(notification => {\n          deletedNotificationIds.add(notification.id);\n        });\n        // Sauvegarder les IDs dans le localStorage\n        this.saveDeletedNotificationIds(deletedNotificationIds);\n        // Appeler le service pour supprimer toutes les notifications\n        this.messageService.deleteAllNotifications().pipe(takeUntil(this.destroy$)).subscribe({\n          next: result => {\n            console.log('Résultat de la suppression de toutes les notifications:', result);\n            if (result && result.success) {\n              console.log(`${result.count} notifications supprimées avec succès`);\n              // Si l'erreur était liée à cette opération, la réinitialiser\n              if (this.error && this.error.message.includes('suppression')) {\n                this.error = null;\n              }\n            }\n          },\n          error: err => {\n            console.error('Erreur lors de la suppression de toutes les notifications:', err);\n            // Même en cas d'erreur, conserver les IDs dans le localStorage\n            this.error = err;\n          }\n        });\n      });\n    }\n    getErrorMessage() {\n      return this.error?.message || 'Unknown error occurred';\n    }\n    /**\n     * Récupère les IDs des notifications supprimées du localStorage\n     * @returns Set contenant les IDs des notifications supprimées\n     */\n    getDeletedNotificationIds() {\n      try {\n        const deletedIdsJson = localStorage.getItem('deletedNotificationIds');\n        if (deletedIdsJson) {\n          return new Set(JSON.parse(deletedIdsJson));\n        }\n        return new Set();\n      } catch (error) {\n        console.error('Erreur lors de la récupération des IDs de notifications supprimées:', error);\n        return new Set();\n      }\n    }\n    /**\n     * Sauvegarde les IDs des notifications supprimées dans le localStorage\n     * @param deletedIds Set contenant les IDs des notifications supprimées\n     */\n    saveDeletedNotificationIds(deletedIds) {\n      try {\n        localStorage.setItem('deletedNotificationIds', JSON.stringify(Array.from(deletedIds)));\n        console.log(`${deletedIds.size} IDs de notifications supprimées sauvegardés dans le localStorage`);\n      } catch (error) {\n        console.error('Erreur lors de la sauvegarde des IDs de notifications supprimées:', error);\n      }\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    /**\n     * Sélectionne ou désélectionne une notification\n     * @param notificationId ID de la notification\n     * @param event Événement de la case à cocher\n     */\n    toggleSelection(notificationId, event) {\n      event.stopPropagation(); // Empêcher la propagation de l'événement\n      if (this.selectedNotifications.has(notificationId)) {\n        this.selectedNotifications.delete(notificationId);\n      } else {\n        this.selectedNotifications.add(notificationId);\n      }\n      // Mettre à jour l'état de sélection globale\n      this.updateSelectionState();\n      // Afficher ou masquer la barre de sélection\n      this.showSelectionBar = this.selectedNotifications.size > 0;\n    }\n    /**\n     * Sélectionne ou désélectionne toutes les notifications\n     * @param event Événement de la case à cocher\n     */\n    toggleSelectAll(event) {\n      event.stopPropagation(); // Empêcher la propagation de l'événement\n      this.allSelected = !this.allSelected;\n      this.filteredNotifications$.pipe(take(1)).subscribe(notifications => {\n        if (this.allSelected) {\n          // Sélectionner toutes les notifications\n          notifications.forEach(notification => {\n            this.selectedNotifications.add(notification.id);\n          });\n        } else {\n          // Désélectionner toutes les notifications\n          this.selectedNotifications.clear();\n        }\n        // Afficher ou masquer la barre de sélection\n        this.showSelectionBar = this.selectedNotifications.size > 0;\n      });\n    }\n    /**\n     * Met à jour l'état de sélection globale\n     */\n    updateSelectionState() {\n      this.filteredNotifications$.pipe(take(1)).subscribe(notifications => {\n        this.allSelected = notifications.length > 0 && this.selectedNotifications.size === notifications.length;\n      });\n    }\n    /**\n     * Supprime les notifications sélectionnées\n     */\n    deleteSelectedNotifications() {\n      if (this.selectedNotifications.size === 0) {\n        return;\n      }\n      const selectedIds = Array.from(this.selectedNotifications);\n      console.log('Suppression des notifications sélectionnées:', selectedIds);\n      // Supprimer localement les notifications sélectionnées\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        const updatedNotifications = notifications.filter(notification => !this.selectedNotifications.has(notification.id));\n        // Mettre à jour l'interface utilisateur immédiatement\n        this.messageService.notifications.next(updatedNotifications);\n        // Mettre à jour le compteur de notifications non lues\n        const unreadCount = updatedNotifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        // Mettre à jour le cache de notifications dans le service\n        this.updateNotificationCache(updatedNotifications);\n        // Réinitialiser la sélection\n        this.selectedNotifications.clear();\n        this.allSelected = false;\n        this.showSelectionBar = false;\n      });\n      // Appeler le service pour supprimer les notifications sélectionnées\n      this.messageService.deleteMultipleNotifications(selectedIds).pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          console.log('Résultat de la suppression multiple:', result);\n          if (result && result.success) {\n            console.log(`${result.count} notifications supprimées avec succès`);\n          }\n        },\n        error: err => {\n          console.error('Erreur lors de la suppression multiple des notifications:', err);\n        }\n      });\n    }\n    /**\n     * Marque les notifications sélectionnées comme lues\n     */\n    markSelectedAsRead() {\n      if (this.selectedNotifications.size === 0) {\n        return;\n      }\n      const selectedIds = Array.from(this.selectedNotifications);\n      console.log('Marquage des notifications sélectionnées comme lues:', selectedIds);\n      // Marquer localement les notifications sélectionnées comme lues\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        const updatedNotifications = notifications.map(notification => this.selectedNotifications.has(notification.id) ? {\n          ...notification,\n          isRead: true,\n          readAt: new Date().toISOString()\n        } : notification);\n        // Mettre à jour l'interface utilisateur immédiatement\n        this.messageService.notifications.next(updatedNotifications);\n        // Mettre à jour le compteur de notifications non lues\n        const unreadCount = updatedNotifications.filter(n => !n.isRead).length;\n        this.messageService.notificationCount.next(unreadCount);\n        // Mettre à jour le cache de notifications dans le service\n        this.updateNotificationCache(updatedNotifications);\n        // Réinitialiser la sélection\n        this.selectedNotifications.clear();\n        this.allSelected = false;\n        this.showSelectionBar = false;\n      });\n      // Appeler le service pour marquer les notifications comme lues\n      this.messageService.markAsRead(selectedIds).pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          console.log('Résultat du marquage comme lu:', result);\n          if (result && result.success) {\n            console.log('Notifications marquées comme lues avec succès');\n          }\n        },\n        error: err => {\n          console.error('Erreur lors du marquage des notifications comme lues:', err);\n        }\n      });\n    }\n    /**\n     * Vérifie si une notification est sélectionnée\n     * @param notificationId ID de la notification\n     * @returns true si la notification est sélectionnée, false sinon\n     */\n    isSelected(notificationId) {\n      return this.selectedNotifications.has(notificationId);\n    }\n    /**\n     * Convertit un type de pièce jointe de notification en type de message\n     * @param type Type de pièce jointe\n     * @returns Type de message correspondant\n     */\n    convertAttachmentType(type) {\n      switch (type) {\n        case 'IMAGE':\n          return MessageType.IMAGE;\n        case 'FILE':\n          return MessageType.FILE;\n        case 'AUDIO':\n          return MessageType.AUDIO;\n        case 'VIDEO':\n          return MessageType.VIDEO;\n        case 'image':\n          return MessageType.IMAGE_LOWER;\n        case 'file':\n          return MessageType.FILE_LOWER;\n        case 'audio':\n          return MessageType.AUDIO_LOWER;\n        case 'video':\n          return MessageType.VIDEO_LOWER;\n        default:\n          return MessageType.FILE;\n        // Type par défaut\n      }\n    }\n\n    static {\n      this.ɵfac = function NotificationListComponent_Factory(t) {\n        return new (t || NotificationListComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ThemeService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NotificationListComponent,\n        selectors: [[\"app-notification-list\"]],\n        viewQuery: function NotificationListComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.notificationContainer = _t.first);\n          }\n        },\n        hostBindings: function NotificationListComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"scroll\", function NotificationListComponent_scroll_HostBindingHandler($event) {\n              return ctx.onScroll($event.target);\n            });\n          }\n        },\n        decls: 28,\n        vars: 19,\n        consts: [[1, \"futuristic-notifications-container\", \"main-grid-container\"], [1, \"background-elements\", \"background-grid\"], [1, \"futuristic-notifications-card\", \"content-card\", \"relative\", \"z-10\"], [1, \"futuristic-notifications-header\"], [1, \"futuristic-title\"], [1, \"fas\", \"fa-bell\", \"mr-2\"], [\"class\", \"flex space-x-2\", 4, \"ngIf\"], [\"class\", \"flex space-x-2 selection-actions\", 4, \"ngIf\"], [\"class\", \"futuristic-loading-container\", 4, \"ngIf\"], [\"class\", \"futuristic-error-message\", 4, \"ngIf\"], [\"class\", \"futuristic-empty-state\", 4, \"ngIf\"], [\"class\", \"futuristic-notifications-list\", 3, \"scroll\", 4, \"ngIf\"], [1, \"futuristic-modal-overlay\", 3, \"click\"], [1, \"futuristic-modal-container\", 3, \"click\"], [1, \"futuristic-modal-header\"], [1, \"futuristic-modal-title\"], [1, \"fas\", \"fa-paperclip\", \"mr-2\"], [1, \"futuristic-modal-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"futuristic-modal-body\"], [\"class\", \"futuristic-attachments-list\", 4, \"ngIf\"], [1, \"flex\", \"space-x-2\"], [\"title\", \"Rafra\\u00EEchir\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"select-all-checkbox\", 4, \"ngIf\"], [\"title\", \"Filtrer les non lues\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-filter\"], [1, \"futuristic-action-button\", 3, \"title\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"futuristic-primary-button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"futuristic-danger-button\", \"title\", \"Supprimer toutes les notifications\", 3, \"click\", 4, \"ngIf\"], [1, \"select-all-checkbox\"], [1, \"futuristic-checkbox\"], [\"type\", \"checkbox\", 3, \"checked\", \"click\"], [1, \"checkmark\"], [1, \"futuristic-primary-button\", 3, \"click\"], [1, \"fas\", \"fa-check-double\", \"mr-1\"], [\"title\", \"Supprimer toutes les notifications\", 1, \"futuristic-danger-button\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\", \"mr-1\"], [1, \"flex\", \"space-x-2\", \"selection-actions\"], [1, \"selection-count\"], [1, \"fas\", \"fa-check\", \"mr-1\"], [1, \"futuristic-danger-button\", 3, \"click\"], [1, \"futuristic-cancel-button\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [1, \"futuristic-loading-container\"], [1, \"futuristic-loading-circle\"], [1, \"futuristic-loading-text\"], [1, \"futuristic-error-message\"], [1, \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-triangle\", \"futuristic-error-icon\"], [1, \"futuristic-error-title\"], [1, \"futuristic-error-text\"], [1, \"futuristic-retry-button\", \"ml-auto\", 3, \"click\"], [1, \"futuristic-empty-state\"], [1, \"futuristic-empty-icon\"], [1, \"fas\", \"fa-bell-slash\"], [1, \"futuristic-empty-title\"], [1, \"futuristic-empty-text\"], [1, \"futuristic-check-button\", 3, \"click\"], [1, \"futuristic-notifications-list\", 3, \"scroll\"], [\"notificationContainer\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"futuristic-loading-more\", 4, \"ngIf\"], [1, \"futuristic-notification-card\"], [1, \"notification-checkbox\"], [1, \"notification-avatar\"], [\"alt\", \"Avatar\", \"onerror\", \"this.src='assets/images/default-avatar.png'\", 3, \"src\"], [1, \"notification-main-content\"], [1, \"notification-content\"], [1, \"notification-header\"], [1, \"notification-header-top\"], [1, \"notification-sender\"], [1, \"notification-time\"], [1, \"notification-text-container\"], [1, \"notification-text\"], [\"class\", \"notification-message-preview\", 4, \"ngIf\"], [\"class\", \"notification-attachments-indicator\", 4, \"ngIf\"], [\"class\", \"unread-indicator\", 4, \"ngIf\"], [1, \"notification-actions\"], [\"class\", \"notification-action-button notification-attachment-button\", \"title\", \"Voir les pi\\u00E8ces jointes\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"notification-action-button notification-join-button\", \"title\", \"Rejoindre la conversation\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Voir les d\\u00E9tails\", 1, \"notification-action-button\", \"notification-details-button\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [\"class\", \"notification-action-button notification-read-button\", \"title\", \"Marquer comme lu\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Supprimer cette notification\", 1, \"notification-action-button\", \"notification-delete-button\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\"], [1, \"notification-message-preview\"], [1, \"notification-attachments-indicator\"], [1, \"fas\", \"fa-paperclip\"], [1, \"unread-indicator\"], [\"title\", \"Voir les pi\\u00E8ces jointes\", 1, \"notification-action-button\", \"notification-attachment-button\", 3, \"click\"], [\"title\", \"Rejoindre la conversation\", 1, \"notification-action-button\", \"notification-join-button\", 3, \"click\"], [1, \"fas\", \"fa-comments\"], [\"title\", \"Marquer comme lu\", 1, \"notification-action-button\", \"notification-read-button\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [1, \"futuristic-loading-more\"], [1, \"futuristic-loading-circle-small\"], [1, \"futuristic-loading-text-small\"], [1, \"fas\", \"fa-file-alt\"], [1, \"futuristic-attachments-list\"], [\"class\", \"futuristic-attachment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"futuristic-attachment-item\"], [\"class\", \"futuristic-attachment-preview\", 4, \"ngIf\"], [\"class\", \"futuristic-attachment-icon\", 4, \"ngIf\"], [1, \"futuristic-attachment-info\"], [1, \"futuristic-attachment-name\"], [1, \"futuristic-attachment-meta\"], [1, \"futuristic-attachment-type\"], [\"class\", \"futuristic-attachment-size\", 4, \"ngIf\"], [1, \"futuristic-attachment-actions\"], [\"title\", \"Ouvrir\", 1, \"futuristic-attachment-button\", 3, \"click\"], [1, \"fas\", \"fa-external-link-alt\"], [\"title\", \"T\\u00E9l\\u00E9charger\", 1, \"futuristic-attachment-button\", 3, \"click\"], [1, \"fas\", \"fa-download\"], [1, \"futuristic-attachment-preview\"], [\"alt\", \"Image\", 3, \"src\", \"click\"], [1, \"futuristic-attachment-icon\"], [1, \"futuristic-attachment-size\"]],\n        template: function NotificationListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵpipe(1, \"async\");\n            i0.ɵɵelement(2, \"div\", 1);\n            i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3)(5, \"h2\", 4);\n            i0.ɵɵelement(6, \"i\", 5);\n            i0.ɵɵtext(7, \" Notifications \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, NotificationListComponent_div_8_Template, 13, 15, \"div\", 6);\n            i0.ɵɵtemplate(9, NotificationListComponent_div_9_Template, 12, 1, \"div\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(10, NotificationListComponent_div_10_Template, 4, 0, \"div\", 8);\n            i0.ɵɵtemplate(11, NotificationListComponent_div_11_Template, 10, 1, \"div\", 9);\n            i0.ɵɵtemplate(12, NotificationListComponent_div_12_Template, 9, 0, \"div\", 10);\n            i0.ɵɵpipe(13, \"async\");\n            i0.ɵɵtemplate(14, NotificationListComponent_div_14_Template, 5, 4, \"div\", 11);\n            i0.ɵɵpipe(15, \"async\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 12);\n            i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_16_listener() {\n              return ctx.closeAttachmentsModal();\n            });\n            i0.ɵɵelementStart(17, \"div\", 13);\n            i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_17_listener($event) {\n              return $event.stopPropagation();\n            });\n            i0.ɵɵelementStart(18, \"div\", 14)(19, \"h3\", 15);\n            i0.ɵɵelement(20, \"i\", 16);\n            i0.ɵɵtext(21, \" Pi\\u00E8ces jointes \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_button_click_22_listener() {\n              return ctx.closeAttachmentsModal();\n            });\n            i0.ɵɵelement(23, \"i\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"div\", 19);\n            i0.ɵɵtemplate(25, NotificationListComponent_div_25_Template, 4, 0, \"div\", 8);\n            i0.ɵɵtemplate(26, NotificationListComponent_div_26_Template, 7, 0, \"div\", 10);\n            i0.ɵɵtemplate(27, NotificationListComponent_div_27_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 13, ctx.isDarkMode$));\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", !ctx.showSelectionBar);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showSelectionBar);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !i0.ɵɵpipeBind1(13, 15, ctx.hasNotifications()));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && i0.ɵɵpipeBind1(15, 17, ctx.hasNotifications()));\n            i0.ɵɵadvance(2);\n            i0.ɵɵstyleProp(\"display\", ctx.showAttachmentsModal ? \"flex\" : \"none\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.loadingAttachments);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loadingAttachments && ctx.currentAttachments.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loadingAttachments && ctx.currentAttachments.length > 0);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.AsyncPipe, i4.DatePipe],\n        styles: [\"@charset \\\"UTF-8\\\";.futuristic-notifications-container[_ngcontent-%COMP%]{padding:0;min-height:calc(100vh - 4rem);position:relative;overflow:hidden;width:100%;display:flex;justify-content:center;padding-top:1rem;margin-bottom:0;height:100vh}:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%]{background-color:#edf1f4;color:#6d6870;position:relative;overflow:hidden}.dark[_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-container[_ngcontent-%COMP%]{background-color:#121212;color:#a0a0a0;position:relative;overflow:hidden}.futuristic-notifications-container[_ngcontent-%COMP%]   .background-elements[_ngcontent-%COMP%]{position:absolute;inset:0;overflow:hidden;pointer-events:none;z-index:0}:not(.dark)[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]:before, :not(.dark)   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;opacity:.05;background-image:linear-gradient(to right,#4f5fad 1px,transparent 1px),linear-gradient(to bottom,#4f5fad 1px,transparent 1px);background-size:calc(100% / 12) 100%,100% calc(100% / 12);z-index:0}.dark[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;opacity:.05;background-image:linear-gradient(to right,rgba(255,140,0,.3) 1px,transparent 1px),linear-gradient(to bottom,rgba(255,140,0,.3) 1px,transparent 1px);background-size:5% 100%,100% 5%;z-index:0;animation:_ngcontent-%COMP%_grid-pulse 4s ease-in-out infinite}.dark[_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .background-elements[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;left:0;right:0;height:2px;background:linear-gradient(to right,transparent 0%,rgba(255,140,0,.5) 50%,transparent 100%);box-shadow:0 0 10px #ff8c0080;z-index:1;animation:_ngcontent-%COMP%_scan 8s linear infinite}@keyframes _ngcontent-%COMP%_grid-pulse{0%{opacity:.03}50%{opacity:.07}to{opacity:.03}}@keyframes _ngcontent-%COMP%_scan{0%{top:-10%;opacity:.5}50%{opacity:.8}to{top:110%;opacity:.5}}.futuristic-notifications-card[_ngcontent-%COMP%]{border-radius:.5rem;overflow:hidden;position:relative;z-index:1;margin:.5rem auto;display:flex;flex-direction:column;width:100%;max-width:1200px;height:calc(100vh - 1rem)}:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%]{background-color:#fff;box-shadow:0 8px 32px #0000001a;border:1px solid rgba(79,95,173,.1)}.dark[_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-card[_ngcontent-%COMP%]{background-color:#1e1e1e;box-shadow:0 8px 32px #0000004d;border:1px solid rgba(109,120,201,.1);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.futuristic-notifications-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1rem;position:sticky;top:0;z-index:10;margin-bottom:1.5rem}:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%]{border-bottom:1px solid rgba(79,95,173,.1);background-color:#f0f4f880;box-shadow:0 4px 20px #0000001a}.dark[_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-header[_ngcontent-%COMP%]{border-bottom:1px solid rgba(0,247,255,.1);background-color:#0003;box-shadow:0 4px 20px #0000004d}:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#4f5fad}.dark[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:var(--accent-color)}:not(.dark)[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]{width:36px;height:36px;display:flex;align-items:center;justify-content:center;background-color:#4f5fad1a;color:#4f5fad;border:none;border-radius:50%;cursor:pointer;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover{background-color:#4f5fad33;transform:translateY(-2px);box-shadow:0 0 15px #4f5fad66}:not(.dark)[_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.5rem 1rem;background:linear-gradient(135deg,#4f5fad,#7826b5);color:#fff;font-size:.875rem;font-weight:500;border:none;border-radius:var(--border-radius-md);cursor:pointer;transition:all .3s ease;box-shadow:0 0 10px #4f5fad66;position:relative;overflow:hidden}:not(.dark)[_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%]:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:all .5s}:not(.dark)[_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 0 15px #4f5fad99}:not(.dark)[_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%]:hover:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%]:hover:before{left:100%}:not(.dark)[_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.5rem 1rem;background:linear-gradient(135deg,#ff5e62,#ff9d00);color:#fff;font-size:.875rem;font-weight:500;border:none;border-radius:var(--border-radius-md);cursor:pointer;transition:all .3s ease;box-shadow:0 0 10px #ff5e6266;position:relative;overflow:hidden}:not(.dark)[_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%]:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:all .5s}:not(.dark)[_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 0 15px #ff5e6299}:not(.dark)[_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%]:hover:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%]:hover:before{left:100%}.dark[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]{width:36px;height:36px;display:flex;align-items:center;justify-content:center;background-color:#00f7ff1a;color:#00f7ff;border:1px solid rgba(0,247,255,.2);border-radius:50%;cursor:pointer;transition:all .3s ease}.dark[_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-action-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:translateY(-2px);box-shadow:0 0 15px #00f7ff80}.dark[_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.5rem 1rem;background:linear-gradient(135deg,#00f7ff,#0066ff);color:#fff;font-size:.875rem;font-weight:500;border:none;border-radius:var(--border-radius-md);cursor:pointer;transition:all .3s ease;box-shadow:0 0 15px #00f7ff66;position:relative;overflow:hidden}.dark[_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:all .5s}.dark[_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 0 20px #00f7ff99}.dark[_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%]:hover:before, .dark   [_nghost-%COMP%]   .futuristic-primary-button[_ngcontent-%COMP%]:hover:before{left:100%}.dark[_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.5rem 1rem;background:linear-gradient(135deg,#ff0076,#ff6b69);color:#fff;font-size:.875rem;font-weight:500;border:none;border-radius:var(--border-radius-md);cursor:pointer;transition:all .3s ease;box-shadow:0 0 15px #ff007666;position:relative;overflow:hidden}.dark[_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:all .5s}.dark[_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 0 20px #ff007699}.dark[_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%]:hover:before, .dark   [_nghost-%COMP%]   .futuristic-danger-button[_ngcontent-%COMP%]:hover:before{left:100%}.futuristic-loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:4rem 2rem}.futuristic-loading-circle[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;border:2px solid transparent;border-top-color:var(--accent-color);border-bottom-color:var(--secondary-color);animation:_ngcontent-%COMP%_futuristic-spin 1.2s linear infinite;box-shadow:0 0 15px #00f7ff4d}@keyframes _ngcontent-%COMP%_futuristic-spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.futuristic-loading-text[_ngcontent-%COMP%]{margin-top:1rem;color:var(--text-dim);font-size:.875rem;text-align:center}.futuristic-error-message[_ngcontent-%COMP%]{margin:1rem;padding:1rem;background-color:#ff004c1a;border-left:4px solid var(--error-color);border-radius:var(--border-radius-md)}.futuristic-error-icon[_ngcontent-%COMP%]{color:var(--error-color);font-size:1.25rem;margin-right:.75rem}.futuristic-error-title[_ngcontent-%COMP%]{color:var(--error-color);font-weight:600;font-size:.875rem}.futuristic-error-text[_ngcontent-%COMP%]{color:var(--text-dim);font-size:.75rem;margin-top:.25rem}.futuristic-retry-button[_ngcontent-%COMP%]{padding:.25rem .75rem;background-color:#ff004c1a;color:var(--error-color);border:1px solid var(--error-color);border-radius:var(--border-radius-sm);font-size:.75rem;cursor:pointer;transition:all var(--transition-fast)}.futuristic-retry-button[_ngcontent-%COMP%]:hover{background-color:#ff004c33;box-shadow:0 0 10px #ff004c4d}.futuristic-empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:4rem 2rem;text-align:center}.futuristic-empty-icon[_ngcontent-%COMP%]{font-size:3rem;color:var(--accent-color);margin-bottom:1rem;opacity:.5}.futuristic-empty-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:var(--text-light);margin-bottom:.5rem}.futuristic-empty-text[_ngcontent-%COMP%]{color:var(--text-dim);font-size:.875rem;margin-bottom:1rem}.futuristic-check-button[_ngcontent-%COMP%]{padding:.5rem 1rem;background-color:#00f7ff1a;color:var(--accent-color);border:1px solid rgba(0,247,255,.3);border-radius:var(--border-radius-md);font-size:.875rem;cursor:pointer;transition:all var(--transition-fast)}.futuristic-check-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:translateY(-2px);box-shadow:var(--glow-effect)}.futuristic-notifications-list[_ngcontent-%COMP%]{padding:0;display:flex;flex-direction:column;flex:1;overflow-y:auto;position:relative;scrollbar-width:thin;z-index:1;width:100%}.futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]{scrollbar-color:#4f5fad transparent;background-color:#fff}:not(.dark)[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, :not(.dark)   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:#4f5fad;border-radius:10px}.dark[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]{scrollbar-color:var(--accent-color) transparent;background-color:transparent}.dark[_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .dark   [_nghost-%COMP%]   .futuristic-notifications-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:var(--accent-color);border-radius:10px}.futuristic-notification-card[_ngcontent-%COMP%]{display:flex;align-items:center;padding:30px 20px 16px;position:relative;transition:all .2s ease;margin:.5rem 1rem;border-radius:8px;flex-wrap:nowrap;justify-content:space-between}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]{border-bottom:1px solid rgba(79,95,173,.1);background-color:#fff;box-shadow:0 1px 3px #0000000d;border-radius:15px;transition:all .3s ease}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover{background-color:#4f5fad0d;transform:translateY(-1px);box-shadow:0 3px 6px #0000001a}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(79,95,173,.1),rgba(61,74,133,.2));border:1px solid rgba(79,95,173,.3);box-shadow:0 2px 10px #0000001a;border-bottom-right-radius:0;position:relative;overflow:hidden}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;border-radius:inherit;pointer-events:none;z-index:-1;box-shadow:inset 0 0 0 1px #fff3}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover{transform:translateY(-2px);background:linear-gradient(135deg,rgba(79,95,173,.15),rgba(61,74,133,.25));box-shadow:0 4px 12px #4f5fad33}.dark[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]{border-bottom:none;background-color:var( --dark-medium-bg, #252740 );border:1px solid rgba(255,255,255,.1);border-radius:15px;box-shadow:0 2px 10px #0003;margin-bottom:15px;margin-left:15px;margin-right:15px;transition:all .3s ease;color:var( --text-light, #ffffff )}.dark[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 15px #0000004d}.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]{position:relative;overflow:hidden;background:linear-gradient(135deg,#00f7ff20,#00c3ff30);border:1px solid rgba(0,247,255,.3);box-shadow:0 2px 10px #0003;border-bottom-right-radius:0;transition:all .3s ease}.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;border-radius:inherit;pointer-events:none;z-index:-1;box-shadow:inset 0 0 0 1px #00f7ff4d}.dark[_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-card.futuristic-notification-read[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 15px #00f7ff33;background:linear-gradient(135deg,#00f7ff30,#00c3ff40)}.futuristic-notification-unread[_ngcontent-%COMP%]{position:relative;overflow:hidden}.futuristic-notification-unread[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:5px;height:100%;z-index:1;animation:_ngcontent-%COMP%_flameBorder 3s ease-in-out infinite alternate}.futuristic-notification-unread[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:0;left:0;width:15px;height:100%;background:radial-gradient(ellipse at left,rgba(255,255,255,.2) 0%,transparent 70%);opacity:0;z-index:0;animation:_ngcontent-%COMP%_candleGlow 2s ease-in-out infinite}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]{background-color:#4f5fad0d;border-left:5px solid transparent;position:relative}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:before{background:linear-gradient(to bottom,#ff9d00,#ff5e62,#ff9d00,#ff5e62,#ff9d00);background-size:200% 200%;box-shadow:0 0 15px #ff9d00b3,0 0 30px #ff5e624d;animation:_ngcontent-%COMP%_flameBorder 3s ease-in-out infinite alternate,colorShift 8s linear infinite}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:after{background:radial-gradient(ellipse at left,rgba(255,157,0,.2) 0%,rgba(255,94,98,.1) 40%,transparent 70%)}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:hover{background-color:#4f5fad1a}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:hover:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:hover:before{animation:_ngcontent-%COMP%_flameBorder 1.5s ease-in-out infinite alternate,colorShift 4s linear infinite;box-shadow:0 0 20px #ff9d00cc,0 0 40px #ff5e6266}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:0;left:0;width:15px;height:100%;background-image:radial-gradient(circle at 30% 20%,rgba(255,255,255,.8) 0%,rgba(255,255,255,0) 3%),radial-gradient(circle at 40% 40%,rgba(255,255,255,.8) 0%,rgba(255,255,255,0) 4%),radial-gradient(circle at 20% 60%,rgba(255,255,255,.8) 0%,rgba(255,255,255,0) 3%),radial-gradient(circle at 40% 80%,rgba(255,255,255,.8) 0%,rgba(255,255,255,0) 4%),radial-gradient(circle at 10% 30%,rgba(255,255,255,.8) 0%,rgba(255,255,255,0) 3%);opacity:0;z-index:2;animation:_ngcontent-%COMP%_sparkle 4s ease-in-out infinite}.dark[_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]{background-color:#00f7ff0d;border-left:5px solid transparent;position:relative}.dark[_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:before{background:linear-gradient(to bottom,#00f7ff,#0066ff,#00f7ff,#0066ff,#00f7ff);background-size:200% 200%;box-shadow:0 0 15px #00f7ffcc,0 0 30px #06f6;animation:_ngcontent-%COMP%_flameBorder 3s ease-in-out infinite alternate,neonPulse 4s linear infinite}.dark[_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:after{background:radial-gradient(ellipse at left,rgba(0,247,255,.3) 0%,rgba(0,102,255,.1) 40%,transparent 70%)}.dark[_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:hover{background-color:#00f7ff1a}.dark[_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:hover:before, .dark   [_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:hover:before{animation:_ngcontent-%COMP%_flameBorder 1.5s ease-in-out infinite alternate,neonPulse 2s linear infinite;box-shadow:0 0 20px #00f7ffe6,0 0 40px #0066ff80}.dark[_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-notification-unread[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:0;left:0;width:15px;height:100%;background-image:radial-gradient(circle at 30% 20%,rgba(0,247,255,.9) 0%,rgba(0,247,255,0) 3%),radial-gradient(circle at 40% 40%,rgba(0,247,255,.9) 0%,rgba(0,247,255,0) 4%),radial-gradient(circle at 20% 60%,rgba(0,247,255,.9) 0%,rgba(0,247,255,0) 3%),radial-gradient(circle at 40% 80%,rgba(0,247,255,.9) 0%,rgba(0,247,255,0) 4%),radial-gradient(circle at 10% 30%,rgba(0,247,255,.9) 0%,rgba(0,247,255,0) 3%);opacity:0;z-index:2;animation:_ngcontent-%COMP%_sparkle 4s ease-in-out infinite}@keyframes _ngcontent-%COMP%_flameBorder{0%{background-position:0% 0%;filter:brightness(1) blur(0px);transform:scaleY(.95)}25%{filter:brightness(1.1) blur(.5px);transform:scaleY(1.05)}50%{background-position:100% 100%;filter:brightness(1.2) blur(1px);transform:scaleY(1)}75%{filter:brightness(1.1) blur(.5px);transform:scaleY(.98)}to{background-position:0% 0%;filter:brightness(1) blur(0px);transform:scaleY(.95)}}@keyframes _ngcontent-%COMP%_candleGlow{0%{opacity:.3;transform:translate(-5px) scaleX(.9)}50%{opacity:.7;transform:translate(-3px) scaleX(1.1)}to{opacity:.3;transform:translate(-5px) scaleX(.9)}}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_scaleIn{0%{transform:scale(.9);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _ngcontent-%COMP%_borderFlow{0%{background-position:0% 0%}to{background-position:200% 0%}}.futuristic-modal-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#000000b3;-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);display:none;align-items:center;justify-content:center;z-index:1000;animation:_ngcontent-%COMP%_fadeIn .3s ease;opacity:0;transition:opacity .3s ease}.futuristic-modal-overlay[style*=\\\"display: flex\\\"][_ngcontent-%COMP%]{opacity:1}.dark[_nghost-%COMP%]   .futuristic-modal-overlay[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-overlay[_ngcontent-%COMP%]{background-color:#000c;-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px);animation:_ngcontent-%COMP%_modalBackdropFadeIn .3s ease-out}@keyframes _ngcontent-%COMP%_modalBackdropFadeIn{0%{background-color:#0000;-webkit-backdrop-filter:blur(0px);backdrop-filter:blur(0px)}to{background-color:#000c;-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px)}}:not(.dark)[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]{width:90%;max-width:600px;max-height:80vh;background-color:#fff;border-radius:12px;overflow:hidden;box-shadow:0 10px 30px #0003;display:flex;flex-direction:column;animation:_ngcontent-%COMP%_scaleIn .3s ease;border:1px solid rgba(79,95,173,.2);position:relative}:not(.dark)[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:1px;background:linear-gradient(90deg,transparent 0%,rgba(79,95,173,.2) 20%,rgba(79,95,173,.8) 50%,rgba(79,95,173,.2) 80%,transparent 100%);background-size:200% 100%;animation:_ngcontent-%COMP%_borderFlow 3s infinite linear;box-shadow:0 0 10px #4f5fad66;z-index:1}.dark[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]{width:90%;max-width:600px;max-height:80vh;background-color:#121212f2;border-radius:16px;overflow:hidden;box-shadow:0 0 40px #00f7ff66,inset 0 0 20px #00f7ff1a;display:flex;flex-direction:column;animation:_ngcontent-%COMP%_modalFadeIn .4s cubic-bezier(.19,1,.22,1);border:1px solid rgba(0,247,255,.3);position:relative}@keyframes _ngcontent-%COMP%_modalFadeIn{0%{opacity:0;transform:scale(.9) translateY(20px)}to{opacity:1;transform:scale(1) translateY(0)}}.dark[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:-2px;background:linear-gradient(45deg,rgba(0,247,255,.3),rgba(157,78,221,.3),rgba(0,247,255,.3));background-size:400% 400%;border-radius:18px;z-index:-1;opacity:.5;animation:_ngcontent-%COMP%_gradientBorderDark 6s linear infinite;pointer-events:none}.dark[_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-modal-container[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:1px;background:linear-gradient(90deg,transparent 0%,rgba(0,247,255,.2) 20%,rgba(0,247,255,.8) 50%,rgba(0,247,255,.2) 80%,transparent 100%);background-size:200% 100%;animation:_ngcontent-%COMP%_borderFlow 3s infinite linear;box-shadow:0 0 15px #00f7ffb3;z-index:1}:not(.dark)[_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%]{padding:16px;display:flex;align-items:center;justify-content:space-between;border-bottom:1px solid rgba(79,95,173,.1);background-color:#4f5fad0d}:not(.dark)[_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#4f5fad;margin:0;display:flex;align-items:center}:not(.dark)[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;background-color:#4f5fad1a;color:#4f5fad;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}:not(.dark)[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover{background-color:#4f5fad33;transform:scale(1.1);box-shadow:0 0 10px #4f5fad4d}.dark[_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-header[_ngcontent-%COMP%]{padding:16px;display:flex;align-items:center;justify-content:space-between;border-bottom:1px solid rgba(0,247,255,.1);background-color:#0003}.dark[_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#00f7ff;margin:0;display:flex;align-items:center}.dark[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;background-color:#00f7ff1a;color:#00f7ff;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}.dark[_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-modal-close[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:scale(1.1);box-shadow:0 0 15px #00f7ff80}.futuristic-modal-body[_ngcontent-%COMP%]{padding:16px;overflow-y:auto;max-height:calc(80vh - 70px)}.futuristic-attachments-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px;border-radius:8px;background-color:#4f5fad0d;border:1px solid rgba(79,95,173,.1);transition:all .2s ease}:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover{background-color:#4f5fad1a;transform:translateY(-2px);box-shadow:0 5px 15px #0000001a}:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:4px;overflow:hidden;margin-right:12px;flex-shrink:0;border:1px solid rgba(79,95,173,.2)}:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:4px;background-color:#4f5fad1a;display:flex;align-items:center;justify-content:center;margin-right:12px;flex-shrink:0}:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px;color:#4f5fad}:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;background-color:#4f5fad1a;color:#4f5fad;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}:not(.dark)[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover{background-color:#4f5fad33;transform:scale(1.1);box-shadow:0 0 10px #4f5fad4d}.dark[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px;border-radius:8px;background-color:#00f7ff0d;border:1px solid rgba(0,247,255,.1);transition:all .2s ease}.dark[_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-attachment-item[_ngcontent-%COMP%]:hover{background-color:#00f7ff1a;transform:translateY(-2px);box-shadow:0 5px 15px #0003}.dark[_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-preview[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:4px;overflow:hidden;margin-right:12px;flex-shrink:0;border:1px solid rgba(0,247,255,.2);box-shadow:0 0 10px #00f7ff33}.dark[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:4px;background-color:#00f7ff1a;display:flex;align-items:center;justify-content:center;margin-right:12px;flex-shrink:0}.dark[_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px;color:#00f7ff}.dark[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;background-color:#00f7ff1a;color:#00f7ff;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease}.dark[_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-attachment-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:scale(1.1);box-shadow:0 0 15px #00f7ff80}.futuristic-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;cursor:pointer;transition:transform .2s ease}.futuristic-attachment-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover{transform:scale(1.1)}.futuristic-attachment-info[_ngcontent-%COMP%]{flex:1;min-width:0}.futuristic-attachment-name[_ngcontent-%COMP%]{font-weight:500;margin-bottom:4px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.futuristic-attachment-meta[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:.8rem;color:var(--text-dim)}.futuristic-attachment-actions[_ngcontent-%COMP%]{display:flex;gap:8px;margin-left:12px}.futuristic-loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 0}.futuristic-loading-circle[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:3px solid transparent;margin-bottom:16px;animation:_ngcontent-%COMP%_spin 1.2s linear infinite}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%]{border-top-color:#4f5fad;box-shadow:0 0 15px #4f5fad4d}.dark[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%]{border-top-color:#00f7ff;box-shadow:0 0 15px #00f7ff80}.futuristic-loading-text[_ngcontent-%COMP%]{font-size:.9rem;color:var(--text-dim)}.futuristic-empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 0;text-align:center}.futuristic-empty-icon[_ngcontent-%COMP%]{font-size:48px;margin-bottom:16px;opacity:.5}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%]{color:#4f5fad}.dark[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%]{color:#00f7ff}.futuristic-empty-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:500;margin-bottom:8px}.futuristic-empty-text[_ngcontent-%COMP%]{font-size:.9rem;color:var(--text-dim);max-width:300px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes _ngcontent-%COMP%_colorShift{0%{background-position:0% 0%;filter:hue-rotate(0deg)}25%{filter:hue-rotate(5deg) saturate(1.2)}50%{background-position:100% 100%;filter:hue-rotate(0deg) saturate(1)}75%{filter:hue-rotate(-5deg) saturate(1.2)}to{background-position:0% 0%;filter:hue-rotate(0deg)}}@keyframes _ngcontent-%COMP%_neonPulse{0%{filter:brightness(1) saturate(1)}25%{filter:brightness(1.2) saturate(1.2)}50%{filter:brightness(1.4) saturate(1.4)}75%{filter:brightness(1.2) saturate(1.2)}to{filter:brightness(1) saturate(1)}}@keyframes _ngcontent-%COMP%_sparkle{0%{opacity:0;transform:translate(0)}20%{opacity:.7;transform:translate(1px)}40%{opacity:0;transform:translate(0)}60%{opacity:.5;transform:translate(2px)}80%{opacity:.8;transform:translate(1px)}to{opacity:0;transform:translate(0)}}@keyframes _ngcontent-%COMP%_gradientBorderDark{0%{background-position:0% 0%}50%{background-position:100% 100%}to{background-position:0% 0%}}.notification-avatar[_ngcontent-%COMP%]{width:40px;height:40px;flex-shrink:0;margin-right:12px;margin-left:10px}.notification-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;border-radius:50%;object-fit:cover}.notification-content[_ngcontent-%COMP%]{flex:1;min-width:0;padding-right:16px}.notification-header[_ngcontent-%COMP%]{margin-bottom:6px}.notification-header-top[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:100%}.notification-sender[_ngcontent-%COMP%]{font-weight:600;font-size:.95rem;color:#4f5fad;padding:2px 0;transition:all .3s ease}.notification-sender[_ngcontent-%COMP%]:hover{color:#3d4a85;text-shadow:0 0 1px rgba(79,95,173,.3)}.dark[_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%]{color:#ff8c00;text-shadow:0 0 5px rgba(255,140,0,.3)}.dark[_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-sender[_ngcontent-%COMP%]:hover{color:#ffa040;text-shadow:0 0 8px rgba(255,140,0,.5)}.notification-text[_ngcontent-%COMP%]{color:#333;font-size:.9rem;position:relative;z-index:1}.dark[_nghost-%COMP%]   .notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-text[_ngcontent-%COMP%]{color:#fff}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%]{color:var( --light-text, #333333 );font-weight:400}.dark[_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-read[_ngcontent-%COMP%]   .notification-text[_ngcontent-%COMP%]{color:var( --dark-text, #ffffff );font-weight:400}.notification-message-preview[_ngcontent-%COMP%]{font-size:.85rem;color:#666;margin-top:4px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:100%}.dark[_nghost-%COMP%]   .notification-message-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-message-preview[_ngcontent-%COMP%]{color:#ccc}.notification-time[_ngcontent-%COMP%]{font-size:.75rem;color:#999;white-space:nowrap;margin-left:auto;padding-left:8px}.dark[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]{color:#aaa}.unread-indicator[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;position:absolute;right:16px;top:16px;animation:_ngcontent-%COMP%_pulse 2s infinite}:not(.dark)[_nghost-%COMP%]   .unread-indicator[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .unread-indicator[_ngcontent-%COMP%]{background-color:#4f5fad;box-shadow:0 0 8px #4f5fadb3}.dark[_nghost-%COMP%]   .unread-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .unread-indicator[_ngcontent-%COMP%]{width:12px;height:12px;background-color:#00f7ff;box-shadow:0 0 15px #00f7ffcc}@keyframes _ngcontent-%COMP%_pulse{0%{transform:translateY(-50%) scale(.95);box-shadow:0 0 #00f7ffb3}70%{transform:translateY(-50%) scale(1.1);box-shadow:0 0 0 10px #00f7ff00}to{transform:translateY(-50%) scale(.95);box-shadow:0 0 #00f7ff00}}.notification-main-content[_ngcontent-%COMP%]{flex:1;display:flex;overflow:hidden;position:relative;padding-right:200px}.notification-actions[_ngcontent-%COMP%]{position:absolute;right:16px;top:50%;transform:translateY(-50%);display:flex;align-items:center;gap:16px;z-index:10}.notification-attachments-indicator[_ngcontent-%COMP%]{font-size:.75rem;color:#ff8c00;margin-top:.25rem;display:flex;align-items:center}.dark[_nghost-%COMP%]   .notification-attachments-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachments-indicator[_ngcontent-%COMP%]{color:#00f7ffe6}.notification-attachments-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.25rem}.notification-action-button[_ngcontent-%COMP%], .mark-as-read-button[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;cursor:pointer;display:flex;align-items:center;justify-content:center;font-size:.9rem;transition:all .3s ease}.mark-as-read-button[_ngcontent-%COMP%]{position:relative}:not(.dark)[_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]{background-color:#6d6870;color:#fff;border:none;box-shadow:0 0 8px #6d687066;transition:all .3s ease}:not(.dark)[_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px) scale(1.1);box-shadow:0 0 12px #6d687099}:not(.dark)[_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:first-child, :not(.dark)   [_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:first-child{background-color:#ff8c00;color:#fff;box-shadow:0 0 8px #ff8c0099}:not(.dark)[_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:first-child:hover, :not(.dark)   [_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:first-child:hover{transform:translateY(-2px) scale(1.1);box-shadow:0 0 15px #ff8c00cc}@keyframes _ngcontent-%COMP%_pulse-light{0%{opacity:.7}50%{opacity:.3}to{opacity:.7}}@keyframes _ngcontent-%COMP%_pulse-dark{0%{opacity:.7}50%{opacity:.3}to{opacity:.7}}@keyframes _ngcontent-%COMP%_rocket-shake{0%,to{transform:translateY(0)}10%,30%,50%,70%,90%{transform:translateY(-1px) rotate(-2deg)}20%,40%,60%,80%{transform:translateY(1px) rotate(2deg)}}:not(.dark)[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad,#3d4a85);color:#fff;box-shadow:0 0 8px #4f5fad99;position:relative;overflow:hidden;z-index:1}:not(.dark)[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]:before, :not(.dark)   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:-50%;left:-50%;width:200%;height:200%;background:radial-gradient(circle,rgba(255,255,255,.3) 0%,rgba(255,255,255,0) 60%);opacity:0;transform:scale(.5);transition:all .3s ease;z-index:-1}:not(.dark)[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px) scale(1.1);box-shadow:0 0 15px #4f5fadcc}:not(.dark)[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]:hover:before, :not(.dark)   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]:hover:before{opacity:1;transform:scale(1);animation:_ngcontent-%COMP%_pulse-light 1.5s infinite}:not(.dark)[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:relative;z-index:2}.dark[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(0,247,255,.2),rgba(0,195,255,.3));color:#00f7ff;border:1px solid rgba(0,247,255,.4);box-shadow:0 0 8px #00f7ff66;position:relative;overflow:hidden;z-index:1}.dark[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:-50%;left:-50%;width:200%;height:200%;background:radial-gradient(circle,rgba(0,247,255,.4) 0%,rgba(0,195,255,.1) 60%);opacity:0;transform:scale(.5);transition:all .3s ease;z-index:-1}.dark[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(0,247,255,.3),rgba(0,195,255,.4));transform:scale(1.1);box-shadow:0 0 15px #00f7ff99}.dark[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]:hover:before, .dark   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]:hover:before{opacity:1;transform:scale(1);animation:_ngcontent-%COMP%_pulse-dark 1.5s infinite}.dark[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:relative;z-index:2}:not(.dark)[_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover{background-color:#4f5fad;transform:scale(1.1);box-shadow:0 0 12px #4f5fad99}:not(.dark)[_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:-2px;border-radius:50%;border:1px solid rgba(79,95,173,.2);animation:_ngcontent-%COMP%_ripple 2s infinite;opacity:0}:not(.dark)[_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover:after, :not(.dark)   [_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover:after{opacity:1}:not(.dark)[_nghost-%COMP%]   .mark-as-read-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .mark-as-read-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad,#7826b5);color:#fff;border:none;box-shadow:0 0 10px #4f5fad80}:not(.dark)[_nghost-%COMP%]   .mark-as-read-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .mark-as-read-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#5a6ac8,#8f2dd6);transform:scale(1.1);box-shadow:0 0 15px #4f5fadb3}:not(.dark)[_nghost-%COMP%]   .mark-as-read-button[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .mark-as-read-button[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:-2px;border-radius:50%;border:1px solid rgba(79,95,173,.3);animation:_ngcontent-%COMP%_ripple 2s infinite}.dark[_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]{background-color:#00f7ff1a;color:#00f7ff;border:1px solid rgba(0,247,255,.3);box-shadow:0 0 8px #00f7ff33}.dark[_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:first-child, .dark   [_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:first-child{background-color:#ff6b0033;color:#ff8c00;border:1px solid rgba(255,107,0,.3);box-shadow:0 0 8px #ff6b0066}.dark[_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;transform:scale(1.1);box-shadow:0 0 12px #00f7ff66}.dark[_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:first-child:hover, .dark   [_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:first-child:hover{background-color:#ff6b004d;transform:scale(1.1);box-shadow:0 0 15px #ff6b0099}.dark[_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:-2px;border-radius:50%;border:1px solid rgba(0,247,255,.2);animation:_ngcontent-%COMP%_ripple 2s infinite;opacity:0}.dark[_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover:after, .dark   [_nghost-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover:after{opacity:1}.dark[_nghost-%COMP%]   .mark-as-read-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .mark-as-read-button[_ngcontent-%COMP%]{background:rgba(0,247,255,.2);color:#00f7ff;border:1px solid rgba(0,247,255,.4);box-shadow:0 0 15px #00f7ff66}.dark[_nghost-%COMP%]   .mark-as-read-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .mark-as-read-button[_ngcontent-%COMP%]:hover{background:rgba(0,247,255,.3);transform:scale(1.1);box-shadow:0 0 20px #00f7ff99}.dark[_nghost-%COMP%]   .mark-as-read-button[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .mark-as-read-button[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:-2px;border-radius:50%;border:1px solid rgba(0,247,255,.3);animation:_ngcontent-%COMP%_ripple 2s infinite}@keyframes _ngcontent-%COMP%_ripple{0%{transform:scale(1);opacity:.7}to{transform:scale(1.5);opacity:0}}@keyframes _ngcontent-%COMP%_gradientBorder{0%{background-position:0% 0%}50%{background-position:100% 100%}to{background-position:0% 0%}}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-badge[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-badge[_ngcontent-%COMP%]{position:absolute;top:.75rem;right:.75rem}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-indicator[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-indicator[_ngcontent-%COMP%]{display:block;width:10px;height:10px;background:linear-gradient(135deg,#7f7fd5,#91eae4);border-radius:50%;box-shadow:0 0 10px #7f7fd580,0 0 20px #7f7fd533;animation:_ngcontent-%COMP%_pulse-gentle 3s infinite,colorChangeGentle 8s infinite alternate;position:relative}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-indicator[_ngcontent-%COMP%]:after, :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-indicator[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:-2px;border-radius:50%;background:transparent;border:1px solid rgba(127,127,213,.3);animation:_ngcontent-%COMP%_rippleGentle 3s infinite}@keyframes _ngcontent-%COMP%_pulse-gentle{0%{box-shadow:0 0 #7f7fd580,0 0 #91eae480;transform:scale(.95)}50%{transform:scale(1.05)}70%{box-shadow:0 0 0 8px #7f7fd500,0 0 0 4px #91eae400;transform:scale(1)}to{box-shadow:0 0 #7f7fd500,0 0 #91eae400;transform:scale(.95)}}@keyframes _ngcontent-%COMP%_colorChangeGentle{0%{background:linear-gradient(135deg,#7f7fd5,#91eae4)}33%{background:linear-gradient(135deg,#91eae4,#86a8e7)}66%{background:linear-gradient(135deg,#86a8e7,#d8b5ff)}to{background:linear-gradient(135deg,#7f7fd5,#91eae4)}}@keyframes _ngcontent-%COMP%_rippleGentle{0%{transform:scale(1);opacity:.7}to{transform:scale(1.3);opacity:0}}:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]{position:relative;width:48px;height:48px;flex-shrink:0}:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:50%;border:2px solid rgba(79,95,173,.3);transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-color:#4f5fad;box-shadow:0 0 15px #4f5fad80}:not(.dark)[_nghost-%COMP%]   .futuristic-avatar-placeholder[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar-placeholder[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#4f5fad1a;color:#4f5fad;border-radius:50%;border:2px solid rgba(79,95,173,.3)}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-type-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-type-icon[_ngcontent-%COMP%]{position:absolute;bottom:-2px;right:-2px;width:18px;height:18px;background:linear-gradient(135deg,#4f5fad,#7826b5);color:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:.625rem;box-shadow:0 0 8px #4f5fad80}.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]{position:relative;width:48px;height:48px;flex-shrink:0}.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:50%;border:2px solid rgba(0,247,255,.3);transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-card[_ngcontent-%COMP%]:hover   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-color:var(--accent-color);box-shadow:0 0 15px #00f7ff80}.dark[_nghost-%COMP%]   .futuristic-avatar-placeholder[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar-placeholder[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#00f7ff1a;color:var(--accent-color);border-radius:50%;border:2px solid rgba(0,247,255,.3)}.dark[_nghost-%COMP%]   .futuristic-notification-type-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-type-icon[_ngcontent-%COMP%]{position:absolute;bottom:-2px;right:-2px;width:18px;height:18px;background:linear-gradient(135deg,var(--accent-color),var(--secondary-color));color:var(--text-light);border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:.625rem;box-shadow:0 0 8px #00f7ff80}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-content[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-content[_ngcontent-%COMP%]{flex:1;min-width:0}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-text[_ngcontent-%COMP%]{color:#6d6870;font-size:.875rem;margin-bottom:.5rem}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-sender[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-sender[_ngcontent-%COMP%]{font-weight:600;color:#4f5fad;margin-right:.25rem}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-time[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-time[_ngcontent-%COMP%]{color:#6d6870;font-size:.75rem}:not(.dark)[_nghost-%COMP%]   .futuristic-message-preview[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-message-preview[_ngcontent-%COMP%]{background-color:#4f5fad0d;border-radius:var(--border-radius-sm);padding:.75rem;margin-top:.5rem;color:#6d6870;font-size:.875rem;border-left:2px solid rgba(79,95,173,.3)}:not(.dark)[_nghost-%COMP%]   .futuristic-notification-actions[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-notification-actions[_ngcontent-%COMP%]{display:flex;gap:.5rem;margin-top:.75rem}:not(.dark)[_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]{padding:.35rem .85rem;background:linear-gradient(135deg,rgba(127,127,213,.2),rgba(145,234,228,.2));color:#4f5fad;border:1px solid rgba(127,127,213,.4);border-radius:var(--border-radius-sm);font-size:.8rem;font-weight:500;cursor:pointer;transition:all var(--transition-fast);position:relative;overflow:hidden;display:flex;align-items:center;gap:.35rem}:not(.dark)[_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(127,127,213,.2),transparent);transition:all .5s}:not(.dark)[_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(127,127,213,.3),rgba(145,234,228,.3));box-shadow:0 0 15px #7f7fd580;transform:translateY(-2px)}:not(.dark)[_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover:before{left:100%}:not(.dark)[_nghost-%COMP%]   .futuristic-accept-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-accept-button[_ngcontent-%COMP%]{padding:.25rem .75rem;background-color:#0080001a;color:#2a5a03;border:1px solid rgba(0,128,0,.3);border-radius:var(--border-radius-sm);font-size:.75rem;cursor:pointer;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-accept-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-accept-button[_ngcontent-%COMP%]:hover{background-color:#00800033;box-shadow:0 0 10px #0080004d}.dark[_nghost-%COMP%]   .futuristic-notification-content[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-content[_ngcontent-%COMP%]{flex:1;min-width:0}.notification-main-content[_ngcontent-%COMP%]{flex:1;margin:0 15px;overflow:hidden;padding-right:20px}.notification-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;margin-bottom:.5rem}.notification-text-container[_ngcontent-%COMP%]{margin-bottom:.75rem;padding-right:20px}.dark[_nghost-%COMP%]   .futuristic-notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-text[_ngcontent-%COMP%]{color:#000c;font-size:.875rem;margin-bottom:.5rem;font-weight:400}.dark[_nghost-%COMP%]   .futuristic-notification-sender[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-sender[_ngcontent-%COMP%]{font-weight:600;color:#ff8c00;margin-right:.25rem}.dark[_nghost-%COMP%]   .futuristic-notification-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-time[_ngcontent-%COMP%]{color:#00000080;font-size:.75rem;font-weight:300}.dark[_nghost-%COMP%]   .futuristic-message-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-message-preview[_ngcontent-%COMP%]{background-color:#f0f0f080;border-radius:var(--border-radius-sm);padding:.75rem;margin-top:.5rem;color:#000000b3;font-size:.875rem;border-left:2px solid rgba(255,140,0,.5)}.dark[_nghost-%COMP%]   .futuristic-notification-actions[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-actions[_ngcontent-%COMP%]{display:flex;gap:.5rem;margin-top:.75rem}.dark[_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]{padding:.35rem .85rem;background:linear-gradient(135deg,rgba(255,140,0,.2),rgba(255,94,98,.2));color:#ff8c00;border:1px solid rgba(255,140,0,.4);border-radius:var(--border-radius-sm);font-size:.8rem;font-weight:500;cursor:pointer;transition:all var(--transition-fast);position:relative;overflow:hidden;display:flex;align-items:center;gap:.35rem}.dark[_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,140,0,.2),transparent);transition:all .5s}.dark[_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(255,140,0,.3),rgba(255,94,98,.3));box-shadow:0 0 15px #ff8c0080;transform:translateY(-2px)}.dark[_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover:before, .dark   [_nghost-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover:before{left:100%}.dark[_nghost-%COMP%]   .futuristic-accept-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-accept-button[_ngcontent-%COMP%]{padding:.25rem .75rem;background-color:#00ff8033;color:#00ff80e6;border:1px solid rgba(0,255,128,.4);border-radius:var(--border-radius-sm);font-size:.75rem;cursor:pointer;transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-accept-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-accept-button[_ngcontent-%COMP%]:hover{background-color:#00ff804d;box-shadow:0 0 10px #00ff8080}.futuristic-loading-more[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:1rem;margin-top:.5rem}.futuristic-loading-circle-small[_ngcontent-%COMP%]{width:20px;height:20px;border-radius:50%;border:2px solid transparent;border-top-color:#4f5fad;border-bottom-color:#4f5fad;animation:_ngcontent-%COMP%_futuristic-spin 1.2s linear infinite}.dark[_nghost-%COMP%]   .futuristic-loading-circle-small[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-circle-small[_ngcontent-%COMP%]{border-top-color:#00f7ff;border-bottom-color:#00f7ff}.futuristic-loading-text-small[_ngcontent-%COMP%]{margin-top:.5rem;color:#6d6870;font-size:.75rem;text-align:center}.dark[_nghost-%COMP%]   .futuristic-loading-text-small[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-text-small[_ngcontent-%COMP%]{color:#aaa}.notification-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:5px;margin-left:auto}.notification-time[_ngcontent-%COMP%]{font-size:.75rem;color:#00f7ffcc;font-weight:500;padding:2px 8px;border-radius:12px;background-color:#00f7ff1a;border:1px solid rgba(0,247,255,.2);display:inline-block;margin-left:8px}.dark[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]{color:#00f7ffe6;background-color:#00f7ff26;border:1px solid rgba(0,247,255,.3);text-shadow:0 0 5px rgba(0,247,255,.4);box-shadow:0 0 8px #00f7ff33;transition:all .3s ease}.notification-time[_ngcontent-%COMP%]:hover{background-color:#00f7ff33;box-shadow:0 0 10px #00f7ff4d;transform:translateY(-1px)}.dark[_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-time[_ngcontent-%COMP%]:hover{background-color:#00f7ff40;box-shadow:0 0 12px #00f7ff66;transform:translateY(-1px)}.notification-text[_ngcontent-%COMP%]{font-size:.9rem;color:var(--text-light);line-height:1.4;display:block;width:100%}.notification-action-button[_ngcontent-%COMP%]{background:none;border:none;color:var(--text-dim);padding:4px;cursor:pointer;transition:all .3s ease;border-radius:50%;width:36px;height:36px;display:flex;align-items:center;justify-content:center;position:relative;box-shadow:0 0 5px #0000001a}.notification-join-button[_ngcontent-%COMP%]{background-color:#00c8531a;color:#00c853;border:1px solid rgba(0,200,83,.3)}.notification-join-button[_ngcontent-%COMP%]:hover{background-color:#00c85333;color:#00c853;transform:scale(1.1);box-shadow:0 0 10px #00c85366}.notification-read-button[_ngcontent-%COMP%]{background-color:#ffc1071a;color:#ffc107;border:1px solid rgba(255,193,7,.3)}.notification-read-button[_ngcontent-%COMP%]:hover{background-color:#ffc10733;color:#ffc107;transform:scale(1.1);box-shadow:0 0 10px #ffc10766}.notification-delete-button[_ngcontent-%COMP%]{background-color:#ff00001a;color:#ff5252;border:1px solid rgba(255,0,0,.3)}.notification-delete-button[_ngcontent-%COMP%]:hover{background-color:#f003;color:#ff5252;transform:scale(1.1);box-shadow:0 0 10px #f006}.notification-details-button[_ngcontent-%COMP%]{background-color:#ffc1071a;color:#ffc107;border:1px solid rgba(255,193,7,.3)}.notification-details-button[_ngcontent-%COMP%]:hover{background-color:#ffc10733;color:#ffc107;transform:scale(1.1);box-shadow:0 0 10px #ffc10766}.notification-attachment-button[_ngcontent-%COMP%]{background-color:#9c27b01a;color:#9c27b0;border:1px solid rgba(156,39,176,.3)}.notification-attachment-button[_ngcontent-%COMP%]:hover{background-color:#9c27b033;color:#9c27b0;transform:scale(1.1);box-shadow:0 0 10px #9c27b066}.dark[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]{background-color:#00c85333;color:#00e676;border:1px solid rgba(0,200,83,.4);box-shadow:0 0 8px #00c8534d}.dark[_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-join-button[_ngcontent-%COMP%]:hover{background-color:#00c8534d;box-shadow:0 0 12px #00c85380}.dark[_nghost-%COMP%]   .notification-read-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-read-button[_ngcontent-%COMP%]{background-color:#ffc10733;color:#ffc107;border:1px solid rgba(255,193,7,.4);box-shadow:0 0 8px #ffc1074d}.dark[_nghost-%COMP%]   .notification-read-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-read-button[_ngcontent-%COMP%]:hover{background-color:#ffc1074d;box-shadow:0 0 12px #ffc10780}.dark[_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]{background-color:#f003;color:#ff7070;border:1px solid rgba(255,0,0,.4);box-shadow:0 0 8px #ff00004d}.dark[_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover{background-color:#ff00004d;box-shadow:0 0 12px #ff000080}.dark[_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%]{background-color:#ffc10733;color:#ffca28;border:1px solid rgba(255,193,7,.4);box-shadow:0 0 8px #ffc1074d}.dark[_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%]:hover{background-color:#ffc1074d;box-shadow:0 0 12px #ffc10780}.dark[_nghost-%COMP%]   .notification-attachment-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-attachment-button[_ngcontent-%COMP%]{background-color:#9c27b033;color:#ce93d8;border:1px solid rgba(156,39,176,.4);box-shadow:0 0 8px #9c27b04d}.dark[_nghost-%COMP%]   .notification-attachment-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-attachment-button[_ngcontent-%COMP%]:hover{background-color:#9c27b04d;box-shadow:0 0 12px #9c27b080}.notification-details-button[_ngcontent-%COMP%]   .gradient-icon[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad,#7826b5);-webkit-background-clip:text;background-clip:text;color:transparent}:not(.dark)[_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff1a;box-shadow:0 0 8px #00f7ff4d}:not(.dark)[_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%]:hover   .gradient-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%]:hover   .gradient-icon[_ngcontent-%COMP%]{background:#00f7ff;-webkit-background-clip:text;background-clip:text;color:transparent}.dark[_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%]:hover{background-color:#00f7ff1a;box-shadow:0 0 8px #00f7ff66}.dark[_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%]:hover   .gradient-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .notification-details-button[_ngcontent-%COMP%]:hover   .gradient-icon[_ngcontent-%COMP%]{background:#00f7ff;-webkit-background-clip:text;background-clip:text;color:transparent}.notification-delete-button[_ngcontent-%COMP%]{color:#ff5e62}:not(.dark)[_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover{background-color:#ff8c001a;color:#ff8c00;box-shadow:0 0 8px #ff8c004d}.dark[_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover{background-color:#ff8c001a;color:#ff8c00;box-shadow:0 0 8px #ff8c0066}@keyframes _ngcontent-%COMP%_shine{0%{background-position:-100% 0}to{background-position:200% 0}}.futuristic-checkbox[_ngcontent-%COMP%]{position:relative;display:inline-block;width:22px;height:22px;cursor:pointer;transition:all .2s ease}.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{position:absolute;opacity:0;cursor:pointer;height:0;width:0}.checkmark[_ngcontent-%COMP%]{position:absolute;top:0;left:0;height:22px;width:22px;background:linear-gradient(135deg,rgba(0,255,200,.1),rgba(0,200,255,.1));border:2px solid transparent;border-radius:50%;transition:all .3s ease;box-shadow:0 0 10px #00ffc866;display:flex;align-items:center;justify-content:center;animation:_ngcontent-%COMP%_glow-pulse 2s infinite alternate;position:relative;z-index:1;overflow:hidden}@keyframes _ngcontent-%COMP%_glow-pulse{0%{box-shadow:0 0 8px #00ffc84d}to{box-shadow:0 0 15px #00c8ff99}}.dark[_nghost-%COMP%]   .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .checkmark[_ngcontent-%COMP%]{background:rgba(0,247,255,.1);border:1px solid rgba(0,247,255,.3);box-shadow:0 0 12px #00f7ff66;animation:_ngcontent-%COMP%_glow-pulse 2s infinite alternate}.futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%] ~ .checkmark[_ngcontent-%COMP%]{background:rgba(0,247,255,.2);border:1px solid rgba(0,247,255,.3);box-shadow:var( --glow-effect );transform:scale(1.05)}.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%] ~ .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%] ~ .checkmark[_ngcontent-%COMP%]{background:rgba(0,247,255,.2);border:1px solid rgba(0,247,255,.3);box-shadow:var( --glow-effect );transform:scale(1.05)}.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked ~ .checkmark[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(0,255,200,.8),rgba(0,200,255,.8));border:2px solid transparent;box-shadow:0 0 20px #00ffc8cc;animation:_ngcontent-%COMP%_checkbox-glow 1.5s infinite alternate}@keyframes _ngcontent-%COMP%_checkbox-glow{0%{box-shadow:0 0 15px #00ffc899;transform:scale(1)}to{box-shadow:0 0 25px #00c8ffe6;transform:scale(1.15)}}@keyframes _ngcontent-%COMP%_checkbox-pulse{0%{transform:scale(1);box-shadow:0 0 15px #00f7ff66}to{transform:scale(1.15);box-shadow:0 0 20px #00f7ffcc}}.dark[_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked ~ .checkmark[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked ~ .checkmark[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(0,255,200,.8),rgba(0,200,255,.8));border:2px solid transparent;box-shadow:0 0 20px #00ffc8cc;animation:_ngcontent-%COMP%_checkbox-glow 1.5s infinite alternate}.checkmark[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;display:none}.futuristic-checkbox[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked ~ .checkmark[_ngcontent-%COMP%]:after{display:block}.futuristic-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after{left:7px;top:3px;width:6px;height:12px;border:solid white;border-width:0 2px 2px 0;transform:rotate(45deg);box-shadow:0 0 5px #fffc;animation:_ngcontent-%COMP%_pulse-check 1.5s infinite alternate}@keyframes _ngcontent-%COMP%_pulse-check{0%{opacity:.8;box-shadow:0 0 5px #fffc}to{opacity:1;box-shadow:0 0 10px #fff}}.select-all-checkbox[_ngcontent-%COMP%]{display:inline-flex;align-items:center;justify-content:center;margin:0 5px}.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]{width:36px;height:36px}.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]{width:36px;height:36px;border-radius:50%;background:rgba(0,247,255,.1);border:1px solid rgba(0,247,255,.3);box-shadow:0 0 10px #00f7ff66}.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .futuristic-checkbox[_ngcontent-%COMP%]:hover   input[_ngcontent-%COMP%] ~ .checkmark[_ngcontent-%COMP%]{background:rgba(0,247,255,.2);box-shadow:var(--glow-effect);transform:scale(1.05)}.flex.space-x-2[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%]   .checkmark[_ngcontent-%COMP%]:after{left:13px;top:7px;width:8px;height:16px}.selection-actions[_ngcontent-%COMP%]{display:flex;align-items:center;background-color:#ff8c001a;border-radius:8px;padding:8px 12px;box-shadow:0 0 15px #ff8c0033;animation:_ngcontent-%COMP%_fadeIn .3s ease}.dark[_nghost-%COMP%]   .selection-actions[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .selection-actions[_ngcontent-%COMP%]{background-color:#ff8c001a;box-shadow:0 0 15px #ff8c0033}.selection-count[_ngcontent-%COMP%]{font-weight:500;margin-right:15px;color:#ff8c00}.dark[_nghost-%COMP%]   .selection-count[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .selection-count[_ngcontent-%COMP%]{color:#ff8c00e6}.futuristic-notification-selected[_ngcontent-%COMP%]{border:1px solid rgba(255,140,0,.5)!important;background-color:#ff8c000d!important;box-shadow:0 5px 15px #ff8c001a!important;transform:translateY(-2px)}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]{border:1px solid rgba(255,140,0,.3)!important;background:linear-gradient(135deg,rgba(255,140,0,.15),rgba(255,0,128,.15),rgba(128,0,255,.15))!important;box-shadow:0 5px 15px #ff8c0033,inset 0 0 20px #ff00801a!important;transform:translateY(-2px);padding:18px 22px!important;margin-bottom:18px!important;position:relative;overflow:hidden}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:-50%;left:-50%;width:200%;height:200%;background:radial-gradient(circle,rgba(255,140,0,.1) 0%,transparent 70%);animation:_ngcontent-%COMP%_rotate-gradient 8s linear infinite;pointer-events:none}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]:after, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;background-image:radial-gradient(circle at 10% 10%,rgba(255,255,255,.8) 0%,rgba(255,255,255,0) 2%),radial-gradient(circle at 20% 30%,rgba(255,140,0,.8) 0%,rgba(255,140,0,0) 2%),radial-gradient(circle at 30% 70%,rgba(255,0,128,.8) 0%,rgba(255,0,128,0) 2%),radial-gradient(circle at 70% 40%,rgba(128,0,255,.8) 0%,rgba(128,0,255,0) 2%),radial-gradient(circle at 80% 80%,rgba(255,255,255,.8) 0%,rgba(255,255,255,0) 2%),radial-gradient(circle at 90% 10%,rgba(255,140,0,.8) 0%,rgba(255,140,0,0) 2%),radial-gradient(circle at 50% 50%,rgba(255,0,128,.8) 0%,rgba(255,0,128,0) 2%);opacity:0;animation:_ngcontent-%COMP%_sparkle-effect 4s ease-in-out infinite;pointer-events:none}@keyframes _ngcontent-%COMP%_sparkle-effect{0%{opacity:0}50%{opacity:1}to{opacity:0}}@keyframes _ngcontent-%COMP%_rotate-gradient{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-text[_ngcontent-%COMP%], .dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-time[_ngcontent-%COMP%]{color:#ffffffe6!important}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-message-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-message-preview[_ngcontent-%COMP%]{background-color:#00000080!important;color:#ffffffe6!important;border-left:2px solid rgba(255,140,0,.5)!important}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-sender[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-notification-sender[_ngcontent-%COMP%]{color:#ff8c00!important;font-weight:600}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(255,140,0,.2),rgba(255,94,98,.2));color:#ff8c00;border:1px solid rgba(255,140,0,.4)}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .futuristic-read-button[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(255,140,0,.3),rgba(255,94,98,.3));box-shadow:0 0 15px #ff8c0080}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%]{color:#ffffffb3;background-color:#ff8c001a;border:1px solid rgba(255,140,0,.3)}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-action-button[_ngcontent-%COMP%]:hover{background-color:#ff8c0033;color:#ff8c00;box-shadow:0 0 15px #ff8c0066}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]{background-color:#ff8c001a;color:#ff8c00;border:1px solid rgba(255,140,0,.3)}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-delete-button[_ngcontent-%COMP%]:hover{background-color:#ff8c0033;color:#ff8c00;box-shadow:0 0 8px #ff8c0066}.notification-separator-dot[_ngcontent-%COMP%]{width:4px;height:4px;border-radius:50%;background-color:#00f7ff99;margin:0 8px;box-shadow:0 0 5px #00f7ff66;animation:_ngcontent-%COMP%_dot-pulse 2s infinite alternate;transition:all .5s ease}.notification-separator-dot.fade-out[_ngcontent-%COMP%]{opacity:0;transform:scale(0);width:0;margin:0}.dark[_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-separator-dot[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-notification-selected[_ngcontent-%COMP%]   .notification-separator-dot[_ngcontent-%COMP%]{background-color:#00f7ffcc;box-shadow:0 0 8px #00f7ff99;animation:_ngcontent-%COMP%_dot-pulse-selected 1.5s infinite alternate}@keyframes _ngcontent-%COMP%_dot-pulse-selected{0%{opacity:.6;transform:scale(1)}to{opacity:1;transform:scale(1.5)}}@keyframes _ngcontent-%COMP%_dot-pulse{0%{opacity:.4;transform:scale(.8)}to{opacity:1;transform:scale(1.2)}}.notification-checkbox[_ngcontent-%COMP%]{position:absolute;top:10px;left:10px;z-index:10}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1)}50%{transform:scale(1.1)}to{transform:scale(1)}}.futuristic-cancel-button[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.5rem 1rem;background-color:#96969633;color:#6d6870;border:none;border-radius:var(--border-radius-md);font-size:.875rem;font-weight:500;cursor:pointer;transition:all .3s ease;box-shadow:0 0 8px #96969633}.futuristic-cancel-button[_ngcontent-%COMP%]:hover{background-color:#9696964d;box-shadow:0 0 12px #9696964d;transform:translateY(-2px)}.dark[_nghost-%COMP%]   .futuristic-cancel-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-cancel-button[_ngcontent-%COMP%]{background-color:#64646433;color:#e0e0e0;box-shadow:0 0 8px #64646433}\"]\n      });\n    }\n  }\n  return NotificationListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}