{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * No fragment cycles\n *\n * The graph of fragment spreads must not form any cycles including spreading itself.\n * Otherwise an operation could infinitely spread or infinitely execute on cycles in the underlying data.\n *\n * See https://spec.graphql.org/draft/#sec-Fragment-spreads-must-not-form-cycles\n */\nexport function NoFragmentCyclesRule(context) {\n  // Tracks already visited fragments to maintain O(N) and to ensure that cycles\n  // are not redundantly reported.\n  const visitedFrags = Object.create(null); // Array of AST nodes used to produce meaningful errors\n\n  const spreadPath = []; // Position in the spread path\n\n  const spreadPathIndexByName = Object.create(null);\n  return {\n    OperationDefinition: () => false,\n    FragmentDefinition(node) {\n      detectCycleRecursive(node);\n      return false;\n    }\n  }; // This does a straight-forward DFS to find cycles.\n  // It does not terminate when a cycle was found but continues to explore\n  // the graph to find all possible cycles.\n\n  function detectCycleRecursive(fragment) {\n    if (visitedFrags[fragment.name.value]) {\n      return;\n    }\n    const fragmentName = fragment.name.value;\n    visitedFrags[fragmentName] = true;\n    const spreadNodes = context.getFragmentSpreads(fragment.selectionSet);\n    if (spreadNodes.length === 0) {\n      return;\n    }\n    spreadPathIndexByName[fragmentName] = spreadPath.length;\n    for (const spreadNode of spreadNodes) {\n      const spreadName = spreadNode.name.value;\n      const cycleIndex = spreadPathIndexByName[spreadName];\n      spreadPath.push(spreadNode);\n      if (cycleIndex === undefined) {\n        const spreadFragment = context.getFragment(spreadName);\n        if (spreadFragment) {\n          detectCycleRecursive(spreadFragment);\n        }\n      } else {\n        const cyclePath = spreadPath.slice(cycleIndex);\n        const viaPath = cyclePath.slice(0, -1).map(s => '\"' + s.name.value + '\"').join(', ');\n        context.reportError(new GraphQLError(`Cannot spread fragment \"${spreadName}\" within itself` + (viaPath !== '' ? ` via ${viaPath}.` : '.'), {\n          nodes: cyclePath\n        }));\n      }\n      spreadPath.pop();\n    }\n    spreadPathIndexByName[fragmentName] = undefined;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}