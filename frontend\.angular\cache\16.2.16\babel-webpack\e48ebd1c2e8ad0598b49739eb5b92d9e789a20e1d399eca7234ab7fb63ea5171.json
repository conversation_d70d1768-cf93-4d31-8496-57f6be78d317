{"ast": null, "code": "import { inspect } from '../../jsutils/inspect.mjs';\nimport { keyMap } from '../../jsutils/keyMap.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { print } from '../../language/printer.mjs';\nimport { isRequiredArgument, isType } from '../../type/definition.mjs';\nimport { specifiedDirectives } from '../../type/directives.mjs';\n\n/**\n * Provided required arguments\n *\n * A field or directive is only valid if all required (non-null without a\n * default value) field arguments have been provided.\n */\nexport function ProvidedRequiredArgumentsRule(context) {\n  return {\n    // eslint-disable-next-line new-cap\n    ...ProvidedRequiredArgumentsOnDirectivesRule(context),\n    Field: {\n      // Validate on leave to allow for deeper errors to appear first.\n      leave(fieldNode) {\n        var _fieldNode$arguments;\n        const fieldDef = context.getFieldDef();\n        if (!fieldDef) {\n          return false;\n        }\n        const providedArgs = new Set(\n        // FIXME: https://github.com/graphql/graphql-js/issues/2203\n        /* c8 ignore next */\n        (_fieldNode$arguments = fieldNode.arguments) === null || _fieldNode$arguments === void 0 ? void 0 : _fieldNode$arguments.map(arg => arg.name.value));\n        for (const argDef of fieldDef.args) {\n          if (!providedArgs.has(argDef.name) && isRequiredArgument(argDef)) {\n            const argTypeStr = inspect(argDef.type);\n            context.reportError(new GraphQLError(`Field \"${fieldDef.name}\" argument \"${argDef.name}\" of type \"${argTypeStr}\" is required, but it was not provided.`, {\n              nodes: fieldNode\n            }));\n          }\n        }\n      }\n    }\n  };\n}\n/**\n * @internal\n */\n\nexport function ProvidedRequiredArgumentsOnDirectivesRule(context) {\n  var _schema$getDirectives;\n  const requiredArgsMap = Object.create(null);\n  const schema = context.getSchema();\n  const definedDirectives = (_schema$getDirectives = schema === null || schema === void 0 ? void 0 : schema.getDirectives()) !== null && _schema$getDirectives !== void 0 ? _schema$getDirectives : specifiedDirectives;\n  for (const directive of definedDirectives) {\n    requiredArgsMap[directive.name] = keyMap(directive.args.filter(isRequiredArgument), arg => arg.name);\n  }\n  const astDefinitions = context.getDocument().definitions;\n  for (const def of astDefinitions) {\n    if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      var _def$arguments;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n      const argNodes = (_def$arguments = def.arguments) !== null && _def$arguments !== void 0 ? _def$arguments : [];\n      requiredArgsMap[def.name.value] = keyMap(argNodes.filter(isRequiredArgumentNode), arg => arg.name.value);\n    }\n  }\n  return {\n    Directive: {\n      // Validate on leave to allow for deeper errors to appear first.\n      leave(directiveNode) {\n        const directiveName = directiveNode.name.value;\n        const requiredArgs = requiredArgsMap[directiveName];\n        if (requiredArgs) {\n          var _directiveNode$argume;\n\n          // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n          /* c8 ignore next */\n          const argNodes = (_directiveNode$argume = directiveNode.arguments) !== null && _directiveNode$argume !== void 0 ? _directiveNode$argume : [];\n          const argNodeMap = new Set(argNodes.map(arg => arg.name.value));\n          for (const [argName, argDef] of Object.entries(requiredArgs)) {\n            if (!argNodeMap.has(argName)) {\n              const argType = isType(argDef.type) ? inspect(argDef.type) : print(argDef.type);\n              context.reportError(new GraphQLError(`Directive \"@${directiveName}\" argument \"${argName}\" of type \"${argType}\" is required, but it was not provided.`, {\n                nodes: directiveNode\n              }));\n            }\n          }\n        }\n      }\n    }\n  };\n}\nfunction isRequiredArgumentNode(arg) {\n  return arg.type.kind === Kind.NON_NULL_TYPE && arg.defaultValue == null;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}