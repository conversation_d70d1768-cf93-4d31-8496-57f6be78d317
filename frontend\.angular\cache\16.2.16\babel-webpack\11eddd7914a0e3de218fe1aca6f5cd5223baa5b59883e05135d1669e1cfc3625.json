{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { collectFields } from '../../execution/collectFields.mjs';\n\n/**\n * Subscriptions must only include a non-introspection field.\n *\n * A GraphQL subscription is valid only if it contains a single root field and\n * that root field is not an introspection field.\n *\n * See https://spec.graphql.org/draft/#sec-Single-root-field\n */\nexport function SingleFieldSubscriptionsRule(context) {\n  return {\n    OperationDefinition(node) {\n      if (node.operation === 'subscription') {\n        const schema = context.getSchema();\n        const subscriptionType = schema.getSubscriptionType();\n        if (subscriptionType) {\n          const operationName = node.name ? node.name.value : null;\n          const variableValues = Object.create(null);\n          const document = context.getDocument();\n          const fragments = Object.create(null);\n          for (const definition of document.definitions) {\n            if (definition.kind === Kind.FRAGMENT_DEFINITION) {\n              fragments[definition.name.value] = definition;\n            }\n          }\n          const fields = collectFields(schema, fragments, variableValues, subscriptionType, node.selectionSet);\n          if (fields.size > 1) {\n            const fieldSelectionLists = [...fields.values()];\n            const extraFieldSelectionLists = fieldSelectionLists.slice(1);\n            const extraFieldSelections = extraFieldSelectionLists.flat();\n            context.reportError(new GraphQLError(operationName != null ? `Subscription \"${operationName}\" must select only one top level field.` : 'Anonymous Subscription must select only one top level field.', {\n              nodes: extraFieldSelections\n            }));\n          }\n          for (const fieldNodes of fields.values()) {\n            const field = fieldNodes[0];\n            const fieldName = field.name.value;\n            if (fieldName.startsWith('__')) {\n              context.reportError(new GraphQLError(operationName != null ? `Subscription \"${operationName}\" must not select an introspection top level field.` : 'Anonymous Subscription must not select an introspection top level field.', {\n                nodes: fieldNodes\n              }));\n            }\n          }\n        }\n      }\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}