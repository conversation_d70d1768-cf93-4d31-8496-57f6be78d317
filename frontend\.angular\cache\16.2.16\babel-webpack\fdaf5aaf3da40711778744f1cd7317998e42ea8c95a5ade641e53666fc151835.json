{"ast": null, "code": "import { didYouMean } from '../jsutils/didYouMean.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { isIterableObject } from '../jsutils/isIterableObject.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { addPath, pathToArray } from '../jsutils/Path.mjs';\nimport { printPathArray } from '../jsutils/printPathArray.mjs';\nimport { suggestionList } from '../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { isInputObjectType, isLeafType, isListType, isNonNullType } from '../type/definition.mjs';\n\n/**\n * Coerces a JavaScript value given a GraphQL Input Type.\n */\nexport function coerceInputValue(inputValue, type, onError = defaultOnError) {\n  return coerceInputValueImpl(inputValue, type, onError, undefined);\n}\nfunction defaultOnError(path, invalidValue, error) {\n  let errorPrefix = 'Invalid value ' + inspect(invalidValue);\n  if (path.length > 0) {\n    errorPrefix += ` at \"value${printPathArray(path)}\"`;\n  }\n  error.message = errorPrefix + ': ' + error.message;\n  throw error;\n}\nfunction coerceInputValueImpl(inputValue, type, onError, path) {\n  if (isNonNullType(type)) {\n    if (inputValue != null) {\n      return coerceInputValueImpl(inputValue, type.ofType, onError, path);\n    }\n    onError(pathToArray(path), inputValue, new GraphQLError(`Expected non-nullable type \"${inspect(type)}\" not to be null.`));\n    return;\n  }\n  if (inputValue == null) {\n    // Explicitly return the value null.\n    return null;\n  }\n  if (isListType(type)) {\n    const itemType = type.ofType;\n    if (isIterableObject(inputValue)) {\n      return Array.from(inputValue, (itemValue, index) => {\n        const itemPath = addPath(path, index, undefined);\n        return coerceInputValueImpl(itemValue, itemType, onError, itemPath);\n      });\n    } // Lists accept a non-list value as a list of one.\n\n    return [coerceInputValueImpl(inputValue, itemType, onError, path)];\n  }\n  if (isInputObjectType(type)) {\n    if (!isObjectLike(inputValue)) {\n      onError(pathToArray(path), inputValue, new GraphQLError(`Expected type \"${type.name}\" to be an object.`));\n      return;\n    }\n    const coercedValue = {};\n    const fieldDefs = type.getFields();\n    for (const field of Object.values(fieldDefs)) {\n      const fieldValue = inputValue[field.name];\n      if (fieldValue === undefined) {\n        if (field.defaultValue !== undefined) {\n          coercedValue[field.name] = field.defaultValue;\n        } else if (isNonNullType(field.type)) {\n          const typeStr = inspect(field.type);\n          onError(pathToArray(path), inputValue, new GraphQLError(`Field \"${field.name}\" of required type \"${typeStr}\" was not provided.`));\n        }\n        continue;\n      }\n      coercedValue[field.name] = coerceInputValueImpl(fieldValue, field.type, onError, addPath(path, field.name, type.name));\n    } // Ensure every provided field is defined.\n\n    for (const fieldName of Object.keys(inputValue)) {\n      if (!fieldDefs[fieldName]) {\n        const suggestions = suggestionList(fieldName, Object.keys(type.getFields()));\n        onError(pathToArray(path), inputValue, new GraphQLError(`Field \"${fieldName}\" is not defined by type \"${type.name}\".` + didYouMean(suggestions)));\n      }\n    }\n    return coercedValue;\n  }\n  if (isLeafType(type)) {\n    let parseResult; // Scalars and Enums determine if a input value is valid via parseValue(),\n    // which can throw to indicate failure. If it throws, maintain a reference\n    // to the original error.\n\n    try {\n      parseResult = type.parseValue(inputValue);\n    } catch (error) {\n      if (error instanceof GraphQLError) {\n        onError(pathToArray(path), inputValue, error);\n      } else {\n        onError(pathToArray(path), inputValue, new GraphQLError(`Expected type \"${type.name}\". ` + error.message, {\n          originalError: error\n        }));\n      }\n      return;\n    }\n    if (parseResult === undefined) {\n      onError(pathToArray(path), inputValue, new GraphQLError(`Expected type \"${type.name}\".`));\n    }\n    return parseResult;\n  }\n  /* c8 ignore next 3 */\n  // Not reachable, all possible types have been considered.\n\n  false || invariant(false, 'Unexpected input type: ' + inspect(type));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}