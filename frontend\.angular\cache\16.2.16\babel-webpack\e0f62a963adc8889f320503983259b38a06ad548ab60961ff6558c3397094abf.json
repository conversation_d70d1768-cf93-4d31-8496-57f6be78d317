{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { keyMap } from '../jsutils/keyMap.mjs';\nimport { mapValue } from '../jsutils/mapValue.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { isTypeDefinitionNode, isTypeExtensionNode } from '../language/predicates.mjs';\nimport { GraphQLEnumType, GraphQLInputObjectType, GraphQLInterfaceType, GraphQLList, GraphQLNonNull, GraphQLObjectType, GraphQLScalarType, GraphQLUnionType, isEnumType, isInputObjectType, isInterfaceType, isListType, isNonNullType, isObjectType, isScalarType, isUnionType } from '../type/definition.mjs';\nimport { GraphQLDeprecatedDirective, GraphQLDirective, GraphQLSpecifiedByDirective } from '../type/directives.mjs';\nimport { introspectionTypes, isIntrospectionType } from '../type/introspection.mjs';\nimport { isSpecifiedScalarType, specifiedScalarTypes } from '../type/scalars.mjs';\nimport { assertSchema, GraphQLSchema } from '../type/schema.mjs';\nimport { assertValidSDLExtension } from '../validation/validate.mjs';\nimport { getDirectiveValues } from '../execution/values.mjs';\nimport { valueFromAST } from './valueFromAST.mjs';\n\n/**\n * Produces a new schema given an existing schema and a document which may\n * contain GraphQL type extensions and definitions. The original schema will\n * remain unaltered.\n *\n * Because a schema represents a graph of references, a schema cannot be\n * extended without effectively making an entire copy. We do not know until it's\n * too late if subgraphs remain unchanged.\n *\n * This algorithm copies the provided schema, applying extensions while\n * producing the copy. The original schema remains unaltered.\n */\nexport function extendSchema(schema, documentAST, options) {\n  assertSchema(schema);\n  documentAST != null && documentAST.kind === Kind.DOCUMENT || devAssert(false, 'Must provide valid Document AST.');\n  if ((options === null || options === void 0 ? void 0 : options.assumeValid) !== true && (options === null || options === void 0 ? void 0 : options.assumeValidSDL) !== true) {\n    assertValidSDLExtension(documentAST, schema);\n  }\n  const schemaConfig = schema.toConfig();\n  const extendedConfig = extendSchemaImpl(schemaConfig, documentAST, options);\n  return schemaConfig === extendedConfig ? schema : new GraphQLSchema(extendedConfig);\n}\n/**\n * @internal\n */\n\nexport function extendSchemaImpl(schemaConfig, documentAST, options) {\n  var _schemaDef, _schemaDef$descriptio, _schemaDef2, _options$assumeValid;\n\n  // Collect the type definitions and extensions found in the document.\n  const typeDefs = [];\n  const typeExtensionsMap = Object.create(null); // New directives and types are separate because a directives and types can\n  // have the same name. For example, a type named \"skip\".\n\n  const directiveDefs = [];\n  let schemaDef; // Schema extensions are collected which may add additional operation types.\n\n  const schemaExtensions = [];\n  for (const def of documentAST.definitions) {\n    if (def.kind === Kind.SCHEMA_DEFINITION) {\n      schemaDef = def;\n    } else if (def.kind === Kind.SCHEMA_EXTENSION) {\n      schemaExtensions.push(def);\n    } else if (isTypeDefinitionNode(def)) {\n      typeDefs.push(def);\n    } else if (isTypeExtensionNode(def)) {\n      const extendedTypeName = def.name.value;\n      const existingTypeExtensions = typeExtensionsMap[extendedTypeName];\n      typeExtensionsMap[extendedTypeName] = existingTypeExtensions ? existingTypeExtensions.concat([def]) : [def];\n    } else if (def.kind === Kind.DIRECTIVE_DEFINITION) {\n      directiveDefs.push(def);\n    }\n  } // If this document contains no new types, extensions, or directives then\n  // return the same unmodified GraphQLSchema instance.\n\n  if (Object.keys(typeExtensionsMap).length === 0 && typeDefs.length === 0 && directiveDefs.length === 0 && schemaExtensions.length === 0 && schemaDef == null) {\n    return schemaConfig;\n  }\n  const typeMap = Object.create(null);\n  for (const existingType of schemaConfig.types) {\n    typeMap[existingType.name] = extendNamedType(existingType);\n  }\n  for (const typeNode of typeDefs) {\n    var _stdTypeMap$name;\n    const name = typeNode.name.value;\n    typeMap[name] = (_stdTypeMap$name = stdTypeMap[name]) !== null && _stdTypeMap$name !== void 0 ? _stdTypeMap$name : buildType(typeNode);\n  }\n  const operationTypes = {\n    // Get the extended root operation types.\n    query: schemaConfig.query && replaceNamedType(schemaConfig.query),\n    mutation: schemaConfig.mutation && replaceNamedType(schemaConfig.mutation),\n    subscription: schemaConfig.subscription && replaceNamedType(schemaConfig.subscription),\n    // Then, incorporate schema definition and all schema extensions.\n    ...(schemaDef && getOperationTypes([schemaDef])),\n    ...getOperationTypes(schemaExtensions)\n  }; // Then produce and return a Schema config with these types.\n\n  return {\n    description: (_schemaDef = schemaDef) === null || _schemaDef === void 0 ? void 0 : (_schemaDef$descriptio = _schemaDef.description) === null || _schemaDef$descriptio === void 0 ? void 0 : _schemaDef$descriptio.value,\n    ...operationTypes,\n    types: Object.values(typeMap),\n    directives: [...schemaConfig.directives.map(replaceDirective), ...directiveDefs.map(buildDirective)],\n    extensions: Object.create(null),\n    astNode: (_schemaDef2 = schemaDef) !== null && _schemaDef2 !== void 0 ? _schemaDef2 : schemaConfig.astNode,\n    extensionASTNodes: schemaConfig.extensionASTNodes.concat(schemaExtensions),\n    assumeValid: (_options$assumeValid = options === null || options === void 0 ? void 0 : options.assumeValid) !== null && _options$assumeValid !== void 0 ? _options$assumeValid : false\n  }; // Below are functions used for producing this schema that have closed over\n  // this scope and have access to the schema, cache, and newly defined types.\n\n  function replaceType(type) {\n    if (isListType(type)) {\n      // @ts-expect-error\n      return new GraphQLList(replaceType(type.ofType));\n    }\n    if (isNonNullType(type)) {\n      // @ts-expect-error\n      return new GraphQLNonNull(replaceType(type.ofType));\n    } // @ts-expect-error FIXME\n\n    return replaceNamedType(type);\n  }\n  function replaceNamedType(type) {\n    // Note: While this could make early assertions to get the correctly\n    // typed values, that would throw immediately while type system\n    // validation with validateSchema() will produce more actionable results.\n    return typeMap[type.name];\n  }\n  function replaceDirective(directive) {\n    const config = directive.toConfig();\n    return new GraphQLDirective({\n      ...config,\n      args: mapValue(config.args, extendArg)\n    });\n  }\n  function extendNamedType(type) {\n    if (isIntrospectionType(type) || isSpecifiedScalarType(type)) {\n      // Builtin types are not extended.\n      return type;\n    }\n    if (isScalarType(type)) {\n      return extendScalarType(type);\n    }\n    if (isObjectType(type)) {\n      return extendObjectType(type);\n    }\n    if (isInterfaceType(type)) {\n      return extendInterfaceType(type);\n    }\n    if (isUnionType(type)) {\n      return extendUnionType(type);\n    }\n    if (isEnumType(type)) {\n      return extendEnumType(type);\n    }\n    if (isInputObjectType(type)) {\n      return extendInputObjectType(type);\n    }\n    /* c8 ignore next 3 */\n    // Not reachable, all possible type definition nodes have been considered.\n\n    false || invariant(false, 'Unexpected type: ' + inspect(type));\n  }\n  function extendInputObjectType(type) {\n    var _typeExtensionsMap$co;\n    const config = type.toConfig();\n    const extensions = (_typeExtensionsMap$co = typeExtensionsMap[config.name]) !== null && _typeExtensionsMap$co !== void 0 ? _typeExtensionsMap$co : [];\n    return new GraphQLInputObjectType({\n      ...config,\n      fields: () => ({\n        ...mapValue(config.fields, field => ({\n          ...field,\n          type: replaceType(field.type)\n        })),\n        ...buildInputFieldMap(extensions)\n      }),\n      extensionASTNodes: config.extensionASTNodes.concat(extensions)\n    });\n  }\n  function extendEnumType(type) {\n    var _typeExtensionsMap$ty;\n    const config = type.toConfig();\n    const extensions = (_typeExtensionsMap$ty = typeExtensionsMap[type.name]) !== null && _typeExtensionsMap$ty !== void 0 ? _typeExtensionsMap$ty : [];\n    return new GraphQLEnumType({\n      ...config,\n      values: {\n        ...config.values,\n        ...buildEnumValueMap(extensions)\n      },\n      extensionASTNodes: config.extensionASTNodes.concat(extensions)\n    });\n  }\n  function extendScalarType(type) {\n    var _typeExtensionsMap$co2;\n    const config = type.toConfig();\n    const extensions = (_typeExtensionsMap$co2 = typeExtensionsMap[config.name]) !== null && _typeExtensionsMap$co2 !== void 0 ? _typeExtensionsMap$co2 : [];\n    let specifiedByURL = config.specifiedByURL;\n    for (const extensionNode of extensions) {\n      var _getSpecifiedByURL;\n      specifiedByURL = (_getSpecifiedByURL = getSpecifiedByURL(extensionNode)) !== null && _getSpecifiedByURL !== void 0 ? _getSpecifiedByURL : specifiedByURL;\n    }\n    return new GraphQLScalarType({\n      ...config,\n      specifiedByURL,\n      extensionASTNodes: config.extensionASTNodes.concat(extensions)\n    });\n  }\n  function extendObjectType(type) {\n    var _typeExtensionsMap$co3;\n    const config = type.toConfig();\n    const extensions = (_typeExtensionsMap$co3 = typeExtensionsMap[config.name]) !== null && _typeExtensionsMap$co3 !== void 0 ? _typeExtensionsMap$co3 : [];\n    return new GraphQLObjectType({\n      ...config,\n      interfaces: () => [...type.getInterfaces().map(replaceNamedType), ...buildInterfaces(extensions)],\n      fields: () => ({\n        ...mapValue(config.fields, extendField),\n        ...buildFieldMap(extensions)\n      }),\n      extensionASTNodes: config.extensionASTNodes.concat(extensions)\n    });\n  }\n  function extendInterfaceType(type) {\n    var _typeExtensionsMap$co4;\n    const config = type.toConfig();\n    const extensions = (_typeExtensionsMap$co4 = typeExtensionsMap[config.name]) !== null && _typeExtensionsMap$co4 !== void 0 ? _typeExtensionsMap$co4 : [];\n    return new GraphQLInterfaceType({\n      ...config,\n      interfaces: () => [...type.getInterfaces().map(replaceNamedType), ...buildInterfaces(extensions)],\n      fields: () => ({\n        ...mapValue(config.fields, extendField),\n        ...buildFieldMap(extensions)\n      }),\n      extensionASTNodes: config.extensionASTNodes.concat(extensions)\n    });\n  }\n  function extendUnionType(type) {\n    var _typeExtensionsMap$co5;\n    const config = type.toConfig();\n    const extensions = (_typeExtensionsMap$co5 = typeExtensionsMap[config.name]) !== null && _typeExtensionsMap$co5 !== void 0 ? _typeExtensionsMap$co5 : [];\n    return new GraphQLUnionType({\n      ...config,\n      types: () => [...type.getTypes().map(replaceNamedType), ...buildUnionTypes(extensions)],\n      extensionASTNodes: config.extensionASTNodes.concat(extensions)\n    });\n  }\n  function extendField(field) {\n    return {\n      ...field,\n      type: replaceType(field.type),\n      args: field.args && mapValue(field.args, extendArg)\n    };\n  }\n  function extendArg(arg) {\n    return {\n      ...arg,\n      type: replaceType(arg.type)\n    };\n  }\n  function getOperationTypes(nodes) {\n    const opTypes = {};\n    for (const node of nodes) {\n      var _node$operationTypes;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      const operationTypesNodes = /* c8 ignore next */\n      (_node$operationTypes = node.operationTypes) !== null && _node$operationTypes !== void 0 ? _node$operationTypes : [];\n      for (const operationType of operationTypesNodes) {\n        // Note: While this could make early assertions to get the correctly\n        // typed values below, that would throw immediately while type system\n        // validation with validateSchema() will produce more actionable results.\n        // @ts-expect-error\n        opTypes[operationType.operation] = getNamedType(operationType.type);\n      }\n    }\n    return opTypes;\n  }\n  function getNamedType(node) {\n    var _stdTypeMap$name2;\n    const name = node.name.value;\n    const type = (_stdTypeMap$name2 = stdTypeMap[name]) !== null && _stdTypeMap$name2 !== void 0 ? _stdTypeMap$name2 : typeMap[name];\n    if (type === undefined) {\n      throw new Error(`Unknown type: \"${name}\".`);\n    }\n    return type;\n  }\n  function getWrappedType(node) {\n    if (node.kind === Kind.LIST_TYPE) {\n      return new GraphQLList(getWrappedType(node.type));\n    }\n    if (node.kind === Kind.NON_NULL_TYPE) {\n      return new GraphQLNonNull(getWrappedType(node.type));\n    }\n    return getNamedType(node);\n  }\n  function buildDirective(node) {\n    var _node$description;\n    return new GraphQLDirective({\n      name: node.name.value,\n      description: (_node$description = node.description) === null || _node$description === void 0 ? void 0 : _node$description.value,\n      // @ts-expect-error\n      locations: node.locations.map(({\n        value\n      }) => value),\n      isRepeatable: node.repeatable,\n      args: buildArgumentMap(node.arguments),\n      astNode: node\n    });\n  }\n  function buildFieldMap(nodes) {\n    const fieldConfigMap = Object.create(null);\n    for (const node of nodes) {\n      var _node$fields;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      const nodeFields = /* c8 ignore next */\n      (_node$fields = node.fields) !== null && _node$fields !== void 0 ? _node$fields : [];\n      for (const field of nodeFields) {\n        var _field$description;\n        fieldConfigMap[field.name.value] = {\n          // Note: While this could make assertions to get the correctly typed\n          // value, that would throw immediately while type system validation\n          // with validateSchema() will produce more actionable results.\n          type: getWrappedType(field.type),\n          description: (_field$description = field.description) === null || _field$description === void 0 ? void 0 : _field$description.value,\n          args: buildArgumentMap(field.arguments),\n          deprecationReason: getDeprecationReason(field),\n          astNode: field\n        };\n      }\n    }\n    return fieldConfigMap;\n  }\n  function buildArgumentMap(args) {\n    // FIXME: https://github.com/graphql/graphql-js/issues/2203\n    const argsNodes = /* c8 ignore next */\n    args !== null && args !== void 0 ? args : [];\n    const argConfigMap = Object.create(null);\n    for (const arg of argsNodes) {\n      var _arg$description;\n\n      // Note: While this could make assertions to get the correctly typed\n      // value, that would throw immediately while type system validation\n      // with validateSchema() will produce more actionable results.\n      const type = getWrappedType(arg.type);\n      argConfigMap[arg.name.value] = {\n        type,\n        description: (_arg$description = arg.description) === null || _arg$description === void 0 ? void 0 : _arg$description.value,\n        defaultValue: valueFromAST(arg.defaultValue, type),\n        deprecationReason: getDeprecationReason(arg),\n        astNode: arg\n      };\n    }\n    return argConfigMap;\n  }\n  function buildInputFieldMap(nodes) {\n    const inputFieldMap = Object.create(null);\n    for (const node of nodes) {\n      var _node$fields2;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      const fieldsNodes = /* c8 ignore next */\n      (_node$fields2 = node.fields) !== null && _node$fields2 !== void 0 ? _node$fields2 : [];\n      for (const field of fieldsNodes) {\n        var _field$description2;\n\n        // Note: While this could make assertions to get the correctly typed\n        // value, that would throw immediately while type system validation\n        // with validateSchema() will produce more actionable results.\n        const type = getWrappedType(field.type);\n        inputFieldMap[field.name.value] = {\n          type,\n          description: (_field$description2 = field.description) === null || _field$description2 === void 0 ? void 0 : _field$description2.value,\n          defaultValue: valueFromAST(field.defaultValue, type),\n          deprecationReason: getDeprecationReason(field),\n          astNode: field\n        };\n      }\n    }\n    return inputFieldMap;\n  }\n  function buildEnumValueMap(nodes) {\n    const enumValueMap = Object.create(null);\n    for (const node of nodes) {\n      var _node$values;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n      const valuesNodes = /* c8 ignore next */\n      (_node$values = node.values) !== null && _node$values !== void 0 ? _node$values : [];\n      for (const value of valuesNodes) {\n        var _value$description;\n        enumValueMap[value.name.value] = {\n          description: (_value$description = value.description) === null || _value$description === void 0 ? void 0 : _value$description.value,\n          deprecationReason: getDeprecationReason(value),\n          astNode: value\n        };\n      }\n    }\n    return enumValueMap;\n  }\n  function buildInterfaces(nodes) {\n    // Note: While this could make assertions to get the correctly typed\n    // values below, that would throw immediately while type system\n    // validation with validateSchema() will produce more actionable results.\n    // @ts-expect-error\n    return nodes.flatMap(\n    // FIXME: https://github.com/graphql/graphql-js/issues/2203\n    node => {\n      var _node$interfaces$map, _node$interfaces;\n      return (/* c8 ignore next */\n        (_node$interfaces$map = (_node$interfaces = node.interfaces) === null || _node$interfaces === void 0 ? void 0 : _node$interfaces.map(getNamedType)) !== null && _node$interfaces$map !== void 0 ? _node$interfaces$map : []\n      );\n    });\n  }\n  function buildUnionTypes(nodes) {\n    // Note: While this could make assertions to get the correctly typed\n    // values below, that would throw immediately while type system\n    // validation with validateSchema() will produce more actionable results.\n    // @ts-expect-error\n    return nodes.flatMap(\n    // FIXME: https://github.com/graphql/graphql-js/issues/2203\n    node => {\n      var _node$types$map, _node$types;\n      return (/* c8 ignore next */\n        (_node$types$map = (_node$types = node.types) === null || _node$types === void 0 ? void 0 : _node$types.map(getNamedType)) !== null && _node$types$map !== void 0 ? _node$types$map : []\n      );\n    });\n  }\n  function buildType(astNode) {\n    var _typeExtensionsMap$na;\n    const name = astNode.name.value;\n    const extensionASTNodes = (_typeExtensionsMap$na = typeExtensionsMap[name]) !== null && _typeExtensionsMap$na !== void 0 ? _typeExtensionsMap$na : [];\n    switch (astNode.kind) {\n      case Kind.OBJECT_TYPE_DEFINITION:\n        {\n          var _astNode$description;\n          const allNodes = [astNode, ...extensionASTNodes];\n          return new GraphQLObjectType({\n            name,\n            description: (_astNode$description = astNode.description) === null || _astNode$description === void 0 ? void 0 : _astNode$description.value,\n            interfaces: () => buildInterfaces(allNodes),\n            fields: () => buildFieldMap(allNodes),\n            astNode,\n            extensionASTNodes\n          });\n        }\n      case Kind.INTERFACE_TYPE_DEFINITION:\n        {\n          var _astNode$description2;\n          const allNodes = [astNode, ...extensionASTNodes];\n          return new GraphQLInterfaceType({\n            name,\n            description: (_astNode$description2 = astNode.description) === null || _astNode$description2 === void 0 ? void 0 : _astNode$description2.value,\n            interfaces: () => buildInterfaces(allNodes),\n            fields: () => buildFieldMap(allNodes),\n            astNode,\n            extensionASTNodes\n          });\n        }\n      case Kind.ENUM_TYPE_DEFINITION:\n        {\n          var _astNode$description3;\n          const allNodes = [astNode, ...extensionASTNodes];\n          return new GraphQLEnumType({\n            name,\n            description: (_astNode$description3 = astNode.description) === null || _astNode$description3 === void 0 ? void 0 : _astNode$description3.value,\n            values: buildEnumValueMap(allNodes),\n            astNode,\n            extensionASTNodes\n          });\n        }\n      case Kind.UNION_TYPE_DEFINITION:\n        {\n          var _astNode$description4;\n          const allNodes = [astNode, ...extensionASTNodes];\n          return new GraphQLUnionType({\n            name,\n            description: (_astNode$description4 = astNode.description) === null || _astNode$description4 === void 0 ? void 0 : _astNode$description4.value,\n            types: () => buildUnionTypes(allNodes),\n            astNode,\n            extensionASTNodes\n          });\n        }\n      case Kind.SCALAR_TYPE_DEFINITION:\n        {\n          var _astNode$description5;\n          return new GraphQLScalarType({\n            name,\n            description: (_astNode$description5 = astNode.description) === null || _astNode$description5 === void 0 ? void 0 : _astNode$description5.value,\n            specifiedByURL: getSpecifiedByURL(astNode),\n            astNode,\n            extensionASTNodes\n          });\n        }\n      case Kind.INPUT_OBJECT_TYPE_DEFINITION:\n        {\n          var _astNode$description6;\n          const allNodes = [astNode, ...extensionASTNodes];\n          return new GraphQLInputObjectType({\n            name,\n            description: (_astNode$description6 = astNode.description) === null || _astNode$description6 === void 0 ? void 0 : _astNode$description6.value,\n            fields: () => buildInputFieldMap(allNodes),\n            astNode,\n            extensionASTNodes\n          });\n        }\n    }\n  }\n}\nconst stdTypeMap = keyMap([...specifiedScalarTypes, ...introspectionTypes], type => type.name);\n/**\n * Given a field or enum value node, returns the string value for the\n * deprecation reason.\n */\n\nfunction getDeprecationReason(node) {\n  const deprecated = getDirectiveValues(GraphQLDeprecatedDirective, node); // @ts-expect-error validated by `getDirectiveValues`\n\n  return deprecated === null || deprecated === void 0 ? void 0 : deprecated.reason;\n}\n/**\n * Given a scalar node, returns the string value for the specifiedByURL.\n */\n\nfunction getSpecifiedByURL(node) {\n  const specifiedBy = getDirectiveValues(GraphQLSpecifiedByDirective, node); // @ts-expect-error validated by `getDirectiveValues`\n\n  return specifiedBy === null || specifiedBy === void 0 ? void 0 : specifiedBy.url;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}