{"ast": null, "code": "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\nexport class Hour1To24Parser extends Parser {\n  priority = 70;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"k\":\n        return parseNumericPattern(numericPatterns.hour24h, dateString);\n      case \"ko\":\n        return match.ordinalNumber(dateString, {\n          unit: \"hour\"\n        });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n  validate(_date, value) {\n    return value >= 1 && value <= 24;\n  }\n  set(date, _flags, value) {\n    const hours = value <= 24 ? value % 24 : value;\n    date.setHours(hours, 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"a\", \"b\", \"h\", \"H\", \"K\", \"t\", \"T\"];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}