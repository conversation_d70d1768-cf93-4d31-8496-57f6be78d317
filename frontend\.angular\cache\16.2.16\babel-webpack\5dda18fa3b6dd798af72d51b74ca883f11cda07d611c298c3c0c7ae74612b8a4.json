{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/rendus.service\";\nimport * as i3 from \"@angular/common\";\nfunction EvaluationDetailsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EvaluationDetailsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 33)(2, \"span\", 41);\n    i0.ɵɵtext(3, \"Structure du code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 42);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 43);\n    i0.ɵɵelement(7, \"div\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.rendu.evaluation.scores.structure, \"/5\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.rendu.evaluation.scores.structure / 5 * 100, \"%\");\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 33)(2, \"span\", 41);\n    i0.ɵɵtext(3, \"Bonnes pratiques\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 42);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 43);\n    i0.ɵɵelement(7, \"div\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r4.rendu.evaluation.scores.pratiques, \"/5\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r4.rendu.evaluation.scores.pratiques / 5 * 100, \"%\");\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 33)(2, \"span\", 41);\n    i0.ɵɵtext(3, \"Fonctionnalit\\u00E9s\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 42);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 43);\n    i0.ɵɵelement(7, \"div\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r5.rendu.evaluation.scores.fonctionnalite, \"/5\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r5.rendu.evaluation.scores.fonctionnalite / 5 * 100, \"%\");\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 33)(2, \"span\", 41);\n    i0.ɵɵtext(3, \"Originalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 42);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 43);\n    i0.ɵɵelement(7, \"div\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r6.rendu.evaluation.scores.originalite, \"/5\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r6.rendu.evaluation.scores.originalite / 5 * 100, \"%\");\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"h3\", 48);\n    i0.ɵɵtext(2, \"Commentaires\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 40)(4, \"p\", 49);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r7.rendu.evaluation.commentaires);\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_41_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 52);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 53);\n    i0.ɵɵelement(2, \"path\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const fichier_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"href\", ctx_r10.getFileUrl(fichier_r11), i0.ɵɵsanitizeUrl)(\"download\", ctx_r10.getFileName(fichier_r11));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r10.getFileName(fichier_r11));\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"h3\", 48);\n    i0.ɵɵtext(2, \"Fichiers soumis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 50);\n    i0.ɵɵtemplate(4, EvaluationDetailsComponent_div_10_div_41_a_4_Template, 5, 3, \"a\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.rendu.fichiers);\n  }\n}\nfunction EvaluationDetailsComponent_div_10_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u00C9valu\\u00E9 le \", i0.ɵɵpipeBind2(2, 1, ctx_r9.rendu.evaluation.dateEvaluation, \"dd/MM/yyyy \\u00E0 HH:mm\"), \" \");\n  }\n}\nconst _c0 = function (a0, a1, a2, a3) {\n  return {\n    \"bg-green-600\": a0,\n    \"bg-blue-600\": a1,\n    \"bg-yellow-600\": a2,\n    \"bg-red-600\": a3\n  };\n};\nfunction EvaluationDetailsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"div\", 15)(3, \"div\")(4, \"h2\", 16);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 17);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 18)(10, \"span\", 19);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 20)(13, \"div\", 21);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 22)(16, \"p\", 23);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 24);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 25)(21, \"span\", 26);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 27)(24, \"h3\", 28);\n    i0.ɵɵtext(25, \"D\\u00E9tails des scores\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 29)(27, \"div\", 30);\n    i0.ɵɵtemplate(28, EvaluationDetailsComponent_div_10_div_28_Template, 8, 3, \"div\", 31);\n    i0.ɵɵtemplate(29, EvaluationDetailsComponent_div_10_div_29_Template, 8, 3, \"div\", 31);\n    i0.ɵɵtemplate(30, EvaluationDetailsComponent_div_10_div_30_Template, 8, 3, \"div\", 31);\n    i0.ɵɵtemplate(31, EvaluationDetailsComponent_div_10_div_31_Template, 8, 3, \"div\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 32)(33, \"div\", 33)(34, \"span\", 34);\n    i0.ɵɵtext(35, \"Score total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 35);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 36);\n    i0.ɵɵelement(39, \"div\", 37);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(40, EvaluationDetailsComponent_div_10_div_40_Template, 6, 1, \"div\", 38);\n    i0.ɵɵtemplate(41, EvaluationDetailsComponent_div_10_div_41_Template, 5, 1, \"div\", 38);\n    i0.ɵɵtemplate(42, EvaluationDetailsComponent_div_10_div_42_Template, 3, 4, \"div\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.rendu.projet == null ? null : ctx_r2.rendu.projet.titre);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Soumis le \", i0.ɵɵpipeBind2(8, 24, ctx_r2.rendu.dateSoumission, \"dd/MM/yyyy \\u00E0 HH:mm\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getScoreClass());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.getScoreTotal(), \"/\", ctx_r2.getScoreMaximum(), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.nom == null ? null : ctx_r2.rendu.etudiant.nom.charAt(0), \"\", ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.prenom == null ? null : ctx_r2.rendu.etudiant.prenom.charAt(0), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.nom, \" \", ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.prenom, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.email);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.rendu.etudiant == null ? null : ctx_r2.rendu.etudiant.groupe) || \"Groupe non sp\\u00E9cifi\\u00E9\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.evaluation == null ? null : ctx_r2.rendu.evaluation.scores);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.evaluation == null ? null : ctx_r2.rendu.evaluation.scores);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.evaluation == null ? null : ctx_r2.rendu.evaluation.scores);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.evaluation == null ? null : ctx_r2.rendu.evaluation.scores);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getScoreClass());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.getScoreTotal(), \"/\", ctx_r2.getScoreMaximum(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r2.getScorePercentage(), \"%\");\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(27, _c0, ctx_r2.getScorePercentage() >= 80, ctx_r2.getScorePercentage() >= 60 && ctx_r2.getScorePercentage() < 80, ctx_r2.getScorePercentage() >= 40 && ctx_r2.getScorePercentage() < 60, ctx_r2.getScorePercentage() < 40));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.evaluation == null ? null : ctx_r2.rendu.evaluation.commentaires);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.fichiers && ctx_r2.rendu.fichiers.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.rendu.evaluation == null ? null : ctx_r2.rendu.evaluation.dateEvaluation);\n  }\n}\nexport let EvaluationDetailsComponent = /*#__PURE__*/(() => {\n  class EvaluationDetailsComponent {\n    constructor(route, router, rendusService) {\n      this.route = route;\n      this.router = router;\n      this.rendusService = rendusService;\n      this.renduId = '';\n      this.rendu = null;\n      this.isLoading = true;\n      this.error = '';\n    }\n    ngOnInit() {\n      this.renduId = this.route.snapshot.paramMap.get('renduId') || '';\n      if (this.renduId) {\n        this.loadRendu();\n      } else {\n        this.error = 'ID de rendu manquant';\n        this.isLoading = false;\n      }\n    }\n    loadRendu() {\n      this.isLoading = true;\n      this.rendusService.getRenduById(this.renduId).subscribe({\n        next: data => {\n          this.rendu = data;\n          this.isLoading = false;\n        },\n        error: err => {\n          this.error = 'Erreur lors du chargement du rendu';\n          this.isLoading = false;\n          console.error(err);\n        }\n      });\n    }\n    getScoreTotal() {\n      if (!this.rendu?.evaluation?.scores) return 0;\n      const scores = this.rendu.evaluation.scores;\n      return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n    }\n    getScoreMaximum() {\n      return 20; // 4 critères x 5 points maximum\n    }\n\n    getScorePercentage() {\n      return this.getScoreTotal() / this.getScoreMaximum() * 100;\n    }\n    getScoreClass() {\n      const percentage = this.getScorePercentage();\n      if (percentage >= 80) return 'text-green-600';\n      if (percentage >= 60) return 'text-blue-600';\n      if (percentage >= 40) return 'text-yellow-600';\n      return 'text-red-600';\n    }\n    retourListe() {\n      this.router.navigate(['/admin/projects/rendus']);\n    }\n    // Méthodes pour gérer les fichiers\n    getFileUrl(filePath) {\n      if (!filePath) return '';\n      // Extraire uniquement le nom du fichier\n      let fileName = filePath;\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        fileName = parts[parts.length - 1];\n      }\n      // Utiliser la route spécifique pour le téléchargement\n      return `${environment.urlBackend}projets/telecharger/${fileName}`;\n    }\n    getFileName(filePath) {\n      if (!filePath) return 'Fichier';\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        return parts[parts.length - 1];\n      }\n      return filePath;\n    }\n    static {\n      this.ɵfac = function EvaluationDetailsComponent_Factory(t) {\n        return new (t || EvaluationDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.RendusService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EvaluationDetailsComponent,\n        selectors: [[\"app-evaluation-details\"]],\n        decls: 11,\n        vars: 3,\n        consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"p-4\", \"md:p-6\"], [1, \"max-w-4xl\", \"mx-auto\"], [1, \"flex\", \"items-center\", \"mb-6\"], [1, \"mr-4\", \"p-2\", \"rounded-full\", \"hover:bg-gray-200\", \"transition-colors\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"text-gray-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10 19l-7-7m0 0l7-7m-7 7h18\"], [1, \"text-2xl\", \"md:text-3xl\", \"font-bold\", \"text-[#4f5fad]\"], [\"class\", \"flex justify-center py-12\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-xl shadow-md overflow-hidden\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"py-12\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-t-2\", \"border-b-2\", \"border-[#4f5fad]\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-md\", \"overflow-hidden\"], [1, \"p-6\", \"border-b\", \"border-gray-200\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"justify-between\", \"items-start\", \"md:items-center\", \"mb-4\"], [1, \"text-xl\", \"font-bold\", \"text-gray-800\"], [1, \"text-sm\", \"text-gray-500\"], [1, \"mt-2\", \"md:mt-0\"], [1, \"text-2xl\", \"font-bold\", 3, \"ngClass\"], [1, \"flex\", \"items-center\", \"mb-4\"], [1, \"h-10\", \"w-10\", \"rounded-full\", \"bg-[#6C63FF]\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"font-bold\"], [1, \"ml-4\"], [1, \"text-sm\", \"font-medium\", \"text-gray-900\"], [1, \"text-xs\", \"text-gray-500\"], [1, \"ml-auto\"], [1, \"bg-[#f0f4f8]\", \"px-3\", \"py-1\", \"rounded-full\", \"text-xs\", \"font-medium\", \"text-[#4f5fad]\"], [1, \"p-6\"], [1, \"text-lg\", \"font-semibold\", \"mb-4\"], [1, \"mb-6\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [\"class\", \"bg-gray-50 p-4 rounded-lg\", 4, \"ngIf\"], [1, \"mt-6\", \"bg-gray-50\", \"p-4\", \"rounded-lg\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-2\"], [1, \"text-base\", \"font-medium\", \"text-gray-700\"], [1, \"text-base\", \"font-bold\", 3, \"ngClass\"], [1, \"w-full\", \"bg-gray-200\", \"rounded-full\", \"h-3\"], [1, \"h-3\", \"rounded-full\", 3, \"ngClass\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"class\", \"text-sm text-gray-500 text-right\", 4, \"ngIf\"], [1, \"bg-gray-50\", \"p-4\", \"rounded-lg\"], [1, \"text-sm\", \"font-medium\", \"text-gray-700\"], [1, \"text-sm\", \"font-bold\"], [1, \"w-full\", \"bg-gray-200\", \"rounded-full\", \"h-2.5\"], [1, \"bg-blue-600\", \"h-2.5\", \"rounded-full\"], [1, \"bg-green-600\", \"h-2.5\", \"rounded-full\"], [1, \"bg-purple-600\", \"h-2.5\", \"rounded-full\"], [1, \"bg-yellow-600\", \"h-2.5\", \"rounded-full\"], [1, \"text-lg\", \"font-semibold\", \"mb-2\"], [1, \"text-gray-700\", \"whitespace-pre-line\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-3\"], [\"target\", \"_blank\", \"class\", \"flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\", 3, \"href\", \"download\", 4, \"ngFor\", \"ngForOf\"], [\"target\", \"_blank\", 1, \"flex\", \"items-center\", \"p-3\", \"bg-gray-50\", \"rounded-lg\", \"hover:bg-gray-100\", \"transition-colors\", 3, \"href\", \"download\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-gray-500\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-sm\", \"text-gray-700\", \"truncate\"], [1, \"text-sm\", \"text-gray-500\", \"text-right\"]],\n        template: function EvaluationDetailsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n            i0.ɵɵlistener(\"click\", function EvaluationDetailsComponent_Template_button_click_3_listener() {\n              return ctx.retourListe();\n            });\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(4, \"svg\", 4);\n            i0.ɵɵelement(5, \"path\", 5);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(6, \"h1\", 6);\n            i0.ɵɵtext(7, \"D\\u00E9tails de l'\\u00E9valuation\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(8, EvaluationDetailsComponent_div_8_Template, 2, 0, \"div\", 7);\n            i0.ɵɵtemplate(9, EvaluationDetailsComponent_div_9_Template, 2, 1, \"div\", 8);\n            i0.ɵɵtemplate(10, EvaluationDetailsComponent_div_10_Template, 43, 32, \"div\", 9);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.rendu);\n          }\n        },\n        dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.DatePipe],\n        styles: [\".loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:2rem 0}.error-message[_ngcontent-%COMP%]{color:#dc3545;margin-top:.25rem}\"]\n      });\n    }\n  }\n  return EvaluationDetailsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}