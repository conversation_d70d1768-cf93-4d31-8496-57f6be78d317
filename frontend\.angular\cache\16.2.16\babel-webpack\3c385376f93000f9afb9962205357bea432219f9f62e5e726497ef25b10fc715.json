{"ast": null, "code": "const MAX_ARRAY_LENGTH = 10;\nconst MAX_RECURSIVE_DEPTH = 2;\n/**\n * Used to print values in error messages.\n */\n\nexport function inspect(value) {\n  return formatValue(value, []);\n}\nfunction formatValue(value, seenValues) {\n  switch (typeof value) {\n    case 'string':\n      return JSON.stringify(value);\n    case 'function':\n      return value.name ? `[function ${value.name}]` : '[function]';\n    case 'object':\n      return formatObjectValue(value, seenValues);\n    default:\n      return String(value);\n  }\n}\nfunction formatObjectValue(value, previouslySeenValues) {\n  if (value === null) {\n    return 'null';\n  }\n  if (previouslySeenValues.includes(value)) {\n    return '[Circular]';\n  }\n  const seenValues = [...previouslySeenValues, value];\n  if (isJSONable(value)) {\n    const jsonValue = value.toJSON(); // check for infinite recursion\n\n    if (jsonValue !== value) {\n      return typeof jsonValue === 'string' ? jsonValue : formatValue(jsonValue, seenValues);\n    }\n  } else if (Array.isArray(value)) {\n    return formatArray(value, seenValues);\n  }\n  return formatObject(value, seenValues);\n}\nfunction isJSONable(value) {\n  return typeof value.toJSON === 'function';\n}\nfunction formatObject(object, seenValues) {\n  const entries = Object.entries(object);\n  if (entries.length === 0) {\n    return '{}';\n  }\n  if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n    return '[' + getObjectTag(object) + ']';\n  }\n  const properties = entries.map(([key, value]) => key + ': ' + formatValue(value, seenValues));\n  return '{ ' + properties.join(', ') + ' }';\n}\nfunction formatArray(array, seenValues) {\n  if (array.length === 0) {\n    return '[]';\n  }\n  if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n    return '[Array]';\n  }\n  const len = Math.min(MAX_ARRAY_LENGTH, array.length);\n  const remaining = array.length - len;\n  const items = [];\n  for (let i = 0; i < len; ++i) {\n    items.push(formatValue(array[i], seenValues));\n  }\n  if (remaining === 1) {\n    items.push('... 1 more item');\n  } else if (remaining > 1) {\n    items.push(`... ${remaining} more items`);\n  }\n  return '[' + items.join(', ') + ']';\n}\nfunction getObjectTag(object) {\n  const tag = Object.prototype.toString.call(object).replace(/^\\[object /, '').replace(/]$/, '');\n  if (tag === 'Object' && typeof object.constructor === 'function') {\n    const name = object.constructor.name;\n    if (typeof name === 'string' && name !== '') {\n      return name;\n    }\n  }\n  return tag;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}