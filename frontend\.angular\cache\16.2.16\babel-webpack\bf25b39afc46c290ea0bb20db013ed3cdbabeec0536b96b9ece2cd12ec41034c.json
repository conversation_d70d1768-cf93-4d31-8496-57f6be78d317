{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Inject, InjectionToken, Directive, Optional, SkipSelf, Input, EventEmitter, Self, ContentChildren, ContentChild, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { _getEventTarget, normalizePassiveListenerOptions, _getShadowRoot } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceElement, coerceNumberProperty, coerceArray } from '@angular/cdk/coercion';\nimport { isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { Subject, Subscription, interval, animationFrameScheduler, Observable, merge } from 'rxjs';\nimport { takeUntil, map, take, startWith, tap, switchMap } from 'rxjs/operators';\nimport * as i1$1 from '@angular/cdk/bidi';\n\n/**\n * Shallow-extends a stylesheet object with another stylesheet-like object.\n * Note that the keys in `source` have to be dash-cased.\n * @docs-private\n */\nfunction extendStyles(dest, source, importantProperties) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      const value = source[key];\n      if (value) {\n        dest.setProperty(key, value, importantProperties?.has(key) ? 'important' : '');\n      } else {\n        dest.removeProperty(key);\n      }\n    }\n  }\n  return dest;\n}\n/**\n * Toggles whether the native drag interactions should be enabled for an element.\n * @param element Element on which to toggle the drag interactions.\n * @param enable Whether the drag interactions should be enabled.\n * @docs-private\n */\nfunction toggleNativeDragInteractions(element, enable) {\n  const userSelect = enable ? '' : 'none';\n  extendStyles(element.style, {\n    'touch-action': enable ? '' : 'none',\n    '-webkit-user-drag': enable ? '' : 'none',\n    '-webkit-tap-highlight-color': enable ? '' : 'transparent',\n    'user-select': userSelect,\n    '-ms-user-select': userSelect,\n    '-webkit-user-select': userSelect,\n    '-moz-user-select': userSelect\n  });\n}\n/**\n * Toggles whether an element is visible while preserving its dimensions.\n * @param element Element whose visibility to toggle\n * @param enable Whether the element should be visible.\n * @param importantProperties Properties to be set as `!important`.\n * @docs-private\n */\nfunction toggleVisibility(element, enable, importantProperties) {\n  extendStyles(element.style, {\n    position: enable ? '' : 'fixed',\n    top: enable ? '' : '0',\n    opacity: enable ? '' : '0',\n    left: enable ? '' : '-999em'\n  }, importantProperties);\n}\n/**\n * Combines a transform string with an optional other transform\n * that exited before the base transform was applied.\n */\nfunction combineTransforms(transform, initialTransform) {\n  return initialTransform && initialTransform != 'none' ? transform + ' ' + initialTransform : transform;\n}\n\n/** Parses a CSS time value to milliseconds. */\nfunction parseCssTimeUnitsToMs(value) {\n  // Some browsers will return it in seconds, whereas others will return milliseconds.\n  const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;\n  return parseFloat(value) * multiplier;\n}\n/** Gets the transform transition duration, including the delay, of an element in milliseconds. */\nfunction getTransformTransitionDurationInMs(element) {\n  const computedStyle = getComputedStyle(element);\n  const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');\n  const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all');\n  // If there's no transition for `all` or `transform`, we shouldn't do anything.\n  if (!property) {\n    return 0;\n  }\n  // Get the index of the property that we're interested in and match\n  // it up to the same index in `transition-delay` and `transition-duration`.\n  const propertyIndex = transitionedProperties.indexOf(property);\n  const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');\n  const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');\n  return parseCssTimeUnitsToMs(rawDurations[propertyIndex]) + parseCssTimeUnitsToMs(rawDelays[propertyIndex]);\n}\n/** Parses out multiple values from a computed style into an array. */\nfunction parseCssPropertyValue(computedStyle, name) {\n  const value = computedStyle.getPropertyValue(name);\n  return value.split(',').map(part => part.trim());\n}\n\n/** Gets a mutable version of an element's bounding `ClientRect`. */\nfunction getMutableClientRect(element) {\n  const clientRect = element.getBoundingClientRect();\n  // We need to clone the `clientRect` here, because all the values on it are readonly\n  // and we need to be able to update them. Also we can't use a spread here, because\n  // the values on a `ClientRect` aren't own properties. See:\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect#Notes\n  return {\n    top: clientRect.top,\n    right: clientRect.right,\n    bottom: clientRect.bottom,\n    left: clientRect.left,\n    width: clientRect.width,\n    height: clientRect.height,\n    x: clientRect.x,\n    y: clientRect.y\n  };\n}\n/**\n * Checks whether some coordinates are within a `ClientRect`.\n * @param clientRect ClientRect that is being checked.\n * @param x Coordinates along the X axis.\n * @param y Coordinates along the Y axis.\n */\nfunction isInsideClientRect(clientRect, x, y) {\n  const {\n    top,\n    bottom,\n    left,\n    right\n  } = clientRect;\n  return y >= top && y <= bottom && x >= left && x <= right;\n}\n/**\n * Updates the top/left positions of a `ClientRect`, as well as their bottom/right counterparts.\n * @param clientRect `ClientRect` that should be updated.\n * @param top Amount to add to the `top` position.\n * @param left Amount to add to the `left` position.\n */\nfunction adjustClientRect(clientRect, top, left) {\n  clientRect.top += top;\n  clientRect.bottom = clientRect.top + clientRect.height;\n  clientRect.left += left;\n  clientRect.right = clientRect.left + clientRect.width;\n}\n/**\n * Checks whether the pointer coordinates are close to a ClientRect.\n * @param rect ClientRect to check against.\n * @param threshold Threshold around the ClientRect.\n * @param pointerX Coordinates along the X axis.\n * @param pointerY Coordinates along the Y axis.\n */\nfunction isPointerNearClientRect(rect, threshold, pointerX, pointerY) {\n  const {\n    top,\n    right,\n    bottom,\n    left,\n    width,\n    height\n  } = rect;\n  const xThreshold = width * threshold;\n  const yThreshold = height * threshold;\n  return pointerY > top - yThreshold && pointerY < bottom + yThreshold && pointerX > left - xThreshold && pointerX < right + xThreshold;\n}\n\n/** Keeps track of the scroll position and dimensions of the parents of an element. */\nclass ParentPositionTracker {\n  constructor(_document) {\n    this._document = _document;\n    /** Cached positions of the scrollable parent elements. */\n    this.positions = new Map();\n  }\n  /** Clears the cached positions. */\n  clear() {\n    this.positions.clear();\n  }\n  /** Caches the positions. Should be called at the beginning of a drag sequence. */\n  cache(elements) {\n    this.clear();\n    this.positions.set(this._document, {\n      scrollPosition: this.getViewportScrollPosition()\n    });\n    elements.forEach(element => {\n      this.positions.set(element, {\n        scrollPosition: {\n          top: element.scrollTop,\n          left: element.scrollLeft\n        },\n        clientRect: getMutableClientRect(element)\n      });\n    });\n  }\n  /** Handles scrolling while a drag is taking place. */\n  handleScroll(event) {\n    const target = _getEventTarget(event);\n    const cachedPosition = this.positions.get(target);\n    if (!cachedPosition) {\n      return null;\n    }\n    const scrollPosition = cachedPosition.scrollPosition;\n    let newTop;\n    let newLeft;\n    if (target === this._document) {\n      const viewportScrollPosition = this.getViewportScrollPosition();\n      newTop = viewportScrollPosition.top;\n      newLeft = viewportScrollPosition.left;\n    } else {\n      newTop = target.scrollTop;\n      newLeft = target.scrollLeft;\n    }\n    const topDifference = scrollPosition.top - newTop;\n    const leftDifference = scrollPosition.left - newLeft;\n    // Go through and update the cached positions of the scroll\n    // parents that are inside the element that was scrolled.\n    this.positions.forEach((position, node) => {\n      if (position.clientRect && target !== node && target.contains(node)) {\n        adjustClientRect(position.clientRect, topDifference, leftDifference);\n      }\n    });\n    scrollPosition.top = newTop;\n    scrollPosition.left = newLeft;\n    return {\n      top: topDifference,\n      left: leftDifference\n    };\n  }\n  /**\n   * Gets the scroll position of the viewport. Note that we use the scrollX and scrollY directly,\n   * instead of going through the `ViewportRuler`, because the first value the ruler looks at is\n   * the top/left offset of the `document.documentElement` which works for most cases, but breaks\n   * if the element is offset by something like the `BlockScrollStrategy`.\n   */\n  getViewportScrollPosition() {\n    return {\n      top: window.scrollY,\n      left: window.scrollX\n    };\n  }\n}\n\n/** Creates a deep clone of an element. */\nfunction deepCloneNode(node) {\n  const clone = node.cloneNode(true);\n  const descendantsWithId = clone.querySelectorAll('[id]');\n  const nodeName = node.nodeName.toLowerCase();\n  // Remove the `id` to avoid having multiple elements with the same id on the page.\n  clone.removeAttribute('id');\n  for (let i = 0; i < descendantsWithId.length; i++) {\n    descendantsWithId[i].removeAttribute('id');\n  }\n  if (nodeName === 'canvas') {\n    transferCanvasData(node, clone);\n  } else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n    transferInputData(node, clone);\n  }\n  transferData('canvas', node, clone, transferCanvasData);\n  transferData('input, textarea, select', node, clone, transferInputData);\n  return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData(selector, node, clone, callback) {\n  const descendantElements = node.querySelectorAll(selector);\n  if (descendantElements.length) {\n    const cloneElements = clone.querySelectorAll(selector);\n    for (let i = 0; i < descendantElements.length; i++) {\n      callback(descendantElements[i], cloneElements[i]);\n    }\n  }\n}\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\nfunction transferInputData(source, clone) {\n  // Browsers throw an error when assigning the value of a file input programmatically.\n  if (clone.type !== 'file') {\n    clone.value = source.value;\n  }\n  // Radio button `name` attributes must be unique for radio button groups\n  // otherwise original radio buttons can lose their checked state\n  // once the clone is inserted in the DOM.\n  if (clone.type === 'radio' && clone.name) {\n    clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n  }\n}\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source, clone) {\n  const context = clone.getContext('2d');\n  if (context) {\n    // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n    // We can't do much about it so just ignore the error.\n    try {\n      context.drawImage(source, 0, 0);\n    } catch {}\n  }\n}\n\n/** Options that can be used to bind a passive event listener. */\nconst passiveEventListenerOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: true\n});\n/** Options that can be used to bind an active event listener. */\nconst activeEventListenerOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: false\n});\n/**\n * Time in milliseconds for which to ignore mouse events, after\n * receiving a touch event. Used to avoid doing double work for\n * touch devices where the browser fires fake mouse events, in\n * addition to touch events.\n */\nconst MOUSE_EVENT_IGNORE_TIME = 800;\n/** Inline styles to be set as `!important` while dragging. */\nconst dragImportantProperties = /*#__PURE__*/new Set([\n// Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n'position']);\n/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */\nclass DragRef {\n  /** Whether starting to drag this element is disabled. */\n  get disabled() {\n    return this._disabled || !!(this._dropContainer && this._dropContainer.disabled);\n  }\n  set disabled(value) {\n    const newValue = coerceBooleanProperty(value);\n    if (newValue !== this._disabled) {\n      this._disabled = newValue;\n      this._toggleNativeDragInteractions();\n      this._handles.forEach(handle => toggleNativeDragInteractions(handle, newValue));\n    }\n  }\n  constructor(element, _config, _document, _ngZone, _viewportRuler, _dragDropRegistry) {\n    this._config = _config;\n    this._document = _document;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._dragDropRegistry = _dragDropRegistry;\n    /**\n     * CSS `transform` applied to the element when it isn't being dragged. We need a\n     * passive transform in order for the dragged element to retain its new position\n     * after the user has stopped dragging and because we need to know the relative\n     * position in case they start dragging again. This corresponds to `element.style.transform`.\n     */\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n    /** CSS `transform` that is applied to the element while it's being dragged. */\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    /**\n     * Whether the dragging sequence has been started. Doesn't\n     * necessarily mean that the element has been moved.\n     */\n    this._hasStartedDragging = false;\n    /** Emits when the item is being moved. */\n    this._moveEvents = new Subject();\n    /** Subscription to pointer movement events. */\n    this._pointerMoveSubscription = Subscription.EMPTY;\n    /** Subscription to the event that is dispatched when the user lifts their pointer. */\n    this._pointerUpSubscription = Subscription.EMPTY;\n    /** Subscription to the viewport being scrolled. */\n    this._scrollSubscription = Subscription.EMPTY;\n    /** Subscription to the viewport being resized. */\n    this._resizeSubscription = Subscription.EMPTY;\n    /** Cached reference to the boundary element. */\n    this._boundaryElement = null;\n    /** Whether the native dragging interactions have been enabled on the root element. */\n    this._nativeInteractionsEnabled = true;\n    /** Elements that can be used to drag the draggable item. */\n    this._handles = [];\n    /** Registered handles that are currently disabled. */\n    this._disabledHandles = new Set();\n    /** Layout direction of the item. */\n    this._direction = 'ltr';\n    /**\n     * Amount of milliseconds to wait after the user has put their\n     * pointer down before starting to drag the element.\n     */\n    this.dragStartDelay = 0;\n    this._disabled = false;\n    /** Emits as the drag sequence is being prepared. */\n    this.beforeStarted = new Subject();\n    /** Emits when the user starts dragging the item. */\n    this.started = new Subject();\n    /** Emits when the user has released a drag item, before any animations have started. */\n    this.released = new Subject();\n    /** Emits when the user stops dragging an item in the container. */\n    this.ended = new Subject();\n    /** Emits when the user has moved the item into a new container. */\n    this.entered = new Subject();\n    /** Emits when the user removes the item its container by dragging it into another container. */\n    this.exited = new Subject();\n    /** Emits when the user drops the item inside a container. */\n    this.dropped = new Subject();\n    /**\n     * Emits as the user is dragging the item. Use with caution,\n     * because this event will fire for every pixel that the user has dragged.\n     */\n    this.moved = this._moveEvents;\n    /** Handler for the `mousedown`/`touchstart` events. */\n    this._pointerDown = event => {\n      this.beforeStarted.next();\n      // Delegate the event based on whether it started from a handle or the element itself.\n      if (this._handles.length) {\n        const targetHandle = this._getTargetHandle(event);\n        if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n          this._initializeDragSequence(targetHandle, event);\n        }\n      } else if (!this.disabled) {\n        this._initializeDragSequence(this._rootElement, event);\n      }\n    };\n    /** Handler that is invoked when the user moves their pointer after they've initiated a drag. */\n    this._pointerMove = event => {\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      if (!this._hasStartedDragging) {\n        const distanceX = Math.abs(pointerPosition.x - this._pickupPositionOnPage.x);\n        const distanceY = Math.abs(pointerPosition.y - this._pickupPositionOnPage.y);\n        const isOverThreshold = distanceX + distanceY >= this._config.dragStartThreshold;\n        // Only start dragging after the user has moved more than the minimum distance in either\n        // direction. Note that this is preferable over doing something like `skip(minimumDistance)`\n        // in the `pointerMove` subscription, because we're not guaranteed to have one move event\n        // per pixel of movement (e.g. if the user moves their pointer quickly).\n        if (isOverThreshold) {\n          const isDelayElapsed = Date.now() >= this._dragStartTime + this._getDragStartDelay(event);\n          const container = this._dropContainer;\n          if (!isDelayElapsed) {\n            this._endDragSequence(event);\n            return;\n          }\n          // Prevent other drag sequences from starting while something in the container is still\n          // being dragged. This can happen while we're waiting for the drop animation to finish\n          // and can cause errors, because some elements might still be moving around.\n          if (!container || !container.isDragging() && !container.isReceiving()) {\n            // Prevent the default action as soon as the dragging sequence is considered as\n            // \"started\" since waiting for the next event can allow the device to begin scrolling.\n            event.preventDefault();\n            this._hasStartedDragging = true;\n            this._ngZone.run(() => this._startDragSequence(event));\n          }\n        }\n        return;\n      }\n      // We prevent the default action down here so that we know that dragging has started. This is\n      // important for touch devices where doing this too early can unnecessarily block scrolling,\n      // if there's a dragging delay.\n      event.preventDefault();\n      const constrainedPointerPosition = this._getConstrainedPointerPosition(pointerPosition);\n      this._hasMoved = true;\n      this._lastKnownPointerPosition = pointerPosition;\n      this._updatePointerDirectionDelta(constrainedPointerPosition);\n      if (this._dropContainer) {\n        this._updateActiveDropContainer(constrainedPointerPosition, pointerPosition);\n      } else {\n        // If there's a position constraint function, we want the element's top/left to be at the\n        // specific position on the page. Use the initial position as a reference if that's the case.\n        const offset = this.constrainPosition ? this._initialClientRect : this._pickupPositionOnPage;\n        const activeTransform = this._activeTransform;\n        activeTransform.x = constrainedPointerPosition.x - offset.x + this._passiveTransform.x;\n        activeTransform.y = constrainedPointerPosition.y - offset.y + this._passiveTransform.y;\n        this._applyRootElementTransform(activeTransform.x, activeTransform.y);\n      }\n      // Since this event gets fired for every pixel while dragging, we only\n      // want to fire it if the consumer opted into it. Also we have to\n      // re-enter the zone because we run all of the events on the outside.\n      if (this._moveEvents.observers.length) {\n        this._ngZone.run(() => {\n          this._moveEvents.next({\n            source: this,\n            pointerPosition: constrainedPointerPosition,\n            event,\n            distance: this._getDragDistance(constrainedPointerPosition),\n            delta: this._pointerDirectionDelta\n          });\n        });\n      }\n    };\n    /** Handler that is invoked when the user lifts their pointer up, after initiating a drag. */\n    this._pointerUp = event => {\n      this._endDragSequence(event);\n    };\n    /** Handles a native `dragstart` event. */\n    this._nativeDragStart = event => {\n      if (this._handles.length) {\n        const targetHandle = this._getTargetHandle(event);\n        if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n          event.preventDefault();\n        }\n      } else if (!this.disabled) {\n        // Usually this isn't necessary since the we prevent the default action in `pointerDown`,\n        // but some cases like dragging of links can slip through (see #24403).\n        event.preventDefault();\n      }\n    };\n    this.withRootElement(element).withParent(_config.parentDragRef || null);\n    this._parentPositions = new ParentPositionTracker(_document);\n    _dragDropRegistry.registerDragItem(this);\n  }\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement() {\n    return this._placeholder;\n  }\n  /** Returns the root draggable element. */\n  getRootElement() {\n    return this._rootElement;\n  }\n  /**\n   * Gets the currently-visible element that represents the drag item.\n   * While dragging this is the placeholder, otherwise it's the root element.\n   */\n  getVisibleElement() {\n    return this.isDragging() ? this.getPlaceholderElement() : this.getRootElement();\n  }\n  /** Registers the handles that can be used to drag the element. */\n  withHandles(handles) {\n    this._handles = handles.map(handle => coerceElement(handle));\n    this._handles.forEach(handle => toggleNativeDragInteractions(handle, this.disabled));\n    this._toggleNativeDragInteractions();\n    // Delete any lingering disabled handles that may have been destroyed. Note that we re-create\n    // the set, rather than iterate over it and filter out the destroyed handles, because while\n    // the ES spec allows for sets to be modified while they're being iterated over, some polyfills\n    // use an array internally which may throw an error.\n    const disabledHandles = new Set();\n    this._disabledHandles.forEach(handle => {\n      if (this._handles.indexOf(handle) > -1) {\n        disabledHandles.add(handle);\n      }\n    });\n    this._disabledHandles = disabledHandles;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag preview.\n   * @param template Template that from which to stamp out the preview.\n   */\n  withPreviewTemplate(template) {\n    this._previewTemplate = template;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag placeholder.\n   * @param template Template that from which to stamp out the placeholder.\n   */\n  withPlaceholderTemplate(template) {\n    this._placeholderTemplate = template;\n    return this;\n  }\n  /**\n   * Sets an alternate drag root element. The root element is the element that will be moved as\n   * the user is dragging. Passing an alternate root element is useful when trying to enable\n   * dragging on an element that you might not have access to.\n   */\n  withRootElement(rootElement) {\n    const element = coerceElement(rootElement);\n    if (element !== this._rootElement) {\n      if (this._rootElement) {\n        this._removeRootElementListeners(this._rootElement);\n      }\n      this._ngZone.runOutsideAngular(() => {\n        element.addEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n        element.addEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n        element.addEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n      });\n      this._initialTransform = undefined;\n      this._rootElement = element;\n    }\n    if (typeof SVGElement !== 'undefined' && this._rootElement instanceof SVGElement) {\n      this._ownerSVGElement = this._rootElement.ownerSVGElement;\n    }\n    return this;\n  }\n  /**\n   * Element to which the draggable's position will be constrained.\n   */\n  withBoundaryElement(boundaryElement) {\n    this._boundaryElement = boundaryElement ? coerceElement(boundaryElement) : null;\n    this._resizeSubscription.unsubscribe();\n    if (boundaryElement) {\n      this._resizeSubscription = this._viewportRuler.change(10).subscribe(() => this._containInsideBoundaryOnResize());\n    }\n    return this;\n  }\n  /** Sets the parent ref that the ref is nested in.  */\n  withParent(parent) {\n    this._parentDragRef = parent;\n    return this;\n  }\n  /** Removes the dragging functionality from the DOM element. */\n  dispose() {\n    this._removeRootElementListeners(this._rootElement);\n    // Do this check before removing from the registry since it'll\n    // stop being considered as dragged once it is removed.\n    if (this.isDragging()) {\n      // Since we move out the element to the end of the body while it's being\n      // dragged, we have to make sure that it's removed if it gets destroyed.\n      this._rootElement?.remove();\n    }\n    this._anchor?.remove();\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._dragDropRegistry.removeDragItem(this);\n    this._removeSubscriptions();\n    this.beforeStarted.complete();\n    this.started.complete();\n    this.released.complete();\n    this.ended.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this._moveEvents.complete();\n    this._handles = [];\n    this._disabledHandles.clear();\n    this._dropContainer = undefined;\n    this._resizeSubscription.unsubscribe();\n    this._parentPositions.clear();\n    this._boundaryElement = this._rootElement = this._ownerSVGElement = this._placeholderTemplate = this._previewTemplate = this._anchor = this._parentDragRef = null;\n  }\n  /** Checks whether the element is currently being dragged. */\n  isDragging() {\n    return this._hasStartedDragging && this._dragDropRegistry.isDragging(this);\n  }\n  /** Resets a standalone drag item to its initial position. */\n  reset() {\n    this._rootElement.style.transform = this._initialTransform || '';\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n  }\n  /**\n   * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n   * @param handle Handle element that should be disabled.\n   */\n  disableHandle(handle) {\n    if (!this._disabledHandles.has(handle) && this._handles.indexOf(handle) > -1) {\n      this._disabledHandles.add(handle);\n      toggleNativeDragInteractions(handle, true);\n    }\n  }\n  /**\n   * Enables a handle, if it has been disabled.\n   * @param handle Handle element to be enabled.\n   */\n  enableHandle(handle) {\n    if (this._disabledHandles.has(handle)) {\n      this._disabledHandles.delete(handle);\n      toggleNativeDragInteractions(handle, this.disabled);\n    }\n  }\n  /** Sets the layout direction of the draggable item. */\n  withDirection(direction) {\n    this._direction = direction;\n    return this;\n  }\n  /** Sets the container that the item is part of. */\n  _withDropContainer(container) {\n    this._dropContainer = container;\n  }\n  /**\n   * Gets the current position in pixels the draggable outside of a drop container.\n   */\n  getFreeDragPosition() {\n    const position = this.isDragging() ? this._activeTransform : this._passiveTransform;\n    return {\n      x: position.x,\n      y: position.y\n    };\n  }\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value) {\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform.x = value.x;\n    this._passiveTransform.y = value.y;\n    if (!this._dropContainer) {\n      this._applyRootElementTransform(value.x, value.y);\n    }\n    return this;\n  }\n  /**\n   * Sets the container into which to insert the preview element.\n   * @param value Container into which to insert the preview.\n   */\n  withPreviewContainer(value) {\n    this._previewContainer = value;\n    return this;\n  }\n  /** Updates the item's sort order based on the last-known pointer position. */\n  _sortFromLastPointerPosition() {\n    const position = this._lastKnownPointerPosition;\n    if (position && this._dropContainer) {\n      this._updateActiveDropContainer(this._getConstrainedPointerPosition(position), position);\n    }\n  }\n  /** Unsubscribes from the global subscriptions. */\n  _removeSubscriptions() {\n    this._pointerMoveSubscription.unsubscribe();\n    this._pointerUpSubscription.unsubscribe();\n    this._scrollSubscription.unsubscribe();\n  }\n  /** Destroys the preview element and its ViewRef. */\n  _destroyPreview() {\n    this._preview?.remove();\n    this._previewRef?.destroy();\n    this._preview = this._previewRef = null;\n  }\n  /** Destroys the placeholder element and its ViewRef. */\n  _destroyPlaceholder() {\n    this._placeholder?.remove();\n    this._placeholderRef?.destroy();\n    this._placeholder = this._placeholderRef = null;\n  }\n  /**\n   * Clears subscriptions and stops the dragging sequence.\n   * @param event Browser event object that ended the sequence.\n   */\n  _endDragSequence(event) {\n    // Note that here we use `isDragging` from the service, rather than from `this`.\n    // The difference is that the one from the service reflects whether a dragging sequence\n    // has been initiated, whereas the one on `this` includes whether the user has passed\n    // the minimum dragging threshold.\n    if (!this._dragDropRegistry.isDragging(this)) {\n      return;\n    }\n    this._removeSubscriptions();\n    this._dragDropRegistry.stopDragging(this);\n    this._toggleNativeDragInteractions();\n    if (this._handles) {\n      this._rootElement.style.webkitTapHighlightColor = this._rootElementTapHighlight;\n    }\n    if (!this._hasStartedDragging) {\n      return;\n    }\n    this.released.next({\n      source: this,\n      event\n    });\n    if (this._dropContainer) {\n      // Stop scrolling immediately, instead of waiting for the animation to finish.\n      this._dropContainer._stopScrolling();\n      this._animatePreviewToPlaceholder().then(() => {\n        this._cleanupDragArtifacts(event);\n        this._cleanupCachedDimensions();\n        this._dragDropRegistry.stopDragging(this);\n      });\n    } else {\n      // Convert the active transform into a passive one. This means that next time\n      // the user starts dragging the item, its position will be calculated relatively\n      // to the new passive transform.\n      this._passiveTransform.x = this._activeTransform.x;\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      this._passiveTransform.y = this._activeTransform.y;\n      this._ngZone.run(() => {\n        this.ended.next({\n          source: this,\n          distance: this._getDragDistance(pointerPosition),\n          dropPoint: pointerPosition,\n          event\n        });\n      });\n      this._cleanupCachedDimensions();\n      this._dragDropRegistry.stopDragging(this);\n    }\n  }\n  /** Starts the dragging sequence. */\n  _startDragSequence(event) {\n    if (isTouchEvent(event)) {\n      this._lastTouchEventTime = Date.now();\n    }\n    this._toggleNativeDragInteractions();\n    const dropContainer = this._dropContainer;\n    if (dropContainer) {\n      const element = this._rootElement;\n      const parent = element.parentNode;\n      const placeholder = this._placeholder = this._createPlaceholderElement();\n      const anchor = this._anchor = this._anchor || this._document.createComment('');\n      // Needs to happen before the root element is moved.\n      const shadowRoot = this._getShadowRoot();\n      // Insert an anchor node so that we can restore the element's position in the DOM.\n      parent.insertBefore(anchor, element);\n      // There's no risk of transforms stacking when inside a drop container so\n      // we can keep the initial transform up to date any time dragging starts.\n      this._initialTransform = element.style.transform || '';\n      // Create the preview after the initial transform has\n      // been cached, because it can be affected by the transform.\n      this._preview = this._createPreviewElement();\n      // We move the element out at the end of the body and we make it hidden, because keeping it in\n      // place will throw off the consumer's `:last-child` selectors. We can't remove the element\n      // from the DOM completely, because iOS will stop firing all subsequent events in the chain.\n      toggleVisibility(element, false, dragImportantProperties);\n      this._document.body.appendChild(parent.replaceChild(placeholder, element));\n      this._getPreviewInsertionPoint(parent, shadowRoot).appendChild(this._preview);\n      this.started.next({\n        source: this,\n        event\n      }); // Emit before notifying the container.\n      dropContainer.start();\n      this._initialContainer = dropContainer;\n      this._initialIndex = dropContainer.getItemIndex(this);\n    } else {\n      this.started.next({\n        source: this,\n        event\n      });\n      this._initialContainer = this._initialIndex = undefined;\n    }\n    // Important to run after we've called `start` on the parent container\n    // so that it has had time to resolve its scrollable parents.\n    this._parentPositions.cache(dropContainer ? dropContainer.getScrollableParents() : []);\n  }\n  /**\n   * Sets up the different variables and subscriptions\n   * that will be necessary for the dragging sequence.\n   * @param referenceElement Element that started the drag sequence.\n   * @param event Browser event object that started the sequence.\n   */\n  _initializeDragSequence(referenceElement, event) {\n    // Stop propagation if the item is inside another\n    // draggable so we don't start multiple drag sequences.\n    if (this._parentDragRef) {\n      event.stopPropagation();\n    }\n    const isDragging = this.isDragging();\n    const isTouchSequence = isTouchEvent(event);\n    const isAuxiliaryMouseButton = !isTouchSequence && event.button !== 0;\n    const rootElement = this._rootElement;\n    const target = _getEventTarget(event);\n    const isSyntheticEvent = !isTouchSequence && this._lastTouchEventTime && this._lastTouchEventTime + MOUSE_EVENT_IGNORE_TIME > Date.now();\n    const isFakeEvent = isTouchSequence ? isFakeTouchstartFromScreenReader(event) : isFakeMousedownFromScreenReader(event);\n    // If the event started from an element with the native HTML drag&drop, it'll interfere\n    // with our own dragging (e.g. `img` tags do it by default). Prevent the default action\n    // to stop it from happening. Note that preventing on `dragstart` also seems to work, but\n    // it's flaky and it fails if the user drags it away quickly. Also note that we only want\n    // to do this for `mousedown` since doing the same for `touchstart` will stop any `click`\n    // events from firing on touch devices.\n    if (target && target.draggable && event.type === 'mousedown') {\n      event.preventDefault();\n    }\n    // Abort if the user is already dragging or is using a mouse button other than the primary one.\n    if (isDragging || isAuxiliaryMouseButton || isSyntheticEvent || isFakeEvent) {\n      return;\n    }\n    // If we've got handles, we need to disable the tap highlight on the entire root element,\n    // otherwise iOS will still add it, even though all the drag interactions on the handle\n    // are disabled.\n    if (this._handles.length) {\n      const rootStyles = rootElement.style;\n      this._rootElementTapHighlight = rootStyles.webkitTapHighlightColor || '';\n      rootStyles.webkitTapHighlightColor = 'transparent';\n    }\n    this._hasStartedDragging = this._hasMoved = false;\n    // Avoid multiple subscriptions and memory leaks when multi touch\n    // (isDragging check above isn't enough because of possible temporal and/or dimensional delays)\n    this._removeSubscriptions();\n    this._initialClientRect = this._rootElement.getBoundingClientRect();\n    this._pointerMoveSubscription = this._dragDropRegistry.pointerMove.subscribe(this._pointerMove);\n    this._pointerUpSubscription = this._dragDropRegistry.pointerUp.subscribe(this._pointerUp);\n    this._scrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(scrollEvent => this._updateOnScroll(scrollEvent));\n    if (this._boundaryElement) {\n      this._boundaryRect = getMutableClientRect(this._boundaryElement);\n    }\n    // If we have a custom preview we can't know ahead of time how large it'll be so we position\n    // it next to the cursor. The exception is when the consumer has opted into making the preview\n    // the same size as the root element, in which case we do know the size.\n    const previewTemplate = this._previewTemplate;\n    this._pickupPositionInElement = previewTemplate && previewTemplate.template && !previewTemplate.matchSize ? {\n      x: 0,\n      y: 0\n    } : this._getPointerPositionInElement(this._initialClientRect, referenceElement, event);\n    const pointerPosition = this._pickupPositionOnPage = this._lastKnownPointerPosition = this._getPointerPositionOnPage(event);\n    this._pointerDirectionDelta = {\n      x: 0,\n      y: 0\n    };\n    this._pointerPositionAtLastDirectionChange = {\n      x: pointerPosition.x,\n      y: pointerPosition.y\n    };\n    this._dragStartTime = Date.now();\n    this._dragDropRegistry.startDragging(this, event);\n  }\n  /** Cleans up the DOM artifacts that were added to facilitate the element being dragged. */\n  _cleanupDragArtifacts(event) {\n    // Restore the element's visibility and insert it at its old position in the DOM.\n    // It's important that we maintain the position, because moving the element around in the DOM\n    // can throw off `NgFor` which does smart diffing and re-creates elements only when necessary,\n    // while moving the existing elements in all other cases.\n    toggleVisibility(this._rootElement, true, dragImportantProperties);\n    this._anchor.parentNode.replaceChild(this._rootElement, this._anchor);\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._initialClientRect = this._boundaryRect = this._previewRect = this._initialTransform = undefined;\n    // Re-enter the NgZone since we bound `document` events on the outside.\n    this._ngZone.run(() => {\n      const container = this._dropContainer;\n      const currentIndex = container.getItemIndex(this);\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      const distance = this._getDragDistance(pointerPosition);\n      const isPointerOverContainer = container._isOverContainer(pointerPosition.x, pointerPosition.y);\n      this.ended.next({\n        source: this,\n        distance,\n        dropPoint: pointerPosition,\n        event\n      });\n      this.dropped.next({\n        item: this,\n        currentIndex,\n        previousIndex: this._initialIndex,\n        container: container,\n        previousContainer: this._initialContainer,\n        isPointerOverContainer,\n        distance,\n        dropPoint: pointerPosition,\n        event\n      });\n      container.drop(this, currentIndex, this._initialIndex, this._initialContainer, isPointerOverContainer, distance, pointerPosition, event);\n      this._dropContainer = this._initialContainer;\n    });\n  }\n  /**\n   * Updates the item's position in its drop container, or moves it\n   * into a new one, depending on its current drag position.\n   */\n  _updateActiveDropContainer({\n    x,\n    y\n  }, {\n    x: rawX,\n    y: rawY\n  }) {\n    // Drop container that draggable has been moved into.\n    let newContainer = this._initialContainer._getSiblingContainerFromPosition(this, x, y);\n    // If we couldn't find a new container to move the item into, and the item has left its\n    // initial container, check whether the it's over the initial container. This handles the\n    // case where two containers are connected one way and the user tries to undo dragging an\n    // item into a new container.\n    if (!newContainer && this._dropContainer !== this._initialContainer && this._initialContainer._isOverContainer(x, y)) {\n      newContainer = this._initialContainer;\n    }\n    if (newContainer && newContainer !== this._dropContainer) {\n      this._ngZone.run(() => {\n        // Notify the old container that the item has left.\n        this.exited.next({\n          item: this,\n          container: this._dropContainer\n        });\n        this._dropContainer.exit(this);\n        // Notify the new container that the item has entered.\n        this._dropContainer = newContainer;\n        this._dropContainer.enter(this, x, y, newContainer === this._initialContainer &&\n        // If we're re-entering the initial container and sorting is disabled,\n        // put item the into its starting index to begin with.\n        newContainer.sortingDisabled ? this._initialIndex : undefined);\n        this.entered.next({\n          item: this,\n          container: newContainer,\n          currentIndex: newContainer.getItemIndex(this)\n        });\n      });\n    }\n    // Dragging may have been interrupted as a result of the events above.\n    if (this.isDragging()) {\n      this._dropContainer._startScrollingIfNecessary(rawX, rawY);\n      this._dropContainer._sortItem(this, x, y, this._pointerDirectionDelta);\n      if (this.constrainPosition) {\n        this._applyPreviewTransform(x, y);\n      } else {\n        this._applyPreviewTransform(x - this._pickupPositionInElement.x, y - this._pickupPositionInElement.y);\n      }\n    }\n  }\n  /**\n   * Creates the element that will be rendered next to the user's pointer\n   * and will be used as a preview of the element that is being dragged.\n   */\n  _createPreviewElement() {\n    const previewConfig = this._previewTemplate;\n    const previewClass = this.previewClass;\n    const previewTemplate = previewConfig ? previewConfig.template : null;\n    let preview;\n    if (previewTemplate && previewConfig) {\n      // Measure the element before we've inserted the preview\n      // since the insertion could throw off the measurement.\n      const rootRect = previewConfig.matchSize ? this._initialClientRect : null;\n      const viewRef = previewConfig.viewContainer.createEmbeddedView(previewTemplate, previewConfig.context);\n      viewRef.detectChanges();\n      preview = getRootNode(viewRef, this._document);\n      this._previewRef = viewRef;\n      if (previewConfig.matchSize) {\n        matchElementSize(preview, rootRect);\n      } else {\n        preview.style.transform = getTransform(this._pickupPositionOnPage.x, this._pickupPositionOnPage.y);\n      }\n    } else {\n      preview = deepCloneNode(this._rootElement);\n      matchElementSize(preview, this._initialClientRect);\n      if (this._initialTransform) {\n        preview.style.transform = this._initialTransform;\n      }\n    }\n    extendStyles(preview.style, {\n      // It's important that we disable the pointer events on the preview, because\n      // it can throw off the `document.elementFromPoint` calls in the `CdkDropList`.\n      'pointer-events': 'none',\n      // We have to reset the margin, because it can throw off positioning relative to the viewport.\n      'margin': '0',\n      'position': 'fixed',\n      'top': '0',\n      'left': '0',\n      'z-index': `${this._config.zIndex || 1000}`\n    }, dragImportantProperties);\n    toggleNativeDragInteractions(preview, false);\n    preview.classList.add('cdk-drag-preview');\n    preview.setAttribute('dir', this._direction);\n    if (previewClass) {\n      if (Array.isArray(previewClass)) {\n        previewClass.forEach(className => preview.classList.add(className));\n      } else {\n        preview.classList.add(previewClass);\n      }\n    }\n    return preview;\n  }\n  /**\n   * Animates the preview element from its current position to the location of the drop placeholder.\n   * @returns Promise that resolves when the animation completes.\n   */\n  _animatePreviewToPlaceholder() {\n    // If the user hasn't moved yet, the transitionend event won't fire.\n    if (!this._hasMoved) {\n      return Promise.resolve();\n    }\n    const placeholderRect = this._placeholder.getBoundingClientRect();\n    // Apply the class that adds a transition to the preview.\n    this._preview.classList.add('cdk-drag-animating');\n    // Move the preview to the placeholder position.\n    this._applyPreviewTransform(placeholderRect.left, placeholderRect.top);\n    // If the element doesn't have a `transition`, the `transitionend` event won't fire. Since\n    // we need to trigger a style recalculation in order for the `cdk-drag-animating` class to\n    // apply its style, we take advantage of the available info to figure out whether we need to\n    // bind the event in the first place.\n    const duration = getTransformTransitionDurationInMs(this._preview);\n    if (duration === 0) {\n      return Promise.resolve();\n    }\n    return this._ngZone.runOutsideAngular(() => {\n      return new Promise(resolve => {\n        const handler = event => {\n          if (!event || _getEventTarget(event) === this._preview && event.propertyName === 'transform') {\n            this._preview?.removeEventListener('transitionend', handler);\n            resolve();\n            clearTimeout(timeout);\n          }\n        };\n        // If a transition is short enough, the browser might not fire the `transitionend` event.\n        // Since we know how long it's supposed to take, add a timeout with a 50% buffer that'll\n        // fire if the transition hasn't completed when it was supposed to.\n        const timeout = setTimeout(handler, duration * 1.5);\n        this._preview.addEventListener('transitionend', handler);\n      });\n    });\n  }\n  /** Creates an element that will be shown instead of the current element while dragging. */\n  _createPlaceholderElement() {\n    const placeholderConfig = this._placeholderTemplate;\n    const placeholderTemplate = placeholderConfig ? placeholderConfig.template : null;\n    let placeholder;\n    if (placeholderTemplate) {\n      this._placeholderRef = placeholderConfig.viewContainer.createEmbeddedView(placeholderTemplate, placeholderConfig.context);\n      this._placeholderRef.detectChanges();\n      placeholder = getRootNode(this._placeholderRef, this._document);\n    } else {\n      placeholder = deepCloneNode(this._rootElement);\n    }\n    // Stop pointer events on the preview so the user can't\n    // interact with it while the preview is animating.\n    placeholder.style.pointerEvents = 'none';\n    placeholder.classList.add('cdk-drag-placeholder');\n    return placeholder;\n  }\n  /**\n   * Figures out the coordinates at which an element was picked up.\n   * @param referenceElement Element that initiated the dragging.\n   * @param event Event that initiated the dragging.\n   */\n  _getPointerPositionInElement(elementRect, referenceElement, event) {\n    const handleElement = referenceElement === this._rootElement ? null : referenceElement;\n    const referenceRect = handleElement ? handleElement.getBoundingClientRect() : elementRect;\n    const point = isTouchEvent(event) ? event.targetTouches[0] : event;\n    const scrollPosition = this._getViewportScrollPosition();\n    const x = point.pageX - referenceRect.left - scrollPosition.left;\n    const y = point.pageY - referenceRect.top - scrollPosition.top;\n    return {\n      x: referenceRect.left - elementRect.left + x,\n      y: referenceRect.top - elementRect.top + y\n    };\n  }\n  /** Determines the point of the page that was touched by the user. */\n  _getPointerPositionOnPage(event) {\n    const scrollPosition = this._getViewportScrollPosition();\n    const point = isTouchEvent(event) ?\n    // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n    // Also note that on real devices we're guaranteed for either `touches` or `changedTouches`\n    // to have a value, but Firefox in device emulation mode has a bug where both can be empty\n    // for `touchstart` and `touchend` so we fall back to a dummy object in order to avoid\n    // throwing an error. The value returned here will be incorrect, but since this only\n    // breaks inside a developer tool and the value is only used for secondary information,\n    // we can get away with it. See https://bugzilla.mozilla.org/show_bug.cgi?id=1615824.\n    event.touches[0] || event.changedTouches[0] || {\n      pageX: 0,\n      pageY: 0\n    } : event;\n    const x = point.pageX - scrollPosition.left;\n    const y = point.pageY - scrollPosition.top;\n    // if dragging SVG element, try to convert from the screen coordinate system to the SVG\n    // coordinate system\n    if (this._ownerSVGElement) {\n      const svgMatrix = this._ownerSVGElement.getScreenCTM();\n      if (svgMatrix) {\n        const svgPoint = this._ownerSVGElement.createSVGPoint();\n        svgPoint.x = x;\n        svgPoint.y = y;\n        return svgPoint.matrixTransform(svgMatrix.inverse());\n      }\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Gets the pointer position on the page, accounting for any position constraints. */\n  _getConstrainedPointerPosition(point) {\n    const dropContainerLock = this._dropContainer ? this._dropContainer.lockAxis : null;\n    let {\n      x,\n      y\n    } = this.constrainPosition ? this.constrainPosition(point, this, this._initialClientRect, this._pickupPositionInElement) : point;\n    if (this.lockAxis === 'x' || dropContainerLock === 'x') {\n      y = this._pickupPositionOnPage.y - (this.constrainPosition ? this._pickupPositionInElement.y : 0);\n    } else if (this.lockAxis === 'y' || dropContainerLock === 'y') {\n      x = this._pickupPositionOnPage.x - (this.constrainPosition ? this._pickupPositionInElement.x : 0);\n    }\n    if (this._boundaryRect) {\n      // If not using a custom constrain we need to account for the pickup position in the element\n      // otherwise we do not need to do this, as it has already been accounted for\n      const {\n        x: pickupX,\n        y: pickupY\n      } = !this.constrainPosition ? this._pickupPositionInElement : {\n        x: 0,\n        y: 0\n      };\n      const boundaryRect = this._boundaryRect;\n      const {\n        width: previewWidth,\n        height: previewHeight\n      } = this._getPreviewRect();\n      const minY = boundaryRect.top + pickupY;\n      const maxY = boundaryRect.bottom - (previewHeight - pickupY);\n      const minX = boundaryRect.left + pickupX;\n      const maxX = boundaryRect.right - (previewWidth - pickupX);\n      x = clamp$1(x, minX, maxX);\n      y = clamp$1(y, minY, maxY);\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the current drag delta, based on the user's current pointer position on the page. */\n  _updatePointerDirectionDelta(pointerPositionOnPage) {\n    const {\n      x,\n      y\n    } = pointerPositionOnPage;\n    const delta = this._pointerDirectionDelta;\n    const positionSinceLastChange = this._pointerPositionAtLastDirectionChange;\n    // Amount of pixels the user has dragged since the last time the direction changed.\n    const changeX = Math.abs(x - positionSinceLastChange.x);\n    const changeY = Math.abs(y - positionSinceLastChange.y);\n    // Because we handle pointer events on a per-pixel basis, we don't want the delta\n    // to change for every pixel, otherwise anything that depends on it can look erratic.\n    // To make the delta more consistent, we track how much the user has moved since the last\n    // delta change and we only update it after it has reached a certain threshold.\n    if (changeX > this._config.pointerDirectionChangeThreshold) {\n      delta.x = x > positionSinceLastChange.x ? 1 : -1;\n      positionSinceLastChange.x = x;\n    }\n    if (changeY > this._config.pointerDirectionChangeThreshold) {\n      delta.y = y > positionSinceLastChange.y ? 1 : -1;\n      positionSinceLastChange.y = y;\n    }\n    return delta;\n  }\n  /** Toggles the native drag interactions, based on how many handles are registered. */\n  _toggleNativeDragInteractions() {\n    if (!this._rootElement || !this._handles) {\n      return;\n    }\n    const shouldEnable = this._handles.length > 0 || !this.isDragging();\n    if (shouldEnable !== this._nativeInteractionsEnabled) {\n      this._nativeInteractionsEnabled = shouldEnable;\n      toggleNativeDragInteractions(this._rootElement, shouldEnable);\n    }\n  }\n  /** Removes the manually-added event listeners from the root element. */\n  _removeRootElementListeners(element) {\n    element.removeEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n    element.removeEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n    element.removeEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n  }\n  /**\n   * Applies a `transform` to the root element, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  _applyRootElementTransform(x, y) {\n    const transform = getTransform(x, y);\n    const styles = this._rootElement.style;\n    // Cache the previous transform amount only after the first drag sequence, because\n    // we don't want our own transforms to stack on top of each other.\n    // Should be excluded none because none + translate3d(x, y, x) is invalid css\n    if (this._initialTransform == null) {\n      this._initialTransform = styles.transform && styles.transform != 'none' ? styles.transform : '';\n    }\n    // Preserve the previous `transform` value, if there was one. Note that we apply our own\n    // transform before the user's, because things like rotation can affect which direction\n    // the element will be translated towards.\n    styles.transform = combineTransforms(transform, this._initialTransform);\n  }\n  /**\n   * Applies a `transform` to the preview, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  _applyPreviewTransform(x, y) {\n    // Only apply the initial transform if the preview is a clone of the original element, otherwise\n    // it could be completely different and the transform might not make sense anymore.\n    const initialTransform = this._previewTemplate?.template ? undefined : this._initialTransform;\n    const transform = getTransform(x, y);\n    this._preview.style.transform = combineTransforms(transform, initialTransform);\n  }\n  /**\n   * Gets the distance that the user has dragged during the current drag sequence.\n   * @param currentPosition Current position of the user's pointer.\n   */\n  _getDragDistance(currentPosition) {\n    const pickupPosition = this._pickupPositionOnPage;\n    if (pickupPosition) {\n      return {\n        x: currentPosition.x - pickupPosition.x,\n        y: currentPosition.y - pickupPosition.y\n      };\n    }\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  /** Cleans up any cached element dimensions that we don't need after dragging has stopped. */\n  _cleanupCachedDimensions() {\n    this._boundaryRect = this._previewRect = undefined;\n    this._parentPositions.clear();\n  }\n  /**\n   * Checks whether the element is still inside its boundary after the viewport has been resized.\n   * If not, the position is adjusted so that the element fits again.\n   */\n  _containInsideBoundaryOnResize() {\n    let {\n      x,\n      y\n    } = this._passiveTransform;\n    if (x === 0 && y === 0 || this.isDragging() || !this._boundaryElement) {\n      return;\n    }\n    // Note: don't use `_clientRectAtStart` here, because we want the latest position.\n    const elementRect = this._rootElement.getBoundingClientRect();\n    const boundaryRect = this._boundaryElement.getBoundingClientRect();\n    // It's possible that the element got hidden away after dragging (e.g. by switching to a\n    // different tab). Don't do anything in this case so we don't clear the user's position.\n    if (boundaryRect.width === 0 && boundaryRect.height === 0 || elementRect.width === 0 && elementRect.height === 0) {\n      return;\n    }\n    const leftOverflow = boundaryRect.left - elementRect.left;\n    const rightOverflow = elementRect.right - boundaryRect.right;\n    const topOverflow = boundaryRect.top - elementRect.top;\n    const bottomOverflow = elementRect.bottom - boundaryRect.bottom;\n    // If the element has become wider than the boundary, we can't\n    // do much to make it fit so we just anchor it to the left.\n    if (boundaryRect.width > elementRect.width) {\n      if (leftOverflow > 0) {\n        x += leftOverflow;\n      }\n      if (rightOverflow > 0) {\n        x -= rightOverflow;\n      }\n    } else {\n      x = 0;\n    }\n    // If the element has become taller than the boundary, we can't\n    // do much to make it fit so we just anchor it to the top.\n    if (boundaryRect.height > elementRect.height) {\n      if (topOverflow > 0) {\n        y += topOverflow;\n      }\n      if (bottomOverflow > 0) {\n        y -= bottomOverflow;\n      }\n    } else {\n      y = 0;\n    }\n    if (x !== this._passiveTransform.x || y !== this._passiveTransform.y) {\n      this.setFreeDragPosition({\n        y,\n        x\n      });\n    }\n  }\n  /** Gets the drag start delay, based on the event type. */\n  _getDragStartDelay(event) {\n    const value = this.dragStartDelay;\n    if (typeof value === 'number') {\n      return value;\n    } else if (isTouchEvent(event)) {\n      return value.touch;\n    }\n    return value ? value.mouse : 0;\n  }\n  /** Updates the internal state of the draggable element when scrolling has occurred. */\n  _updateOnScroll(event) {\n    const scrollDifference = this._parentPositions.handleScroll(event);\n    if (scrollDifference) {\n      const target = _getEventTarget(event);\n      // ClientRect dimensions are based on the scroll position of the page and its parent\n      // node so we have to update the cached boundary ClientRect if the user has scrolled.\n      if (this._boundaryRect && target !== this._boundaryElement && target.contains(this._boundaryElement)) {\n        adjustClientRect(this._boundaryRect, scrollDifference.top, scrollDifference.left);\n      }\n      this._pickupPositionOnPage.x += scrollDifference.left;\n      this._pickupPositionOnPage.y += scrollDifference.top;\n      // If we're in free drag mode, we have to update the active transform, because\n      // it isn't relative to the viewport like the preview inside a drop list.\n      if (!this._dropContainer) {\n        this._activeTransform.x -= scrollDifference.left;\n        this._activeTransform.y -= scrollDifference.top;\n        this._applyRootElementTransform(this._activeTransform.x, this._activeTransform.y);\n      }\n    }\n  }\n  /** Gets the scroll position of the viewport. */\n  _getViewportScrollPosition() {\n    return this._parentPositions.positions.get(this._document)?.scrollPosition || this._parentPositions.getViewportScrollPosition();\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  _getShadowRoot() {\n    if (this._cachedShadowRoot === undefined) {\n      this._cachedShadowRoot = _getShadowRoot(this._rootElement);\n    }\n    return this._cachedShadowRoot;\n  }\n  /** Gets the element into which the drag preview should be inserted. */\n  _getPreviewInsertionPoint(initialParent, shadowRoot) {\n    const previewContainer = this._previewContainer || 'global';\n    if (previewContainer === 'parent') {\n      return initialParent;\n    }\n    if (previewContainer === 'global') {\n      const documentRef = this._document;\n      // We can't use the body if the user is in fullscreen mode,\n      // because the preview will render under the fullscreen element.\n      // TODO(crisbeto): dedupe this with the `FullscreenOverlayContainer` eventually.\n      return shadowRoot || documentRef.fullscreenElement || documentRef.webkitFullscreenElement || documentRef.mozFullScreenElement || documentRef.msFullscreenElement || documentRef.body;\n    }\n    return coerceElement(previewContainer);\n  }\n  /** Lazily resolves and returns the dimensions of the preview. */\n  _getPreviewRect() {\n    // Cache the preview element rect if we haven't cached it already or if\n    // we cached it too early before the element dimensions were computed.\n    if (!this._previewRect || !this._previewRect.width && !this._previewRect.height) {\n      this._previewRect = this._preview ? this._preview.getBoundingClientRect() : this._initialClientRect;\n    }\n    return this._previewRect;\n  }\n  /** Gets a handle that is the target of an event. */\n  _getTargetHandle(event) {\n    return this._handles.find(handle => {\n      return event.target && (event.target === handle || handle.contains(event.target));\n    });\n  }\n}\n/**\n * Gets a 3d `transform` that can be applied to an element.\n * @param x Desired position of the element along the X axis.\n * @param y Desired position of the element along the Y axis.\n */\nfunction getTransform(x, y) {\n  // Round the transforms since some browsers will\n  // blur the elements for sub-pixel transforms.\n  return `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;\n}\n/** Clamps a value between a minimum and a maximum. */\nfunction clamp$1(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n/** Determines whether an event is a touch event. */\nfunction isTouchEvent(event) {\n  // This function is called for every pixel that the user has dragged so we need it to be\n  // as fast as possible. Since we only bind mouse events and touch events, we can assume\n  // that if the event's name starts with `t`, it's a touch event.\n  return event.type[0] === 't';\n}\n/**\n * Gets the root HTML element of an embedded view.\n * If the root is not an HTML element it gets wrapped in one.\n */\nfunction getRootNode(viewRef, _document) {\n  const rootNodes = viewRef.rootNodes;\n  if (rootNodes.length === 1 && rootNodes[0].nodeType === _document.ELEMENT_NODE) {\n    return rootNodes[0];\n  }\n  const wrapper = _document.createElement('div');\n  rootNodes.forEach(node => wrapper.appendChild(node));\n  return wrapper;\n}\n/**\n * Matches the target element's size to the source's size.\n * @param target Element that needs to be resized.\n * @param sourceRect Dimensions of the source element.\n */\nfunction matchElementSize(target, sourceRect) {\n  target.style.width = `${sourceRect.width}px`;\n  target.style.height = `${sourceRect.height}px`;\n  target.style.transform = getTransform(sourceRect.left, sourceRect.top);\n}\n\n/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */\nfunction moveItemInArray(array, fromIndex, toIndex) {\n  const from = clamp(fromIndex, array.length - 1);\n  const to = clamp(toIndex, array.length - 1);\n  if (from === to) {\n    return;\n  }\n  const target = array[from];\n  const delta = to < from ? -1 : 1;\n  for (let i = from; i !== to; i += delta) {\n    array[i] = array[i + delta];\n  }\n  array[to] = target;\n}\n/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */\nfunction transferArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const from = clamp(currentIndex, currentArray.length - 1);\n  const to = clamp(targetIndex, targetArray.length);\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray.splice(from, 1)[0]);\n  }\n}\n/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */\nfunction copyArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const to = clamp(targetIndex, targetArray.length);\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray[currentIndex]);\n  }\n}\n/** Clamps a number between zero and a maximum. */\nfunction clamp(value, max) {\n  return Math.max(0, Math.min(max, value));\n}\n\n/**\n * Strategy that only supports sorting along a single axis.\n * Items are reordered using CSS transforms which allows for sorting to be animated.\n * @docs-private\n */\nclass SingleAxisSortStrategy {\n  constructor(_element, _dragDropRegistry) {\n    this._element = _element;\n    this._dragDropRegistry = _dragDropRegistry;\n    /** Cache of the dimensions of all the items inside the container. */\n    this._itemPositions = [];\n    /** Direction in which the list is oriented. */\n    this.orientation = 'vertical';\n    /**\n     * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n     * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n     * overlap with the swapped item after the swapping occurred.\n     */\n    this._previousSwap = {\n      drag: null,\n      delta: 0,\n      overlaps: false\n    };\n  }\n  /**\n   * To be called when the drag sequence starts.\n   * @param items Items that are currently in the list.\n   */\n  start(items) {\n    this.withItems(items);\n  }\n  /**\n   * To be called when an item is being sorted.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  sort(item, pointerX, pointerY, pointerDelta) {\n    const siblings = this._itemPositions;\n    const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY, pointerDelta);\n    if (newIndex === -1 && siblings.length > 0) {\n      return null;\n    }\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentIndex = siblings.findIndex(currentItem => currentItem.drag === item);\n    const siblingAtNewPosition = siblings[newIndex];\n    const currentPosition = siblings[currentIndex].clientRect;\n    const newPosition = siblingAtNewPosition.clientRect;\n    const delta = currentIndex > newIndex ? 1 : -1;\n    // How many pixels the item's placeholder should be offset.\n    const itemOffset = this._getItemOffsetPx(currentPosition, newPosition, delta);\n    // How many pixels all the other items should be offset.\n    const siblingOffset = this._getSiblingOffsetPx(currentIndex, siblings, delta);\n    // Save the previous order of the items before moving the item to its new index.\n    // We use this to check whether an item has been moved as a result of the sorting.\n    const oldOrder = siblings.slice();\n    // Shuffle the array in place.\n    moveItemInArray(siblings, currentIndex, newIndex);\n    siblings.forEach((sibling, index) => {\n      // Don't do anything if the position hasn't changed.\n      if (oldOrder[index] === sibling) {\n        return;\n      }\n      const isDraggedItem = sibling.drag === item;\n      const offset = isDraggedItem ? itemOffset : siblingOffset;\n      const elementToOffset = isDraggedItem ? item.getPlaceholderElement() : sibling.drag.getRootElement();\n      // Update the offset to reflect the new position.\n      sibling.offset += offset;\n      // Since we're moving the items with a `transform`, we need to adjust their cached\n      // client rects to reflect their new position, as well as swap their positions in the cache.\n      // Note that we shouldn't use `getBoundingClientRect` here to update the cache, because the\n      // elements may be mid-animation which will give us a wrong result.\n      if (isHorizontal) {\n        // Round the transforms since some browsers will\n        // blur the elements, for sub-pixel transforms.\n        elementToOffset.style.transform = combineTransforms(`translate3d(${Math.round(sibling.offset)}px, 0, 0)`, sibling.initialTransform);\n        adjustClientRect(sibling.clientRect, 0, offset);\n      } else {\n        elementToOffset.style.transform = combineTransforms(`translate3d(0, ${Math.round(sibling.offset)}px, 0)`, sibling.initialTransform);\n        adjustClientRect(sibling.clientRect, offset, 0);\n      }\n    });\n    // Note that it's important that we do this after the client rects have been adjusted.\n    this._previousSwap.overlaps = isInsideClientRect(newPosition, pointerX, pointerY);\n    this._previousSwap.drag = siblingAtNewPosition.drag;\n    this._previousSwap.delta = isHorizontal ? pointerDelta.x : pointerDelta.y;\n    return {\n      previousIndex: currentIndex,\n      currentIndex: newIndex\n    };\n  }\n  /**\n   * Called when an item is being moved into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item, pointerX, pointerY, index) {\n    const newIndex = index == null || index < 0 ?\n    // We use the coordinates of where the item entered the drop\n    // zone to figure out at which index it should be inserted.\n    this._getItemIndexFromPointerPosition(item, pointerX, pointerY) : index;\n    const activeDraggables = this._activeDraggables;\n    const currentIndex = activeDraggables.indexOf(item);\n    const placeholder = item.getPlaceholderElement();\n    let newPositionReference = activeDraggables[newIndex];\n    // If the item at the new position is the same as the item that is being dragged,\n    // it means that we're trying to restore the item to its initial position. In this\n    // case we should use the next item from the list as the reference.\n    if (newPositionReference === item) {\n      newPositionReference = activeDraggables[newIndex + 1];\n    }\n    // If we didn't find a new position reference, it means that either the item didn't start off\n    // in this container, or that the item requested to be inserted at the end of the list.\n    if (!newPositionReference && (newIndex == null || newIndex === -1 || newIndex < activeDraggables.length - 1) && this._shouldEnterAsFirstChild(pointerX, pointerY)) {\n      newPositionReference = activeDraggables[0];\n    }\n    // Since the item may be in the `activeDraggables` already (e.g. if the user dragged it\n    // into another container and back again), we have to ensure that it isn't duplicated.\n    if (currentIndex > -1) {\n      activeDraggables.splice(currentIndex, 1);\n    }\n    // Don't use items that are being dragged as a reference, because\n    // their element has been moved down to the bottom of the body.\n    if (newPositionReference && !this._dragDropRegistry.isDragging(newPositionReference)) {\n      const element = newPositionReference.getRootElement();\n      element.parentElement.insertBefore(placeholder, element);\n      activeDraggables.splice(newIndex, 0, item);\n    } else {\n      coerceElement(this._element).appendChild(placeholder);\n      activeDraggables.push(item);\n    }\n    // The transform needs to be cleared so it doesn't throw off the measurements.\n    placeholder.style.transform = '';\n    // Note that usually `start` is called together with `enter` when an item goes into a new\n    // container. This will cache item positions, but we need to refresh them since the amount\n    // of items has changed.\n    this._cacheItemPositions();\n  }\n  /** Sets the items that are currently part of the list. */\n  withItems(items) {\n    this._activeDraggables = items.slice();\n    this._cacheItemPositions();\n  }\n  /** Assigns a sort predicate to the strategy. */\n  withSortPredicate(predicate) {\n    this._sortPredicate = predicate;\n  }\n  /** Resets the strategy to its initial state before dragging was started. */\n  reset() {\n    // TODO(crisbeto): may have to wait for the animations to finish.\n    this._activeDraggables.forEach(item => {\n      const rootElement = item.getRootElement();\n      if (rootElement) {\n        const initialTransform = this._itemPositions.find(p => p.drag === item)?.initialTransform;\n        rootElement.style.transform = initialTransform || '';\n      }\n    });\n    this._itemPositions = [];\n    this._activeDraggables = [];\n    this._previousSwap.drag = null;\n    this._previousSwap.delta = 0;\n    this._previousSwap.overlaps = false;\n  }\n  /**\n   * Gets a snapshot of items currently in the list.\n   * Can include items that we dragged in from another list.\n   */\n  getActiveItemsSnapshot() {\n    return this._activeDraggables;\n  }\n  /** Gets the index of a specific item. */\n  getItemIndex(item) {\n    // Items are sorted always by top/left in the cache, however they flow differently in RTL.\n    // The rest of the logic still stands no matter what orientation we're in, however\n    // we need to invert the array when determining the index.\n    const items = this.orientation === 'horizontal' && this.direction === 'rtl' ? this._itemPositions.slice().reverse() : this._itemPositions;\n    return items.findIndex(currentItem => currentItem.drag === item);\n  }\n  /** Used to notify the strategy that the scroll position has changed. */\n  updateOnScroll(topDifference, leftDifference) {\n    // Since we know the amount that the user has scrolled we can shift all of the\n    // client rectangles ourselves. This is cheaper than re-measuring everything and\n    // we can avoid inconsistent behavior where we might be measuring the element before\n    // its position has changed.\n    this._itemPositions.forEach(({\n      clientRect\n    }) => {\n      adjustClientRect(clientRect, topDifference, leftDifference);\n    });\n    // We need two loops for this, because we want all of the cached\n    // positions to be up-to-date before we re-sort the item.\n    this._itemPositions.forEach(({\n      drag\n    }) => {\n      if (this._dragDropRegistry.isDragging(drag)) {\n        // We need to re-sort the item manually, because the pointer move\n        // events won't be dispatched while the user is scrolling.\n        drag._sortFromLastPointerPosition();\n      }\n    });\n  }\n  /** Refreshes the position cache of the items and sibling containers. */\n  _cacheItemPositions() {\n    const isHorizontal = this.orientation === 'horizontal';\n    this._itemPositions = this._activeDraggables.map(drag => {\n      const elementToMeasure = drag.getVisibleElement();\n      return {\n        drag,\n        offset: 0,\n        initialTransform: elementToMeasure.style.transform || '',\n        clientRect: getMutableClientRect(elementToMeasure)\n      };\n    }).sort((a, b) => {\n      return isHorizontal ? a.clientRect.left - b.clientRect.left : a.clientRect.top - b.clientRect.top;\n    });\n  }\n  /**\n   * Gets the offset in pixels by which the item that is being dragged should be moved.\n   * @param currentPosition Current position of the item.\n   * @param newPosition Position of the item where the current item should be moved.\n   * @param delta Direction in which the user is moving.\n   */\n  _getItemOffsetPx(currentPosition, newPosition, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    let itemOffset = isHorizontal ? newPosition.left - currentPosition.left : newPosition.top - currentPosition.top;\n    // Account for differences in the item width/height.\n    if (delta === -1) {\n      itemOffset += isHorizontal ? newPosition.width - currentPosition.width : newPosition.height - currentPosition.height;\n    }\n    return itemOffset;\n  }\n  /**\n   * Gets the offset in pixels by which the items that aren't being dragged should be moved.\n   * @param currentIndex Index of the item currently being dragged.\n   * @param siblings All of the items in the list.\n   * @param delta Direction in which the user is moving.\n   */\n  _getSiblingOffsetPx(currentIndex, siblings, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentPosition = siblings[currentIndex].clientRect;\n    const immediateSibling = siblings[currentIndex + delta * -1];\n    let siblingOffset = currentPosition[isHorizontal ? 'width' : 'height'] * delta;\n    if (immediateSibling) {\n      const start = isHorizontal ? 'left' : 'top';\n      const end = isHorizontal ? 'right' : 'bottom';\n      // Get the spacing between the start of the current item and the end of the one immediately\n      // after it in the direction in which the user is dragging, or vice versa. We add it to the\n      // offset in order to push the element to where it will be when it's inline and is influenced\n      // by the `margin` of its siblings.\n      if (delta === -1) {\n        siblingOffset -= immediateSibling.clientRect[start] - currentPosition[end];\n      } else {\n        siblingOffset += currentPosition[start] - immediateSibling.clientRect[end];\n      }\n    }\n    return siblingOffset;\n  }\n  /**\n   * Checks if pointer is entering in the first position\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   */\n  _shouldEnterAsFirstChild(pointerX, pointerY) {\n    if (!this._activeDraggables.length) {\n      return false;\n    }\n    const itemPositions = this._itemPositions;\n    const isHorizontal = this.orientation === 'horizontal';\n    // `itemPositions` are sorted by position while `activeDraggables` are sorted by child index\n    // check if container is using some sort of \"reverse\" ordering (eg: flex-direction: row-reverse)\n    const reversed = itemPositions[0].drag !== this._activeDraggables[0];\n    if (reversed) {\n      const lastItemRect = itemPositions[itemPositions.length - 1].clientRect;\n      return isHorizontal ? pointerX >= lastItemRect.right : pointerY >= lastItemRect.bottom;\n    } else {\n      const firstItemRect = itemPositions[0].clientRect;\n      return isHorizontal ? pointerX <= firstItemRect.left : pointerY <= firstItemRect.top;\n    }\n  }\n  /**\n   * Gets the index of an item in the drop container, based on the position of the user's pointer.\n   * @param item Item that is being sorted.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   * @param delta Direction in which the user is moving their pointer.\n   */\n  _getItemIndexFromPointerPosition(item, pointerX, pointerY, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const index = this._itemPositions.findIndex(({\n      drag,\n      clientRect\n    }) => {\n      // Skip the item itself.\n      if (drag === item) {\n        return false;\n      }\n      if (delta) {\n        const direction = isHorizontal ? delta.x : delta.y;\n        // If the user is still hovering over the same item as last time, their cursor hasn't left\n        // the item after we made the swap, and they didn't change the direction in which they're\n        // dragging, we don't consider it a direction swap.\n        if (drag === this._previousSwap.drag && this._previousSwap.overlaps && direction === this._previousSwap.delta) {\n          return false;\n        }\n      }\n      return isHorizontal ?\n      // Round these down since most browsers report client rects with\n      // sub-pixel precision, whereas the pointer coordinates are rounded to pixels.\n      pointerX >= Math.floor(clientRect.left) && pointerX < Math.floor(clientRect.right) : pointerY >= Math.floor(clientRect.top) && pointerY < Math.floor(clientRect.bottom);\n    });\n    return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n  }\n}\n\n/**\n * Proximity, as a ratio to width/height, at which a\n * dragged item will affect the drop container.\n */\nconst DROP_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Proximity, as a ratio to width/height at which to start auto-scrolling the drop list or the\n * viewport. The value comes from trying it out manually until it feels right.\n */\nconst SCROLL_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */\nclass DropListRef {\n  constructor(element, _dragDropRegistry, _document, _ngZone, _viewportRuler) {\n    this._dragDropRegistry = _dragDropRegistry;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    /** Whether starting a dragging sequence from this container is disabled. */\n    this.disabled = false;\n    /** Whether sorting items within the list is disabled. */\n    this.sortingDisabled = false;\n    /**\n     * Whether auto-scrolling the view when the user\n     * moves their pointer close to the edges is disabled.\n     */\n    this.autoScrollDisabled = false;\n    /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n    this.autoScrollStep = 2;\n    /**\n     * Function that is used to determine whether an item\n     * is allowed to be moved into a drop container.\n     */\n    this.enterPredicate = () => true;\n    /** Function that is used to determine whether an item can be sorted into a particular index. */\n    this.sortPredicate = () => true;\n    /** Emits right before dragging has started. */\n    this.beforeStarted = new Subject();\n    /**\n     * Emits when the user has moved a new drag item into this container.\n     */\n    this.entered = new Subject();\n    /**\n     * Emits when the user removes an item from the container\n     * by dragging it into another container.\n     */\n    this.exited = new Subject();\n    /** Emits when the user drops an item inside the container. */\n    this.dropped = new Subject();\n    /** Emits as the user is swapping items while actively dragging. */\n    this.sorted = new Subject();\n    /** Emits when a dragging sequence is started in a list connected to the current one. */\n    this.receivingStarted = new Subject();\n    /** Emits when a dragging sequence is stopped from a list connected to the current one. */\n    this.receivingStopped = new Subject();\n    /** Whether an item in the list is being dragged. */\n    this._isDragging = false;\n    /** Draggable items in the container. */\n    this._draggables = [];\n    /** Drop lists that are connected to the current one. */\n    this._siblings = [];\n    /** Connected siblings that currently have a dragged item. */\n    this._activeSiblings = new Set();\n    /** Subscription to the window being scrolled. */\n    this._viewportScrollSubscription = Subscription.EMPTY;\n    /** Vertical direction in which the list is currently scrolling. */\n    this._verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n    /** Horizontal direction in which the list is currently scrolling. */\n    this._horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n    /** Used to signal to the current auto-scroll sequence when to stop. */\n    this._stopScrollTimers = new Subject();\n    /** Shadow root of the current element. Necessary for `elementFromPoint` to resolve correctly. */\n    this._cachedShadowRoot = null;\n    /** Starts the interval that'll auto-scroll the element. */\n    this._startScrollInterval = () => {\n      this._stopScrolling();\n      interval(0, animationFrameScheduler).pipe(takeUntil(this._stopScrollTimers)).subscribe(() => {\n        const node = this._scrollNode;\n        const scrollStep = this.autoScrollStep;\n        if (this._verticalScrollDirection === 1 /* AutoScrollVerticalDirection.UP */) {\n          node.scrollBy(0, -scrollStep);\n        } else if (this._verticalScrollDirection === 2 /* AutoScrollVerticalDirection.DOWN */) {\n          node.scrollBy(0, scrollStep);\n        }\n        if (this._horizontalScrollDirection === 1 /* AutoScrollHorizontalDirection.LEFT */) {\n          node.scrollBy(-scrollStep, 0);\n        } else if (this._horizontalScrollDirection === 2 /* AutoScrollHorizontalDirection.RIGHT */) {\n          node.scrollBy(scrollStep, 0);\n        }\n      });\n    };\n    this.element = coerceElement(element);\n    this._document = _document;\n    this.withScrollableParents([this.element]);\n    _dragDropRegistry.registerDropContainer(this);\n    this._parentPositions = new ParentPositionTracker(_document);\n    this._sortStrategy = new SingleAxisSortStrategy(this.element, _dragDropRegistry);\n    this._sortStrategy.withSortPredicate((index, item) => this.sortPredicate(index, item, this));\n  }\n  /** Removes the drop list functionality from the DOM element. */\n  dispose() {\n    this._stopScrolling();\n    this._stopScrollTimers.complete();\n    this._viewportScrollSubscription.unsubscribe();\n    this.beforeStarted.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this.sorted.complete();\n    this.receivingStarted.complete();\n    this.receivingStopped.complete();\n    this._activeSiblings.clear();\n    this._scrollNode = null;\n    this._parentPositions.clear();\n    this._dragDropRegistry.removeDropContainer(this);\n  }\n  /** Whether an item from this list is currently being dragged. */\n  isDragging() {\n    return this._isDragging;\n  }\n  /** Starts dragging an item. */\n  start() {\n    this._draggingStarted();\n    this._notifyReceivingSiblings();\n  }\n  /**\n   * Attempts to move an item into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item, pointerX, pointerY, index) {\n    this._draggingStarted();\n    // If sorting is disabled, we want the item to return to its starting\n    // position if the user is returning it to its initial container.\n    if (index == null && this.sortingDisabled) {\n      index = this._draggables.indexOf(item);\n    }\n    this._sortStrategy.enter(item, pointerX, pointerY, index);\n    // Note that this usually happens inside `_draggingStarted` as well, but the dimensions\n    // can change when the sort strategy moves the item around inside `enter`.\n    this._cacheParentPositions();\n    // Notify siblings at the end so that the item has been inserted into the `activeDraggables`.\n    this._notifyReceivingSiblings();\n    this.entered.next({\n      item,\n      container: this,\n      currentIndex: this.getItemIndex(item)\n    });\n  }\n  /**\n   * Removes an item from the container after it was dragged into another container by the user.\n   * @param item Item that was dragged out.\n   */\n  exit(item) {\n    this._reset();\n    this.exited.next({\n      item,\n      container: this\n    });\n  }\n  /**\n   * Drops an item into this container.\n   * @param item Item being dropped into the container.\n   * @param currentIndex Index at which the item should be inserted.\n   * @param previousIndex Index of the item when dragging started.\n   * @param previousContainer Container from which the item got dragged in.\n   * @param isPointerOverContainer Whether the user's pointer was over the\n   *    container when the item was dropped.\n   * @param distance Distance the user has dragged since the start of the dragging sequence.\n   * @param event Event that triggered the dropping sequence.\n   *\n   * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n   */\n  drop(item, currentIndex, previousIndex, previousContainer, isPointerOverContainer, distance, dropPoint, event = {}) {\n    this._reset();\n    this.dropped.next({\n      item,\n      currentIndex,\n      previousIndex,\n      container: this,\n      previousContainer,\n      isPointerOverContainer,\n      distance,\n      dropPoint,\n      event\n    });\n  }\n  /**\n   * Sets the draggable items that are a part of this list.\n   * @param items Items that are a part of this list.\n   */\n  withItems(items) {\n    const previousItems = this._draggables;\n    this._draggables = items;\n    items.forEach(item => item._withDropContainer(this));\n    if (this.isDragging()) {\n      const draggedItems = previousItems.filter(item => item.isDragging());\n      // If all of the items being dragged were removed\n      // from the list, abort the current drag sequence.\n      if (draggedItems.every(item => items.indexOf(item) === -1)) {\n        this._reset();\n      } else {\n        this._sortStrategy.withItems(this._draggables);\n      }\n    }\n    return this;\n  }\n  /** Sets the layout direction of the drop list. */\n  withDirection(direction) {\n    this._sortStrategy.direction = direction;\n    return this;\n  }\n  /**\n   * Sets the containers that are connected to this one. When two or more containers are\n   * connected, the user will be allowed to transfer items between them.\n   * @param connectedTo Other containers that the current containers should be connected to.\n   */\n  connectedTo(connectedTo) {\n    this._siblings = connectedTo.slice();\n    return this;\n  }\n  /**\n   * Sets the orientation of the container.\n   * @param orientation New orientation for the container.\n   */\n  withOrientation(orientation) {\n    // TODO(crisbeto): eventually we should be constructing the new sort strategy here based on\n    // the new orientation. For now we can assume that it'll always be `SingleAxisSortStrategy`.\n    this._sortStrategy.orientation = orientation;\n    return this;\n  }\n  /**\n   * Sets which parent elements are can be scrolled while the user is dragging.\n   * @param elements Elements that can be scrolled.\n   */\n  withScrollableParents(elements) {\n    const element = coerceElement(this.element);\n    // We always allow the current element to be scrollable\n    // so we need to ensure that it's in the array.\n    this._scrollableElements = elements.indexOf(element) === -1 ? [element, ...elements] : elements.slice();\n    return this;\n  }\n  /** Gets the scrollable parents that are registered with this drop container. */\n  getScrollableParents() {\n    return this._scrollableElements;\n  }\n  /**\n   * Figures out the index of an item in the container.\n   * @param item Item whose index should be determined.\n   */\n  getItemIndex(item) {\n    return this._isDragging ? this._sortStrategy.getItemIndex(item) : this._draggables.indexOf(item);\n  }\n  /**\n   * Whether the list is able to receive the item that\n   * is currently being dragged inside a connected drop list.\n   */\n  isReceiving() {\n    return this._activeSiblings.size > 0;\n  }\n  /**\n   * Sorts an item inside the container based on its position.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  _sortItem(item, pointerX, pointerY, pointerDelta) {\n    // Don't sort the item if sorting is disabled or it's out of range.\n    if (this.sortingDisabled || !this._clientRect || !isPointerNearClientRect(this._clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n      return;\n    }\n    const result = this._sortStrategy.sort(item, pointerX, pointerY, pointerDelta);\n    if (result) {\n      this.sorted.next({\n        previousIndex: result.previousIndex,\n        currentIndex: result.currentIndex,\n        container: this,\n        item\n      });\n    }\n  }\n  /**\n   * Checks whether the user's pointer is close to the edges of either the\n   * viewport or the drop list and starts the auto-scroll sequence.\n   * @param pointerX User's pointer position along the x axis.\n   * @param pointerY User's pointer position along the y axis.\n   */\n  _startScrollingIfNecessary(pointerX, pointerY) {\n    if (this.autoScrollDisabled) {\n      return;\n    }\n    let scrollNode;\n    let verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n    let horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n    // Check whether we should start scrolling any of the parent containers.\n    this._parentPositions.positions.forEach((position, element) => {\n      // We have special handling for the `document` below. Also this would be\n      // nicer with a  for...of loop, but it requires changing a compiler flag.\n      if (element === this._document || !position.clientRect || scrollNode) {\n        return;\n      }\n      if (isPointerNearClientRect(position.clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n        [verticalScrollDirection, horizontalScrollDirection] = getElementScrollDirections(element, position.clientRect, pointerX, pointerY);\n        if (verticalScrollDirection || horizontalScrollDirection) {\n          scrollNode = element;\n        }\n      }\n    });\n    // Otherwise check if we can start scrolling the viewport.\n    if (!verticalScrollDirection && !horizontalScrollDirection) {\n      const {\n        width,\n        height\n      } = this._viewportRuler.getViewportSize();\n      const clientRect = {\n        width,\n        height,\n        top: 0,\n        right: width,\n        bottom: height,\n        left: 0\n      };\n      verticalScrollDirection = getVerticalScrollDirection(clientRect, pointerY);\n      horizontalScrollDirection = getHorizontalScrollDirection(clientRect, pointerX);\n      scrollNode = window;\n    }\n    if (scrollNode && (verticalScrollDirection !== this._verticalScrollDirection || horizontalScrollDirection !== this._horizontalScrollDirection || scrollNode !== this._scrollNode)) {\n      this._verticalScrollDirection = verticalScrollDirection;\n      this._horizontalScrollDirection = horizontalScrollDirection;\n      this._scrollNode = scrollNode;\n      if ((verticalScrollDirection || horizontalScrollDirection) && scrollNode) {\n        this._ngZone.runOutsideAngular(this._startScrollInterval);\n      } else {\n        this._stopScrolling();\n      }\n    }\n  }\n  /** Stops any currently-running auto-scroll sequences. */\n  _stopScrolling() {\n    this._stopScrollTimers.next();\n  }\n  /** Starts the dragging sequence within the list. */\n  _draggingStarted() {\n    const styles = coerceElement(this.element).style;\n    this.beforeStarted.next();\n    this._isDragging = true;\n    // We need to disable scroll snapping while the user is dragging, because it breaks automatic\n    // scrolling. The browser seems to round the value based on the snapping points which means\n    // that we can't increment/decrement the scroll position.\n    this._initialScrollSnap = styles.msScrollSnapType || styles.scrollSnapType || '';\n    styles.scrollSnapType = styles.msScrollSnapType = 'none';\n    this._sortStrategy.start(this._draggables);\n    this._cacheParentPositions();\n    this._viewportScrollSubscription.unsubscribe();\n    this._listenToScrollEvents();\n  }\n  /** Caches the positions of the configured scrollable parents. */\n  _cacheParentPositions() {\n    const element = coerceElement(this.element);\n    this._parentPositions.cache(this._scrollableElements);\n    // The list element is always in the `scrollableElements`\n    // so we can take advantage of the cached `ClientRect`.\n    this._clientRect = this._parentPositions.positions.get(element).clientRect;\n  }\n  /** Resets the container to its initial state. */\n  _reset() {\n    this._isDragging = false;\n    const styles = coerceElement(this.element).style;\n    styles.scrollSnapType = styles.msScrollSnapType = this._initialScrollSnap;\n    this._siblings.forEach(sibling => sibling._stopReceiving(this));\n    this._sortStrategy.reset();\n    this._stopScrolling();\n    this._viewportScrollSubscription.unsubscribe();\n    this._parentPositions.clear();\n  }\n  /**\n   * Checks whether the user's pointer is positioned over the container.\n   * @param x Pointer position along the X axis.\n   * @param y Pointer position along the Y axis.\n   */\n  _isOverContainer(x, y) {\n    return this._clientRect != null && isInsideClientRect(this._clientRect, x, y);\n  }\n  /**\n   * Figures out whether an item should be moved into a sibling\n   * drop container, based on its current position.\n   * @param item Drag item that is being moved.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _getSiblingContainerFromPosition(item, x, y) {\n    return this._siblings.find(sibling => sibling._canReceive(item, x, y));\n  }\n  /**\n   * Checks whether the drop list can receive the passed-in item.\n   * @param item Item that is being dragged into the list.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _canReceive(item, x, y) {\n    if (!this._clientRect || !isInsideClientRect(this._clientRect, x, y) || !this.enterPredicate(item, this)) {\n      return false;\n    }\n    const elementFromPoint = this._getShadowRoot().elementFromPoint(x, y);\n    // If there's no element at the pointer position, then\n    // the client rect is probably scrolled out of the view.\n    if (!elementFromPoint) {\n      return false;\n    }\n    const nativeElement = coerceElement(this.element);\n    // The `ClientRect`, that we're using to find the container over which the user is\n    // hovering, doesn't give us any information on whether the element has been scrolled\n    // out of the view or whether it's overlapping with other containers. This means that\n    // we could end up transferring the item into a container that's invisible or is positioned\n    // below another one. We use the result from `elementFromPoint` to get the top-most element\n    // at the pointer position and to find whether it's one of the intersecting drop containers.\n    return elementFromPoint === nativeElement || nativeElement.contains(elementFromPoint);\n  }\n  /**\n   * Called by one of the connected drop lists when a dragging sequence has started.\n   * @param sibling Sibling in which dragging has started.\n   */\n  _startReceiving(sibling, items) {\n    const activeSiblings = this._activeSiblings;\n    if (!activeSiblings.has(sibling) && items.every(item => {\n      // Note that we have to add an exception to the `enterPredicate` for items that started off\n      // in this drop list. The drag ref has logic that allows an item to return to its initial\n      // container, if it has left the initial container and none of the connected containers\n      // allow it to enter. See `DragRef._updateActiveDropContainer` for more context.\n      return this.enterPredicate(item, this) || this._draggables.indexOf(item) > -1;\n    })) {\n      activeSiblings.add(sibling);\n      this._cacheParentPositions();\n      this._listenToScrollEvents();\n      this.receivingStarted.next({\n        initiator: sibling,\n        receiver: this,\n        items\n      });\n    }\n  }\n  /**\n   * Called by a connected drop list when dragging has stopped.\n   * @param sibling Sibling whose dragging has stopped.\n   */\n  _stopReceiving(sibling) {\n    this._activeSiblings.delete(sibling);\n    this._viewportScrollSubscription.unsubscribe();\n    this.receivingStopped.next({\n      initiator: sibling,\n      receiver: this\n    });\n  }\n  /**\n   * Starts listening to scroll events on the viewport.\n   * Used for updating the internal state of the list.\n   */\n  _listenToScrollEvents() {\n    this._viewportScrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(event => {\n      if (this.isDragging()) {\n        const scrollDifference = this._parentPositions.handleScroll(event);\n        if (scrollDifference) {\n          this._sortStrategy.updateOnScroll(scrollDifference.top, scrollDifference.left);\n        }\n      } else if (this.isReceiving()) {\n        this._cacheParentPositions();\n      }\n    });\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  _getShadowRoot() {\n    if (!this._cachedShadowRoot) {\n      const shadowRoot = _getShadowRoot(coerceElement(this.element));\n      this._cachedShadowRoot = shadowRoot || this._document;\n    }\n    return this._cachedShadowRoot;\n  }\n  /** Notifies any siblings that may potentially receive the item. */\n  _notifyReceivingSiblings() {\n    const draggedItems = this._sortStrategy.getActiveItemsSnapshot().filter(item => item.isDragging());\n    this._siblings.forEach(sibling => sibling._startReceiving(this, draggedItems));\n  }\n}\n/**\n * Gets whether the vertical auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getVerticalScrollDirection(clientRect, pointerY) {\n  const {\n    top,\n    bottom,\n    height\n  } = clientRect;\n  const yThreshold = height * SCROLL_PROXIMITY_THRESHOLD;\n  if (pointerY >= top - yThreshold && pointerY <= top + yThreshold) {\n    return 1 /* AutoScrollVerticalDirection.UP */;\n  } else if (pointerY >= bottom - yThreshold && pointerY <= bottom + yThreshold) {\n    return 2 /* AutoScrollVerticalDirection.DOWN */;\n  }\n\n  return 0 /* AutoScrollVerticalDirection.NONE */;\n}\n/**\n * Gets whether the horizontal auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerX Position of the user's pointer along the x axis.\n */\nfunction getHorizontalScrollDirection(clientRect, pointerX) {\n  const {\n    left,\n    right,\n    width\n  } = clientRect;\n  const xThreshold = width * SCROLL_PROXIMITY_THRESHOLD;\n  if (pointerX >= left - xThreshold && pointerX <= left + xThreshold) {\n    return 1 /* AutoScrollHorizontalDirection.LEFT */;\n  } else if (pointerX >= right - xThreshold && pointerX <= right + xThreshold) {\n    return 2 /* AutoScrollHorizontalDirection.RIGHT */;\n  }\n\n  return 0 /* AutoScrollHorizontalDirection.NONE */;\n}\n/**\n * Gets the directions in which an element node should be scrolled,\n * assuming that the user's pointer is already within it scrollable region.\n * @param element Element for which we should calculate the scroll direction.\n * @param clientRect Bounding client rectangle of the element.\n * @param pointerX Position of the user's pointer along the x axis.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getElementScrollDirections(element, clientRect, pointerX, pointerY) {\n  const computedVertical = getVerticalScrollDirection(clientRect, pointerY);\n  const computedHorizontal = getHorizontalScrollDirection(clientRect, pointerX);\n  let verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n  let horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n  // Note that we here we do some extra checks for whether the element is actually scrollable in\n  // a certain direction and we only assign the scroll direction if it is. We do this so that we\n  // can allow other elements to be scrolled, if the current element can't be scrolled anymore.\n  // This allows us to handle cases where the scroll regions of two scrollable elements overlap.\n  if (computedVertical) {\n    const scrollTop = element.scrollTop;\n    if (computedVertical === 1 /* AutoScrollVerticalDirection.UP */) {\n      if (scrollTop > 0) {\n        verticalScrollDirection = 1 /* AutoScrollVerticalDirection.UP */;\n      }\n    } else if (element.scrollHeight - scrollTop > element.clientHeight) {\n      verticalScrollDirection = 2 /* AutoScrollVerticalDirection.DOWN */;\n    }\n  }\n\n  if (computedHorizontal) {\n    const scrollLeft = element.scrollLeft;\n    if (computedHorizontal === 1 /* AutoScrollHorizontalDirection.LEFT */) {\n      if (scrollLeft > 0) {\n        horizontalScrollDirection = 1 /* AutoScrollHorizontalDirection.LEFT */;\n      }\n    } else if (element.scrollWidth - scrollLeft > element.clientWidth) {\n      horizontalScrollDirection = 2 /* AutoScrollHorizontalDirection.RIGHT */;\n    }\n  }\n\n  return [verticalScrollDirection, horizontalScrollDirection];\n}\n\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = /*#__PURE__*/normalizePassiveListenerOptions({\n  passive: false,\n  capture: true\n});\n/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */\n// Note: this class is generic, rather than referencing CdkDrag and CdkDropList directly, in order\n// to avoid circular imports. If we were to reference them here, importing the registry into the\n// classes that are registering themselves will introduce a circular import.\nlet DragDropRegistry = /*#__PURE__*/(() => {\n  class DragDropRegistry {\n    constructor(_ngZone, _document) {\n      this._ngZone = _ngZone;\n      /** Registered drop container instances. */\n      this._dropInstances = new Set();\n      /** Registered drag item instances. */\n      this._dragInstances = new Set();\n      /** Drag item instances that are currently being dragged. */\n      this._activeDragInstances = [];\n      /** Keeps track of the event listeners that we've bound to the `document`. */\n      this._globalListeners = new Map();\n      /**\n       * Predicate function to check if an item is being dragged.  Moved out into a property,\n       * because it'll be called a lot and we don't want to create a new function every time.\n       */\n      this._draggingPredicate = item => item.isDragging();\n      /**\n       * Emits the `touchmove` or `mousemove` events that are dispatched\n       * while the user is dragging a drag item instance.\n       */\n      this.pointerMove = new Subject();\n      /**\n       * Emits the `touchend` or `mouseup` events that are dispatched\n       * while the user is dragging a drag item instance.\n       */\n      this.pointerUp = new Subject();\n      /**\n       * Emits when the viewport has been scrolled while the user is dragging an item.\n       * @deprecated To be turned into a private member. Use the `scrolled` method instead.\n       * @breaking-change 13.0.0\n       */\n      this.scroll = new Subject();\n      /**\n       * Event listener that will prevent the default browser action while the user is dragging.\n       * @param event Event whose default action should be prevented.\n       */\n      this._preventDefaultWhileDragging = event => {\n        if (this._activeDragInstances.length > 0) {\n          event.preventDefault();\n        }\n      };\n      /** Event listener for `touchmove` that is bound even if no dragging is happening. */\n      this._persistentTouchmoveListener = event => {\n        if (this._activeDragInstances.length > 0) {\n          // Note that we only want to prevent the default action after dragging has actually started.\n          // Usually this is the same time at which the item is added to the `_activeDragInstances`,\n          // but it could be pushed back if the user has set up a drag delay or threshold.\n          if (this._activeDragInstances.some(this._draggingPredicate)) {\n            event.preventDefault();\n          }\n          this.pointerMove.next(event);\n        }\n      };\n      this._document = _document;\n    }\n    /** Adds a drop container to the registry. */\n    registerDropContainer(drop) {\n      if (!this._dropInstances.has(drop)) {\n        this._dropInstances.add(drop);\n      }\n    }\n    /** Adds a drag item instance to the registry. */\n    registerDragItem(drag) {\n      this._dragInstances.add(drag);\n      // The `touchmove` event gets bound once, ahead of time, because WebKit\n      // won't preventDefault on a dynamically-added `touchmove` listener.\n      // See https://bugs.webkit.org/show_bug.cgi?id=184250.\n      if (this._dragInstances.size === 1) {\n        this._ngZone.runOutsideAngular(() => {\n          // The event handler has to be explicitly active,\n          // because newer browsers make it passive by default.\n          this._document.addEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n        });\n      }\n    }\n    /** Removes a drop container from the registry. */\n    removeDropContainer(drop) {\n      this._dropInstances.delete(drop);\n    }\n    /** Removes a drag item instance from the registry. */\n    removeDragItem(drag) {\n      this._dragInstances.delete(drag);\n      this.stopDragging(drag);\n      if (this._dragInstances.size === 0) {\n        this._document.removeEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n      }\n    }\n    /**\n     * Starts the dragging sequence for a drag instance.\n     * @param drag Drag instance which is being dragged.\n     * @param event Event that initiated the dragging.\n     */\n    startDragging(drag, event) {\n      // Do not process the same drag twice to avoid memory leaks and redundant listeners\n      if (this._activeDragInstances.indexOf(drag) > -1) {\n        return;\n      }\n      this._activeDragInstances.push(drag);\n      if (this._activeDragInstances.length === 1) {\n        const isTouchEvent = event.type.startsWith('touch');\n        // We explicitly bind __active__ listeners here, because newer browsers will default to\n        // passive ones for `mousemove` and `touchmove`. The events need to be active, because we\n        // use `preventDefault` to prevent the page from scrolling while the user is dragging.\n        this._globalListeners.set(isTouchEvent ? 'touchend' : 'mouseup', {\n          handler: e => this.pointerUp.next(e),\n          options: true\n        }).set('scroll', {\n          handler: e => this.scroll.next(e),\n          // Use capturing so that we pick up scroll changes in any scrollable nodes that aren't\n          // the document. See https://github.com/angular/components/issues/17144.\n          options: true\n        })\n        // Preventing the default action on `mousemove` isn't enough to disable text selection\n        // on Safari so we need to prevent the selection event as well. Alternatively this can\n        // be done by setting `user-select: none` on the `body`, however it has causes a style\n        // recalculation which can be expensive on pages with a lot of elements.\n        .set('selectstart', {\n          handler: this._preventDefaultWhileDragging,\n          options: activeCapturingEventOptions\n        });\n        // We don't have to bind a move event for touch drag sequences, because\n        // we already have a persistent global one bound from `registerDragItem`.\n        if (!isTouchEvent) {\n          this._globalListeners.set('mousemove', {\n            handler: e => this.pointerMove.next(e),\n            options: activeCapturingEventOptions\n          });\n        }\n        this._ngZone.runOutsideAngular(() => {\n          this._globalListeners.forEach((config, name) => {\n            this._document.addEventListener(name, config.handler, config.options);\n          });\n        });\n      }\n    }\n    /** Stops dragging a drag item instance. */\n    stopDragging(drag) {\n      const index = this._activeDragInstances.indexOf(drag);\n      if (index > -1) {\n        this._activeDragInstances.splice(index, 1);\n        if (this._activeDragInstances.length === 0) {\n          this._clearGlobalListeners();\n        }\n      }\n    }\n    /** Gets whether a drag item instance is currently being dragged. */\n    isDragging(drag) {\n      return this._activeDragInstances.indexOf(drag) > -1;\n    }\n    /**\n     * Gets a stream that will emit when any element on the page is scrolled while an item is being\n     * dragged.\n     * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n     *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n     *   be used to include an additional top-level listener at the shadow root level.\n     */\n    scrolled(shadowRoot) {\n      const streams = [this.scroll];\n      if (shadowRoot && shadowRoot !== this._document) {\n        // Note that this is basically the same as `fromEvent` from rxjs, but we do it ourselves,\n        // because we want to guarantee that the event is bound outside of the `NgZone`. With\n        // `fromEvent` it'll only happen if the subscription is outside the `NgZone`.\n        streams.push(new Observable(observer => {\n          return this._ngZone.runOutsideAngular(() => {\n            const eventOptions = true;\n            const callback = event => {\n              if (this._activeDragInstances.length) {\n                observer.next(event);\n              }\n            };\n            shadowRoot.addEventListener('scroll', callback, eventOptions);\n            return () => {\n              shadowRoot.removeEventListener('scroll', callback, eventOptions);\n            };\n          });\n        }));\n      }\n      return merge(...streams);\n    }\n    ngOnDestroy() {\n      this._dragInstances.forEach(instance => this.removeDragItem(instance));\n      this._dropInstances.forEach(instance => this.removeDropContainer(instance));\n      this._clearGlobalListeners();\n      this.pointerMove.complete();\n      this.pointerUp.complete();\n    }\n    /** Clears out the global event listeners from the `document`. */\n    _clearGlobalListeners() {\n      this._globalListeners.forEach((config, name) => {\n        this._document.removeEventListener(name, config.handler, config.options);\n      });\n      this._globalListeners.clear();\n    }\n    static {\n      this.ɵfac = function DragDropRegistry_Factory(t) {\n        return new (t || DragDropRegistry)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: DragDropRegistry,\n        factory: DragDropRegistry.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return DragDropRegistry;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Default configuration to be used when creating a `DragRef`. */\nconst DEFAULT_CONFIG = {\n  dragStartThreshold: 5,\n  pointerDirectionChangeThreshold: 5\n};\n/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */\nlet DragDrop = /*#__PURE__*/(() => {\n  class DragDrop {\n    constructor(_document, _ngZone, _viewportRuler, _dragDropRegistry) {\n      this._document = _document;\n      this._ngZone = _ngZone;\n      this._viewportRuler = _viewportRuler;\n      this._dragDropRegistry = _dragDropRegistry;\n    }\n    /**\n     * Turns an element into a draggable item.\n     * @param element Element to which to attach the dragging functionality.\n     * @param config Object used to configure the dragging behavior.\n     */\n    createDrag(element, config = DEFAULT_CONFIG) {\n      return new DragRef(element, config, this._document, this._ngZone, this._viewportRuler, this._dragDropRegistry);\n    }\n    /**\n     * Turns an element into a drop list.\n     * @param element Element to which to attach the drop list functionality.\n     */\n    createDropList(element) {\n      return new DropListRef(element, this._dragDropRegistry, this._document, this._ngZone, this._viewportRuler);\n    }\n    static {\n      this.ɵfac = function DragDrop_Factory(t) {\n        return new (t || DragDrop)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(DragDropRegistry));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: DragDrop,\n        factory: DragDrop.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return DragDrop;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */\nconst CDK_DRAG_PARENT = /*#__PURE__*/new InjectionToken('CDK_DRAG_PARENT');\n\n/**\n * Asserts that a particular node is an element.\n * @param node Node to be checked.\n * @param name Name to attach to the error message.\n */\nfunction assertElementNode(node, name) {\n  if (node.nodeType !== 1) {\n    throw Error(`${name} must be attached to an element node. ` + `Currently attached to \"${node.nodeName}\".`);\n  }\n}\n\n/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_HANDLE = /*#__PURE__*/new InjectionToken('CdkDragHandle');\n/** Handle that can be used to drag a CdkDrag instance. */\nlet CdkDragHandle = /*#__PURE__*/(() => {\n  class CdkDragHandle {\n    /** Whether starting to drag through this handle is disabled. */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n      this._stateChanges.next(this);\n    }\n    constructor(element, parentDrag) {\n      this.element = element;\n      /** Emits when the state of the handle has changed. */\n      this._stateChanges = new Subject();\n      this._disabled = false;\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        assertElementNode(element.nativeElement, 'cdkDragHandle');\n      }\n      this._parentDrag = parentDrag;\n    }\n    ngOnDestroy() {\n      this._stateChanges.complete();\n    }\n    static {\n      this.ɵfac = function CdkDragHandle_Factory(t) {\n        return new (t || CdkDragHandle)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CDK_DRAG_PARENT, 12));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkDragHandle,\n        selectors: [[\"\", \"cdkDragHandle\", \"\"]],\n        hostAttrs: [1, \"cdk-drag-handle\"],\n        inputs: {\n          disabled: [\"cdkDragHandleDisabled\", \"disabled\"]\n        },\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: CDK_DRAG_HANDLE,\n          useExisting: CdkDragHandle\n        }])]\n      });\n    }\n  }\n  return CdkDragHandle;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PLACEHOLDER = /*#__PURE__*/new InjectionToken('CdkDragPlaceholder');\n/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */\nlet CdkDragPlaceholder = /*#__PURE__*/(() => {\n  class CdkDragPlaceholder {\n    constructor(templateRef) {\n      this.templateRef = templateRef;\n    }\n    static {\n      this.ɵfac = function CdkDragPlaceholder_Factory(t) {\n        return new (t || CdkDragPlaceholder)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkDragPlaceholder,\n        selectors: [[\"ng-template\", \"cdkDragPlaceholder\", \"\"]],\n        inputs: {\n          data: \"data\"\n        },\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: CDK_DRAG_PLACEHOLDER,\n          useExisting: CdkDragPlaceholder\n        }])]\n      });\n    }\n  }\n  return CdkDragPlaceholder;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PREVIEW = /*#__PURE__*/new InjectionToken('CdkDragPreview');\n/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */\nlet CdkDragPreview = /*#__PURE__*/(() => {\n  class CdkDragPreview {\n    /** Whether the preview should preserve the same size as the item that is being dragged. */\n    get matchSize() {\n      return this._matchSize;\n    }\n    set matchSize(value) {\n      this._matchSize = coerceBooleanProperty(value);\n    }\n    constructor(templateRef) {\n      this.templateRef = templateRef;\n      this._matchSize = false;\n    }\n    static {\n      this.ɵfac = function CdkDragPreview_Factory(t) {\n        return new (t || CdkDragPreview)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkDragPreview,\n        selectors: [[\"ng-template\", \"cdkDragPreview\", \"\"]],\n        inputs: {\n          data: \"data\",\n          matchSize: \"matchSize\"\n        },\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: CDK_DRAG_PREVIEW,\n          useExisting: CdkDragPreview\n        }])]\n      });\n    }\n  }\n  return CdkDragPreview;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */\nconst CDK_DRAG_CONFIG = /*#__PURE__*/new InjectionToken('CDK_DRAG_CONFIG');\nconst DRAG_HOST_CLASS = 'cdk-drag';\n/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST = /*#__PURE__*/new InjectionToken('CdkDropList');\n/** Element that can be moved inside a CdkDropList container. */\nlet CdkDrag = /*#__PURE__*/(() => {\n  class CdkDrag {\n    static {\n      this._dragInstances = [];\n    }\n    /** Whether starting to drag this element is disabled. */\n    get disabled() {\n      return this._disabled || this.dropContainer && this.dropContainer.disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n      this._dragRef.disabled = this._disabled;\n    }\n    constructor( /** Element that the draggable is attached to. */\n    element, /** Droppable container that the draggable is a part of. */\n    dropContainer,\n    /**\n     * @deprecated `_document` parameter no longer being used and will be removed.\n     * @breaking-change 12.0.0\n     */\n    _document, _ngZone, _viewContainerRef, config, _dir, dragDrop, _changeDetectorRef, _selfHandle, _parentDrag) {\n      this.element = element;\n      this.dropContainer = dropContainer;\n      this._ngZone = _ngZone;\n      this._viewContainerRef = _viewContainerRef;\n      this._dir = _dir;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._selfHandle = _selfHandle;\n      this._parentDrag = _parentDrag;\n      this._destroyed = new Subject();\n      /** Emits when the user starts dragging the item. */\n      this.started = new EventEmitter();\n      /** Emits when the user has released a drag item, before any animations have started. */\n      this.released = new EventEmitter();\n      /** Emits when the user stops dragging an item in the container. */\n      this.ended = new EventEmitter();\n      /** Emits when the user has moved the item into a new container. */\n      this.entered = new EventEmitter();\n      /** Emits when the user removes the item its container by dragging it into another container. */\n      this.exited = new EventEmitter();\n      /** Emits when the user drops the item inside a container. */\n      this.dropped = new EventEmitter();\n      /**\n       * Emits as the user is dragging the item. Use with caution,\n       * because this event will fire for every pixel that the user has dragged.\n       */\n      this.moved = new Observable(observer => {\n        const subscription = this._dragRef.moved.pipe(map(movedEvent => ({\n          source: this,\n          pointerPosition: movedEvent.pointerPosition,\n          event: movedEvent.event,\n          delta: movedEvent.delta,\n          distance: movedEvent.distance\n        }))).subscribe(observer);\n        return () => {\n          subscription.unsubscribe();\n        };\n      });\n      this._dragRef = dragDrop.createDrag(element, {\n        dragStartThreshold: config && config.dragStartThreshold != null ? config.dragStartThreshold : 5,\n        pointerDirectionChangeThreshold: config && config.pointerDirectionChangeThreshold != null ? config.pointerDirectionChangeThreshold : 5,\n        zIndex: config?.zIndex\n      });\n      this._dragRef.data = this;\n      // We have to keep track of the drag instances in order to be able to match an element to\n      // a drag instance. We can't go through the global registry of `DragRef`, because the root\n      // element could be different.\n      CdkDrag._dragInstances.push(this);\n      if (config) {\n        this._assignDefaults(config);\n      }\n      // Note that usually the container is assigned when the drop list is picks up the item, but in\n      // some cases (mainly transplanted views with OnPush, see #18341) we may end up in a situation\n      // where there are no items on the first change detection pass, but the items get picked up as\n      // soon as the user triggers another pass by dragging. This is a problem, because the item would\n      // have to switch from standalone mode to drag mode in the middle of the dragging sequence which\n      // is too late since the two modes save different kinds of information. We work around it by\n      // assigning the drop container both from here and the list.\n      if (dropContainer) {\n        this._dragRef._withDropContainer(dropContainer._dropListRef);\n        dropContainer.addItem(this);\n      }\n      this._syncInputs(this._dragRef);\n      this._handleEvents(this._dragRef);\n    }\n    /**\n     * Returns the element that is being used as a placeholder\n     * while the current element is being dragged.\n     */\n    getPlaceholderElement() {\n      return this._dragRef.getPlaceholderElement();\n    }\n    /** Returns the root draggable element. */\n    getRootElement() {\n      return this._dragRef.getRootElement();\n    }\n    /** Resets a standalone drag item to its initial position. */\n    reset() {\n      this._dragRef.reset();\n    }\n    /**\n     * Gets the pixel coordinates of the draggable outside of a drop container.\n     */\n    getFreeDragPosition() {\n      return this._dragRef.getFreeDragPosition();\n    }\n    /**\n     * Sets the current position in pixels the draggable outside of a drop container.\n     * @param value New position to be set.\n     */\n    setFreeDragPosition(value) {\n      this._dragRef.setFreeDragPosition(value);\n    }\n    ngAfterViewInit() {\n      // Normally this isn't in the zone, but it can cause major performance regressions for apps\n      // using `zone-patch-rxjs` because it'll trigger a change detection when it unsubscribes.\n      this._ngZone.runOutsideAngular(() => {\n        // We need to wait for the zone to stabilize, in order for the reference\n        // element to be in the proper place in the DOM. This is mostly relevant\n        // for draggable elements inside portals since they get stamped out in\n        // their original DOM position and then they get transferred to the portal.\n        this._ngZone.onStable.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n          this._updateRootElement();\n          this._setupHandlesListener();\n          if (this.freeDragPosition) {\n            this._dragRef.setFreeDragPosition(this.freeDragPosition);\n          }\n        });\n      });\n    }\n    ngOnChanges(changes) {\n      const rootSelectorChange = changes['rootElementSelector'];\n      const positionChange = changes['freeDragPosition'];\n      // We don't have to react to the first change since it's being\n      // handled in `ngAfterViewInit` where it needs to be deferred.\n      if (rootSelectorChange && !rootSelectorChange.firstChange) {\n        this._updateRootElement();\n      }\n      // Skip the first change since it's being handled in `ngAfterViewInit`.\n      if (positionChange && !positionChange.firstChange && this.freeDragPosition) {\n        this._dragRef.setFreeDragPosition(this.freeDragPosition);\n      }\n    }\n    ngOnDestroy() {\n      if (this.dropContainer) {\n        this.dropContainer.removeItem(this);\n      }\n      const index = CdkDrag._dragInstances.indexOf(this);\n      if (index > -1) {\n        CdkDrag._dragInstances.splice(index, 1);\n      }\n      // Unnecessary in most cases, but used to avoid extra change detections with `zone-paths-rxjs`.\n      this._ngZone.runOutsideAngular(() => {\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._dragRef.dispose();\n      });\n    }\n    /** Syncs the root element with the `DragRef`. */\n    _updateRootElement() {\n      const element = this.element.nativeElement;\n      let rootElement = element;\n      if (this.rootElementSelector) {\n        rootElement = element.closest !== undefined ? element.closest(this.rootElementSelector) :\n        // Comment tag doesn't have closest method, so use parent's one.\n        element.parentElement?.closest(this.rootElementSelector);\n      }\n      if (rootElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        assertElementNode(rootElement, 'cdkDrag');\n      }\n      this._dragRef.withRootElement(rootElement || element);\n    }\n    /** Gets the boundary element, based on the `boundaryElement` value. */\n    _getBoundaryElement() {\n      const boundary = this.boundaryElement;\n      if (!boundary) {\n        return null;\n      }\n      if (typeof boundary === 'string') {\n        return this.element.nativeElement.closest(boundary);\n      }\n      return coerceElement(boundary);\n    }\n    /** Syncs the inputs of the CdkDrag with the options of the underlying DragRef. */\n    _syncInputs(ref) {\n      ref.beforeStarted.subscribe(() => {\n        if (!ref.isDragging()) {\n          const dir = this._dir;\n          const dragStartDelay = this.dragStartDelay;\n          const placeholder = this._placeholderTemplate ? {\n            template: this._placeholderTemplate.templateRef,\n            context: this._placeholderTemplate.data,\n            viewContainer: this._viewContainerRef\n          } : null;\n          const preview = this._previewTemplate ? {\n            template: this._previewTemplate.templateRef,\n            context: this._previewTemplate.data,\n            matchSize: this._previewTemplate.matchSize,\n            viewContainer: this._viewContainerRef\n          } : null;\n          ref.disabled = this.disabled;\n          ref.lockAxis = this.lockAxis;\n          ref.dragStartDelay = typeof dragStartDelay === 'object' && dragStartDelay ? dragStartDelay : coerceNumberProperty(dragStartDelay);\n          ref.constrainPosition = this.constrainPosition;\n          ref.previewClass = this.previewClass;\n          ref.withBoundaryElement(this._getBoundaryElement()).withPlaceholderTemplate(placeholder).withPreviewTemplate(preview).withPreviewContainer(this.previewContainer || 'global');\n          if (dir) {\n            ref.withDirection(dir.value);\n          }\n        }\n      });\n      // This only needs to be resolved once.\n      ref.beforeStarted.pipe(take(1)).subscribe(() => {\n        // If we managed to resolve a parent through DI, use it.\n        if (this._parentDrag) {\n          ref.withParent(this._parentDrag._dragRef);\n          return;\n        }\n        // Otherwise fall back to resolving the parent by looking up the DOM. This can happen if\n        // the item was projected into another item by something like `ngTemplateOutlet`.\n        let parent = this.element.nativeElement.parentElement;\n        while (parent) {\n          if (parent.classList.contains(DRAG_HOST_CLASS)) {\n            ref.withParent(CdkDrag._dragInstances.find(drag => {\n              return drag.element.nativeElement === parent;\n            })?._dragRef || null);\n            break;\n          }\n          parent = parent.parentElement;\n        }\n      });\n    }\n    /** Handles the events from the underlying `DragRef`. */\n    _handleEvents(ref) {\n      ref.started.subscribe(startEvent => {\n        this.started.emit({\n          source: this,\n          event: startEvent.event\n        });\n        // Since all of these events run outside of change detection,\n        // we need to ensure that everything is marked correctly.\n        this._changeDetectorRef.markForCheck();\n      });\n      ref.released.subscribe(releaseEvent => {\n        this.released.emit({\n          source: this,\n          event: releaseEvent.event\n        });\n      });\n      ref.ended.subscribe(endEvent => {\n        this.ended.emit({\n          source: this,\n          distance: endEvent.distance,\n          dropPoint: endEvent.dropPoint,\n          event: endEvent.event\n        });\n        // Since all of these events run outside of change detection,\n        // we need to ensure that everything is marked correctly.\n        this._changeDetectorRef.markForCheck();\n      });\n      ref.entered.subscribe(enterEvent => {\n        this.entered.emit({\n          container: enterEvent.container.data,\n          item: this,\n          currentIndex: enterEvent.currentIndex\n        });\n      });\n      ref.exited.subscribe(exitEvent => {\n        this.exited.emit({\n          container: exitEvent.container.data,\n          item: this\n        });\n      });\n      ref.dropped.subscribe(dropEvent => {\n        this.dropped.emit({\n          previousIndex: dropEvent.previousIndex,\n          currentIndex: dropEvent.currentIndex,\n          previousContainer: dropEvent.previousContainer.data,\n          container: dropEvent.container.data,\n          isPointerOverContainer: dropEvent.isPointerOverContainer,\n          item: this,\n          distance: dropEvent.distance,\n          dropPoint: dropEvent.dropPoint,\n          event: dropEvent.event\n        });\n      });\n    }\n    /** Assigns the default input values based on a provided config object. */\n    _assignDefaults(config) {\n      const {\n        lockAxis,\n        dragStartDelay,\n        constrainPosition,\n        previewClass,\n        boundaryElement,\n        draggingDisabled,\n        rootElementSelector,\n        previewContainer\n      } = config;\n      this.disabled = draggingDisabled == null ? false : draggingDisabled;\n      this.dragStartDelay = dragStartDelay || 0;\n      if (lockAxis) {\n        this.lockAxis = lockAxis;\n      }\n      if (constrainPosition) {\n        this.constrainPosition = constrainPosition;\n      }\n      if (previewClass) {\n        this.previewClass = previewClass;\n      }\n      if (boundaryElement) {\n        this.boundaryElement = boundaryElement;\n      }\n      if (rootElementSelector) {\n        this.rootElementSelector = rootElementSelector;\n      }\n      if (previewContainer) {\n        this.previewContainer = previewContainer;\n      }\n    }\n    /** Sets up the listener that syncs the handles with the drag ref. */\n    _setupHandlesListener() {\n      // Listen for any newly-added handles.\n      this._handles.changes.pipe(startWith(this._handles),\n      // Sync the new handles with the DragRef.\n      tap(handles => {\n        const childHandleElements = handles.filter(handle => handle._parentDrag === this).map(handle => handle.element);\n        // Usually handles are only allowed to be a descendant of the drag element, but if\n        // the consumer defined a different drag root, we should allow the drag element\n        // itself to be a handle too.\n        if (this._selfHandle && this.rootElementSelector) {\n          childHandleElements.push(this.element);\n        }\n        this._dragRef.withHandles(childHandleElements);\n      }),\n      // Listen if the state of any of the handles changes.\n      switchMap(handles => {\n        return merge(...handles.map(item => {\n          return item._stateChanges.pipe(startWith(item));\n        }));\n      }), takeUntil(this._destroyed)).subscribe(handleInstance => {\n        // Enabled/disable the handle that changed in the DragRef.\n        const dragRef = this._dragRef;\n        const handle = handleInstance.element.nativeElement;\n        handleInstance.disabled ? dragRef.disableHandle(handle) : dragRef.enableHandle(handle);\n      });\n    }\n    static {\n      this.ɵfac = function CdkDrag_Factory(t) {\n        return new (t || CdkDrag)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CDK_DROP_LIST, 12), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_DRAG_CONFIG, 8), i0.ɵɵdirectiveInject(i1$1.Directionality, 8), i0.ɵɵdirectiveInject(DragDrop), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(CDK_DRAG_HANDLE, 10), i0.ɵɵdirectiveInject(CDK_DRAG_PARENT, 12));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkDrag,\n        selectors: [[\"\", \"cdkDrag\", \"\"]],\n        contentQueries: function CdkDrag_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, CDK_DRAG_PREVIEW, 5);\n            i0.ɵɵcontentQuery(dirIndex, CDK_DRAG_PLACEHOLDER, 5);\n            i0.ɵɵcontentQuery(dirIndex, CDK_DRAG_HANDLE, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previewTemplate = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._placeholderTemplate = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._handles = _t);\n          }\n        },\n        hostAttrs: [1, \"cdk-drag\"],\n        hostVars: 4,\n        hostBindings: function CdkDrag_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"cdk-drag-disabled\", ctx.disabled)(\"cdk-drag-dragging\", ctx._dragRef.isDragging());\n          }\n        },\n        inputs: {\n          data: [\"cdkDragData\", \"data\"],\n          lockAxis: [\"cdkDragLockAxis\", \"lockAxis\"],\n          rootElementSelector: [\"cdkDragRootElement\", \"rootElementSelector\"],\n          boundaryElement: [\"cdkDragBoundary\", \"boundaryElement\"],\n          dragStartDelay: [\"cdkDragStartDelay\", \"dragStartDelay\"],\n          freeDragPosition: [\"cdkDragFreeDragPosition\", \"freeDragPosition\"],\n          disabled: [\"cdkDragDisabled\", \"disabled\"],\n          constrainPosition: [\"cdkDragConstrainPosition\", \"constrainPosition\"],\n          previewClass: [\"cdkDragPreviewClass\", \"previewClass\"],\n          previewContainer: [\"cdkDragPreviewContainer\", \"previewContainer\"]\n        },\n        outputs: {\n          started: \"cdkDragStarted\",\n          released: \"cdkDragReleased\",\n          ended: \"cdkDragEnded\",\n          entered: \"cdkDragEntered\",\n          exited: \"cdkDragExited\",\n          dropped: \"cdkDragDropped\",\n          moved: \"cdkDragMoved\"\n        },\n        exportAs: [\"cdkDrag\"],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: CDK_DRAG_PARENT,\n          useExisting: CdkDrag\n        }]), i0.ɵɵNgOnChangesFeature]\n      });\n    }\n  }\n  return CdkDrag;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST_GROUP = /*#__PURE__*/new InjectionToken('CdkDropListGroup');\n/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */\nlet CdkDropListGroup = /*#__PURE__*/(() => {\n  class CdkDropListGroup {\n    constructor() {\n      /** Drop lists registered inside the group. */\n      this._items = new Set();\n      this._disabled = false;\n    }\n    /** Whether starting a dragging sequence from inside this group is disabled. */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n    ngOnDestroy() {\n      this._items.clear();\n    }\n    static {\n      this.ɵfac = function CdkDropListGroup_Factory(t) {\n        return new (t || CdkDropListGroup)();\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkDropListGroup,\n        selectors: [[\"\", \"cdkDropListGroup\", \"\"]],\n        inputs: {\n          disabled: [\"cdkDropListGroupDisabled\", \"disabled\"]\n        },\n        exportAs: [\"cdkDropListGroup\"],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([{\n          provide: CDK_DROP_LIST_GROUP,\n          useExisting: CdkDropListGroup\n        }])]\n      });\n    }\n  }\n  return CdkDropListGroup;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Counter used to generate unique ids for drop zones. */\nlet _uniqueIdCounter = 0;\n/** Container that wraps a set of draggable items. */\nlet CdkDropList = /*#__PURE__*/(() => {\n  class CdkDropList {\n    /** Keeps track of the drop lists that are currently on the page. */\n    static {\n      this._dropLists = [];\n    }\n    /** Whether starting a dragging sequence from this container is disabled. */\n    get disabled() {\n      return this._disabled || !!this._group && this._group.disabled;\n    }\n    set disabled(value) {\n      // Usually we sync the directive and ref state right before dragging starts, in order to have\n      // a single point of failure and to avoid having to use setters for everything. `disabled` is\n      // a special case, because it can prevent the `beforeStarted` event from firing, which can lock\n      // the user in a disabled state, so we also need to sync it as it's being set.\n      this._dropListRef.disabled = this._disabled = coerceBooleanProperty(value);\n    }\n    constructor( /** Element that the drop list is attached to. */\n    element, dragDrop, _changeDetectorRef, _scrollDispatcher, _dir, _group, config) {\n      this.element = element;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._scrollDispatcher = _scrollDispatcher;\n      this._dir = _dir;\n      this._group = _group;\n      /** Emits when the list has been destroyed. */\n      this._destroyed = new Subject();\n      /**\n       * Other draggable containers that this container is connected to and into which the\n       * container's items can be transferred. Can either be references to other drop containers,\n       * or their unique IDs.\n       */\n      this.connectedTo = [];\n      /**\n       * Unique ID for the drop zone. Can be used as a reference\n       * in the `connectedTo` of another `CdkDropList`.\n       */\n      this.id = `cdk-drop-list-${_uniqueIdCounter++}`;\n      /**\n       * Function that is used to determine whether an item\n       * is allowed to be moved into a drop container.\n       */\n      this.enterPredicate = () => true;\n      /** Functions that is used to determine whether an item can be sorted into a particular index. */\n      this.sortPredicate = () => true;\n      /** Emits when the user drops an item inside the container. */\n      this.dropped = new EventEmitter();\n      /**\n       * Emits when the user has moved a new drag item into this container.\n       */\n      this.entered = new EventEmitter();\n      /**\n       * Emits when the user removes an item from the container\n       * by dragging it into another container.\n       */\n      this.exited = new EventEmitter();\n      /** Emits as the user is swapping items while actively dragging. */\n      this.sorted = new EventEmitter();\n      /**\n       * Keeps track of the items that are registered with this container. Historically we used to\n       * do this with a `ContentChildren` query, however queries don't handle transplanted views very\n       * well which means that we can't handle cases like dragging the headers of a `mat-table`\n       * correctly. What we do instead is to have the items register themselves with the container\n       * and then we sort them based on their position in the DOM.\n       */\n      this._unsortedItems = new Set();\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        assertElementNode(element.nativeElement, 'cdkDropList');\n      }\n      this._dropListRef = dragDrop.createDropList(element);\n      this._dropListRef.data = this;\n      if (config) {\n        this._assignDefaults(config);\n      }\n      this._dropListRef.enterPredicate = (drag, drop) => {\n        return this.enterPredicate(drag.data, drop.data);\n      };\n      this._dropListRef.sortPredicate = (index, drag, drop) => {\n        return this.sortPredicate(index, drag.data, drop.data);\n      };\n      this._setupInputSyncSubscription(this._dropListRef);\n      this._handleEvents(this._dropListRef);\n      CdkDropList._dropLists.push(this);\n      if (_group) {\n        _group._items.add(this);\n      }\n    }\n    /** Registers an items with the drop list. */\n    addItem(item) {\n      this._unsortedItems.add(item);\n      if (this._dropListRef.isDragging()) {\n        this._syncItemsWithRef();\n      }\n    }\n    /** Removes an item from the drop list. */\n    removeItem(item) {\n      this._unsortedItems.delete(item);\n      if (this._dropListRef.isDragging()) {\n        this._syncItemsWithRef();\n      }\n    }\n    /** Gets the registered items in the list, sorted by their position in the DOM. */\n    getSortedItems() {\n      return Array.from(this._unsortedItems).sort((a, b) => {\n        const documentPosition = a._dragRef.getVisibleElement().compareDocumentPosition(b._dragRef.getVisibleElement());\n        // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n        // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n        // tslint:disable-next-line:no-bitwise\n        return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n      });\n    }\n    ngOnDestroy() {\n      const index = CdkDropList._dropLists.indexOf(this);\n      if (index > -1) {\n        CdkDropList._dropLists.splice(index, 1);\n      }\n      if (this._group) {\n        this._group._items.delete(this);\n      }\n      this._unsortedItems.clear();\n      this._dropListRef.dispose();\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /** Syncs the inputs of the CdkDropList with the options of the underlying DropListRef. */\n    _setupInputSyncSubscription(ref) {\n      if (this._dir) {\n        this._dir.change.pipe(startWith(this._dir.value), takeUntil(this._destroyed)).subscribe(value => ref.withDirection(value));\n      }\n      ref.beforeStarted.subscribe(() => {\n        const siblings = coerceArray(this.connectedTo).map(drop => {\n          if (typeof drop === 'string') {\n            const correspondingDropList = CdkDropList._dropLists.find(list => list.id === drop);\n            if (!correspondingDropList && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n              console.warn(`CdkDropList could not find connected drop list with id \"${drop}\"`);\n            }\n            return correspondingDropList;\n          }\n          return drop;\n        });\n        if (this._group) {\n          this._group._items.forEach(drop => {\n            if (siblings.indexOf(drop) === -1) {\n              siblings.push(drop);\n            }\n          });\n        }\n        // Note that we resolve the scrollable parents here so that we delay the resolution\n        // as long as possible, ensuring that the element is in its final place in the DOM.\n        if (!this._scrollableParentsResolved) {\n          const scrollableParents = this._scrollDispatcher.getAncestorScrollContainers(this.element).map(scrollable => scrollable.getElementRef().nativeElement);\n          this._dropListRef.withScrollableParents(scrollableParents);\n          // Only do this once since it involves traversing the DOM and the parents\n          // shouldn't be able to change without the drop list being destroyed.\n          this._scrollableParentsResolved = true;\n        }\n        ref.disabled = this.disabled;\n        ref.lockAxis = this.lockAxis;\n        ref.sortingDisabled = coerceBooleanProperty(this.sortingDisabled);\n        ref.autoScrollDisabled = coerceBooleanProperty(this.autoScrollDisabled);\n        ref.autoScrollStep = coerceNumberProperty(this.autoScrollStep, 2);\n        ref.connectedTo(siblings.filter(drop => drop && drop !== this).map(list => list._dropListRef)).withOrientation(this.orientation);\n      });\n    }\n    /** Handles events from the underlying DropListRef. */\n    _handleEvents(ref) {\n      ref.beforeStarted.subscribe(() => {\n        this._syncItemsWithRef();\n        this._changeDetectorRef.markForCheck();\n      });\n      ref.entered.subscribe(event => {\n        this.entered.emit({\n          container: this,\n          item: event.item.data,\n          currentIndex: event.currentIndex\n        });\n      });\n      ref.exited.subscribe(event => {\n        this.exited.emit({\n          container: this,\n          item: event.item.data\n        });\n        this._changeDetectorRef.markForCheck();\n      });\n      ref.sorted.subscribe(event => {\n        this.sorted.emit({\n          previousIndex: event.previousIndex,\n          currentIndex: event.currentIndex,\n          container: this,\n          item: event.item.data\n        });\n      });\n      ref.dropped.subscribe(dropEvent => {\n        this.dropped.emit({\n          previousIndex: dropEvent.previousIndex,\n          currentIndex: dropEvent.currentIndex,\n          previousContainer: dropEvent.previousContainer.data,\n          container: dropEvent.container.data,\n          item: dropEvent.item.data,\n          isPointerOverContainer: dropEvent.isPointerOverContainer,\n          distance: dropEvent.distance,\n          dropPoint: dropEvent.dropPoint,\n          event: dropEvent.event\n        });\n        // Mark for check since all of these events run outside of change\n        // detection and we're not guaranteed for something else to have triggered it.\n        this._changeDetectorRef.markForCheck();\n      });\n      merge(ref.receivingStarted, ref.receivingStopped).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    /** Assigns the default input values based on a provided config object. */\n    _assignDefaults(config) {\n      const {\n        lockAxis,\n        draggingDisabled,\n        sortingDisabled,\n        listAutoScrollDisabled,\n        listOrientation\n      } = config;\n      this.disabled = draggingDisabled == null ? false : draggingDisabled;\n      this.sortingDisabled = sortingDisabled == null ? false : sortingDisabled;\n      this.autoScrollDisabled = listAutoScrollDisabled == null ? false : listAutoScrollDisabled;\n      this.orientation = listOrientation || 'vertical';\n      if (lockAxis) {\n        this.lockAxis = lockAxis;\n      }\n    }\n    /** Syncs up the registered drag items with underlying drop list ref. */\n    _syncItemsWithRef() {\n      this._dropListRef.withItems(this.getSortedItems().map(item => item._dragRef));\n    }\n    static {\n      this.ɵfac = function CdkDropList_Factory(t) {\n        return new (t || CdkDropList)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DragDrop), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i1$1.Directionality, 8), i0.ɵɵdirectiveInject(CDK_DROP_LIST_GROUP, 12), i0.ɵɵdirectiveInject(CDK_DRAG_CONFIG, 8));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkDropList,\n        selectors: [[\"\", \"cdkDropList\", \"\"], [\"cdk-drop-list\"]],\n        hostAttrs: [1, \"cdk-drop-list\"],\n        hostVars: 7,\n        hostBindings: function CdkDropList_HostBindings(rf, ctx) {\n          if (rf & 2) {\n            i0.ɵɵattribute(\"id\", ctx.id);\n            i0.ɵɵclassProp(\"cdk-drop-list-disabled\", ctx.disabled)(\"cdk-drop-list-dragging\", ctx._dropListRef.isDragging())(\"cdk-drop-list-receiving\", ctx._dropListRef.isReceiving());\n          }\n        },\n        inputs: {\n          connectedTo: [\"cdkDropListConnectedTo\", \"connectedTo\"],\n          data: [\"cdkDropListData\", \"data\"],\n          orientation: [\"cdkDropListOrientation\", \"orientation\"],\n          id: \"id\",\n          lockAxis: [\"cdkDropListLockAxis\", \"lockAxis\"],\n          disabled: [\"cdkDropListDisabled\", \"disabled\"],\n          sortingDisabled: [\"cdkDropListSortingDisabled\", \"sortingDisabled\"],\n          enterPredicate: [\"cdkDropListEnterPredicate\", \"enterPredicate\"],\n          sortPredicate: [\"cdkDropListSortPredicate\", \"sortPredicate\"],\n          autoScrollDisabled: [\"cdkDropListAutoScrollDisabled\", \"autoScrollDisabled\"],\n          autoScrollStep: [\"cdkDropListAutoScrollStep\", \"autoScrollStep\"]\n        },\n        outputs: {\n          dropped: \"cdkDropListDropped\",\n          entered: \"cdkDropListEntered\",\n          exited: \"cdkDropListExited\",\n          sorted: \"cdkDropListSorted\"\n        },\n        exportAs: [\"cdkDropList\"],\n        standalone: true,\n        features: [i0.ɵɵProvidersFeature([\n        // Prevent child drop lists from picking up the same group as their parent.\n        {\n          provide: CDK_DROP_LIST_GROUP,\n          useValue: undefined\n        }, {\n          provide: CDK_DROP_LIST,\n          useExisting: CdkDropList\n        }])]\n      });\n    }\n  }\n  return CdkDropList;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst DRAG_DROP_DIRECTIVES = [CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder];\nlet DragDropModule = /*#__PURE__*/(() => {\n  class DragDropModule {\n    static {\n      this.ɵfac = function DragDropModule_Factory(t) {\n        return new (t || DragDropModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: DragDropModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        providers: [DragDrop],\n        imports: [CdkScrollableModule]\n      });\n    }\n  }\n  return DragDropModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_DRAG_CONFIG, CDK_DRAG_HANDLE, CDK_DRAG_PARENT, CDK_DRAG_PLACEHOLDER, CDK_DRAG_PREVIEW, CDK_DROP_LIST, CDK_DROP_LIST_GROUP, CdkDrag, CdkDragHandle, CdkDragPlaceholder, CdkDragPreview, CdkDropList, CdkDropListGroup, DragDrop, DragDropModule, DragDropRegistry, DragRef, DropListRef, copyArrayItem, moveItemInArray, transferArrayItem };\n//# sourceMappingURL=drag-drop.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}