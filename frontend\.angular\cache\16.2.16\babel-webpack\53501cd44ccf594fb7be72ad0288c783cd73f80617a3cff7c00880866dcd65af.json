{"ast": null, "code": "import { inspect } from '../../jsutils/inspect.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { isNonNullType } from '../../type/definition.mjs';\nimport { isTypeSubTypeOf } from '../../utilities/typeComparators.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\n\n/**\n * Variables in allowed position\n *\n * Variable usages must be compatible with the arguments they are passed to.\n *\n * See https://spec.graphql.org/draft/#sec-All-Variable-Usages-are-Allowed\n */\nexport function VariablesInAllowedPositionRule(context) {\n  let varDefMap = Object.create(null);\n  return {\n    OperationDefinition: {\n      enter() {\n        varDefMap = Object.create(null);\n      },\n      leave(operation) {\n        const usages = context.getRecursiveVariableUsages(operation);\n        for (const {\n          node,\n          type,\n          defaultValue\n        } of usages) {\n          const varName = node.name.value;\n          const varDef = varDefMap[varName];\n          if (varDef && type) {\n            // A var type is allowed if it is the same or more strict (e.g. is\n            // a subtype of) than the expected type. It can be more strict if\n            // the variable type is non-null when the expected type is nullable.\n            // If both are list types, the variable item type can be more strict\n            // than the expected item type (contravariant).\n            const schema = context.getSchema();\n            const varType = typeFromAST(schema, varDef.type);\n            if (varType && !allowedVariableUsage(schema, varType, varDef.defaultValue, type, defaultValue)) {\n              const varTypeStr = inspect(varType);\n              const typeStr = inspect(type);\n              context.reportError(new GraphQLError(`Variable \"$${varName}\" of type \"${varTypeStr}\" used in position expecting type \"${typeStr}\".`, {\n                nodes: [varDef, node]\n              }));\n            }\n          }\n        }\n      }\n    },\n    VariableDefinition(node) {\n      varDefMap[node.variable.name.value] = node;\n    }\n  };\n}\n/**\n * Returns true if the variable is allowed in the location it was found,\n * which includes considering if default values exist for either the variable\n * or the location at which it is located.\n */\n\nfunction allowedVariableUsage(schema, varType, varDefaultValue, locationType, locationDefaultValue) {\n  if (isNonNullType(locationType) && !isNonNullType(varType)) {\n    const hasNonNullVariableDefaultValue = varDefaultValue != null && varDefaultValue.kind !== Kind.NULL;\n    const hasLocationDefaultValue = locationDefaultValue !== undefined;\n    if (!hasNonNullVariableDefaultValue && !hasLocationDefaultValue) {\n      return false;\n    }\n    const nullableLocationType = locationType.ofType;\n    return isTypeSubTypeOf(schema, varType, nullableLocationType);\n  }\n  return isTypeSubTypeOf(schema, varType, locationType);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}