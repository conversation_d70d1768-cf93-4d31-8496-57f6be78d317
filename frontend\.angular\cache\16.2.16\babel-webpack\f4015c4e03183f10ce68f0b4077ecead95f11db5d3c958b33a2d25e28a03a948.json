{"ast": null, "code": "import { __assign, __extends } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\n// Make builtins like Map and Set safe to use with non-extensible objects.\nimport \"./fixPolyfills.js\";\nimport { wrap } from \"optimism\";\nimport { equal } from \"@wry/equality\";\nimport { ApolloCache } from \"../core/cache.js\";\nimport { MissingFieldError } from \"../core/types/common.js\";\nimport { addTypenameToDocument, isReference, DocumentTransform, canonicalStringify, print, cacheSizes } from \"../../utilities/index.js\";\nimport { StoreReader } from \"./readFromStore.js\";\nimport { StoreWriter } from \"./writeToStore.js\";\nimport { EntityStore, supportsResultCaching } from \"./entityStore.js\";\nimport { makeVar, forgetCache, recallCache } from \"./reactiveVars.js\";\nimport { Policies } from \"./policies.js\";\nimport { hasOwn, normalizeConfig, shouldCanonizeResults } from \"./helpers.js\";\nimport { getInMemoryCacheMemoryInternals } from \"../../utilities/caching/getMemoryInternals.js\";\nvar InMemoryCache = /** @class */function (_super) {\n  __extends(InMemoryCache, _super);\n  function InMemoryCache(config) {\n    if (config === void 0) {\n      config = {};\n    }\n    var _this = _super.call(this) || this;\n    _this.watches = new Set();\n    _this.addTypenameTransform = new DocumentTransform(addTypenameToDocument);\n    // Override the default value, since InMemoryCache result objects are frozen\n    // in development and expected to remain logically immutable in production.\n    _this.assumeImmutableResults = true;\n    _this.makeVar = makeVar;\n    _this.txCount = 0;\n    _this.config = normalizeConfig(config);\n    _this.addTypename = !!_this.config.addTypename;\n    _this.policies = new Policies({\n      cache: _this,\n      dataIdFromObject: _this.config.dataIdFromObject,\n      possibleTypes: _this.config.possibleTypes,\n      typePolicies: _this.config.typePolicies\n    });\n    _this.init();\n    return _this;\n  }\n  InMemoryCache.prototype.init = function () {\n    // Passing { resultCaching: false } in the InMemoryCache constructor options\n    // will completely disable dependency tracking, which will improve memory\n    // usage but worsen the performance of repeated reads.\n    var rootStore = this.data = new EntityStore.Root({\n      policies: this.policies,\n      resultCaching: this.config.resultCaching\n    });\n    // When no optimistic writes are currently active, cache.optimisticData ===\n    // cache.data, so there are no additional layers on top of the actual data.\n    // When an optimistic update happens, this.optimisticData will become a\n    // linked list of EntityStore Layer objects that terminates with the\n    // original this.data cache object.\n    this.optimisticData = rootStore.stump;\n    this.resetResultCache();\n  };\n  InMemoryCache.prototype.resetResultCache = function (resetResultIdentities) {\n    var _this = this;\n    var previousReader = this.storeReader;\n    var fragments = this.config.fragments;\n    // The StoreWriter is mostly stateless and so doesn't really need to be\n    // reset, but it does need to have its writer.storeReader reference updated,\n    // so it's simpler to update this.storeWriter as well.\n    this.storeWriter = new StoreWriter(this, this.storeReader = new StoreReader({\n      cache: this,\n      addTypename: this.addTypename,\n      resultCacheMaxSize: this.config.resultCacheMaxSize,\n      canonizeResults: shouldCanonizeResults(this.config),\n      canon: resetResultIdentities ? void 0 : previousReader && previousReader.canon,\n      fragments: fragments\n    }), fragments);\n    this.maybeBroadcastWatch = wrap(function (c, options) {\n      return _this.broadcastWatch(c, options);\n    }, {\n      max: this.config.resultCacheMaxSize || cacheSizes[\"inMemoryCache.maybeBroadcastWatch\"] || 5000 /* defaultCacheSizes[\"inMemoryCache.maybeBroadcastWatch\"] */,\n      makeCacheKey: function (c) {\n        // Return a cache key (thus enabling result caching) only if we're\n        // currently using a data store that can track cache dependencies.\n        var store = c.optimistic ? _this.optimisticData : _this.data;\n        if (supportsResultCaching(store)) {\n          var optimistic = c.optimistic,\n            id = c.id,\n            variables = c.variables;\n          return store.makeCacheKey(c.query,\n          // Different watches can have the same query, optimistic\n          // status, rootId, and variables, but if their callbacks are\n          // different, the (identical) result needs to be delivered to\n          // each distinct callback. The easiest way to achieve that\n          // separation is to include c.callback in the cache key for\n          // maybeBroadcastWatch calls. See issue #5733.\n          c.callback, canonicalStringify({\n            optimistic: optimistic,\n            id: id,\n            variables: variables\n          }));\n        }\n      }\n    });\n    // Since we have thrown away all the cached functions that depend on the\n    // CacheGroup dependencies maintained by EntityStore, we should also reset\n    // all CacheGroup dependency information.\n    new Set([this.data.group, this.optimisticData.group]).forEach(function (group) {\n      return group.resetCaching();\n    });\n  };\n  InMemoryCache.prototype.restore = function (data) {\n    this.init();\n    // Since calling this.init() discards/replaces the entire StoreReader, along\n    // with the result caches it maintains, this.data.replace(data) won't have\n    // to bother deleting the old data.\n    if (data) this.data.replace(data);\n    return this;\n  };\n  InMemoryCache.prototype.extract = function (optimistic) {\n    if (optimistic === void 0) {\n      optimistic = false;\n    }\n    return (optimistic ? this.optimisticData : this.data).extract();\n  };\n  InMemoryCache.prototype.read = function (options) {\n    var\n      // Since read returns data or null, without any additional metadata\n      // about whether/where there might have been missing fields, the\n      // default behavior cannot be returnPartialData = true (like it is\n      // for the diff method), since defaulting to true would violate the\n      // integrity of the T in the return type. However, partial data may\n      // be useful in some cases, so returnPartialData:true may be\n      // specified explicitly.\n      _a = options.returnPartialData,\n      // Since read returns data or null, without any additional metadata\n      // about whether/where there might have been missing fields, the\n      // default behavior cannot be returnPartialData = true (like it is\n      // for the diff method), since defaulting to true would violate the\n      // integrity of the T in the return type. However, partial data may\n      // be useful in some cases, so returnPartialData:true may be\n      // specified explicitly.\n      returnPartialData = _a === void 0 ? false : _a;\n    try {\n      return this.storeReader.diffQueryAgainstStore(__assign(__assign({}, options), {\n        store: options.optimistic ? this.optimisticData : this.data,\n        config: this.config,\n        returnPartialData: returnPartialData\n      })).result || null;\n    } catch (e) {\n      if (e instanceof MissingFieldError) {\n        // Swallow MissingFieldError and return null, so callers do not need to\n        // worry about catching \"normal\" exceptions resulting from incomplete\n        // cache data. Unexpected errors will be re-thrown. If you need more\n        // information about which fields were missing, use cache.diff instead,\n        // and examine diffResult.missing.\n        return null;\n      }\n      throw e;\n    }\n  };\n  InMemoryCache.prototype.write = function (options) {\n    try {\n      ++this.txCount;\n      return this.storeWriter.writeToStore(this.data, options);\n    } finally {\n      if (! --this.txCount && options.broadcast !== false) {\n        this.broadcastWatches();\n      }\n    }\n  };\n  InMemoryCache.prototype.modify = function (options) {\n    if (hasOwn.call(options, \"id\") && !options.id) {\n      // To my knowledge, TypeScript does not currently provide a way to\n      // enforce that an optional property?:type must *not* be undefined\n      // when present. That ability would be useful here, because we want\n      // options.id to default to ROOT_QUERY only when no options.id was\n      // provided. If the caller attempts to pass options.id with a\n      // falsy/undefined value (perhaps because cache.identify failed), we\n      // should not assume the goal was to modify the ROOT_QUERY object.\n      // We could throw, but it seems natural to return false to indicate\n      // that nothing was modified.\n      return false;\n    }\n    var store = options.optimistic // Defaults to false.\n    ? this.optimisticData : this.data;\n    try {\n      ++this.txCount;\n      return store.modify(options.id || \"ROOT_QUERY\", options.fields);\n    } finally {\n      if (! --this.txCount && options.broadcast !== false) {\n        this.broadcastWatches();\n      }\n    }\n  };\n  InMemoryCache.prototype.diff = function (options) {\n    return this.storeReader.diffQueryAgainstStore(__assign(__assign({}, options), {\n      store: options.optimistic ? this.optimisticData : this.data,\n      rootId: options.id || \"ROOT_QUERY\",\n      config: this.config\n    }));\n  };\n  InMemoryCache.prototype.watch = function (watch) {\n    var _this = this;\n    if (!this.watches.size) {\n      // In case we previously called forgetCache(this) because\n      // this.watches became empty (see below), reattach this cache to any\n      // reactive variables on which it previously depended. It might seem\n      // paradoxical that we're able to recall something we supposedly\n      // forgot, but the point of calling forgetCache(this) is to silence\n      // useless broadcasts while this.watches is empty, and to allow the\n      // cache to be garbage collected. If, however, we manage to call\n      // recallCache(this) here, this cache object must not have been\n      // garbage collected yet, and should resume receiving updates from\n      // reactive variables, now that it has a watcher to notify.\n      recallCache(this);\n    }\n    this.watches.add(watch);\n    if (watch.immediate) {\n      this.maybeBroadcastWatch(watch);\n    }\n    return function () {\n      // Once we remove the last watch from this.watches, cache.broadcastWatches\n      // no longer does anything, so we preemptively tell the reactive variable\n      // system to exclude this cache from future broadcasts.\n      if (_this.watches.delete(watch) && !_this.watches.size) {\n        forgetCache(_this);\n      }\n      // Remove this watch from the LRU cache managed by the\n      // maybeBroadcastWatch OptimisticWrapperFunction, to prevent memory\n      // leaks involving the closure of watch.callback.\n      _this.maybeBroadcastWatch.forget(watch);\n    };\n  };\n  InMemoryCache.prototype.gc = function (options) {\n    var _a;\n    canonicalStringify.reset();\n    print.reset();\n    this.addTypenameTransform.resetCache();\n    (_a = this.config.fragments) === null || _a === void 0 ? void 0 : _a.resetCaches();\n    var ids = this.optimisticData.gc();\n    if (options && !this.txCount) {\n      if (options.resetResultCache) {\n        this.resetResultCache(options.resetResultIdentities);\n      } else if (options.resetResultIdentities) {\n        this.storeReader.resetCanon();\n      }\n    }\n    return ids;\n  };\n  // Call this method to ensure the given root ID remains in the cache after\n  // garbage collection, along with its transitive child entities. Note that\n  // the cache automatically retains all directly written entities. By default,\n  // the retainment persists after optimistic updates are removed. Pass true\n  // for the optimistic argument if you would prefer for the retainment to be\n  // discarded when the top-most optimistic layer is removed. Returns the\n  // resulting (non-negative) retainment count.\n  InMemoryCache.prototype.retain = function (rootId, optimistic) {\n    return (optimistic ? this.optimisticData : this.data).retain(rootId);\n  };\n  // Call this method to undo the effect of the retain method, above. Once the\n  // retainment count falls to zero, the given ID will no longer be preserved\n  // during garbage collection, though it may still be preserved by other safe\n  // entities that refer to it. Returns the resulting (non-negative) retainment\n  // count, in case that's useful.\n  InMemoryCache.prototype.release = function (rootId, optimistic) {\n    return (optimistic ? this.optimisticData : this.data).release(rootId);\n  };\n  // Returns the canonical ID for a given StoreObject, obeying typePolicies\n  // and keyFields (and dataIdFromObject, if you still use that). At minimum,\n  // the object must contain a __typename and any primary key fields required\n  // to identify entities of that type. If you pass a query result object, be\n  // sure that none of the primary key fields have been renamed by aliasing.\n  // If you pass a Reference object, its __ref ID string will be returned.\n  InMemoryCache.prototype.identify = function (object) {\n    if (isReference(object)) return object.__ref;\n    try {\n      return this.policies.identify(object)[0];\n    } catch (e) {\n      globalThis.__DEV__ !== false && invariant.warn(e);\n    }\n  };\n  InMemoryCache.prototype.evict = function (options) {\n    if (!options.id) {\n      if (hasOwn.call(options, \"id\")) {\n        // See comment in modify method about why we return false when\n        // options.id exists but is falsy/undefined.\n        return false;\n      }\n      options = __assign(__assign({}, options), {\n        id: \"ROOT_QUERY\"\n      });\n    }\n    try {\n      // It's unlikely that the eviction will end up invoking any other\n      // cache update operations while it's running, but {in,de}crementing\n      // this.txCount still seems like a good idea, for uniformity with\n      // the other update methods.\n      ++this.txCount;\n      // Pass this.data as a limit on the depth of the eviction, so evictions\n      // during optimistic updates (when this.data is temporarily set equal to\n      // this.optimisticData) do not escape their optimistic Layer.\n      return this.optimisticData.evict(options, this.data);\n    } finally {\n      if (! --this.txCount && options.broadcast !== false) {\n        this.broadcastWatches();\n      }\n    }\n  };\n  InMemoryCache.prototype.reset = function (options) {\n    var _this = this;\n    this.init();\n    canonicalStringify.reset();\n    if (options && options.discardWatches) {\n      // Similar to what happens in the unsubscribe function returned by\n      // cache.watch, applied to all current watches.\n      this.watches.forEach(function (watch) {\n        return _this.maybeBroadcastWatch.forget(watch);\n      });\n      this.watches.clear();\n      forgetCache(this);\n    } else {\n      // Calling this.init() above unblocks all maybeBroadcastWatch caching, so\n      // this.broadcastWatches() triggers a broadcast to every current watcher\n      // (letting them know their data is now missing). This default behavior is\n      // convenient because it means the watches do not have to be manually\n      // reestablished after resetting the cache. To prevent this broadcast and\n      // cancel all watches, pass true for options.discardWatches.\n      this.broadcastWatches();\n    }\n    return Promise.resolve();\n  };\n  InMemoryCache.prototype.removeOptimistic = function (idToRemove) {\n    var newOptimisticData = this.optimisticData.removeLayer(idToRemove);\n    if (newOptimisticData !== this.optimisticData) {\n      this.optimisticData = newOptimisticData;\n      this.broadcastWatches();\n    }\n  };\n  InMemoryCache.prototype.batch = function (options) {\n    var _this = this;\n    var update = options.update,\n      _a = options.optimistic,\n      optimistic = _a === void 0 ? true : _a,\n      removeOptimistic = options.removeOptimistic,\n      onWatchUpdated = options.onWatchUpdated;\n    var updateResult;\n    var perform = function (layer) {\n      var _a = _this,\n        data = _a.data,\n        optimisticData = _a.optimisticData;\n      ++_this.txCount;\n      if (layer) {\n        _this.data = _this.optimisticData = layer;\n      }\n      try {\n        return updateResult = update(_this);\n      } finally {\n        --_this.txCount;\n        _this.data = data;\n        _this.optimisticData = optimisticData;\n      }\n    };\n    var alreadyDirty = new Set();\n    if (onWatchUpdated && !this.txCount) {\n      // If an options.onWatchUpdated callback is provided, we want to call it\n      // with only the Cache.WatchOptions objects affected by options.update,\n      // but there might be dirty watchers already waiting to be broadcast that\n      // have nothing to do with the update. To prevent including those watchers\n      // in the post-update broadcast, we perform this initial broadcast to\n      // collect the dirty watchers, so we can re-dirty them later, after the\n      // post-update broadcast, allowing them to receive their pending\n      // broadcasts the next time broadcastWatches is called, just as they would\n      // if we never called cache.batch.\n      this.broadcastWatches(__assign(__assign({}, options), {\n        onWatchUpdated: function (watch) {\n          alreadyDirty.add(watch);\n          return false;\n        }\n      }));\n    }\n    if (typeof optimistic === \"string\") {\n      // Note that there can be multiple layers with the same optimistic ID.\n      // When removeOptimistic(id) is called for that id, all matching layers\n      // will be removed, and the remaining layers will be reapplied.\n      this.optimisticData = this.optimisticData.addLayer(optimistic, perform);\n    } else if (optimistic === false) {\n      // Ensure both this.data and this.optimisticData refer to the root\n      // (non-optimistic) layer of the cache during the update. Note that\n      // this.data could be a Layer if we are currently executing an optimistic\n      // update function, but otherwise will always be an EntityStore.Root\n      // instance.\n      perform(this.data);\n    } else {\n      // Otherwise, leave this.data and this.optimisticData unchanged and run\n      // the update with broadcast batching.\n      perform();\n    }\n    if (typeof removeOptimistic === \"string\") {\n      this.optimisticData = this.optimisticData.removeLayer(removeOptimistic);\n    }\n    // Note: if this.txCount > 0, then alreadyDirty.size === 0, so this code\n    // takes the else branch and calls this.broadcastWatches(options), which\n    // does nothing when this.txCount > 0.\n    if (onWatchUpdated && alreadyDirty.size) {\n      this.broadcastWatches(__assign(__assign({}, options), {\n        onWatchUpdated: function (watch, diff) {\n          var result = onWatchUpdated.call(this, watch, diff);\n          if (result !== false) {\n            // Since onWatchUpdated did not return false, this diff is\n            // about to be broadcast to watch.callback, so we don't need\n            // to re-dirty it with the other alreadyDirty watches below.\n            alreadyDirty.delete(watch);\n          }\n          return result;\n        }\n      }));\n      // Silently re-dirty any watches that were already dirty before the update\n      // was performed, and were not broadcast just now.\n      if (alreadyDirty.size) {\n        alreadyDirty.forEach(function (watch) {\n          return _this.maybeBroadcastWatch.dirty(watch);\n        });\n      }\n    } else {\n      // If alreadyDirty is empty or we don't have an onWatchUpdated\n      // function, we don't need to go to the trouble of wrapping\n      // options.onWatchUpdated.\n      this.broadcastWatches(options);\n    }\n    return updateResult;\n  };\n  InMemoryCache.prototype.performTransaction = function (update, optimisticId) {\n    return this.batch({\n      update: update,\n      optimistic: optimisticId || optimisticId !== null\n    });\n  };\n  InMemoryCache.prototype.transformDocument = function (document) {\n    return this.addTypenameToDocument(this.addFragmentsToDocument(document));\n  };\n  InMemoryCache.prototype.fragmentMatches = function (fragment, typename) {\n    return this.policies.fragmentMatches(fragment, typename);\n  };\n  InMemoryCache.prototype.lookupFragment = function (fragmentName) {\n    var _a;\n    return ((_a = this.config.fragments) === null || _a === void 0 ? void 0 : _a.lookup(fragmentName)) || null;\n  };\n  InMemoryCache.prototype.broadcastWatches = function (options) {\n    var _this = this;\n    if (!this.txCount) {\n      this.watches.forEach(function (c) {\n        return _this.maybeBroadcastWatch(c, options);\n      });\n    }\n  };\n  InMemoryCache.prototype.addFragmentsToDocument = function (document) {\n    var fragments = this.config.fragments;\n    return fragments ? fragments.transform(document) : document;\n  };\n  InMemoryCache.prototype.addTypenameToDocument = function (document) {\n    if (this.addTypename) {\n      return this.addTypenameTransform.transformDocument(document);\n    }\n    return document;\n  };\n  // This method is wrapped by maybeBroadcastWatch, which is called by\n  // broadcastWatches, so that we compute and broadcast results only when\n  // the data that would be broadcast might have changed. It would be\n  // simpler to check for changes after recomputing a result but before\n  // broadcasting it, but this wrapping approach allows us to skip both\n  // the recomputation and the broadcast, in most cases.\n  InMemoryCache.prototype.broadcastWatch = function (c, options) {\n    var lastDiff = c.lastDiff;\n    // Both WatchOptions and DiffOptions extend ReadOptions, and DiffOptions\n    // currently requires no additional properties, so we can use c (a\n    // WatchOptions object) as DiffOptions, without having to allocate a new\n    // object, and without having to enumerate the relevant properties (query,\n    // variables, etc.) explicitly. There will be some additional properties\n    // (lastDiff, callback, etc.), but cache.diff ignores them.\n    var diff = this.diff(c);\n    if (options) {\n      if (c.optimistic && typeof options.optimistic === \"string\") {\n        diff.fromOptimisticTransaction = true;\n      }\n      if (options.onWatchUpdated && options.onWatchUpdated.call(this, c, diff, lastDiff) === false) {\n        // Returning false from the onWatchUpdated callback will prevent\n        // calling c.callback(diff) for this watcher.\n        return;\n      }\n    }\n    if (!lastDiff || !equal(lastDiff.result, diff.result)) {\n      c.callback(c.lastDiff = diff, lastDiff);\n    }\n  };\n  return InMemoryCache;\n}(ApolloCache);\nexport { InMemoryCache };\nif (globalThis.__DEV__ !== false) {\n  InMemoryCache.prototype.getMemoryInternals = getInMemoryCacheMemoryInternals;\n}\n//# sourceMappingURL=inMemoryCache.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}