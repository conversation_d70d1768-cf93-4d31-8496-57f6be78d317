{"ast": null, "code": "import { Kind } from '../language/kinds.mjs';\nimport { visit } from '../language/visitor.mjs';\n/**\n * separateOperations accepts a single AST document which may contain many\n * operations and fragments and returns a collection of AST documents each of\n * which contains a single operation as well the fragment definitions it\n * refers to.\n */\n\nexport function separateOperations(documentAST) {\n  const operations = [];\n  const depGraph = Object.create(null); // Populate metadata and build a dependency graph.\n\n  for (const definitionNode of documentAST.definitions) {\n    switch (definitionNode.kind) {\n      case Kind.OPERATION_DEFINITION:\n        operations.push(definitionNode);\n        break;\n      case Kind.FRAGMENT_DEFINITION:\n        depGraph[definitionNode.name.value] = collectDependencies(definitionNode.selectionSet);\n        break;\n      default: // ignore non-executable definitions\n    }\n  } // For each operation, produce a new synthesized AST which includes only what\n  // is necessary for completing that operation.\n\n  const separatedDocumentASTs = Object.create(null);\n  for (const operation of operations) {\n    const dependencies = new Set();\n    for (const fragmentName of collectDependencies(operation.selectionSet)) {\n      collectTransitiveDependencies(dependencies, depGraph, fragmentName);\n    } // Provides the empty string for anonymous operations.\n\n    const operationName = operation.name ? operation.name.value : ''; // The list of definition nodes to be included for this operation, sorted\n    // to retain the same order as the original document.\n\n    separatedDocumentASTs[operationName] = {\n      kind: Kind.DOCUMENT,\n      definitions: documentAST.definitions.filter(node => node === operation || node.kind === Kind.FRAGMENT_DEFINITION && dependencies.has(node.name.value))\n    };\n  }\n  return separatedDocumentASTs;\n}\n\n// From a dependency graph, collects a list of transitive dependencies by\n// recursing through a dependency graph.\nfunction collectTransitiveDependencies(collected, depGraph, fromName) {\n  if (!collected.has(fromName)) {\n    collected.add(fromName);\n    const immediateDeps = depGraph[fromName];\n    if (immediateDeps !== undefined) {\n      for (const toName of immediateDeps) {\n        collectTransitiveDependencies(collected, depGraph, toName);\n      }\n    }\n  }\n}\nfunction collectDependencies(selectionSet) {\n  const dependencies = [];\n  visit(selectionSet, {\n    FragmentSpread(node) {\n      dependencies.push(node.name.value);\n    }\n  });\n  return dependencies;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}