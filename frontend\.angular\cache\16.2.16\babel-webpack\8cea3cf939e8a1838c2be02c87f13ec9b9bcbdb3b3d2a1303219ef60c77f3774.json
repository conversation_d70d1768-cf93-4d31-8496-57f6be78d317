{"ast": null, "code": "import { naturalCompare } from './naturalCompare.mjs';\n/**\n * Given an invalid input string and a list of valid options, returns a filtered\n * list of valid options sorted based on their similarity with the input.\n */\n\nexport function suggestionList(input, options) {\n  const optionsByDistance = Object.create(null);\n  const lexicalDistance = new LexicalDistance(input);\n  const threshold = Math.floor(input.length * 0.4) + 1;\n  for (const option of options) {\n    const distance = lexicalDistance.measure(option, threshold);\n    if (distance !== undefined) {\n      optionsByDistance[option] = distance;\n    }\n  }\n  return Object.keys(optionsByDistance).sort((a, b) => {\n    const distanceDiff = optionsByDistance[a] - optionsByDistance[b];\n    return distanceDiff !== 0 ? distanceDiff : naturalCompare(a, b);\n  });\n}\n/**\n * Computes the lexical distance between strings A and B.\n *\n * The \"distance\" between two strings is given by counting the minimum number\n * of edits needed to transform string A into string B. An edit can be an\n * insertion, deletion, or substitution of a single character, or a swap of two\n * adjacent characters.\n *\n * Includes a custom alteration from <PERSON>rau-<PERSON>enshtein to treat case changes\n * as a single edit which helps identify mis-cased values with an edit distance\n * of 1.\n *\n * This distance can be useful for detecting typos in input or sorting\n */\n\nclass LexicalDistance {\n  constructor(input) {\n    this._input = input;\n    this._inputLowerCase = input.toLowerCase();\n    this._inputArray = stringToArray(this._inputLowerCase);\n    this._rows = [new Array(input.length + 1).fill(0), new Array(input.length + 1).fill(0), new Array(input.length + 1).fill(0)];\n  }\n  measure(option, threshold) {\n    if (this._input === option) {\n      return 0;\n    }\n    const optionLowerCase = option.toLowerCase(); // Any case change counts as a single edit\n\n    if (this._inputLowerCase === optionLowerCase) {\n      return 1;\n    }\n    let a = stringToArray(optionLowerCase);\n    let b = this._inputArray;\n    if (a.length < b.length) {\n      const tmp = a;\n      a = b;\n      b = tmp;\n    }\n    const aLength = a.length;\n    const bLength = b.length;\n    if (aLength - bLength > threshold) {\n      return undefined;\n    }\n    const rows = this._rows;\n    for (let j = 0; j <= bLength; j++) {\n      rows[0][j] = j;\n    }\n    for (let i = 1; i <= aLength; i++) {\n      const upRow = rows[(i - 1) % 3];\n      const currentRow = rows[i % 3];\n      let smallestCell = currentRow[0] = i;\n      for (let j = 1; j <= bLength; j++) {\n        const cost = a[i - 1] === b[j - 1] ? 0 : 1;\n        let currentCell = Math.min(upRow[j] + 1,\n        // delete\n        currentRow[j - 1] + 1,\n        // insert\n        upRow[j - 1] + cost // substitute\n        );\n\n        if (i > 1 && j > 1 && a[i - 1] === b[j - 2] && a[i - 2] === b[j - 1]) {\n          // transposition\n          const doubleDiagonalCell = rows[(i - 2) % 3][j - 2];\n          currentCell = Math.min(currentCell, doubleDiagonalCell + 1);\n        }\n        if (currentCell < smallestCell) {\n          smallestCell = currentCell;\n        }\n        currentRow[j] = currentCell;\n      } // Early exit, since distance can't go smaller than smallest element of the previous row.\n\n      if (smallestCell > threshold) {\n        return undefined;\n      }\n    }\n    const distance = rows[aLength % 3][bLength];\n    return distance <= threshold ? distance : undefined;\n  }\n}\nfunction stringToArray(str) {\n  const strLength = str.length;\n  const array = new Array(strLength);\n  for (let i = 0; i < strLength; ++i) {\n    array[i] = str.charCodeAt(i);\n  }\n  return array;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}