{"ast": null, "code": "import { __extends } from \"tslib\";\nvar MissingFieldError = /** @class */function (_super) {\n  __extends(MissingFieldError, _super);\n  function MissingFieldError(message, path, query, variables) {\n    var _a;\n    // 'Error' breaks prototype chain here\n    var _this = _super.call(this, message) || this;\n    _this.message = message;\n    _this.path = path;\n    _this.query = query;\n    _this.variables = variables;\n    if (Array.isArray(_this.path)) {\n      _this.missing = _this.message;\n      for (var i = _this.path.length - 1; i >= 0; --i) {\n        _this.missing = (_a = {}, _a[_this.path[i]] = _this.missing, _a);\n      }\n    } else {\n      _this.missing = _this.path;\n    }\n    // We're not using `Object.setPrototypeOf` here as it isn't fully supported\n    // on Android (see issue #3236).\n    _this.__proto__ = MissingFieldError.prototype;\n    return _this;\n  }\n  return MissingFieldError;\n}(<PERSON>rror);\nexport { MissingFieldError };\n//# sourceMappingURL=common.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}