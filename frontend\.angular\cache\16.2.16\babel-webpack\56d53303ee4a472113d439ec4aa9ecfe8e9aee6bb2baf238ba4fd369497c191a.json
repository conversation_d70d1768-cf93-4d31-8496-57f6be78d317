{"ast": null, "code": "import { constructFrom } from \"../../constructFrom.js\";\nimport { transpose } from \"../../transpose.js\";\nconst TIMEZONE_UNIT_PRIORITY = 10;\nexport class Setter {\n  subPriority = 0;\n  validate(_utcDate, _options) {\n    return true;\n  }\n}\nexport class ValueSetter extends Setter {\n  constructor(value, validateValue, setValue, priority, subPriority) {\n    super();\n    this.value = value;\n    this.validateValue = validateValue;\n    this.setValue = setValue;\n    this.priority = priority;\n    if (subPriority) {\n      this.subPriority = subPriority;\n    }\n  }\n  validate(date, options) {\n    return this.validateValue(date, this.value, options);\n  }\n  set(date, flags, options) {\n    return this.setValue(date, flags, this.value, options);\n  }\n}\nexport class DateTimezoneSetter extends Setter {\n  priority = TIMEZONE_UNIT_PRIORITY;\n  subPriority = -1;\n  constructor(context, reference) {\n    super();\n    this.context = context || (date => constructFrom(reference, date));\n  }\n  set(date, flags) {\n    if (flags.timestampIsSet) return date;\n    return constructFrom(date, transpose(date, this.context));\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}