{"ast": null, "code": "import { __assign, __extends } from \"tslib\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { NetworkStatus, isNetworkRequestInFlight } from \"./networkStatus.js\";\nimport { cloneDeep, compact, getOperationDefinition, Observable, iterateObserversSafely, fixObservableSubclass, getQueryDefinition, preventUnhandledRejection } from \"../utilities/index.js\";\nimport { ApolloError, isApolloError } from \"../errors/index.js\";\nimport { equalByQuery } from \"./equalByQuery.js\";\nimport { Slot } from \"optimism\";\nvar assign = Object.assign,\n  hasOwnProperty = Object.hasOwnProperty;\nvar ObservableQuery = /** @class */function (_super) {\n  __extends(ObservableQuery, _super);\n  function ObservableQuery(_a) {\n    var queryManager = _a.queryManager,\n      queryInfo = _a.queryInfo,\n      options = _a.options;\n    var _this = this;\n    var startedInactive = ObservableQuery.inactiveOnCreation.getValue();\n    _this = _super.call(this, function (observer) {\n      if (startedInactive) {\n        queryManager[\"queries\"].set(_this.queryId, queryInfo);\n        startedInactive = false;\n      }\n      // Zen Observable has its own error function, so in order to log correctly\n      // we need to provide a custom error callback.\n      try {\n        var subObserver = observer._subscription._observer;\n        if (subObserver && !subObserver.error) {\n          subObserver.error = defaultSubscriptionObserverErrorCallback;\n        }\n      } catch (_a) {}\n      var first = !_this.observers.size;\n      _this.observers.add(observer);\n      // Deliver most recent error or result.\n      var last = _this.last;\n      if (last && last.error) {\n        observer.error && observer.error(last.error);\n      } else if (last && last.result) {\n        observer.next && observer.next(_this.maskResult(last.result));\n      }\n      // Initiate observation of this query if it hasn't been reported to\n      // the QueryManager yet.\n      if (first) {\n        // Blindly catching here prevents unhandled promise rejections,\n        // and is safe because the ObservableQuery handles this error with\n        // this.observer.error, so we're not just swallowing the error by\n        // ignoring it here.\n        _this.reobserve().catch(function () {});\n      }\n      return function () {\n        if (_this.observers.delete(observer) && !_this.observers.size) {\n          _this.tearDownQuery();\n        }\n      };\n    }) || this;\n    _this.observers = new Set();\n    _this.subscriptions = new Set();\n    _this.dirty = false;\n    // related classes\n    _this.queryInfo = queryInfo;\n    _this.queryManager = queryManager;\n    // active state\n    _this.waitForOwnResult = skipCacheDataFor(options.fetchPolicy);\n    _this.isTornDown = false;\n    _this.subscribeToMore = _this.subscribeToMore.bind(_this);\n    _this.maskResult = _this.maskResult.bind(_this);\n    var _b = queryManager.defaultOptions.watchQuery,\n      _c = _b === void 0 ? {} : _b,\n      _d = _c.fetchPolicy,\n      defaultFetchPolicy = _d === void 0 ? \"cache-first\" : _d;\n    var _e = options.fetchPolicy,\n      fetchPolicy = _e === void 0 ? defaultFetchPolicy : _e,\n      // Make sure we don't store \"standby\" as the initialFetchPolicy.\n      _f = options.initialFetchPolicy,\n      // Make sure we don't store \"standby\" as the initialFetchPolicy.\n      initialFetchPolicy = _f === void 0 ? fetchPolicy === \"standby\" ? defaultFetchPolicy : fetchPolicy : _f;\n    _this.options = __assign(__assign({}, options), {\n      // Remember the initial options.fetchPolicy so we can revert back to this\n      // policy when variables change. This information can also be specified\n      // (or overridden) by providing options.initialFetchPolicy explicitly.\n      initialFetchPolicy: initialFetchPolicy,\n      // This ensures this.options.fetchPolicy always has a string value, in\n      // case options.fetchPolicy was not provided.\n      fetchPolicy: fetchPolicy\n    });\n    _this.queryId = queryInfo.queryId || queryManager.generateQueryId();\n    var opDef = getOperationDefinition(_this.query);\n    _this.queryName = opDef && opDef.name && opDef.name.value;\n    return _this;\n  }\n  Object.defineProperty(ObservableQuery.prototype, \"query\", {\n    // The `query` computed property will always reflect the document transformed\n    // by the last run query. `this.options.query` will always reflect the raw\n    // untransformed query to ensure document transforms with runtime conditionals\n    // are run on the original document.\n    get: function () {\n      return this.lastQuery || this.options.query;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(ObservableQuery.prototype, \"variables\", {\n    // Computed shorthand for this.options.variables, preserved for\n    // backwards compatibility.\n    /**\n     * An object containing the variables that were provided for the query.\n     */\n    get: function () {\n      return this.options.variables;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  ObservableQuery.prototype.result = function () {\n    var _this = this;\n    return new Promise(function (resolve, reject) {\n      // TODO: this code doesn’t actually make sense insofar as the observer\n      // will never exist in this.observers due how zen-observable wraps observables.\n      // https://github.com/zenparsing/zen-observable/blob/master/src/Observable.js#L169\n      var observer = {\n        next: function (result) {\n          resolve(result);\n          // Stop the query within the QueryManager if we can before\n          // this function returns.\n          //\n          // We do this in order to prevent observers piling up within\n          // the QueryManager. Notice that we only fully unsubscribe\n          // from the subscription in a setTimeout(..., 0)  call. This call can\n          // actually be handled by the browser at a much later time. If queries\n          // are fired in the meantime, observers that should have been removed\n          // from the QueryManager will continue to fire, causing an unnecessary\n          // performance hit.\n          _this.observers.delete(observer);\n          if (!_this.observers.size) {\n            _this.queryManager.removeQuery(_this.queryId);\n          }\n          setTimeout(function () {\n            subscription.unsubscribe();\n          }, 0);\n        },\n        error: reject\n      };\n      var subscription = _this.subscribe(observer);\n    });\n  };\n  /** @internal */\n  ObservableQuery.prototype.resetDiff = function () {\n    this.queryInfo.resetDiff();\n  };\n  ObservableQuery.prototype.getCurrentFullResult = function (saveAsLastResult) {\n    if (saveAsLastResult === void 0) {\n      saveAsLastResult = true;\n    }\n    // Use the last result as long as the variables match this.variables.\n    var lastResult = this.getLastResult(true);\n    var networkStatus = this.queryInfo.networkStatus || lastResult && lastResult.networkStatus || NetworkStatus.ready;\n    var result = __assign(__assign({}, lastResult), {\n      loading: isNetworkRequestInFlight(networkStatus),\n      networkStatus: networkStatus\n    });\n    var _a = this.options.fetchPolicy,\n      fetchPolicy = _a === void 0 ? \"cache-first\" : _a;\n    if (\n    // These fetch policies should never deliver data from the cache, unless\n    // redelivering a previously delivered result.\n    skipCacheDataFor(fetchPolicy) ||\n    // If this.options.query has @client(always: true) fields, we cannot\n    // trust diff.result, since it was read from the cache without running\n    // local resolvers (and it's too late to run resolvers now, since we must\n    // return a result synchronously).\n    this.queryManager.getDocumentInfo(this.query).hasForcedResolvers) {\n      // Fall through.\n    } else if (this.waitForOwnResult) {\n      // This would usually be a part of `QueryInfo.getDiff()`.\n      // which we skip in the waitForOwnResult case since we are not\n      // interested in the diff.\n      this.queryInfo[\"updateWatch\"]();\n    } else {\n      var diff = this.queryInfo.getDiff();\n      if (diff.complete || this.options.returnPartialData) {\n        result.data = diff.result;\n      }\n      if (equal(result.data, {})) {\n        result.data = void 0;\n      }\n      if (diff.complete) {\n        // Similar to setting result.partial to false, but taking advantage of the\n        // falsiness of missing fields.\n        delete result.partial;\n        // If the diff is complete, and we're using a FetchPolicy that\n        // terminates after a complete cache read, we can assume the next result\n        // we receive will have NetworkStatus.ready and !loading.\n        if (diff.complete && result.networkStatus === NetworkStatus.loading && (fetchPolicy === \"cache-first\" || fetchPolicy === \"cache-only\")) {\n          result.networkStatus = NetworkStatus.ready;\n          result.loading = false;\n        }\n      } else {\n        result.partial = true;\n      }\n      // We need to check for both both `error` and `errors` field because there\n      // are cases where sometimes `error` is set, but not `errors` and\n      // vice-versa. This will be updated in the next major version when\n      // `errors` is deprecated in favor of `error`.\n      if (result.networkStatus === NetworkStatus.ready && (result.error || result.errors)) {\n        result.networkStatus = NetworkStatus.error;\n      }\n      if (globalThis.__DEV__ !== false && !diff.complete && !this.options.partialRefetch && !result.loading && !result.data && !result.error) {\n        logMissingFieldErrors(diff.missing);\n      }\n    }\n    if (saveAsLastResult) {\n      this.updateLastResult(result);\n    }\n    return result;\n  };\n  ObservableQuery.prototype.getCurrentResult = function (saveAsLastResult) {\n    if (saveAsLastResult === void 0) {\n      saveAsLastResult = true;\n    }\n    return this.maskResult(this.getCurrentFullResult(saveAsLastResult));\n  };\n  // Compares newResult to the snapshot we took of this.lastResult when it was\n  // first received.\n  ObservableQuery.prototype.isDifferentFromLastResult = function (newResult, variables) {\n    if (!this.last) {\n      return true;\n    }\n    var documentInfo = this.queryManager.getDocumentInfo(this.query);\n    var dataMasking = this.queryManager.dataMasking;\n    var query = dataMasking ? documentInfo.nonReactiveQuery : this.query;\n    var resultIsDifferent = dataMasking || documentInfo.hasNonreactiveDirective ? !equalByQuery(query, this.last.result, newResult, this.variables) : !equal(this.last.result, newResult);\n    return resultIsDifferent || variables && !equal(this.last.variables, variables);\n  };\n  ObservableQuery.prototype.getLast = function (key, variablesMustMatch) {\n    var last = this.last;\n    if (last && last[key] && (!variablesMustMatch || equal(last.variables, this.variables))) {\n      return last[key];\n    }\n  };\n  ObservableQuery.prototype.getLastResult = function (variablesMustMatch) {\n    return this.getLast(\"result\", variablesMustMatch);\n  };\n  ObservableQuery.prototype.getLastError = function (variablesMustMatch) {\n    return this.getLast(\"error\", variablesMustMatch);\n  };\n  ObservableQuery.prototype.resetLastResults = function () {\n    delete this.last;\n    this.isTornDown = false;\n  };\n  ObservableQuery.prototype.resetQueryStoreErrors = function () {\n    this.queryManager.resetErrors(this.queryId);\n  };\n  /**\n   * Update the variables of this observable query, and fetch the new results.\n   * This method should be preferred over `setVariables` in most use cases.\n   *\n   * @param variables - The new set of variables. If there are missing variables,\n   * the previous values of those variables will be used.\n   */\n  ObservableQuery.prototype.refetch = function (variables) {\n    var _a;\n    var reobserveOptions = {\n      // Always disable polling for refetches.\n      pollInterval: 0\n    };\n    // Unless the provided fetchPolicy always consults the network\n    // (no-cache, network-only, or cache-and-network), override it with\n    // network-only to force the refetch for this fetchQuery call.\n    var fetchPolicy = this.options.fetchPolicy;\n    if (fetchPolicy === \"no-cache\") {\n      reobserveOptions.fetchPolicy = \"no-cache\";\n    } else {\n      reobserveOptions.fetchPolicy = \"network-only\";\n    }\n    if (globalThis.__DEV__ !== false && variables && hasOwnProperty.call(variables, \"variables\")) {\n      var queryDef = getQueryDefinition(this.query);\n      var vars = queryDef.variableDefinitions;\n      if (!vars || !vars.some(function (v) {\n        return v.variable.name.value === \"variables\";\n      })) {\n        globalThis.__DEV__ !== false && invariant.warn(21, variables, ((_a = queryDef.name) === null || _a === void 0 ? void 0 : _a.value) || queryDef);\n      }\n    }\n    if (variables && !equal(this.options.variables, variables)) {\n      // Update the existing options with new variables\n      reobserveOptions.variables = this.options.variables = __assign(__assign({}, this.options.variables), variables);\n    }\n    this.queryInfo.resetLastWrite();\n    return this.reobserve(reobserveOptions, NetworkStatus.refetch);\n  };\n  /**\n   * A function that helps you fetch the next set of results for a [paginated list field](https://www.apollographql.com/docs/react/pagination/core-api/).\n   */\n  ObservableQuery.prototype.fetchMore = function (fetchMoreOptions) {\n    var _this = this;\n    var combinedOptions = __assign(__assign({}, fetchMoreOptions.query ? fetchMoreOptions : __assign(__assign(__assign(__assign({}, this.options), {\n      query: this.options.query\n    }), fetchMoreOptions), {\n      variables: __assign(__assign({}, this.options.variables), fetchMoreOptions.variables)\n    })), {\n      // The fetchMore request goes immediately to the network and does\n      // not automatically write its result to the cache (hence no-cache\n      // instead of network-only), because we allow the caller of\n      // fetchMore to provide an updateQuery callback that determines how\n      // the data gets written to the cache.\n      fetchPolicy: \"no-cache\"\n    });\n    combinedOptions.query = this.transformDocument(combinedOptions.query);\n    var qid = this.queryManager.generateQueryId();\n    // If a temporary query is passed to `fetchMore`, we don't want to store\n    // it as the last query result since it may be an optimized query for\n    // pagination. We will however run the transforms on the original document\n    // as well as the document passed in `fetchMoreOptions` to ensure the cache\n    // uses the most up-to-date document which may rely on runtime conditionals.\n    this.lastQuery = fetchMoreOptions.query ? this.transformDocument(this.options.query) : combinedOptions.query;\n    // Simulate a loading result for the original query with\n    // result.networkStatus === NetworkStatus.fetchMore.\n    var queryInfo = this.queryInfo;\n    var originalNetworkStatus = queryInfo.networkStatus;\n    queryInfo.networkStatus = NetworkStatus.fetchMore;\n    if (combinedOptions.notifyOnNetworkStatusChange) {\n      this.observe();\n    }\n    var updatedQuerySet = new Set();\n    var updateQuery = fetchMoreOptions === null || fetchMoreOptions === void 0 ? void 0 : fetchMoreOptions.updateQuery;\n    var isCached = this.options.fetchPolicy !== \"no-cache\";\n    if (!isCached) {\n      invariant(updateQuery, 22);\n    }\n    return this.queryManager.fetchQuery(qid, combinedOptions, NetworkStatus.fetchMore).then(function (fetchMoreResult) {\n      _this.queryManager.removeQuery(qid);\n      if (queryInfo.networkStatus === NetworkStatus.fetchMore) {\n        queryInfo.networkStatus = originalNetworkStatus;\n      }\n      if (isCached) {\n        // Performing this cache update inside a cache.batch transaction ensures\n        // any affected cache.watch watchers are notified at most once about any\n        // updates. Most watchers will be using the QueryInfo class, which\n        // responds to notifications by calling reobserveCacheFirst to deliver\n        // fetchMore cache results back to this ObservableQuery.\n        _this.queryManager.cache.batch({\n          update: function (cache) {\n            var updateQuery = fetchMoreOptions.updateQuery;\n            if (updateQuery) {\n              cache.updateQuery({\n                query: _this.query,\n                variables: _this.variables,\n                returnPartialData: true,\n                optimistic: false\n              }, function (previous) {\n                return updateQuery(previous, {\n                  fetchMoreResult: fetchMoreResult.data,\n                  variables: combinedOptions.variables\n                });\n              });\n            } else {\n              // If we're using a field policy instead of updateQuery, the only\n              // thing we need to do is write the new data to the cache using\n              // combinedOptions.variables (instead of this.variables, which is\n              // what this.updateQuery uses, because it works by abusing the\n              // original field value, keyed by the original variables).\n              cache.writeQuery({\n                query: combinedOptions.query,\n                variables: combinedOptions.variables,\n                data: fetchMoreResult.data\n              });\n            }\n          },\n          onWatchUpdated: function (watch) {\n            // Record the DocumentNode associated with any watched query whose\n            // data were updated by the cache writes above.\n            updatedQuerySet.add(watch.query);\n          }\n        });\n      } else {\n        // There is a possibility `lastResult` may not be set when\n        // `fetchMore` is called which would cause this to crash. This should\n        // only happen if we haven't previously reported a result. We don't\n        // quite know what the right behavior should be here since this block\n        // of code runs after the fetch result has executed on the network.\n        // We plan to let it crash in the meantime.\n        //\n        // If we get bug reports due to the `data` property access on\n        // undefined, this should give us a real-world scenario that we can\n        // use to test against and determine the right behavior. If we do end\n        // up changing this behavior, this may require, for example, an\n        // adjustment to the types on `updateQuery` since that function\n        // expects that the first argument always contains previous result\n        // data, but not `undefined`.\n        var lastResult = _this.getLast(\"result\");\n        var data = updateQuery(lastResult.data, {\n          fetchMoreResult: fetchMoreResult.data,\n          variables: combinedOptions.variables\n        });\n        _this.reportResult(__assign(__assign({}, lastResult), {\n          networkStatus: originalNetworkStatus,\n          loading: isNetworkRequestInFlight(originalNetworkStatus),\n          data: data\n        }), _this.variables);\n      }\n      return _this.maskResult(fetchMoreResult);\n    }).finally(function () {\n      // In case the cache writes above did not generate a broadcast\n      // notification (which would have been intercepted by onWatchUpdated),\n      // likely because the written data were the same as what was already in\n      // the cache, we still want fetchMore to deliver its final loading:false\n      // result with the unchanged data.\n      if (isCached && !updatedQuerySet.has(_this.query)) {\n        _this.reobserveCacheFirst();\n      }\n    });\n  };\n  // XXX the subscription variables are separate from the query variables.\n  // if you want to update subscription variables, right now you have to do that separately,\n  // and you can only do it by stopping the subscription and then subscribing again with new variables.\n  /**\n   * A function that enables you to execute a [subscription](https://www.apollographql.com/docs/react/data/subscriptions/), usually to subscribe to specific fields that were included in the query.\n   *\n   * This function returns _another_ function that you can call to terminate the subscription.\n   */\n  ObservableQuery.prototype.subscribeToMore = function (options) {\n    var _this = this;\n    var subscription = this.queryManager.startGraphQLSubscription({\n      query: options.document,\n      variables: options.variables,\n      context: options.context\n    }).subscribe({\n      next: function (subscriptionData) {\n        var updateQuery = options.updateQuery;\n        if (updateQuery) {\n          _this.updateQuery(function (previous, updateOptions) {\n            return updateQuery(previous, __assign({\n              subscriptionData: subscriptionData\n            }, updateOptions));\n          });\n        }\n      },\n      error: function (err) {\n        if (options.onError) {\n          options.onError(err);\n          return;\n        }\n        globalThis.__DEV__ !== false && invariant.error(23, err);\n      }\n    });\n    this.subscriptions.add(subscription);\n    return function () {\n      if (_this.subscriptions.delete(subscription)) {\n        subscription.unsubscribe();\n      }\n    };\n  };\n  ObservableQuery.prototype.setOptions = function (newOptions) {\n    return this.reobserve(newOptions);\n  };\n  ObservableQuery.prototype.silentSetOptions = function (newOptions) {\n    var mergedOptions = compact(this.options, newOptions || {});\n    assign(this.options, mergedOptions);\n  };\n  /**\n   * Update the variables of this observable query, and fetch the new results\n   * if they've changed. Most users should prefer `refetch` instead of\n   * `setVariables` in order to to be properly notified of results even when\n   * they come from the cache.\n   *\n   * Note: the `next` callback will *not* fire if the variables have not changed\n   * or if the result is coming from cache.\n   *\n   * Note: the promise will return the old results immediately if the variables\n   * have not changed.\n   *\n   * Note: the promise will return null immediately if the query is not active\n   * (there are no subscribers).\n   *\n   * @param variables - The new set of variables. If there are missing variables,\n   * the previous values of those variables will be used.\n   */\n  ObservableQuery.prototype.setVariables = function (variables) {\n    if (equal(this.variables, variables)) {\n      // If we have no observers, then we don't actually want to make a network\n      // request. As soon as someone observes the query, the request will kick\n      // off. For now, we just store any changes. (See #1077)\n      return this.observers.size ? this.result() : Promise.resolve();\n    }\n    this.options.variables = variables;\n    // See comment above\n    if (!this.observers.size) {\n      return Promise.resolve();\n    }\n    return this.reobserve({\n      // Reset options.fetchPolicy to its original value.\n      fetchPolicy: this.options.initialFetchPolicy,\n      variables: variables\n    }, NetworkStatus.setVariables);\n  };\n  /**\n   * A function that enables you to update the query's cached result without executing a followup GraphQL operation.\n   *\n   * See [using updateQuery and updateFragment](https://www.apollographql.com/docs/react/caching/cache-interaction/#using-updatequery-and-updatefragment) for additional information.\n   */\n  ObservableQuery.prototype.updateQuery = function (mapFn) {\n    var queryManager = this.queryManager;\n    var _a = queryManager.cache.diff({\n        query: this.options.query,\n        variables: this.variables,\n        returnPartialData: true,\n        optimistic: false\n      }),\n      result = _a.result,\n      complete = _a.complete;\n    var newResult = mapFn(result, {\n      variables: this.variables,\n      complete: !!complete,\n      previousData: result\n    });\n    if (newResult) {\n      queryManager.cache.writeQuery({\n        query: this.options.query,\n        data: newResult,\n        variables: this.variables\n      });\n      queryManager.broadcastQueries();\n    }\n  };\n  /**\n   * A function that instructs the query to begin re-executing at a specified interval (in milliseconds).\n   */\n  ObservableQuery.prototype.startPolling = function (pollInterval) {\n    this.options.pollInterval = pollInterval;\n    this.updatePolling();\n  };\n  /**\n   * A function that instructs the query to stop polling after a previous call to `startPolling`.\n   */\n  ObservableQuery.prototype.stopPolling = function () {\n    this.options.pollInterval = 0;\n    this.updatePolling();\n  };\n  // Update options.fetchPolicy according to options.nextFetchPolicy.\n  ObservableQuery.prototype.applyNextFetchPolicy = function (reason,\n  // It's possible to use this method to apply options.nextFetchPolicy to\n  // options.fetchPolicy even if options !== this.options, though that happens\n  // most often when the options are temporary, used for only one request and\n  // then thrown away, so nextFetchPolicy may not end up mattering.\n  options) {\n    if (options.nextFetchPolicy) {\n      var _a = options.fetchPolicy,\n        fetchPolicy = _a === void 0 ? \"cache-first\" : _a,\n        _b = options.initialFetchPolicy,\n        initialFetchPolicy = _b === void 0 ? fetchPolicy : _b;\n      if (fetchPolicy === \"standby\") {\n        // Do nothing, leaving options.fetchPolicy unchanged.\n      } else if (typeof options.nextFetchPolicy === \"function\") {\n        // When someone chooses \"cache-and-network\" or \"network-only\" as their\n        // initial FetchPolicy, they often do not want future cache updates to\n        // trigger unconditional network requests, which is what repeatedly\n        // applying the \"cache-and-network\" or \"network-only\" policies would\n        // seem to imply. Instead, when the cache reports an update after the\n        // initial network request, it may be desirable for subsequent network\n        // requests to be triggered only if the cache result is incomplete. To\n        // that end, the options.nextFetchPolicy option provides an easy way to\n        // update options.fetchPolicy after the initial network request, without\n        // having to call observableQuery.setOptions.\n        options.fetchPolicy = options.nextFetchPolicy(fetchPolicy, {\n          reason: reason,\n          options: options,\n          observable: this,\n          initialFetchPolicy: initialFetchPolicy\n        });\n      } else if (reason === \"variables-changed\") {\n        options.fetchPolicy = initialFetchPolicy;\n      } else {\n        options.fetchPolicy = options.nextFetchPolicy;\n      }\n    }\n    return options.fetchPolicy;\n  };\n  ObservableQuery.prototype.fetch = function (options, newNetworkStatus, query) {\n    // TODO Make sure we update the networkStatus (and infer fetchVariables)\n    // before actually committing to the fetch.\n    var queryInfo = this.queryManager.getOrCreateQuery(this.queryId);\n    queryInfo.setObservableQuery(this);\n    return this.queryManager[\"fetchConcastWithInfo\"](queryInfo, options, newNetworkStatus, query);\n  };\n  // Turns polling on or off based on this.options.pollInterval.\n  ObservableQuery.prototype.updatePolling = function () {\n    var _this = this;\n    // Avoid polling in SSR mode\n    if (this.queryManager.ssrMode) {\n      return;\n    }\n    var _a = this,\n      pollingInfo = _a.pollingInfo,\n      pollInterval = _a.options.pollInterval;\n    if (!pollInterval || !this.hasObservers()) {\n      if (pollingInfo) {\n        clearTimeout(pollingInfo.timeout);\n        delete this.pollingInfo;\n      }\n      return;\n    }\n    if (pollingInfo && pollingInfo.interval === pollInterval) {\n      return;\n    }\n    invariant(pollInterval, 24);\n    var info = pollingInfo || (this.pollingInfo = {});\n    info.interval = pollInterval;\n    var maybeFetch = function () {\n      var _a, _b;\n      if (_this.pollingInfo) {\n        if (!isNetworkRequestInFlight(_this.queryInfo.networkStatus) && !((_b = (_a = _this.options).skipPollAttempt) === null || _b === void 0 ? void 0 : _b.call(_a))) {\n          _this.reobserve({\n            // Most fetchPolicy options don't make sense to use in a polling context, as\n            // users wouldn't want to be polling the cache directly. However, network-only and\n            // no-cache are both useful for when the user wants to control whether or not the\n            // polled results are written to the cache.\n            fetchPolicy: _this.options.initialFetchPolicy === \"no-cache\" ? \"no-cache\" : \"network-only\"\n          }, NetworkStatus.poll).then(poll, poll);\n        } else {\n          poll();\n        }\n      }\n    };\n    var poll = function () {\n      var info = _this.pollingInfo;\n      if (info) {\n        clearTimeout(info.timeout);\n        info.timeout = setTimeout(maybeFetch, info.interval);\n      }\n    };\n    poll();\n  };\n  ObservableQuery.prototype.updateLastResult = function (newResult, variables) {\n    if (variables === void 0) {\n      variables = this.variables;\n    }\n    var error = this.getLastError();\n    // Preserve this.last.error unless the variables have changed.\n    if (error && this.last && !equal(variables, this.last.variables)) {\n      error = void 0;\n    }\n    return this.last = __assign({\n      result: this.queryManager.assumeImmutableResults ? newResult : cloneDeep(newResult),\n      variables: variables\n    }, error ? {\n      error: error\n    } : null);\n  };\n  ObservableQuery.prototype.reobserveAsConcast = function (newOptions, newNetworkStatus) {\n    var _this = this;\n    this.isTornDown = false;\n    var useDisposableConcast =\n    // Refetching uses a disposable Concast to allow refetches using different\n    // options/variables, without permanently altering the options of the\n    // original ObservableQuery.\n    newNetworkStatus === NetworkStatus.refetch ||\n    // The fetchMore method does not actually call the reobserve method, but,\n    // if it did, it would definitely use a disposable Concast.\n    newNetworkStatus === NetworkStatus.fetchMore ||\n    // Polling uses a disposable Concast so the polling options (which force\n    // fetchPolicy to be \"network-only\" or \"no-cache\") won't override the original options.\n    newNetworkStatus === NetworkStatus.poll;\n    // Save the old variables, since Object.assign may modify them below.\n    var oldVariables = this.options.variables;\n    var oldFetchPolicy = this.options.fetchPolicy;\n    var mergedOptions = compact(this.options, newOptions || {});\n    var options = useDisposableConcast ?\n    // Disposable Concast fetches receive a shallow copy of this.options\n    // (merged with newOptions), leaving this.options unmodified.\n    mergedOptions : assign(this.options, mergedOptions);\n    // Don't update options.query with the transformed query to avoid\n    // overwriting this.options.query when we aren't using a disposable concast.\n    // We want to ensure we can re-run the custom document transforms the next\n    // time a request is made against the original query.\n    var query = this.transformDocument(options.query);\n    this.lastQuery = query;\n    if (!useDisposableConcast) {\n      // We can skip calling updatePolling if we're not changing this.options.\n      this.updatePolling();\n      // Reset options.fetchPolicy to its original value when variables change,\n      // unless a new fetchPolicy was provided by newOptions.\n      if (newOptions && newOptions.variables && !equal(newOptions.variables, oldVariables) &&\n      // Don't mess with the fetchPolicy if it's currently \"standby\".\n      options.fetchPolicy !== \"standby\" && (\n      // If we're changing the fetchPolicy anyway, don't try to change it here\n      // using applyNextFetchPolicy. The explicit options.fetchPolicy wins.\n      options.fetchPolicy === oldFetchPolicy ||\n      // A `nextFetchPolicy` function has even higher priority, though,\n      // so in that case `applyNextFetchPolicy` must be called.\n      typeof options.nextFetchPolicy === \"function\")) {\n        this.applyNextFetchPolicy(\"variables-changed\", options);\n        if (newNetworkStatus === void 0) {\n          newNetworkStatus = NetworkStatus.setVariables;\n        }\n      }\n    }\n    this.waitForOwnResult && (this.waitForOwnResult = skipCacheDataFor(options.fetchPolicy));\n    var finishWaitingForOwnResult = function () {\n      if (_this.concast === concast) {\n        _this.waitForOwnResult = false;\n      }\n    };\n    var variables = options.variables && __assign({}, options.variables);\n    var _a = this.fetch(options, newNetworkStatus, query),\n      concast = _a.concast,\n      fromLink = _a.fromLink;\n    var observer = {\n      next: function (result) {\n        if (equal(_this.variables, variables)) {\n          finishWaitingForOwnResult();\n          _this.reportResult(result, variables);\n        }\n      },\n      error: function (error) {\n        if (equal(_this.variables, variables)) {\n          // Coming from `getResultsFromLink`, `error` here should always be an `ApolloError`.\n          // However, calling `concast.cancel` can inject another type of error, so we have to\n          // wrap it again here.\n          if (!isApolloError(error)) {\n            error = new ApolloError({\n              networkError: error\n            });\n          }\n          finishWaitingForOwnResult();\n          _this.reportError(error, variables);\n        }\n      }\n    };\n    if (!useDisposableConcast && (fromLink || !this.concast)) {\n      // We use the {add,remove}Observer methods directly to avoid wrapping\n      // observer with an unnecessary SubscriptionObserver object.\n      if (this.concast && this.observer) {\n        this.concast.removeObserver(this.observer);\n      }\n      this.concast = concast;\n      this.observer = observer;\n    }\n    concast.addObserver(observer);\n    return concast;\n  };\n  ObservableQuery.prototype.reobserve = function (newOptions, newNetworkStatus) {\n    return preventUnhandledRejection(this.reobserveAsConcast(newOptions, newNetworkStatus).promise.then(this.maskResult));\n  };\n  ObservableQuery.prototype.resubscribeAfterError = function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    // If `lastError` is set in the current when the subscription is re-created,\n    // the subscription will immediately receive the error, which will\n    // cause it to terminate again. To avoid this, we first clear\n    // the last error/result from the `observableQuery` before re-starting\n    // the subscription, and restore the last value afterwards so that the\n    // subscription has a chance to stay open.\n    var last = this.last;\n    this.resetLastResults();\n    var subscription = this.subscribe.apply(this, args);\n    this.last = last;\n    return subscription;\n  };\n  // (Re)deliver the current result to this.observers without applying fetch\n  // policies or making network requests.\n  ObservableQuery.prototype.observe = function () {\n    this.reportResult(\n    // Passing false is important so that this.getCurrentResult doesn't\n    // save the fetchMore result as this.lastResult, causing it to be\n    // ignored due to the this.isDifferentFromLastResult check in\n    // this.reportResult.\n    this.getCurrentFullResult(false), this.variables);\n  };\n  ObservableQuery.prototype.reportResult = function (result, variables) {\n    var lastError = this.getLastError();\n    var isDifferent = this.isDifferentFromLastResult(result, variables);\n    // Update the last result even when isDifferentFromLastResult returns false,\n    // because the query may be using the @nonreactive directive, and we want to\n    // save the the latest version of any nonreactive subtrees (in case\n    // getCurrentResult is called), even though we skip broadcasting changes.\n    if (lastError || !result.partial || this.options.returnPartialData) {\n      this.updateLastResult(result, variables);\n    }\n    if (lastError || isDifferent) {\n      iterateObserversSafely(this.observers, \"next\", this.maskResult(result));\n    }\n  };\n  ObservableQuery.prototype.reportError = function (error, variables) {\n    // Since we don't get the current result on errors, only the error, we\n    // must mirror the updates that occur in QueryStore.markQueryError here\n    var errorResult = __assign(__assign({}, this.getLastResult()), {\n      error: error,\n      errors: error.graphQLErrors,\n      networkStatus: NetworkStatus.error,\n      loading: false\n    });\n    this.updateLastResult(errorResult, variables);\n    iterateObserversSafely(this.observers, \"error\", this.last.error = error);\n  };\n  ObservableQuery.prototype.hasObservers = function () {\n    return this.observers.size > 0;\n  };\n  ObservableQuery.prototype.tearDownQuery = function () {\n    if (this.isTornDown) return;\n    if (this.concast && this.observer) {\n      this.concast.removeObserver(this.observer);\n      delete this.concast;\n      delete this.observer;\n    }\n    this.stopPolling();\n    // stop all active GraphQL subscriptions\n    this.subscriptions.forEach(function (sub) {\n      return sub.unsubscribe();\n    });\n    this.subscriptions.clear();\n    this.queryManager.stopQuery(this.queryId);\n    this.observers.clear();\n    this.isTornDown = true;\n  };\n  ObservableQuery.prototype.transformDocument = function (document) {\n    return this.queryManager.transform(document);\n  };\n  ObservableQuery.prototype.maskResult = function (result) {\n    return result && \"data\" in result ? __assign(__assign({}, result), {\n      data: this.queryManager.maskOperation({\n        document: this.query,\n        data: result.data,\n        fetchPolicy: this.options.fetchPolicy,\n        id: this.queryId\n      })\n    }) : result;\n  };\n  /** @internal */\n  ObservableQuery.prototype.resetNotifications = function () {\n    this.cancelNotifyTimeout();\n    this.dirty = false;\n  };\n  ObservableQuery.prototype.cancelNotifyTimeout = function () {\n    if (this.notifyTimeout) {\n      clearTimeout(this.notifyTimeout);\n      this.notifyTimeout = void 0;\n    }\n  };\n  /** @internal */\n  ObservableQuery.prototype.scheduleNotify = function () {\n    var _this = this;\n    if (this.dirty) return;\n    this.dirty = true;\n    if (!this.notifyTimeout) {\n      this.notifyTimeout = setTimeout(function () {\n        return _this.notify();\n      }, 0);\n    }\n  };\n  /** @internal */\n  ObservableQuery.prototype.notify = function () {\n    this.cancelNotifyTimeout();\n    if (this.dirty) {\n      if (this.options.fetchPolicy == \"cache-only\" || this.options.fetchPolicy == \"cache-and-network\" || !isNetworkRequestInFlight(this.queryInfo.networkStatus)) {\n        var diff = this.queryInfo.getDiff();\n        if (diff.fromOptimisticTransaction) {\n          // If this diff came from an optimistic transaction, deliver the\n          // current cache data to the ObservableQuery, but don't perform a\n          // reobservation, since oq.reobserveCacheFirst might make a network\n          // request, and we never want to trigger network requests in the\n          // middle of optimistic updates.\n          this.observe();\n        } else {\n          // Otherwise, make the ObservableQuery \"reobserve\" the latest data\n          // using a temporary fetch policy of \"cache-first\", so complete cache\n          // results have a chance to be delivered without triggering additional\n          // network requests, even when options.fetchPolicy is \"network-only\"\n          // or \"cache-and-network\". All other fetch policies are preserved by\n          // this method, and are handled by calling oq.reobserve(). If this\n          // reobservation is spurious, isDifferentFromLastResult still has a\n          // chance to catch it before delivery to ObservableQuery subscribers.\n          this.reobserveCacheFirst();\n        }\n      }\n    }\n    this.dirty = false;\n  };\n  // Reobserve with fetchPolicy effectively set to \"cache-first\", triggering\n  // delivery of any new data from the cache, possibly falling back to the network\n  // if any cache data are missing. This allows _complete_ cache results to be\n  // delivered without also kicking off unnecessary network requests when\n  // this.options.fetchPolicy is \"cache-and-network\" or \"network-only\". When\n  // this.options.fetchPolicy is any other policy (\"cache-first\", \"cache-only\",\n  // \"standby\", or \"no-cache\"), we call this.reobserve() as usual.\n  ObservableQuery.prototype.reobserveCacheFirst = function () {\n    var _a = this.options,\n      fetchPolicy = _a.fetchPolicy,\n      nextFetchPolicy = _a.nextFetchPolicy;\n    if (fetchPolicy === \"cache-and-network\" || fetchPolicy === \"network-only\") {\n      return this.reobserve({\n        fetchPolicy: \"cache-first\",\n        // Use a temporary nextFetchPolicy function that replaces itself with the\n        // previous nextFetchPolicy value and returns the original fetchPolicy.\n        nextFetchPolicy: function (currentFetchPolicy, context) {\n          // Replace this nextFetchPolicy function in the options object with the\n          // original this.options.nextFetchPolicy value.\n          this.nextFetchPolicy = nextFetchPolicy;\n          // If the original nextFetchPolicy value was a function, give it a\n          // chance to decide what happens here.\n          if (typeof this.nextFetchPolicy === \"function\") {\n            return this.nextFetchPolicy(currentFetchPolicy, context);\n          }\n          // Otherwise go back to the original this.options.fetchPolicy.\n          return fetchPolicy;\n        }\n      });\n    }\n    return this.reobserve();\n  };\n  /**\n   * @internal\n   * A slot used by the `useQuery` hook to indicate that `client.watchQuery`\n   * should not register the query immediately, but instead wait for the query to\n   * be started registered with the `QueryManager` when `useSyncExternalStore`\n   * actively subscribes to it.\n   */\n  ObservableQuery.inactiveOnCreation = new Slot();\n  return ObservableQuery;\n}(Observable);\nexport { ObservableQuery };\n// Necessary because the ObservableQuery constructor has a different\n// signature than the Observable constructor.\nfixObservableSubclass(ObservableQuery);\nfunction defaultSubscriptionObserverErrorCallback(error) {\n  globalThis.__DEV__ !== false && invariant.error(25, error.message, error.stack);\n}\nexport function logMissingFieldErrors(missing) {\n  if (globalThis.__DEV__ !== false && missing) {\n    globalThis.__DEV__ !== false && invariant.debug(26, missing);\n  }\n}\nfunction skipCacheDataFor(fetchPolicy /* `undefined` would mean `\"cache-first\"` */) {\n  return fetchPolicy === \"network-only\" || fetchPolicy === \"no-cache\" || fetchPolicy === \"standby\";\n}\n//# sourceMappingURL=ObservableQuery.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}