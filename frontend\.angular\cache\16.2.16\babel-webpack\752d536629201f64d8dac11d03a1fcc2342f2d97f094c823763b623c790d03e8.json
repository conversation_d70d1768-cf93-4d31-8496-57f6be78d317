{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { graphQLResultHasProtocolErrors, PROTOCOL_ERRORS_SYMBOL } from \"../../errors/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nexport function onError(errorHandler) {\n  return new ApolloLink(function (operation, forward) {\n    return new Observable(function (observer) {\n      var sub;\n      var retriedSub;\n      var retriedResult;\n      try {\n        sub = forward(operation).subscribe({\n          next: function (result) {\n            if (result.errors) {\n              retriedResult = errorHandler({\n                graphQLErrors: result.errors,\n                response: result,\n                operation: operation,\n                forward: forward\n              });\n            } else if (graphQLResultHasProtocolErrors(result)) {\n              retriedResult = errorHandler({\n                protocolErrors: result.extensions[PROTOCOL_ERRORS_SYMBOL],\n                response: result,\n                operation: operation,\n                forward: forward\n              });\n            }\n            if (retriedResult) {\n              retriedSub = retriedResult.subscribe({\n                next: observer.next.bind(observer),\n                error: observer.error.bind(observer),\n                complete: observer.complete.bind(observer)\n              });\n              return;\n            }\n            observer.next(result);\n          },\n          error: function (networkError) {\n            retriedResult = errorHandler({\n              operation: operation,\n              networkError: networkError,\n              //Network errors can return GraphQL errors on for example a 403\n              graphQLErrors: networkError && networkError.result && networkError.result.errors || void 0,\n              forward: forward\n            });\n            if (retriedResult) {\n              retriedSub = retriedResult.subscribe({\n                next: observer.next.bind(observer),\n                error: observer.error.bind(observer),\n                complete: observer.complete.bind(observer)\n              });\n              return;\n            }\n            observer.error(networkError);\n          },\n          complete: function () {\n            // disable the previous sub from calling complete on observable\n            // if retry is in flight.\n            if (!retriedResult) {\n              observer.complete.bind(observer)();\n            }\n          }\n        });\n      } catch (e) {\n        errorHandler({\n          networkError: e,\n          operation: operation,\n          forward: forward\n        });\n        observer.error(e);\n      }\n      return function () {\n        if (sub) sub.unsubscribe();\n        if (retriedSub) sub.unsubscribe();\n      };\n    });\n  });\n}\nvar ErrorLink = /** @class */function (_super) {\n  __extends(ErrorLink, _super);\n  function ErrorLink(errorHandler) {\n    var _this = _super.call(this) || this;\n    _this.link = onError(errorHandler);\n    return _this;\n  }\n  ErrorLink.prototype.request = function (operation, forward) {\n    return this.link.request(operation, forward);\n  };\n  return ErrorLink;\n}(ApolloLink);\nexport { ErrorLink };\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}