{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique type names\n *\n * A GraphQL document is only valid if all defined types have unique names.\n */\nexport function UniqueTypeNamesRule(context) {\n  const knownTypeNames = Object.create(null);\n  const schema = context.getSchema();\n  return {\n    ScalarTypeDefinition: checkTypeName,\n    ObjectTypeDefinition: checkTypeName,\n    InterfaceTypeDefinition: checkTypeName,\n    UnionTypeDefinition: checkTypeName,\n    EnumTypeDefinition: checkTypeName,\n    InputObjectTypeDefinition: checkTypeName\n  };\n  function checkTypeName(node) {\n    const typeName = node.name.value;\n    if (schema !== null && schema !== void 0 && schema.getType(typeName)) {\n      context.reportError(new GraphQLError(`Type \"${typeName}\" already exists in the schema. It cannot also be defined in this type definition.`, {\n        nodes: node.name\n      }));\n      return;\n    }\n    if (knownTypeNames[typeName]) {\n      context.reportError(new GraphQLError(`There can be only one type named \"${typeName}\".`, {\n        nodes: [knownTypeNames[typeName], node.name]\n      }));\n    } else {\n      knownTypeNames[typeName] = node.name;\n    }\n    return false;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}