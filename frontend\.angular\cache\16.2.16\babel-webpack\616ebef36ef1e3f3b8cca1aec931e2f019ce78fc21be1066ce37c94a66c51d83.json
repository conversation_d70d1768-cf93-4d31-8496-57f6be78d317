{"ast": null, "code": "import { Slot } from \"./slot.js\";\nexport { Slot };\nexport const {\n  bind,\n  noContext\n} = Slot;\n// Like global.setTimeout, except the callback runs with captured context.\nexport { setTimeoutWithContext as setTimeout };\nfunction setTimeoutWithContext(callback, delay) {\n  return setTimeout(bind(callback), delay);\n}\n// Turn any generator function into an async function (using yield instead\n// of await), with context automatically preserved across yields.\nexport function asyncFromGen(genFn) {\n  return function () {\n    const gen = genFn.apply(this, arguments);\n    const boundNext = bind(gen.next);\n    const boundThrow = bind(gen.throw);\n    return new Promise((resolve, reject) => {\n      function invoke(method, argument) {\n        try {\n          var result = method.call(gen, argument);\n        } catch (error) {\n          return reject(error);\n        }\n        const next = result.done ? resolve : invokeNext;\n        if (isPromiseLike(result.value)) {\n          result.value.then(next, result.done ? reject : invokeThrow);\n        } else {\n          next(result.value);\n        }\n      }\n      const invokeNext = value => invoke(boundNext, value);\n      const invokeThrow = error => invoke(boundThrow, error);\n      invokeNext();\n    });\n  };\n}\nfunction isPromiseLike(value) {\n  return value && typeof value.then === \"function\";\n}\n// If you use the fibers npm package to implement coroutines in Node.js,\n// you should call this function at least once to ensure context management\n// remains coherent across any yields.\nconst wrappedFibers = [];\nexport function wrapYieldingFiberMethods(Fiber) {\n  // There can be only one implementation of Fiber per process, so this array\n  // should never grow longer than one element.\n  if (wrappedFibers.indexOf(Fiber) < 0) {\n    const wrap = (obj, method) => {\n      const fn = obj[method];\n      obj[method] = function () {\n        return noContext(fn, arguments, this);\n      };\n    };\n    // These methods can yield, according to\n    // https://github.com/laverdet/node-fibers/blob/ddebed9b8ae3883e57f822e2108e6943e5c8d2a8/fibers.js#L97-L100\n    wrap(Fiber, \"yield\");\n    wrap(Fiber.prototype, \"run\");\n    wrap(Fiber.prototype, \"throwInto\");\n    wrappedFibers.push(Fiber);\n  }\n  return Fiber;\n}\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}