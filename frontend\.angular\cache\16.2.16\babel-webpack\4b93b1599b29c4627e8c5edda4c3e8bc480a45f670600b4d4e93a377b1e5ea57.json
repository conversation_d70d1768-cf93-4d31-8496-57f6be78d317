{"ast": null, "code": "import { getOperationDefinition } from \"./getFromAST.js\";\nfunction isOperation(document, operation) {\n  var _a;\n  return ((_a = getOperationDefinition(document)) === null || _a === void 0 ? void 0 : _a.operation) === operation;\n}\nexport function isMutationOperation(document) {\n  return isOperation(document, \"mutation\");\n}\nexport function isQueryOperation(document) {\n  return isOperation(document, \"query\");\n}\nexport function isSubscriptionOperation(document) {\n  return isOperation(document, \"subscription\");\n}\n//# sourceMappingURL=operations.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}