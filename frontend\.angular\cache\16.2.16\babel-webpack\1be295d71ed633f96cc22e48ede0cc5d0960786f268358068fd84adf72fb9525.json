{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Observable } from \"./Observable.js\";\nimport { iterateObserversSafely } from \"./iteration.js\";\nimport { fixObservableSubclass } from \"./subclassing.js\";\nfunction isPromiseLike(value) {\n  return value && typeof value.then === \"function\";\n}\n// A Concast<T> observable concatenates the given sources into a single\n// non-overlapping sequence of Ts, automatically unwrapping any promises,\n// and broadcasts the T elements of that sequence to any number of\n// subscribers, all without creating a bunch of intermediary Observable\n// wrapper objects.\n//\n// Even though any number of observers can subscribe to the Concast, each\n// source observable is guaranteed to receive at most one subscribe call,\n// and the results are multicast to all observers.\n//\n// In addition to broadcasting every next/error message to this.observers,\n// the Concast stores the most recent message using this.latest, so any\n// new observers can immediately receive the latest message, even if it\n// was originally delivered in the past. This behavior means we can assume\n// every active observer in this.observers has received the same most\n// recent message.\n//\n// With the exception of this.latest replay, a Concast is a \"hot\"\n// observable in the sense that it does not replay past results from the\n// beginning of time for each new observer.\n//\n// Could we have used some existing RxJS class instead? Concast<T> is\n// similar to a BehaviorSubject<T>, because it is multicast and redelivers\n// the latest next/error message to new subscribers. Unlike Subject<T>,\n// Concast<T> does not expose an Observer<T> interface (this.handlers is\n// intentionally private), since Concast<T> gets its inputs from the\n// concatenated sources. If we ever switch to RxJS, there may be some\n// value in reusing their code, but for now we use zen-observable, which\n// does not contain any Subject implementations.\nvar Concast = /** @class */function (_super) {\n  __extends(Concast, _super);\n  // Not only can the individual elements of the iterable be promises, but\n  // also the iterable itself can be wrapped in a promise.\n  function Concast(sources) {\n    var _this = _super.call(this, function (observer) {\n      _this.addObserver(observer);\n      return function () {\n        return _this.removeObserver(observer);\n      };\n    }) || this;\n    // Active observers receiving broadcast messages. Thanks to this.latest,\n    // we can assume all observers in this Set have received the same most\n    // recent message, though possibly at different times in the past.\n    _this.observers = new Set();\n    _this.promise = new Promise(function (resolve, reject) {\n      _this.resolve = resolve;\n      _this.reject = reject;\n    });\n    // Bound handler functions that can be reused for every internal\n    // subscription.\n    _this.handlers = {\n      next: function (result) {\n        if (_this.sub !== null) {\n          _this.latest = [\"next\", result];\n          _this.notify(\"next\", result);\n          iterateObserversSafely(_this.observers, \"next\", result);\n        }\n      },\n      error: function (error) {\n        var sub = _this.sub;\n        if (sub !== null) {\n          // Delay unsubscribing from the underlying subscription slightly,\n          // so that immediately subscribing another observer can keep the\n          // subscription active.\n          if (sub) setTimeout(function () {\n            return sub.unsubscribe();\n          });\n          _this.sub = null;\n          _this.latest = [\"error\", error];\n          _this.reject(error);\n          _this.notify(\"error\", error);\n          iterateObserversSafely(_this.observers, \"error\", error);\n        }\n      },\n      complete: function () {\n        var _a = _this,\n          sub = _a.sub,\n          _b = _a.sources,\n          sources = _b === void 0 ? [] : _b;\n        if (sub !== null) {\n          // If complete is called before concast.start, this.sources may be\n          // undefined, so we use a default value of [] for sources. That works\n          // here because it falls into the if (!value) {...} block, which\n          // appropriately terminates the Concast, even if this.sources might\n          // eventually have been initialized to a non-empty array.\n          var value = sources.shift();\n          if (!value) {\n            if (sub) setTimeout(function () {\n              return sub.unsubscribe();\n            });\n            _this.sub = null;\n            if (_this.latest && _this.latest[0] === \"next\") {\n              _this.resolve(_this.latest[1]);\n            } else {\n              _this.resolve();\n            }\n            _this.notify(\"complete\");\n            // We do not store this.latest = [\"complete\"], because doing so\n            // discards useful information about the previous next (or\n            // error) message. Instead, if new observers subscribe after\n            // this Concast has completed, they will receive the final\n            // 'next' message (unless there was an error) immediately\n            // followed by a 'complete' message (see addObserver).\n            iterateObserversSafely(_this.observers, \"complete\");\n          } else if (isPromiseLike(value)) {\n            value.then(function (obs) {\n              return _this.sub = obs.subscribe(_this.handlers);\n            }, _this.handlers.error);\n          } else {\n            _this.sub = value.subscribe(_this.handlers);\n          }\n        }\n      }\n    };\n    _this.nextResultListeners = new Set();\n    // A public way to abort observation and broadcast.\n    _this.cancel = function (reason) {\n      _this.reject(reason);\n      _this.sources = [];\n      _this.handlers.error(reason);\n    };\n    // Suppress rejection warnings for this.promise, since it's perfectly\n    // acceptable to pay no attention to this.promise if you're consuming\n    // the results through the normal observable API.\n    _this.promise.catch(function (_) {});\n    // If someone accidentally tries to create a Concast using a subscriber\n    // function, recover by creating an Observable from that subscriber and\n    // using it as the source.\n    if (typeof sources === \"function\") {\n      sources = [new Observable(sources)];\n    }\n    if (isPromiseLike(sources)) {\n      sources.then(function (iterable) {\n        return _this.start(iterable);\n      }, _this.handlers.error);\n    } else {\n      _this.start(sources);\n    }\n    return _this;\n  }\n  Concast.prototype.start = function (sources) {\n    if (this.sub !== void 0) return;\n    // In practice, sources is most often simply an Array of observables.\n    // TODO Consider using sources[Symbol.iterator]() to take advantage\n    // of the laziness of non-Array iterables.\n    this.sources = Array.from(sources);\n    // Calling this.handlers.complete() kicks off consumption of the first\n    // source observable. It's tempting to do this step lazily in\n    // addObserver, but this.promise can be accessed without calling\n    // addObserver, so consumption needs to begin eagerly.\n    this.handlers.complete();\n  };\n  Concast.prototype.deliverLastMessage = function (observer) {\n    if (this.latest) {\n      var nextOrError = this.latest[0];\n      var method = observer[nextOrError];\n      if (method) {\n        method.call(observer, this.latest[1]);\n      }\n      // If the subscription is already closed, and the last message was\n      // a 'next' message, simulate delivery of the final 'complete'\n      // message again.\n      if (this.sub === null && nextOrError === \"next\" && observer.complete) {\n        observer.complete();\n      }\n    }\n  };\n  Concast.prototype.addObserver = function (observer) {\n    if (!this.observers.has(observer)) {\n      // Immediately deliver the most recent message, so we can always\n      // be sure all observers have the latest information.\n      this.deliverLastMessage(observer);\n      this.observers.add(observer);\n    }\n  };\n  Concast.prototype.removeObserver = function (observer) {\n    if (this.observers.delete(observer) && this.observers.size < 1) {\n      // In case there are still any listeners in this.nextResultListeners, and\n      // no error or completion has been broadcast yet, make sure those\n      // observers have a chance to run and then remove themselves from\n      // this.observers.\n      this.handlers.complete();\n    }\n  };\n  Concast.prototype.notify = function (method, arg) {\n    var nextResultListeners = this.nextResultListeners;\n    if (nextResultListeners.size) {\n      // Replacing this.nextResultListeners first ensures it does not grow while\n      // we are iterating over it, potentially leading to infinite loops.\n      this.nextResultListeners = new Set();\n      nextResultListeners.forEach(function (listener) {\n        return listener(method, arg);\n      });\n    }\n  };\n  // We need a way to run callbacks just *before* the next result (or error or\n  // completion) is delivered by this Concast, so we can be sure any code that\n  // runs as a result of delivering that result/error observes the effects of\n  // running the callback(s). It was tempting to reuse the Observer type instead\n  // of introducing NextResultListener, but that messes with the sizing and\n  // maintenance of this.observers, and ends up being more code overall.\n  Concast.prototype.beforeNext = function (callback) {\n    var called = false;\n    this.nextResultListeners.add(function (method, arg) {\n      if (!called) {\n        called = true;\n        callback(method, arg);\n      }\n    });\n  };\n  return Concast;\n}(Observable);\nexport { Concast };\n// Necessary because the Concast constructor has a different signature\n// than the Observable constructor.\nfixObservableSubclass(Concast);\n//# sourceMappingURL=Concast.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}