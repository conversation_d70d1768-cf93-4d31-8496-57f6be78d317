{"ast": null, "code": "import { newInvariantError } from \"../globals/index.js\";\nimport { isNonNullObject } from \"../common/objects.js\";\nimport { getFragmentFromSelection } from \"./fragments.js\";\nimport { canonicalStringify } from \"../common/canonicalStringify.js\";\nexport function makeReference(id) {\n  return {\n    __ref: String(id)\n  };\n}\nexport function isReference(obj) {\n  return Boolean(obj && typeof obj === \"object\" && typeof obj.__ref === \"string\");\n}\nexport function isDocumentNode(value) {\n  return isNonNullObject(value) && value.kind === \"Document\" && Array.isArray(value.definitions);\n}\nfunction isStringValue(value) {\n  return value.kind === \"StringValue\";\n}\nfunction isBooleanValue(value) {\n  return value.kind === \"BooleanValue\";\n}\nfunction isIntValue(value) {\n  return value.kind === \"IntValue\";\n}\nfunction isFloatValue(value) {\n  return value.kind === \"FloatValue\";\n}\nfunction isVariable(value) {\n  return value.kind === \"Variable\";\n}\nfunction isObjectValue(value) {\n  return value.kind === \"ObjectValue\";\n}\nfunction isListValue(value) {\n  return value.kind === \"ListValue\";\n}\nfunction isEnumValue(value) {\n  return value.kind === \"EnumValue\";\n}\nfunction isNullValue(value) {\n  return value.kind === \"NullValue\";\n}\nexport function valueToObjectRepresentation(argObj, name, value, variables) {\n  if (isIntValue(value) || isFloatValue(value)) {\n    argObj[name.value] = Number(value.value);\n  } else if (isBooleanValue(value) || isStringValue(value)) {\n    argObj[name.value] = value.value;\n  } else if (isObjectValue(value)) {\n    var nestedArgObj_1 = {};\n    value.fields.map(function (obj) {\n      return valueToObjectRepresentation(nestedArgObj_1, obj.name, obj.value, variables);\n    });\n    argObj[name.value] = nestedArgObj_1;\n  } else if (isVariable(value)) {\n    var variableValue = (variables || {})[value.name.value];\n    argObj[name.value] = variableValue;\n  } else if (isListValue(value)) {\n    argObj[name.value] = value.values.map(function (listValue) {\n      var nestedArgArrayObj = {};\n      valueToObjectRepresentation(nestedArgArrayObj, name, listValue, variables);\n      return nestedArgArrayObj[name.value];\n    });\n  } else if (isEnumValue(value)) {\n    argObj[name.value] = value.value;\n  } else if (isNullValue(value)) {\n    argObj[name.value] = null;\n  } else {\n    throw newInvariantError(96, name.value, value.kind);\n  }\n}\nexport function storeKeyNameFromField(field, variables) {\n  var directivesObj = null;\n  if (field.directives) {\n    directivesObj = {};\n    field.directives.forEach(function (directive) {\n      directivesObj[directive.name.value] = {};\n      if (directive.arguments) {\n        directive.arguments.forEach(function (_a) {\n          var name = _a.name,\n            value = _a.value;\n          return valueToObjectRepresentation(directivesObj[directive.name.value], name, value, variables);\n        });\n      }\n    });\n  }\n  var argObj = null;\n  if (field.arguments && field.arguments.length) {\n    argObj = {};\n    field.arguments.forEach(function (_a) {\n      var name = _a.name,\n        value = _a.value;\n      return valueToObjectRepresentation(argObj, name, value, variables);\n    });\n  }\n  return getStoreKeyName(field.name.value, argObj, directivesObj);\n}\nvar KNOWN_DIRECTIVES = [\"connection\", \"include\", \"skip\", \"client\", \"rest\", \"export\", \"nonreactive\"];\n// Default stable JSON.stringify implementation used by getStoreKeyName. Can be\n// updated/replaced with something better by calling\n// getStoreKeyName.setStringify(newStringifyFunction).\nvar storeKeyNameStringify = canonicalStringify;\nexport var getStoreKeyName = Object.assign(function (fieldName, args, directives) {\n  if (args && directives && directives[\"connection\"] && directives[\"connection\"][\"key\"]) {\n    if (directives[\"connection\"][\"filter\"] && directives[\"connection\"][\"filter\"].length > 0) {\n      var filterKeys = directives[\"connection\"][\"filter\"] ? directives[\"connection\"][\"filter\"] : [];\n      filterKeys.sort();\n      var filteredArgs_1 = {};\n      filterKeys.forEach(function (key) {\n        filteredArgs_1[key] = args[key];\n      });\n      return \"\".concat(directives[\"connection\"][\"key\"], \"(\").concat(storeKeyNameStringify(filteredArgs_1), \")\");\n    } else {\n      return directives[\"connection\"][\"key\"];\n    }\n  }\n  var completeFieldName = fieldName;\n  if (args) {\n    // We can't use `JSON.stringify` here since it's non-deterministic,\n    // and can lead to different store key names being created even though\n    // the `args` object used during creation has the same properties/values.\n    var stringifiedArgs = storeKeyNameStringify(args);\n    completeFieldName += \"(\".concat(stringifiedArgs, \")\");\n  }\n  if (directives) {\n    Object.keys(directives).forEach(function (key) {\n      if (KNOWN_DIRECTIVES.indexOf(key) !== -1) return;\n      if (directives[key] && Object.keys(directives[key]).length) {\n        completeFieldName += \"@\".concat(key, \"(\").concat(storeKeyNameStringify(directives[key]), \")\");\n      } else {\n        completeFieldName += \"@\".concat(key);\n      }\n    });\n  }\n  return completeFieldName;\n}, {\n  setStringify: function (s) {\n    var previous = storeKeyNameStringify;\n    storeKeyNameStringify = s;\n    return previous;\n  }\n});\nexport function argumentsObjectFromField(field, variables) {\n  if (field.arguments && field.arguments.length) {\n    var argObj_1 = {};\n    field.arguments.forEach(function (_a) {\n      var name = _a.name,\n        value = _a.value;\n      return valueToObjectRepresentation(argObj_1, name, value, variables);\n    });\n    return argObj_1;\n  }\n  return null;\n}\nexport function resultKeyNameFromField(field) {\n  return field.alias ? field.alias.value : field.name.value;\n}\nexport function getTypenameFromResult(result, selectionSet, fragmentMap) {\n  var fragments;\n  for (var _i = 0, _a = selectionSet.selections; _i < _a.length; _i++) {\n    var selection = _a[_i];\n    if (isField(selection)) {\n      if (selection.name.value === \"__typename\") {\n        return result[resultKeyNameFromField(selection)];\n      }\n    } else if (fragments) {\n      fragments.push(selection);\n    } else {\n      fragments = [selection];\n    }\n  }\n  if (typeof result.__typename === \"string\") {\n    return result.__typename;\n  }\n  if (fragments) {\n    for (var _b = 0, fragments_1 = fragments; _b < fragments_1.length; _b++) {\n      var selection = fragments_1[_b];\n      var typename = getTypenameFromResult(result, getFragmentFromSelection(selection, fragmentMap).selectionSet, fragmentMap);\n      if (typeof typename === \"string\") {\n        return typename;\n      }\n    }\n  }\n}\nexport function isField(selection) {\n  return selection.kind === \"Field\";\n}\nexport function isInlineFragment(selection) {\n  return selection.kind === \"InlineFragment\";\n}\n//# sourceMappingURL=storeUtils.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}