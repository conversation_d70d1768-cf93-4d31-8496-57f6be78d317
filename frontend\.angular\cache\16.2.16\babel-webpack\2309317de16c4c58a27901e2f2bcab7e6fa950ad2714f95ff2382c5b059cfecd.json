{"ast": null, "code": "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/promise.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function promiseIterator(promise) {\n  var resolved = false;\n  var iterator = {\n    next: function () {\n      if (resolved) return Promise.resolve({\n        value: undefined,\n        done: true\n      });\n      resolved = true;\n      return new Promise(function (resolve, reject) {\n        promise.then(function (value) {\n          resolve({\n            value: value,\n            done: false\n          });\n        }).catch(reject);\n      });\n    }\n  };\n  if (canUseAsyncIteratorSymbol) {\n    iterator[Symbol.asyncIterator] = function () {\n      return this;\n    };\n  }\n  return iterator;\n}\n//# sourceMappingURL=promise.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}