{"ast": null, "code": "import { Observable } from '../Observable';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { OperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { innerFrom } from './innerFrom';\nexport function onErrorResumeNext(...sources) {\n  const nextSources = argsOrArgArray(sources);\n  return new Observable(subscriber => {\n    let sourceIndex = 0;\n    const subscribeNext = () => {\n      if (sourceIndex < nextSources.length) {\n        let nextSource;\n        try {\n          nextSource = innerFrom(nextSources[sourceIndex++]);\n        } catch (err) {\n          subscribeNext();\n          return;\n        }\n        const innerSubscriber = new OperatorSubscriber(subscriber, undefined, noop, noop);\n        nextSource.subscribe(innerSubscriber);\n        innerSubscriber.add(subscribeNext);\n      } else {\n        subscriber.complete();\n      }\n    };\n    subscribeNext();\n  });\n}\n//# sourceMappingURL=onErrorResumeNext.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}