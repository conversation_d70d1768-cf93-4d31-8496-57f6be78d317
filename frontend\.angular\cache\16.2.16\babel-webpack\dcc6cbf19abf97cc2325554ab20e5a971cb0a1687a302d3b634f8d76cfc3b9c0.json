{"ast": null, "code": "import { EraParser } from \"./parsers/EraParser.js\";\nimport { YearParser } from \"./parsers/YearParser.js\";\nimport { LocalWeekYearParser } from \"./parsers/LocalWeekYearParser.js\";\nimport { ISOWeekYearParser } from \"./parsers/ISOWeekYearParser.js\";\nimport { ExtendedYearParser } from \"./parsers/ExtendedYearParser.js\";\nimport { QuarterParser } from \"./parsers/QuarterParser.js\";\nimport { StandAloneQuarterParser } from \"./parsers/StandAloneQuarterParser.js\";\nimport { MonthParser } from \"./parsers/MonthParser.js\";\nimport { StandAloneMonthParser } from \"./parsers/StandAloneMonthParser.js\";\nimport { LocalWeekParser } from \"./parsers/LocalWeekParser.js\";\nimport { ISOWeekParser } from \"./parsers/ISOWeekParser.js\";\nimport { DateParser } from \"./parsers/DateParser.js\";\nimport { DayOfYearParser } from \"./parsers/DayOfYearParser.js\";\nimport { DayParser } from \"./parsers/DayParser.js\";\nimport { LocalDayParser } from \"./parsers/LocalDayParser.js\";\nimport { StandAloneLocalDayParser } from \"./parsers/StandAloneLocalDayParser.js\";\nimport { ISODayParser } from \"./parsers/ISODayParser.js\";\nimport { AMPMParser } from \"./parsers/AMPMParser.js\";\nimport { AMPMMidnightParser } from \"./parsers/AMPMMidnightParser.js\";\nimport { DayPeriodParser } from \"./parsers/DayPeriodParser.js\";\nimport { Hour1to12Parser } from \"./parsers/Hour1to12Parser.js\";\nimport { Hour0to23Parser } from \"./parsers/Hour0to23Parser.js\";\nimport { Hour0To11Parser } from \"./parsers/Hour0To11Parser.js\";\nimport { Hour1To24Parser } from \"./parsers/Hour1To24Parser.js\";\nimport { MinuteParser } from \"./parsers/MinuteParser.js\";\nimport { SecondParser } from \"./parsers/SecondParser.js\";\nimport { FractionOfSecondParser } from \"./parsers/FractionOfSecondParser.js\";\nimport { ISOTimezoneWithZParser } from \"./parsers/ISOTimezoneWithZParser.js\";\nimport { ISOTimezoneParser } from \"./parsers/ISOTimezoneParser.js\";\nimport { TimestampSecondsParser } from \"./parsers/TimestampSecondsParser.js\";\nimport { TimestampMillisecondsParser } from \"./parsers/TimestampMillisecondsParser.js\";\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* | Milliseconds in day            |\n * |  b  | AM, PM, noon, midnight         |  B  | Flexible day period            |\n * |  c  | Stand-alone local day of week  |  C* | Localized hour w/ day period   |\n * |  d  | Day of month                   |  D  | Day of year                    |\n * |  e  | Local day of week              |  E  | Day of week                    |\n * |  f  |                                |  F* | Day of week in month           |\n * |  g* | Modified Julian day            |  G  | Era                            |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  i! | ISO day of week                |  I! | ISO week of year               |\n * |  j* | Localized hour w/ day period   |  J* | Localized hour w/o day period  |\n * |  k  | Hour [1-24]                    |  K  | Hour [0-11]                    |\n * |  l* | (deprecated)                   |  L  | Stand-alone month              |\n * |  m  | Minute                         |  M  | Month                          |\n * |  n  |                                |  N  |                                |\n * |  o! | Ordinal number modifier        |  O* | Timezone (GMT)                 |\n * |  p  |                                |  P  |                                |\n * |  q  | Stand-alone quarter            |  Q  | Quarter                        |\n * |  r* | Related Gregorian year         |  R! | ISO week-numbering year        |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  t! | Seconds timestamp              |  T! | Milliseconds timestamp         |\n * |  u  | Extended year                  |  U* | Cyclic year                    |\n * |  v* | Timezone (generic non-locat.)  |  V* | Timezone (location)            |\n * |  w  | Local week of year             |  W* | Week of month                  |\n * |  x  | Timezone (ISO-8601 w/o Z)      |  X  | Timezone (ISO-8601)            |\n * |  y  | Year (abs)                     |  Y  | Local week-numbering year      |\n * |  z* | Timezone (specific non-locat.) |  Z* | Timezone (aliases)             |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n *\n * Letters marked by ! are non-standard, but implemented by date-fns:\n * - `o` modifies the previous token to turn it into an ordinal (see `parse` docs)\n * - `i` is ISO day of week. For `i` and `ii` is returns numeric ISO week days,\n *   i.e. 7 for Sunday, 1 for Monday, etc.\n * - `I` is ISO week of year, as opposed to `w` which is local week of year.\n * - `R` is ISO week-numbering year, as opposed to `Y` which is local week-numbering year.\n *   `R` is supposed to be used in conjunction with `I` and `i`\n *   for universal ISO week-numbering date, whereas\n *   `Y` is supposed to be used in conjunction with `w` and `e`\n *   for week-numbering date specific to the locale.\n */\nexport const parsers = {\n  G: new EraParser(),\n  y: new YearParser(),\n  Y: new LocalWeekYearParser(),\n  R: new ISOWeekYearParser(),\n  u: new ExtendedYearParser(),\n  Q: new QuarterParser(),\n  q: new StandAloneQuarterParser(),\n  M: new MonthParser(),\n  L: new StandAloneMonthParser(),\n  w: new LocalWeekParser(),\n  I: new ISOWeekParser(),\n  d: new DateParser(),\n  D: new DayOfYearParser(),\n  E: new DayParser(),\n  e: new LocalDayParser(),\n  c: new StandAloneLocalDayParser(),\n  i: new ISODayParser(),\n  a: new AMPMParser(),\n  b: new AMPMMidnightParser(),\n  B: new DayPeriodParser(),\n  h: new Hour1to12Parser(),\n  H: new Hour0to23Parser(),\n  K: new Hour0To11Parser(),\n  k: new Hour1To24Parser(),\n  m: new MinuteParser(),\n  s: new SecondParser(),\n  S: new FractionOfSecondParser(),\n  X: new ISOTimezoneWithZParser(),\n  x: new ISOTimezoneParser(),\n  t: new TimestampSecondsParser(),\n  T: new TimestampMillisecondsParser()\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}