{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { visit } from \"graphql\";\nexport function filterOperationVariables(variables, query) {\n  var result = __assign({}, variables);\n  var unusedNames = new Set(Object.keys(variables));\n  visit(query, {\n    Variable: function (node, _key, parent) {\n      // A variable type definition at the top level of a query is not\n      // enough to silence server-side errors about the variable being\n      // unused, so variable definitions do not count as usage.\n      // https://spec.graphql.org/draft/#sec-All-Variables-Used\n      if (parent && parent.kind !== \"VariableDefinition\") {\n        unusedNames.delete(node.name.value);\n      }\n    }\n  });\n  unusedNames.forEach(function (name) {\n    delete result[name];\n  });\n  return result;\n}\n//# sourceMappingURL=filterOperationVariables.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}