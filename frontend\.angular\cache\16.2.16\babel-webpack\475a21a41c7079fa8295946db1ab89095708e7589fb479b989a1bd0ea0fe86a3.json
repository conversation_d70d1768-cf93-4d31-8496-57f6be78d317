{"ast": null, "code": "import { Slot } from \"@wry/context\";\nexport const parentEntrySlot = new Slot();\nexport function nonReactive(fn) {\n  return parentEntrySlot.withValue(void 0, fn);\n}\nexport { Slot };\nexport { bind as bindContext, noContext, setTimeout, asyncFromGen } from \"@wry/context\";\n//# sourceMappingURL=context.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}