{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@app/services/authuser.service\";\nimport * as i5 from \"@angular/common\";\nfunction AddProjectComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 5);\n    i0.ɵɵelement(2, \"path\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Le titre est requis\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AddProjectComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 5);\n    i0.ɵɵelement(2, \"path\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"La description est requise\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AddProjectComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 5);\n    i0.ɵɵelement(2, \"path\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"La date limite est requise\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AddProjectComponent_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 5);\n    i0.ɵɵelement(2, \"path\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Le groupe est requis\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let AddProjectComponent = /*#__PURE__*/(() => {\n  class AddProjectComponent {\n    constructor(fb, projetService, router, authService) {\n      this.fb = fb;\n      this.projetService = projetService;\n      this.router = router;\n      this.authService = authService;\n      this.selectedFiles = [];\n      this.isSubmitting = false;\n      this.projetForm = this.fb.group({\n        titre: ['', Validators.required],\n        description: [''],\n        dateLimite: ['', Validators.required],\n        fichiers: [null],\n        groupe: ['', Validators.required] // ← champ pour l'ID du groupe\n      });\n    }\n\n    onFileChange(event) {\n      const input = event.target;\n      if (input.files) {\n        this.selectedFiles = Array.from(input.files);\n      }\n    }\n    onSubmit() {\n      if (this.projetForm.invalid) return;\n      this.isSubmitting = true;\n      console.log('Soumission du formulaire de projet');\n      const formData = new FormData();\n      formData.append('titre', this.projetForm.value.titre);\n      formData.append('description', this.projetForm.value.description || '');\n      formData.append('dateLimite', this.projetForm.value.dateLimite);\n      formData.append('groupe', this.projetForm.value.groupe);\n      // Méthode 1: Via le service d'authentification (recommandée)\n      const userIdFromService = this.authService.getCurrentUserId();\n      // Méthode 2: Via le currentUser du service\n      const currentUser = this.authService.getCurrentUser();\n      // Méthode 3: Vérification localStorage\n      const user = localStorage.getItem('user');\n      // Utiliser l'ID du service d'authentification en priorité\n      let userId = userIdFromService;\n      if (!userId && currentUser) {\n        userId = currentUser._id || currentUser.id;\n      }\n      if (!userId && user) {\n        userId = JSON.parse(user).id;\n      }\n      if (userId) {\n        formData.append('professeur', userId);\n      } else {\n        alert(\"Erreur: Impossible de récupérer l'ID utilisateur. Veuillez vous reconnecter.\");\n        return;\n      }\n      this.selectedFiles.forEach(file => {\n        formData.append('fichiers', file);\n      });\n      console.log('Données du formulaire:', {\n        titre: this.projetForm.value.titre,\n        description: this.projetForm.value.description,\n        dateLimite: this.projetForm.value.dateLimite,\n        groupe: this.projetForm.value.groupe,\n        fichiers: this.selectedFiles.map(f => f.name)\n      });\n      this.projetService.addProjet(formData).subscribe({\n        next: () => {\n          console.log('Projet ajouté avec succès');\n          alert('Projet ajouté avec succès');\n          this.router.navigate(['/admin/projects']);\n        },\n        error: err => {\n          console.error(\"Erreur lors de l'ajout du projet:\", err);\n          alert(\"Erreur lors de l'ajout du projet: \" + (err.error?.message || err.message || 'Erreur inconnue'));\n          this.isSubmitting = false;\n        },\n        complete: () => {\n          this.isSubmitting = false;\n        }\n      });\n    }\n    static {\n      this.ɵfac = function AddProjectComponent_Factory(t) {\n        return new (t || AddProjectComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.AuthuserService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AddProjectComponent,\n        selectors: [[\"app-add-project\"]],\n        decls: 135,\n        vars: 6,\n        consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [\"routerLink\", \"/admin/projects/list-project\", 1, \"hover:text-primary\", \"dark:hover:text-dark-accent-primary\", \"transition-colors\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"text-primary\", \"dark:text-dark-accent-primary\", \"font-medium\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 6v6m0 0v6m0-6h6m-6 0H6\"], [1, \"text-3xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"max-w-4xl\", \"mx-auto\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"overflow-hidden\"], [1, \"p-8\"], [\"enctype\", \"multipart/form-data\", 1, \"space-y-8\", 3, \"formGroup\", \"ngSubmit\"], [1, \"space-y-6\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-6\"], [1, \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-lg\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"space-y-2\"], [\"for\", \"titre\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"], [1, \"text-danger\", \"dark:text-danger-dark\"], [1, \"relative\"], [\"type\", \"text\", \"id\", \"titre\", \"formControlName\", \"titre\", \"placeholder\", \"Ex: D\\u00E9veloppement d'une application web\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-gray-400\", \"dark:placeholder-dark-text-secondary\"], [\"class\", \"flex items-center space-x-2 text-danger dark:text-danger-dark text-sm\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h7\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"4\", \"placeholder\", \"D\\u00E9crivez les objectifs, les livrables attendus et les crit\\u00E8res d'\\u00E9valuation...\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-gray-400\", \"dark:placeholder-dark-text-secondary\", \"resize-none\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"for\", \"dateLimite\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [\"type\", \"date\", \"id\", \"dateLimite\", \"formControlName\", \"dateLimite\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"for\", \"groupe\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [\"id\", \"groupe\", \"formControlName\", \"groupe\", 1, \"w-full\", \"px-4\", \"py-3\", \"pr-10\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"appearance-none\"], [\"value\", \"\"], [\"value\", \"1cinfo\"], [\"value\", \"2cinfo1\"], [\"value\", \"2cinfo2\"], [\"value\", \"2cinfo3\"], [\"value\", \"tous\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"pr-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-400\", \"dark:text-dark-text-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 9l-7 7-7-7\"], [1, \"bg-secondary/10\", \"dark:bg-dark-accent-secondary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-secondary\", \"dark:text-dark-accent-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [\"for\", \"fichiers\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-secondary\", \"dark:text-dark-accent-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\"], [\"type\", \"file\", \"id\", \"fichiers\", \"multiple\", \"\", 1, \"absolute\", \"inset-0\", \"w-full\", \"h-full\", \"opacity-0\", \"cursor-pointer\", \"z-10\", 3, \"change\"], [1, \"w-full\", \"px-6\", \"py-8\", \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"border-2\", \"border-dashed\", \"border-gray-300\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"hover:border-primary\", \"dark:hover:border-dark-accent-primary\", \"transition-all\", \"duration-200\", \"text-center\"], [1, \"space-y-3\"], [1, \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"p-3\", \"rounded-xl\", \"w-fit\", \"mx-auto\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-primary\", \"dark:text-dark-accent-primary\"], [1, \"text-text-dark\", \"dark:text-dark-text-primary\", \"font-medium\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\", \"pt-6\", \"border-t\", \"border-gray-200\", \"dark:border-dark-bg-tertiary/50\"], [\"type\", \"button\", \"routerLink\", \"/admin/projects/list-project\", 1, \"flex-1\", \"px-6\", \"py-3\", \"bg-gray-100\", \"dark:bg-dark-bg-tertiary\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"hover:bg-gray-200\", \"dark:hover:bg-dark-bg-tertiary/80\", \"rounded-xl\", \"transition-all\", \"duration-200\", \"font-medium\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [\"type\", \"submit\", 1, \"flex-1\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:hover:scale-100\", \"disabled:hover:shadow-none\", 3, \"disabled\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-danger\", \"dark:text-danger-dark\", \"text-sm\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"]],\n        template: function AddProjectComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"nav\", 3)(4, \"a\", 4);\n            i0.ɵɵtext(5, \"Projets\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(6, \"svg\", 5);\n            i0.ɵɵelement(7, \"path\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(8, \"span\", 7);\n            i0.ɵɵtext(9, \"Nouveau projet\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(13, \"svg\", 11);\n            i0.ɵɵelement(14, \"path\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(15, \"div\")(16, \"h1\", 13);\n            i0.ɵɵtext(17, \" Cr\\u00E9er un nouveau projet \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"p\", 14);\n            i0.ɵɵtext(19, \" Ajoutez un projet pour organiser le travail de vos \\u00E9tudiants \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(20, \"div\", 15)(21, \"div\", 16)(22, \"div\", 17)(23, \"form\", 18);\n            i0.ɵɵlistener(\"ngSubmit\", function AddProjectComponent_Template_form_ngSubmit_23_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(24, \"div\", 19)(25, \"div\", 20)(26, \"div\", 21);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(27, \"svg\", 22);\n            i0.ɵɵelement(28, \"path\", 23);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(29, \"h3\", 24);\n            i0.ɵɵtext(30, \"Informations g\\u00E9n\\u00E9rales\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 25)(32, \"label\", 26);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(33, \"svg\", 27);\n            i0.ɵɵelement(34, \"path\", 28);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(35, \"span\");\n            i0.ɵɵtext(36, \"Titre du projet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"span\", 29);\n            i0.ɵɵtext(38, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(39, \"div\", 30);\n            i0.ɵɵelement(40, \"input\", 31);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(41, AddProjectComponent_div_41_Template, 5, 0, \"div\", 32);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"div\", 25)(43, \"label\", 33);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(44, \"svg\", 27);\n            i0.ɵɵelement(45, \"path\", 34);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(46, \"span\");\n            i0.ɵɵtext(47, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"span\", 29);\n            i0.ɵɵtext(49, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(50, \"div\", 30);\n            i0.ɵɵelement(51, \"textarea\", 35);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(52, AddProjectComponent_div_52_Template, 5, 0, \"div\", 32);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"div\", 36)(54, \"div\", 25)(55, \"label\", 37);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(56, \"svg\", 27);\n            i0.ɵɵelement(57, \"path\", 38);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(58, \"span\");\n            i0.ɵɵtext(59, \"Date limite\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"span\", 29);\n            i0.ɵɵtext(61, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(62, \"div\", 30);\n            i0.ɵɵelement(63, \"input\", 39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(64, AddProjectComponent_div_64_Template, 5, 0, \"div\", 32);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"div\", 25)(66, \"label\", 40);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(67, \"svg\", 27);\n            i0.ɵɵelement(68, \"path\", 41);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(69, \"span\");\n            i0.ɵɵtext(70, \"Groupe cible\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"span\", 29);\n            i0.ɵɵtext(72, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(73, \"div\", 30)(74, \"select\", 42)(75, \"option\", 43);\n            i0.ɵɵtext(76, \"S\\u00E9lectionner un groupe\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"option\", 44);\n            i0.ɵɵtext(78, \"1cinfo\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"option\", 45);\n            i0.ɵɵtext(80, \"2cinfo1\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(81, \"option\", 46);\n            i0.ɵɵtext(82, \"2cinfo2\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(83, \"option\", 47);\n            i0.ɵɵtext(84, \"2cinfo3\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(85, \"option\", 48);\n            i0.ɵɵtext(86, \"Tous les groupes\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(87, \"div\", 49);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(88, \"svg\", 50);\n            i0.ɵɵelement(89, \"path\", 51);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(90, AddProjectComponent_div_90_Template, 5, 0, \"div\", 32);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(91, \"div\", 19)(92, \"div\", 20)(93, \"div\", 52);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(94, \"svg\", 53);\n            i0.ɵɵelement(95, \"path\", 54);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(96, \"h3\", 24);\n            i0.ɵɵtext(97, \"Fichiers du projet\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(98, \"div\", 25)(99, \"label\", 55);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(100, \"svg\", 56);\n            i0.ɵɵelement(101, \"path\", 57);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(102, \"span\");\n            i0.ɵɵtext(103, \"Documents et ressources\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(104, \"span\", 58);\n            i0.ɵɵtext(105, \"(optionnel)\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(106, \"div\", 30)(107, \"input\", 59);\n            i0.ɵɵlistener(\"change\", function AddProjectComponent_Template_input_change_107_listener($event) {\n              return ctx.onFileChange($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(108, \"div\", 60)(109, \"div\", 61)(110, \"div\", 62);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(111, \"svg\", 63);\n            i0.ɵɵelement(112, \"path\", 57);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(113, \"div\")(114, \"p\", 64);\n            i0.ɵɵtext(115, \"Glissez vos fichiers ici\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(116, \"p\", 65);\n            i0.ɵɵtext(117, \"ou cliquez pour parcourir\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(118, \"p\", 58);\n            i0.ɵɵtext(119, \"PDF, DOC, DOCX, images jusqu'\\u00E0 10MB\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(120, \"p\", 58);\n            i0.ɵɵtext(121, \"Ajoutez des \\u00E9nonc\\u00E9s, des ressources ou des exemples pour aider vos \\u00E9tudiants\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(122, \"div\", 66)(123, \"button\", 67)(124, \"div\", 68);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(125, \"svg\", 69);\n            i0.ɵɵelement(126, \"path\", 70);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(127, \"span\");\n            i0.ɵɵtext(128, \"Annuler\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(129, \"button\", 71)(130, \"div\", 68);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(131, \"svg\", 69);\n            i0.ɵɵelement(132, \"path\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(133, \"span\");\n            i0.ɵɵtext(134, \"Cr\\u00E9er le projet\");\n            i0.ɵɵelementEnd()()()()()()()()()();\n          }\n          if (rf & 2) {\n            let tmp_1_0;\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_4_0;\n            i0.ɵɵadvance(23);\n            i0.ɵɵproperty(\"formGroup\", ctx.projetForm);\n            i0.ɵɵadvance(18);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.projetForm.get(\"titre\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.projetForm.get(\"titre\")) == null ? null : tmp_1_0.touched));\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.projetForm.get(\"description\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.projetForm.get(\"description\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.projetForm.get(\"dateLimite\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.projetForm.get(\"dateLimite\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(26);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.projetForm.get(\"groupe\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.projetForm.get(\"groupe\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(39);\n            i0.ɵɵproperty(\"disabled\", ctx.projetForm.invalid);\n          }\n        },\n        dependencies: [i5.NgIf, i3.RouterLink, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName]\n      });\n    }\n  }\n  return AddProjectComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}