{"ast": null, "code": "import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { inspect } from '../../jsutils/inspect.mjs';\nimport { keyMap } from '../../jsutils/keyMap.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { print } from '../../language/printer.mjs';\nimport { getNamedType, getNullableType, isInputObjectType, isLeafType, isListType, isNonNullType, isRequiredInputField } from '../../type/definition.mjs';\n\n/**\n * Value literals of correct type\n *\n * A GraphQL document is only valid if all value literals are of the type\n * expected at their position.\n *\n * See https://spec.graphql.org/draft/#sec-Values-of-Correct-Type\n */\nexport function ValuesOfCorrectTypeRule(context) {\n  return {\n    ListValue(node) {\n      // Note: TypeInfo will traverse into a list's item type, so look to the\n      // parent input type to check if it is a list.\n      const type = getNullableType(context.getParentInputType());\n      if (!isListType(type)) {\n        isValidValueNode(context, node);\n        return false; // Don't traverse further.\n      }\n    },\n\n    ObjectValue(node) {\n      const type = getNamedType(context.getInputType());\n      if (!isInputObjectType(type)) {\n        isValidValueNode(context, node);\n        return false; // Don't traverse further.\n      } // Ensure every required field exists.\n\n      const fieldNodeMap = keyMap(node.fields, field => field.name.value);\n      for (const fieldDef of Object.values(type.getFields())) {\n        const fieldNode = fieldNodeMap[fieldDef.name];\n        if (!fieldNode && isRequiredInputField(fieldDef)) {\n          const typeStr = inspect(fieldDef.type);\n          context.reportError(new GraphQLError(`Field \"${type.name}.${fieldDef.name}\" of required type \"${typeStr}\" was not provided.`, {\n            nodes: node\n          }));\n        }\n      }\n    },\n    ObjectField(node) {\n      const parentType = getNamedType(context.getParentInputType());\n      const fieldType = context.getInputType();\n      if (!fieldType && isInputObjectType(parentType)) {\n        const suggestions = suggestionList(node.name.value, Object.keys(parentType.getFields()));\n        context.reportError(new GraphQLError(`Field \"${node.name.value}\" is not defined by type \"${parentType.name}\".` + didYouMean(suggestions), {\n          nodes: node\n        }));\n      }\n    },\n    NullValue(node) {\n      const type = context.getInputType();\n      if (isNonNullType(type)) {\n        context.reportError(new GraphQLError(`Expected value of type \"${inspect(type)}\", found ${print(node)}.`, {\n          nodes: node\n        }));\n      }\n    },\n    EnumValue: node => isValidValueNode(context, node),\n    IntValue: node => isValidValueNode(context, node),\n    FloatValue: node => isValidValueNode(context, node),\n    StringValue: node => isValidValueNode(context, node),\n    BooleanValue: node => isValidValueNode(context, node)\n  };\n}\n/**\n * Any value literal may be a valid representation of a Scalar, depending on\n * that scalar type.\n */\n\nfunction isValidValueNode(context, node) {\n  // Report any error at the full type expected by the location.\n  const locationType = context.getInputType();\n  if (!locationType) {\n    return;\n  }\n  const type = getNamedType(locationType);\n  if (!isLeafType(type)) {\n    const typeStr = inspect(locationType);\n    context.reportError(new GraphQLError(`Expected value of type \"${typeStr}\", found ${print(node)}.`, {\n      nodes: node\n    }));\n    return;\n  } // Scalars and Enums determine if a literal value is valid via parseLiteral(),\n  // which may throw or return an invalid value to indicate failure.\n\n  try {\n    const parseResult = type.parseLiteral(node, undefined\n    /* variables */);\n\n    if (parseResult === undefined) {\n      const typeStr = inspect(locationType);\n      context.reportError(new GraphQLError(`Expected value of type \"${typeStr}\", found ${print(node)}.`, {\n        nodes: node\n      }));\n    }\n  } catch (error) {\n    const typeStr = inspect(locationType);\n    if (error instanceof GraphQLError) {\n      context.reportError(error);\n    } else {\n      context.reportError(new GraphQLError(`Expected value of type \"${typeStr}\", found ${print(node)}; ` + error.message, {\n        nodes: node,\n        originalError: error\n      }));\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}