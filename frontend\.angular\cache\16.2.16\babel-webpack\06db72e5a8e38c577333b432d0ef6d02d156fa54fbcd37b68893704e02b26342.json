{"ast": null, "code": "import { inspect } from './inspect.mjs';\n/**\n * Sometimes a non-error is thrown, wrap it as an Error instance to ensure a consistent Error interface.\n */\n\nexport function toError(thrownValue) {\n  return thrownValue instanceof Error ? thrownValue : new NonErrorThrown(thrownValue);\n}\nclass NonErrorThrown extends Error {\n  constructor(thrownValue) {\n    super('Unexpected error value: ' + inspect(thrownValue));\n    this.name = 'NonErrorThrown';\n    this.thrownValue = thrownValue;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}