{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nexport let FileService = /*#__PURE__*/(() => {\n  class FileService {\n    constructor() {}\n    /**\n     * Génère une URL de téléchargement pour un fichier\n     * @param filePath Chemin du fichier\n     * @returns URL de téléchargement\n     */\n    getDownloadUrl(filePath) {\n      // Si le chemin est vide ou null, retourner une chaîne vide\n      if (!filePath) return '';\n      // Extraire uniquement le nom du fichier, peu importe le format du chemin\n      let fileName = filePath;\n      // Si c'est un chemin complet (contient C:/ ou autre)\n      if (filePath.includes('C:') || filePath.includes('/') || filePath.includes('\\\\')) {\n        // Prendre uniquement le nom du fichier (dernière partie après / ou \\)\n        const parts = filePath.split(/[\\/\\\\]/);\n        fileName = parts[parts.length - 1];\n      }\n      // Utiliser l'endpoint API spécifique pour le téléchargement\n      return `${environment.urlBackend}projets/telecharger/${fileName}`;\n    }\n    /**\n     * Extrait le nom du fichier à partir d'un chemin\n     * @param filePath Chemin du fichier\n     * @returns Nom du fichier\n     */\n    getFileName(filePath) {\n      if (!filePath) return 'fichier';\n      // Si c'est un chemin complet (contient / ou \\)\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        return parts[parts.length - 1];\n      }\n      return filePath;\n    }\n    static {\n      this.ɵfac = function FileService_Factory(t) {\n        return new (t || FileService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: FileService,\n        factory: FileService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return FileService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}