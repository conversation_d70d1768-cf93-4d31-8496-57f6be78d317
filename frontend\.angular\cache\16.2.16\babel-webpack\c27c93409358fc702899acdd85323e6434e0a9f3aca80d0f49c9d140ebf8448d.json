{"ast": null, "code": "\"use strict\";\n\nconst {\n  ApolloLink,\n  Observable\n} = require(\"@apollo/client/core\");\nconst {\n  createSignalIfSupported,\n  fallbackHttpConfig,\n  parseAndCheckHttpResponse,\n  rewriteURIForGET,\n  selectHttpOptionsAndBody,\n  selectURI,\n  serializeFetchParameter\n} = require(\"@apollo/client/link/http\");\nconst extractFiles = require(\"extract-files/public/extractFiles.js\");\nconst formDataAppendFile = require(\"./formDataAppendFile.js\");\nconst isExtractableFile = require(\"./isExtractableFile.js\");\n\n/**\n * Creates a\n * [terminating Apollo Link](https://apollographql.com/docs/react/api/link/introduction/#the-terminating-link)\n * for [Apollo Client](https://apollographql.com/docs/react) that fetches a\n * [GraphQL multipart request](https://github.com/jaydenseric/graphql-multipart-request-spec)\n * if the GraphQL variables contain files (by default\n * [`FileList`](https://developer.mozilla.org/en-US/docs/Web/API/FileList),\n * [`File`](https://developer.mozilla.org/en-US/docs/Web/API/File),\n * [`Blob`](https://developer.mozilla.org/en-US/docs/Web/API/Blob), or\n * [`ReactNativeFile`](#class-reactnativefile) instances), or else fetches a\n * regular\n * [GraphQL POST or GET request](https://apollographql.com/docs/apollo-server/requests)\n * (depending on the config and GraphQL operation).\n *\n * Some of the options are similar to the\n * [`createHttpLink` options](https://apollographql.com/docs/react/api/link/apollo-link-http/#httplink-constructor-options).\n * @see [GraphQL multipart request spec](https://github.com/jaydenseric/graphql-multipart-request-spec).\n * @kind function\n * @name createUploadLink\n * @param {object} options Options.\n * @param {string} [options.uri=\"/graphql\"] GraphQL endpoint URI.\n * @param {boolean} [options.useGETForQueries] Should GET be used to fetch queries, if there are no files to upload.\n * @param {ExtractableFileMatcher} [options.isExtractableFile=isExtractableFile] Customizes how files are matched in the GraphQL operation for extraction.\n * @param {class} [options.FormData] [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) implementation to use, defaulting to the [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) global.\n * @param {FormDataFileAppender} [options.formDataAppendFile=formDataAppendFile] Customizes how extracted files are appended to the [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) instance.\n * @param {Function} [options.fetch] [`fetch`](https://fetch.spec.whatwg.org) implementation to use, defaulting to the [`fetch`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch) global.\n * @param {FetchOptions} [options.fetchOptions] [`fetch` options]{@link FetchOptions}; overridden by upload requirements.\n * @param {string} [options.credentials] Overrides `options.fetchOptions.credentials`.\n * @param {object} [options.headers] Merges with and overrides `options.fetchOptions.headers`.\n * @param {boolean} [options.includeExtensions=false] Toggles sending `extensions` fields to the GraphQL server.\n * @returns {ApolloLink} A [terminating Apollo Link](https://apollographql.com/docs/react/api/link/introduction/#the-terminating-link).\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { createUploadLink } from \"apollo-upload-client\";\n * ```\n *\n * ```js\n * import createUploadLink from \"apollo-upload-client/public/createUploadLink.js\";\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { createUploadLink } = require(\"apollo-upload-client\");\n * ```\n *\n * ```js\n * const createUploadLink = require(\"apollo-upload-client/public/createUploadLink.js\");\n * ```\n * @example <caption>A basic Apollo Client setup.</caption>\n * ```js\n * import { ApolloClient, InMemoryCache } from \"@apollo/client\";\n * import createUploadLink from \"apollo-upload-client/public/createUploadLink.js\";\n *\n * const client = new ApolloClient({\n *   cache: new InMemoryCache(),\n *   link: createUploadLink(),\n * });\n * ```\n */\nmodule.exports = function createUploadLink({\n  uri: fetchUri = \"/graphql\",\n  useGETForQueries,\n  isExtractableFile: customIsExtractableFile = isExtractableFile,\n  FormData: CustomFormData,\n  formDataAppendFile: customFormDataAppendFile = formDataAppendFile,\n  fetch: customFetch,\n  fetchOptions,\n  credentials,\n  headers,\n  includeExtensions\n} = {}) {\n  const linkConfig = {\n    http: {\n      includeExtensions\n    },\n    options: fetchOptions,\n    credentials,\n    headers\n  };\n  return new ApolloLink(operation => {\n    const context = operation.getContext();\n    const {\n      // Apollo Studio client awareness `name` and `version` can be configured\n      // via `ApolloClient` constructor options:\n      // https://apollographql.com/docs/studio/client-awareness/#using-apollo-server-and-apollo-client\n      clientAwareness: {\n        name,\n        version\n      } = {},\n      headers\n    } = context;\n    const contextConfig = {\n      http: context.http,\n      options: context.fetchOptions,\n      credentials: context.credentials,\n      headers: {\n        // Client awareness headers can be overridden by context `headers`.\n        ...(name && {\n          \"apollographql-client-name\": name\n        }),\n        ...(version && {\n          \"apollographql-client-version\": version\n        }),\n        ...headers\n      }\n    };\n    const {\n      options,\n      body\n    } = selectHttpOptionsAndBody(operation, fallbackHttpConfig, linkConfig, contextConfig);\n    const {\n      clone,\n      files\n    } = extractFiles(body, \"\", customIsExtractableFile);\n    let uri = selectURI(operation, fetchUri);\n    if (files.size) {\n      // Automatically set by `fetch` when the `body` is a `FormData` instance.\n      delete options.headers[\"content-type\"];\n\n      // GraphQL multipart request spec:\n      // https://github.com/jaydenseric/graphql-multipart-request-spec\n\n      const RuntimeFormData = CustomFormData || FormData;\n      const form = new RuntimeFormData();\n      form.append(\"operations\", serializeFetchParameter(clone, \"Payload\"));\n      const map = {};\n      let i = 0;\n      files.forEach(paths => {\n        map[++i] = paths;\n      });\n      form.append(\"map\", JSON.stringify(map));\n      i = 0;\n      files.forEach((paths, file) => {\n        customFormDataAppendFile(form, ++i, file);\n      });\n      options.body = form;\n    } else {\n      if (useGETForQueries &&\n      // If the operation contains some mutations GET shouldn’t be used.\n      !operation.query.definitions.some(definition => definition.kind === \"OperationDefinition\" && definition.operation === \"mutation\")) options.method = \"GET\";\n      if (options.method === \"GET\") {\n        const {\n          newURI,\n          parseError\n        } = rewriteURIForGET(uri, body);\n        if (parseError)\n          // Apollo’s `HttpLink` uses `fromError` for this, but it’s not\n          // exported from `@apollo/client/link/http`.\n          return new Observable(observer => {\n            observer.error(parseError);\n          });\n        uri = newURI;\n      } else options.body = serializeFetchParameter(clone, \"Payload\");\n    }\n    const {\n      controller\n    } = createSignalIfSupported();\n    if (controller) {\n      if (options.signal)\n        // Respect the user configured abort controller signal.\n        options.signal.aborted ?\n        // Signal already aborted, so immediately abort.\n        controller.abort() :\n        // Signal not already aborted, so setup a listener to abort when it\n        // does.\n        options.signal.addEventListener(\"abort\", () => {\n          controller.abort();\n        }, {\n          // Prevent a memory leak if the user configured abort controller\n          // is long lasting, or controls multiple things.\n          once: true\n        });\n      options.signal = controller.signal;\n    }\n    const runtimeFetch = customFetch || fetch;\n    return new Observable(observer => {\n      // Used to track if the observable is being cleaned up.\n      let cleaningUp;\n      runtimeFetch(uri, options).then(response => {\n        // Forward the response on the context.\n        operation.setContext({\n          response\n        });\n        return response;\n      }).then(parseAndCheckHttpResponse(operation)).then(result => {\n        observer.next(result);\n        observer.complete();\n      }).catch(error => {\n        // If the observable is being cleaned up, there is no need to call\n        // next or error because there are no more subscribers. An error after\n        // cleanup begins is likely from the cleanup function aborting the\n        // fetch.\n        if (!cleaningUp) {\n          // For errors such as an invalid fetch URI there will be no GraphQL\n          // result with errors or data to forward.\n          if (error.result && error.result.errors && error.result.data) observer.next(error.result);\n          observer.error(error);\n        }\n      });\n\n      // Cleanup function.\n      return () => {\n        cleaningUp = true;\n\n        // Abort fetch. It’s ok to signal an abort even when not fetching.\n        if (controller) controller.abort();\n      };\n    });\n  });\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}