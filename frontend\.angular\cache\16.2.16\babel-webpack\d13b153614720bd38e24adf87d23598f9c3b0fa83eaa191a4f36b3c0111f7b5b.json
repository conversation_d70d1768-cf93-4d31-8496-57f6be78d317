{"ast": null, "code": "import { isWhiteSpace } from './characterClasses.mjs';\n/**\n * Produces the value of a block string from its parsed raw value, similar to\n * CoffeeScript's block string, Python's docstring trim or Ruby's strip_heredoc.\n *\n * This implements the GraphQL spec's BlockStringValue() static algorithm.\n *\n * @internal\n */\n\nexport function dedentBlockStringLines(lines) {\n  var _firstNonEmptyLine2;\n  let commonIndent = Number.MAX_SAFE_INTEGER;\n  let firstNonEmptyLine = null;\n  let lastNonEmptyLine = -1;\n  for (let i = 0; i < lines.length; ++i) {\n    var _firstNonEmptyLine;\n    const line = lines[i];\n    const indent = leadingWhitespace(line);\n    if (indent === line.length) {\n      continue; // skip empty lines\n    }\n\n    firstNonEmptyLine = (_firstNonEmptyLine = firstNonEmptyLine) !== null && _firstNonEmptyLine !== void 0 ? _firstNonEmptyLine : i;\n    lastNonEmptyLine = i;\n    if (i !== 0 && indent < commonIndent) {\n      commonIndent = indent;\n    }\n  }\n  return lines // Remove common indentation from all lines but first.\n  .map((line, i) => i === 0 ? line : line.slice(commonIndent)) // Remove leading and trailing blank lines.\n  .slice((_firstNonEmptyLine2 = firstNonEmptyLine) !== null && _firstNonEmptyLine2 !== void 0 ? _firstNonEmptyLine2 : 0, lastNonEmptyLine + 1);\n}\nfunction leadingWhitespace(str) {\n  let i = 0;\n  while (i < str.length && isWhiteSpace(str.charCodeAt(i))) {\n    ++i;\n  }\n  return i;\n}\n/**\n * @internal\n */\n\nexport function isPrintableAsBlockString(value) {\n  if (value === '') {\n    return true; // empty string is printable\n  }\n\n  let isEmptyLine = true;\n  let hasIndent = false;\n  let hasCommonIndent = true;\n  let seenNonEmptyLine = false;\n  for (let i = 0; i < value.length; ++i) {\n    switch (value.codePointAt(i)) {\n      case 0x0000:\n      case 0x0001:\n      case 0x0002:\n      case 0x0003:\n      case 0x0004:\n      case 0x0005:\n      case 0x0006:\n      case 0x0007:\n      case 0x0008:\n      case 0x000b:\n      case 0x000c:\n      case 0x000e:\n      case 0x000f:\n        return false;\n      // Has non-printable characters\n\n      case 0x000d:\n        //  \\r\n        return false;\n      // Has \\r or \\r\\n which will be replaced as \\n\n\n      case 10:\n        //  \\n\n        if (isEmptyLine && !seenNonEmptyLine) {\n          return false; // Has leading new line\n        }\n\n        seenNonEmptyLine = true;\n        isEmptyLine = true;\n        hasIndent = false;\n        break;\n      case 9: //   \\t\n\n      case 32:\n        //  <space>\n        hasIndent || (hasIndent = isEmptyLine);\n        break;\n      default:\n        hasCommonIndent && (hasCommonIndent = hasIndent);\n        isEmptyLine = false;\n    }\n  }\n  if (isEmptyLine) {\n    return false; // Has trailing empty lines\n  }\n\n  if (hasCommonIndent && seenNonEmptyLine) {\n    return false; // Has internal indent\n  }\n\n  return true;\n}\n/**\n * Print a block string in the indented block form by adding a leading and\n * trailing blank line. However, if a block string starts with whitespace and is\n * a single-line, adding a leading blank line would strip that whitespace.\n *\n * @internal\n */\n\nexport function printBlockString(value, options) {\n  const escapedValue = value.replace(/\"\"\"/g, '\\\\\"\"\"'); // Expand a block string's raw value into independent lines.\n\n  const lines = escapedValue.split(/\\r\\n|[\\n\\r]/g);\n  const isSingleLine = lines.length === 1; // If common indentation is found we can fix some of those cases by adding leading new line\n\n  const forceLeadingNewLine = lines.length > 1 && lines.slice(1).every(line => line.length === 0 || isWhiteSpace(line.charCodeAt(0))); // Trailing triple quotes just looks confusing but doesn't force trailing new line\n\n  const hasTrailingTripleQuotes = escapedValue.endsWith('\\\\\"\"\"'); // Trailing quote (single or double) or slash forces trailing new line\n\n  const hasTrailingQuote = value.endsWith('\"') && !hasTrailingTripleQuotes;\n  const hasTrailingSlash = value.endsWith('\\\\');\n  const forceTrailingNewline = hasTrailingQuote || hasTrailingSlash;\n  const printAsMultipleLines = !(options !== null && options !== void 0 && options.minimize) && (\n  // add leading and trailing new lines only if it improves readability\n  !isSingleLine || value.length > 70 || forceTrailingNewline || forceLeadingNewLine || hasTrailingTripleQuotes);\n  let result = ''; // Format a multi-line block quote to account for leading space.\n\n  const skipLeadingNewLine = isSingleLine && isWhiteSpace(value.charCodeAt(0));\n  if (printAsMultipleLines && !skipLeadingNewLine || forceLeadingNewLine) {\n    result += '\\n';\n  }\n  result += escapedValue;\n  if (printAsMultipleLines || forceTrailingNewline) {\n    result += '\\n';\n  }\n  return '\"\"\"' + result + '\"\"\"';\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}