{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/projets.service\";\nimport * as i4 from \"src/app/services/rendus.service\";\nimport * as i5 from \"src/app/services/authuser.service\";\nimport * as i6 from \"@angular/common\";\nfunction ProjectSubmissionComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"div\", 23)(3, \"div\", 24);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectSubmissionComponent_div_28_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"La description est requise et doit contenir au moins 10 caract\\u00E8res.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectSubmissionComponent_div_28_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Veuillez s\\u00E9lectionner au moins un fichier.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectSubmissionComponent_div_28_div_49_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"div\", 73)(2, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 43);\n    i0.ɵɵelement(4, \"path\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\")(6, \"p\", 110);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 111);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"button\", 112);\n    i0.ɵɵlistener(\"click\", function ProjectSubmissionComponent_div_28_div_49_div_7_Template_button_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const i_r13 = restoredCtx.index;\n      const ctx_r14 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r14.removeFile(i_r13));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 8);\n    i0.ɵɵelement(12, \"path\", 113);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r12 = ctx.$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(file_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.getFileSize(file_r12.size));\n  }\n}\nfunction ProjectSubmissionComponent_div_28_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 89)(2, \"p\", 93);\n    i0.ɵɵtext(3, \"Fichiers s\\u00E9lectionn\\u00E9s :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 51);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 107);\n    i0.ɵɵtemplate(7, ProjectSubmissionComponent_div_28_div_49_div_7_Template, 13, 2, \"div\", 108);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.selectedFiles.length, \" fichier\", ctx_r4.selectedFiles.length > 1 ? \"s\" : \"\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedFiles);\n  }\n}\nfunction ProjectSubmissionComponent_div_28__svg_svg_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 61);\n    i0.ɵɵelement(1, \"path\", 16);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_28_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 114);\n  }\n}\nfunction ProjectSubmissionComponent_div_28_span_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumettre le projet\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_28_span_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Soumission en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectSubmissionComponent_div_28_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 115);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Compl\\u00E9tez le formulaire pour soumettre\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProjectSubmissionComponent_div_28_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Pr\\u00EAt \\u00E0 soumettre\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/projects/detail\", a1];\n};\nfunction ProjectSubmissionComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"div\", 28)(4, \"div\", 29);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 30);\n    i0.ɵɵelement(6, \"path\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"h3\", 32);\n    i0.ɵɵtext(8, \"Description de votre travail\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"form\", 33);\n    i0.ɵɵlistener(\"ngSubmit\", function ProjectSubmissionComponent_div_28_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onSubmit());\n    });\n    i0.ɵɵelementStart(10, \"div\", 34)(11, \"label\", 35);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 36);\n    i0.ɵɵelement(13, \"path\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"Rapport de projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 38);\n    i0.ɵɵtext(17, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 22);\n    i0.ɵɵelement(19, \"textarea\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 40)(21, \"span\");\n    i0.ɵɵtext(22, \"Minimum 10 caract\\u00E8res requis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(25, ProjectSubmissionComponent_div_28_div_25_Template, 5, 0, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 34)(27, \"label\", 42);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 43);\n    i0.ɵɵelement(29, \"path\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(30, \"span\");\n    i0.ɵɵtext(31, \"Fichiers du projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 38);\n    i0.ɵɵtext(33, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 22)(35, \"input\", 44);\n    i0.ɵɵlistener(\"change\", function ProjectSubmissionComponent_div_28_Template_input_change_35_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onFileChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 45)(37, \"div\", 46)(38, \"div\", 47);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(39, \"svg\", 48);\n    i0.ɵɵelement(40, \"path\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(41, \"div\")(42, \"p\", 49);\n    i0.ɵɵtext(43, \"Glissez vos fichiers ici\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\", 50);\n    i0.ɵɵtext(45, \"ou cliquez pour parcourir\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"p\", 51);\n    i0.ɵɵtext(47, \"Tous types de fichiers accept\\u00E9s\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(48, ProjectSubmissionComponent_div_28_div_48_Template, 5, 0, \"div\", 41);\n    i0.ɵɵtemplate(49, ProjectSubmissionComponent_div_28_div_49_Template, 8, 3, \"div\", 52);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(50, \"div\", 27)(51, \"div\", 28)(52, \"div\", 53);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(53, \"svg\", 54);\n    i0.ɵɵelement(54, \"path\", 55)(55, \"path\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(56, \"h3\", 32);\n    i0.ɵɵtext(57, \"Actions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 57)(59, \"div\", 58)(60, \"a\", 59)(61, \"div\", 60);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(62, \"svg\", 61);\n    i0.ɵɵelement(63, \"path\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(64, \"span\");\n    i0.ɵɵtext(65, \"Retour aux d\\u00E9tails\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(66, \"button\", 63)(67, \"div\", 60);\n    i0.ɵɵtemplate(68, ProjectSubmissionComponent_div_28__svg_svg_68_Template, 2, 0, \"svg\", 64);\n    i0.ɵɵtemplate(69, ProjectSubmissionComponent_div_28_div_69_Template, 1, 0, \"div\", 65);\n    i0.ɵɵtemplate(70, ProjectSubmissionComponent_div_28_span_70_Template, 2, 0, \"span\", 66);\n    i0.ɵɵtemplate(71, ProjectSubmissionComponent_div_28_span_71_Template, 2, 0, \"span\", 66);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(72, \"div\", 67);\n    i0.ɵɵtemplate(73, ProjectSubmissionComponent_div_28_div_73_Template, 5, 0, \"div\", 68);\n    i0.ɵɵtemplate(74, ProjectSubmissionComponent_div_28_div_74_Template, 5, 0, \"div\", 69);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(75, \"div\", 70)(76, \"div\", 71)(77, \"div\", 72)(78, \"div\", 73)(79, \"div\", 74);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(80, \"svg\", 75);\n    i0.ɵɵelement(81, \"path\", 76);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(82, \"div\")(83, \"h3\", 77);\n    i0.ɵɵtext(84, \"Informations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(85, \"p\", 78);\n    i0.ɵɵtext(86, \"D\\u00E9tails du projet\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(87, \"div\", 79)(88, \"div\", 80)(89, \"div\", 81)(90, \"div\", 82);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(91, \"svg\", 83);\n    i0.ɵɵelement(92, \"path\", 84);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(93, \"div\", 85)(94, \"p\", 86);\n    i0.ɵɵtext(95, \"Titre\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(96, \"p\", 87);\n    i0.ɵɵtext(97);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(98, \"div\", 80)(99, \"div\", 81)(100, \"div\", 29);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(101, \"svg\", 36);\n    i0.ɵɵelement(102, \"path\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(103, \"div\", 85)(104, \"p\", 86);\n    i0.ɵɵtext(105, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(106, \"p\", 88);\n    i0.ɵɵtext(107);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(108, \"div\", 80)(109, \"div\", 89)(110, \"div\", 73)(111, \"div\", 90);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(112, \"svg\", 91);\n    i0.ɵɵelement(113, \"path\", 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(114, \"div\")(115, \"p\", 86);\n    i0.ɵɵtext(116, \"Date limite\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(117, \"p\", 93);\n    i0.ɵɵtext(118);\n    i0.ɵɵpipe(119, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(120, \"div\", 94)(121, \"p\", 51);\n    i0.ɵɵtext(122);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(123, \"p\", 95);\n    i0.ɵɵtext(124, \"restants\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(125, \"div\", 80)(126, \"div\", 73)(127, \"div\", 96);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(128, \"svg\", 97);\n    i0.ɵɵelement(129, \"path\", 98);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(130, \"div\")(131, \"p\", 86);\n    i0.ɵɵtext(132, \"Groupe cible\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(133, \"p\", 93);\n    i0.ɵɵtext(134);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(135, \"div\", 27)(136, \"div\", 28)(137, \"div\", 29);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(138, \"svg\", 30);\n    i0.ɵɵelement(139, \"path\", 76);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(140, \"h3\", 32);\n    i0.ɵɵtext(141, \"Conseils\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(142, \"div\", 99)(143, \"div\", 100);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(144, \"svg\", 101);\n    i0.ɵɵelement(145, \"path\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(146, \"span\");\n    i0.ɵɵtext(147, \"D\\u00E9crivez clairement votre travail et les technologies utilis\\u00E9es\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(148, \"div\", 100);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(149, \"svg\", 101);\n    i0.ɵɵelement(150, \"path\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(151, \"span\");\n    i0.ɵɵtext(152, \"Incluez tous les fichiers sources et la documentation\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(153, \"div\", 100);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(154, \"svg\", 101);\n    i0.ɵɵelement(155, \"path\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(156, \"span\");\n    i0.ɵɵtext(157, \"Mentionnez les difficult\\u00E9s rencontr\\u00E9es et solutions apport\\u00E9es\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(158, \"div\", 100);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(159, \"svg\", 103);\n    i0.ɵɵelement(160, \"path\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(161, \"span\");\n    i0.ɵɵtext(162, \"V\\u00E9rifiez que tous vos fichiers sont bien s\\u00E9lectionn\\u00E9s\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.submissionForm);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate1(\"\", ((tmp_1_0 = ctx_r1.submissionForm.get(\"description\")) == null ? null : tmp_1_0.value == null ? null : tmp_1_0.value.length) || 0, \" caract\\u00E8res\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r1.submissionForm.get(\"description\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r1.submissionForm.get(\"description\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(23);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFiles.length === 0 && ctx_r1.submissionForm.touched);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFiles.length > 0);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(21, _c0, ctx_r1.projetId));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.submissionForm.invalid || ctx_r1.selectedFiles.length === 0 || ctx_r1.isSubmitting);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isSubmitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSubmitting);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submissionForm.invalid || ctx_r1.selectedFiles.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.submissionForm.valid && ctx_r1.selectedFiles.length > 0);\n    i0.ɵɵadvance(23);\n    i0.ɵɵtextInterpolate(ctx_r1.projet.titre);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r1.projet.description || \"Aucune description\");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(119, 18, ctx_r1.projet.dateLimite, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getRemainingDays(), \" jours\");\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r1.projet.groupe || \"Tous les groupes\");\n  }\n}\n// Composant pour soumettre un projet\nexport let ProjectSubmissionComponent = /*#__PURE__*/(() => {\n  class ProjectSubmissionComponent {\n    constructor(fb, route, router, projetService, rendusService, authService) {\n      this.fb = fb;\n      this.route = route;\n      this.router = router;\n      this.projetService = projetService;\n      this.rendusService = rendusService;\n      this.authService = authService;\n      this.projetId = '';\n      this.selectedFiles = [];\n      this.isLoading = true;\n      this.isSubmitting = false;\n      this.submissionForm = this.fb.group({\n        description: ['', [Validators.required, Validators.minLength(10)]]\n      });\n    }\n    ngOnInit() {\n      this.projetId = this.route.snapshot.paramMap.get('id') || '';\n      this.loadProjetDetails();\n    }\n    loadProjetDetails() {\n      this.isLoading = true;\n      this.projetService.getProjetById(this.projetId).subscribe({\n        next: projet => {\n          this.projet = projet;\n          this.isLoading = false;\n        },\n        error: err => {\n          console.error('Erreur lors du chargement du projet', err);\n          this.isLoading = false;\n          this.router.navigate(['/projects']);\n        }\n      });\n    }\n    onFileChange(event) {\n      const input = event.target;\n      if (input.files) {\n        this.selectedFiles = Array.from(input.files);\n      }\n    }\n    onSubmit() {\n      if (this.submissionForm.invalid || this.selectedFiles.length === 0) {\n        return;\n      }\n      this.isSubmitting = true;\n      const formData = new FormData();\n      formData.append('projet', this.projetId);\n      formData.append('etudiant', this.authService.getCurrentUserId() || '');\n      formData.append('description', this.submissionForm.value.description);\n      this.selectedFiles.forEach(file => {\n        formData.append('fichiers', file);\n      });\n      this.rendusService.submitRendu(formData).subscribe({\n        next: response => {\n          alert('Votre projet a été soumis avec succès');\n          this.router.navigate(['/projects']);\n        },\n        error: err => {\n          console.error('Erreur lors de la soumission du projet', err);\n          alert('Une erreur est survenue lors de la soumission du projet');\n          this.isSubmitting = false;\n        }\n      });\n    }\n    // Méthode pour supprimer un fichier de la sélection\n    removeFile(index) {\n      this.selectedFiles.splice(index, 1);\n    }\n    // Méthode pour formater la taille des fichiers\n    getFileSize(bytes) {\n      if (bytes === 0) return '0 B';\n      const k = 1024;\n      const sizes = ['B', 'KB', 'MB', 'GB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    }\n    // Méthode pour calculer les jours restants\n    getRemainingDays() {\n      if (!this.projet?.dateLimite) return 0;\n      const now = new Date();\n      const deadline = new Date(this.projet.dateLimite);\n      const diffTime = deadline.getTime() - now.getTime();\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n      return Math.max(0, diffDays);\n    }\n    static {\n      this.ɵfac = function ProjectSubmissionComponent_Factory(t) {\n        return new (t || ProjectSubmissionComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ProjetService), i0.ɵɵdirectiveInject(i4.RendusService), i0.ɵɵdirectiveInject(i5.AuthuserService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectSubmissionComponent,\n        selectors: [[\"app-project-submission\"]],\n        decls: 29,\n        vars: 6,\n        consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\", \"relative\", \"z-10\"], [1, \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-4\"], [\"routerLink\", \"/projects\", 1, \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\", 3, \"routerLink\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-green-500\", \"to-green-600\", \"dark:from-green-600\", \"dark:to-green-700\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"], [1, \"text-3xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-green-600\", \"to-green-700\", \"dark:from-green-400\", \"dark:to-green-500\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"class\", \"flex justify-center my-12\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 lg:grid-cols-3 gap-8\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"my-12\"], [1, \"relative\"], [1, \"w-14\", \"h-14\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\", \"gap-8\"], [1, \"lg:col-span-2\", \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-4\"], [1, \"bg-blue-100\", \"dark:bg-blue-900/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-blue-600\", \"dark:text-blue-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h7\"], [1, \"text-lg\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [\"id\", \"submissionForm\", 1, \"space-y-6\", 3, \"formGroup\", \"ngSubmit\"], [1, \"space-y-2\"], [\"for\", \"description\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-blue-600\", \"dark:text-blue-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-red-500\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"6\", \"placeholder\", \"D\\u00E9crivez votre travail : fonctionnalit\\u00E9s impl\\u00E9ment\\u00E9es, technologies utilis\\u00E9es, difficult\\u00E9s rencontr\\u00E9es, solutions apport\\u00E9es...\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-[#2a2a2a]\", \"border-2\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-4\", \"focus:ring-[#4f5fad]/10\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", \"duration-200\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"placeholder-[#6d6870]\", \"dark:placeholder-[#a0a0a0]\", \"resize-none\"], [1, \"flex\", \"justify-between\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"class\", \"flex items-center space-x-2 text-red-500 text-sm\", 4, \"ngIf\"], [\"for\", \"fichiers\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-green-600\", \"dark:text-green-400\"], [\"type\", \"file\", \"id\", \"fichiers\", \"multiple\", \"\", 1, \"absolute\", \"inset-0\", \"w-full\", \"h-full\", \"opacity-0\", \"cursor-pointer\", \"z-10\", 3, \"change\"], [1, \"w-full\", \"px-6\", \"py-8\", \"bg-[#edf1f4]/70\", \"dark:bg-[#2a2a2a]/70\", \"border-2\", \"border-dashed\", \"border-[#4f5fad]/30\", \"dark:border-[#6d78c9]/30\", \"rounded-xl\", \"hover:border-[#4f5fad]\", \"dark:hover:border-[#6d78c9]\", \"transition-all\", \"duration-200\", \"text-center\"], [1, \"space-y-3\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"p-3\", \"rounded-xl\", \"w-fit\", \"mx-auto\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"font-medium\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"class\", \"space-y-3\", 4, \"ngIf\"], [1, \"bg-green-100\", \"dark:bg-green-900/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-green-600\", \"dark:text-green-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"space-y-4\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-4\"], [1, \"flex\", \"items-center\", \"justify-center\", \"px-6\", \"py-3\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"rounded-xl\", \"transition-all\", \"duration-200\", \"font-medium\", \"order-2\", \"sm:order-1\", 3, \"routerLink\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10 19l-7-7m0 0l7-7m-7 7h18\"], [\"type\", \"submit\", \"form\", \"submissionForm\", 1, \"flex\", \"items-center\", \"justify-center\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-green-500\", \"to-green-600\", \"dark:from-green-600\", \"dark:to-green-700\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:hover:scale-100\", \"disabled:hover:shadow-none\", \"order-1\", \"sm:order-2\", 3, \"disabled\"], [\"class\", \"w-5 h-5\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [\"class\", \"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-center\"], [\"class\", \"flex items-center justify-center space-x-2 text-orange-600 dark:text-orange-400 text-sm\", 4, \"ngIf\"], [\"class\", \"flex items-center justify-center space-x-2 text-green-600 dark:text-green-400 text-sm\", 4, \"ngIf\"], [1, \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-[#1e1e1e]/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"p-6\", \"text-white\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"bg-white/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-lg\", \"font-semibold\"], [1, \"text-sm\", \"text-white/80\"], [1, \"p-6\", \"space-y-4\"], [1, \"p-3\", \"bg-[#edf1f4]/50\", \"dark:bg-[#2a2a2a]/50\", \"rounded-xl\"], [1, \"flex\", \"items-start\", \"space-x-3\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"], [1, \"flex-1\"], [1, \"text-xs\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"uppercase\", \"tracking-wider\"], [1, \"text-sm\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"leading-tight\"], [1, \"text-sm\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\", \"leading-tight\", \"line-clamp-3\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"bg-orange-100\", \"dark:bg-orange-900/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-orange-600\", \"dark:text-orange-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"font-semibold\", \"text-[#3d4a85]\", \"dark:text-[#6d78c9]\"], [1, \"text-right\"], [1, \"text-xs\", \"text-orange-600\", \"dark:text-orange-400\"], [1, \"bg-purple-100\", \"dark:bg-purple-900/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-purple-600\", \"dark:text-purple-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"space-y-3\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"items-start\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-green-600\", \"dark:text-green-400\", \"mt-0.5\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-orange-600\", \"dark:text-orange-400\", \"mt-0.5\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-red-500\", \"text-sm\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"grid\", \"grid-cols-1\", \"gap-2\"], [\"class\", \"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/30 rounded-xl\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"p-3\", \"bg-green-50\", \"dark:bg-green-900/20\", \"border\", \"border-green-200\", \"dark:border-green-800/30\", \"rounded-xl\"], [1, \"text-sm\", \"font-medium\", \"text-green-800\", \"dark:text-green-400\"], [1, \"text-xs\", \"text-green-600\", \"dark:text-green-500\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"w-5\", \"h-5\", \"border-2\", \"border-white/30\", \"border-t-white\", \"rounded-full\", \"animate-spin\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", \"text-orange-600\", \"dark:text-orange-400\", \"text-sm\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", \"text-green-600\", \"dark:text-green-400\", \"text-sm\"]],\n        template: function ProjectSubmissionComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"nav\", 6)(7, \"a\", 7);\n            i0.ɵɵtext(8, \"Mes Projets\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(9, \"svg\", 8);\n            i0.ɵɵelement(10, \"path\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(11, \"a\", 10);\n            i0.ɵɵtext(12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(13, \"svg\", 8);\n            i0.ɵɵelement(14, \"path\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(15, \"span\", 11);\n            i0.ɵɵtext(16, \"Soumettre\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 12)(18, \"div\", 13)(19, \"div\", 14);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(20, \"svg\", 15);\n            i0.ɵɵelement(21, \"path\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(22, \"div\")(23, \"h1\", 17);\n            i0.ɵɵtext(24, \" Soumettre mon projet \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"p\", 18);\n            i0.ɵɵtext(26, \" T\\u00E9l\\u00E9chargez vos fichiers et d\\u00E9crivez votre travail \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(27, ProjectSubmissionComponent_div_27_Template, 4, 0, \"div\", 19);\n            i0.ɵɵtemplate(28, ProjectSubmissionComponent_div_28_Template, 163, 23, \"div\", 20);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, ctx.projetId));\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.titre) || \"Projet\");\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.projet && !ctx.isLoading);\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i2.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.DatePipe],\n        styles: [\".submission-form[_ngcontent-%COMP%]{max-width:800px;margin:0 auto}.form-section[_ngcontent-%COMP%]{margin-bottom:2rem}.file-upload[_ngcontent-%COMP%]{border:2px dashed #ccc;padding:1.5rem;text-align:center;border-radius:.5rem;margin-bottom:1rem}.file-upload[_ngcontent-%COMP%]:hover{border-color:#6366f1}.file-list[_ngcontent-%COMP%]{margin-top:1rem}.file-item[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:.5rem;background-color:#f9fafb;border-radius:.25rem;margin-bottom:.5rem}\"]\n      });\n    }\n  }\n  return ProjectSubmissionComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}