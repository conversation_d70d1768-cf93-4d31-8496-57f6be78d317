{"ast": null, "code": "import { addDays } from \"./addDays.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameDay } from \"./isSameDay.js\";\n\n/**\n * The {@link isTomorrow} function options.\n */\n\n/**\n * @name isTomorrow\n * @category Day Helpers\n * @summary Is the given date tomorrow?\n * @pure false\n *\n * @description\n * Is the given date tomorrow?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is tomorrow\n *\n * @example\n * // If today is 6 October 2014, is 7 October 14:00:00 tomorrow?\n * const result = isTomorrow(new Date(2014, 9, 7, 14, 0))\n * //=> true\n */\nexport function isTomorrow(date, options) {\n  return isSameDay(date, addDays(constructNow(options?.in || date), 1), options);\n}\n\n// Fallback for modularized imports:\nexport default isTomorrow;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}