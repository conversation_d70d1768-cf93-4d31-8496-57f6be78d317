{"ast": null, "code": "export function iterateObserversSafely(observers, method, argument) {\n  // In case observers is modified during iteration, we need to commit to the\n  // original elements, which also provides an opportunity to filter them down\n  // to just the observers with the given method.\n  var observersWithMethod = [];\n  observers.forEach(function (obs) {\n    return obs[method] && observersWithMethod.push(obs);\n  });\n  observersWithMethod.forEach(function (obs) {\n    return obs[method](argument);\n  });\n}\n//# sourceMappingURL=iteration.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}