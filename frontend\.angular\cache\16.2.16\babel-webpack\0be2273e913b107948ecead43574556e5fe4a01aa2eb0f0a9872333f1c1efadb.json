{"ast": null, "code": "import { finalize } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/auth.service\";\nimport * as i2 from \"@app/services/data.service\";\nimport * as i3 from \"@app/services/authadmin.service\";\nimport * as i4 from \"@app/services/authuser.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nfunction ProfileComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction ProfileComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.message, \" \");\n  }\n}\nfunction ProfileComponent_div_7_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 79);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r4.user.profileImage && ctx_r4.user.profileImage !== \"null\" && ctx_r4.user.profileImage.trim() !== \"\" ? ctx_r4.user.profileImage : \"assets/images/default-profile.png\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileComponent_div_7_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 80);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r5.previewUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ProfileComponent_div_7_button_50_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 84);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 42);\n    i0.ɵɵelement(2, \"path\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Upload \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_button_50_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 86);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 87);\n    i0.ɵɵelement(2, \"circle\", 88)(3, \"path\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Uploading... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_button_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_7_button_50_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.onUpload());\n    });\n    i0.ɵɵtemplate(1, ProfileComponent_div_7_button_50_span_1_Template, 4, 0, \"span\", 82);\n    i0.ɵɵtemplate(2, ProfileComponent_div_7_button_50_span_2_Template, 5, 0, \"span\", 83);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.uploadLoading);\n  }\n}\nfunction ProfileComponent_div_7_button_51_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 84);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 42);\n    i0.ɵɵelement(2, \"path\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Remove \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_button_51_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 86);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 87);\n    i0.ɵɵelement(2, \"circle\", 88)(3, \"path\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Removing \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_7_button_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_7_button_51_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.removeProfileImage());\n    });\n    i0.ɵɵtemplate(1, ProfileComponent_div_7_button_51_span_1_Template, 4, 0, \"span\", 82);\n    i0.ɵɵtemplate(2, ProfileComponent_div_7_button_51_span_2_Template, 5, 0, \"span\", 83);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r7.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.removeLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.removeLoading);\n  }\n}\nfunction ProfileComponent_div_7_div_125_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"div\", 84);\n    i0.ɵɵelement(2, \"div\", 93);\n    i0.ɵɵelementStart(3, \"div\")(4, \"div\", 94);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 95);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 96);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const activity_r17 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", activity_r17.action, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", activity_r17.target, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.formatDate(activity_r17.timestamp), \" \");\n  }\n}\nfunction ProfileComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"div\", 13);\n    i0.ɵɵelement(3, \"img\", 14);\n    i0.ɵɵelementStart(4, \"div\", 15)(5, \"div\", 16);\n    i0.ɵɵtemplate(6, ProfileComponent_div_7_img_6_Template, 1, 1, \"img\", 17);\n    i0.ɵɵtemplate(7, ProfileComponent_div_7_img_7_Template, 1, 1, \"img\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"label\", 19);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 20);\n    i0.ɵɵelement(10, \"path\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"input\", 22);\n    i0.ɵɵlistener(\"change\", function ProfileComponent_div_7_Template_input_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 23)(13, \"h2\", 24);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 25)(16, \"span\", 26);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 27)(20, \"h3\", 28);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(21, \"svg\", 29);\n    i0.ɵɵelement(22, \"path\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Account Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(24, \"div\", 31)(25, \"div\", 32)(26, \"div\", 33);\n    i0.ɵɵtext(27, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 34);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 35)(31, \"div\", 36)(32, \"div\", 33);\n    i0.ɵɵtext(33, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\")(35, \"span\", 37);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"div\", 36)(38, \"div\", 33);\n    i0.ɵɵtext(39, \" Verification \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\")(41, \"span\", 37);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(43, \"div\", 32)(44, \"div\", 33);\n    i0.ɵɵtext(45, \"Member Since\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 34);\n    i0.ɵɵtext(47);\n    i0.ɵɵpipe(48, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(49, \"div\", 38);\n    i0.ɵɵtemplate(50, ProfileComponent_div_7_button_50_Template, 3, 3, \"button\", 39);\n    i0.ɵɵtemplate(51, ProfileComponent_div_7_button_51_Template, 3, 3, \"button\", 40);\n    i0.ɵɵelementStart(52, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_7_Template_button_click_52_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.navigateTo(\"/change-password\"));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(53, \"svg\", 42);\n    i0.ɵɵelement(54, \"path\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(55, \" Password \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(56, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_7_Template_button_click_56_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.logout());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(57, \"svg\", 42);\n    i0.ɵɵelement(58, \"path\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(59, \" Logout \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(60, \"div\", 46)(61, \"div\", 12)(62, \"div\", 47)(63, \"h3\", 48);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(64, \"svg\", 29);\n    i0.ɵɵelement(65, \"path\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(66, \" User Statistics \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(67, \"div\", 50)(68, \"div\", 51)(69, \"div\")(70, \"div\", 52)(71, \"div\", 33);\n    i0.ɵɵtext(72, \" Total Users \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"div\", 53);\n    i0.ɵɵtext(74);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(75, \"div\", 54);\n    i0.ɵɵelement(76, \"div\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(77, \"div\")(78, \"div\", 52)(79, \"div\", 33);\n    i0.ɵɵtext(80, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"div\", 56)(82, \"span\", 57);\n    i0.ɵɵtext(83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"span\", 58);\n    i0.ɵɵtext(85, \"|\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(86, \"span\", 59);\n    i0.ɵɵtext(87);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(88, \"div\", 60);\n    i0.ɵɵelement(89, \"div\", 61)(90, \"div\", 62);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(91, \"div\")(92, \"div\", 52)(93, \"div\", 33);\n    i0.ɵɵtext(94, \"User Roles\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(95, \"div\", 56)(96, \"span\", 63);\n    i0.ɵɵtext(97);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"span\", 58);\n    i0.ɵɵtext(99, \"|\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"span\", 64);\n    i0.ɵɵtext(101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(102, \"span\", 58);\n    i0.ɵɵtext(103, \"|\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(104, \"span\", 65);\n    i0.ɵɵtext(105);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(106, \"div\", 60);\n    i0.ɵɵelement(107, \"div\", 66)(108, \"div\", 67)(109, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(110, \"div\", 69)(111, \"span\");\n    i0.ɵɵtext(112, \"Students\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(113, \"span\");\n    i0.ɵɵtext(114, \"Teachers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(115, \"span\");\n    i0.ɵɵtext(116, \"Admins\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(117, \"div\", 12)(118, \"div\", 47)(119, \"h3\", 48);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(120, \"svg\", 29);\n    i0.ɵɵelement(121, \"path\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(122, \" Recent Activity \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(123, \"div\", 50)(124, \"div\", 71);\n    i0.ɵɵtemplate(125, ProfileComponent_div_7_div_125_Template, 10, 3, \"div\", 72);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(126, \"div\", 12)(127, \"div\", 47)(128, \"h3\", 48);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(129, \"svg\", 29);\n    i0.ɵɵelement(130, \"path\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(131, \" Quick Actions \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(132, \"div\", 50)(133, \"div\", 74)(134, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_7_Template_button_click_134_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.navigateTo(\"/admin/dashboard\"));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(135, \"svg\", 42);\n    i0.ɵɵelement(136, \"path\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(137, \" Users \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(138, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_7_Template_button_click_138_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.navigateTo(\"/\"));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(139, \"svg\", 42);\n    i0.ɵɵelement(140, \"path\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(141, \" Home \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.previewUrl || ctx_r3.uploadLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.previewUrl && !ctx_r3.uploadLoading);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.user.fullName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(18, 29, ctx_r3.user.role), \" \");\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate(ctx_r3.user.email);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.user.isActive !== false ? \"bg-[#afcf75]/10 text-[#2a5a03]\" : \"bg-[#ff6b69]/10 text-[#ff6b69]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.user.isActive !== false ? \"Active\" : \"Inactive\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.user.verified ? \"bg-[#afcf75]/10 text-[#2a5a03]\" : \"bg-[#ff6b69]/10 text-[#ff6b69]\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.user.verified ? \"Verified\" : \"Not Verified\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(48, 31, ctx_r3.user.createdAt, \"mediumDate\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedImage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.user.profileImage);\n    i0.ɵɵadvance(23);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.stats.totalUsers, \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.stats.activeUsers, \" Active\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.stats.inactiveUsers, \" Inactive\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.stats.totalUsers ? ctx_r3.stats.activeUsers / ctx_r3.stats.totalUsers * 100 : 0, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.stats.totalUsers ? ctx_r3.stats.inactiveUsers / ctx_r3.stats.totalUsers * 100 : 0, \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.stats.students);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.stats.teachers);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.stats.admins);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.stats.totalUsers ? ctx_r3.stats.students / ctx_r3.stats.totalUsers * 100 : 0, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.stats.totalUsers ? ctx_r3.stats.teachers / ctx_r3.stats.totalUsers * 100 : 0, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.stats.totalUsers ? ctx_r3.stats.admins / ctx_r3.stats.totalUsers * 100 : 0, \"%\");\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.recentActivity);\n  }\n}\nexport let ProfileComponent = /*#__PURE__*/(() => {\n  class ProfileComponent {\n    constructor(authService, dataService, authAdminService, authUserService, router) {\n      this.authService = authService;\n      this.dataService = dataService;\n      this.authAdminService = authAdminService;\n      this.authUserService = authUserService;\n      this.router = router;\n      this.user = null;\n      this.selectedImage = null;\n      this.message = '';\n      this.error = '';\n      this.loading = true;\n      this.uploadLoading = false;\n      this.removeLoading = false;\n      this.stats = {\n        totalUsers: 0,\n        activeUsers: 0,\n        inactiveUsers: 0,\n        students: 0,\n        teachers: 0,\n        admins: 0\n      };\n      this.recentActivity = [{\n        action: 'User Deactivated',\n        target: 'John Doe',\n        timestamp: new Date(Date.now() - 3600000)\n      }, {\n        action: 'Role Changed',\n        target: 'Jane Smith',\n        timestamp: new Date(Date.now() - 7200000)\n      }, {\n        action: 'User Added',\n        target: 'Robert Johnson',\n        timestamp: new Date(Date.now() - 86400000)\n      }];\n      this.previewUrl = null;\n    }\n    ngOnInit() {\n      this.loadUserData();\n      this.loadStats();\n    }\n    loadUserData() {\n      const token = localStorage.getItem('token');\n      if (!token) {\n        this.router.navigate(['/admin/login']);\n        return;\n      }\n      this.loading = true;\n      this.authService.getProfile(token).subscribe({\n        next: res => {\n          this.user = res;\n          this.loading = false;\n        },\n        error: err => {\n          this.error = err.error?.message || 'Failed to load profile.';\n          this.loading = false;\n        }\n      });\n    }\n    loadStats() {\n      const token = localStorage.getItem('token');\n      if (!token) return;\n      this.authService.getAllUsers(token).subscribe({\n        next: res => {\n          const users = res;\n          this.stats.totalUsers = users.length;\n          this.stats.activeUsers = users.filter(u => u.isActive !== false).length;\n          this.stats.inactiveUsers = users.filter(u => u.isActive === false).length;\n          this.stats.students = users.filter(u => u.role === 'student').length;\n          this.stats.teachers = users.filter(u => u.role === 'teacher').length;\n          this.stats.admins = users.filter(u => u.role === 'admin').length;\n        },\n        error: () => {\n          // Silently fail, stats are not critical\n        }\n      });\n    }\n    onFileSelected(event) {\n      const input = event.target;\n      if (input.files?.length) {\n        const file = input.files[0];\n        const validTypes = ['image/jpeg', 'image/png', 'image/webp'];\n        if (!validTypes.includes(file.type)) {\n          this.error = 'Seuls les JPEG, PNG et WebP sont autorisés';\n          this.resetFileInput();\n          return;\n        }\n        if (file.size > 2 * 1024 * 1024) {\n          this.error = \"L'image ne doit pas dépasser 2MB\";\n          this.resetFileInput();\n          return;\n        }\n        this.selectedImage = file;\n        this.error = '';\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.previewUrl = e.target?.result || null;\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n    resetFileInput() {\n      this.selectedImage = null;\n      this.previewUrl = null;\n      const fileInput = document.getElementById('profile-upload');\n      if (fileInput) fileInput.value = '';\n    }\n    onUpload() {\n      if (!this.selectedImage) return;\n      this.uploadLoading = true; // Activer l'état de chargement\n      this.message = '';\n      this.error = '';\n      console.log('Upload started, uploadLoading:', this.uploadLoading);\n      this.dataService.uploadProfileImage(this.selectedImage).pipe(finalize(() => {\n        this.uploadLoading = false;\n        console.log('Upload finished, uploadLoading:', this.uploadLoading);\n      })).subscribe({\n        next: response => {\n          this.message = response.message || 'Profile updated successfully';\n          // Update both properties to ensure consistency\n          this.user.profileImageURL = response.imageUrl;\n          this.user.profileImage = response.imageUrl;\n          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\n          this.dataService.updateCurrentUser({\n            profileImage: response.imageUrl,\n            image: response.imageUrl\n          });\n          this.selectedImage = null;\n          this.previewUrl = null;\n          this.resetFileInput();\n          if (response.token) {\n            localStorage.setItem('token', response.token);\n          }\n          // Auto-hide message after 3 seconds\n          setTimeout(() => {\n            this.message = '';\n          }, 3000);\n        },\n        error: err => {\n          this.error = err.error?.message || 'Upload failed';\n          // Auto-hide error after 3 seconds\n          setTimeout(() => {\n            this.error = '';\n          }, 3000);\n        }\n      });\n    }\n    removeProfileImage() {\n      if (!confirm('Are you sure you want to remove your profile picture?')) return;\n      this.removeLoading = true;\n      this.message = '';\n      this.error = '';\n      this.dataService.removeProfileImage().pipe(finalize(() => this.removeLoading = false)).subscribe({\n        next: response => {\n          this.message = response.message || 'Profile picture removed successfully';\n          // Update both properties to ensure consistency\n          this.user.profileImageURL = null;\n          this.user.profileImage = null;\n          // Mettre à jour l'utilisateur dans le service pour synchroniser avec le layout\n          this.dataService.updateCurrentUser({\n            profileImage: 'assets/images/default-profile.png',\n            image: 'assets/images/default-profile.png'\n          });\n          if (response.token) {\n            localStorage.setItem('token', response.token);\n          }\n          // Auto-hide message after 3 seconds\n          setTimeout(() => {\n            this.message = '';\n          }, 3000);\n        },\n        error: err => {\n          this.error = err.error?.message || 'Removal failed';\n          // Auto-hide error after 3 seconds\n          setTimeout(() => {\n            this.error = '';\n          }, 3000);\n        }\n      });\n    }\n    navigateTo(path) {\n      this.router.navigate([path]);\n    }\n    logout() {\n      this.authUserService.logout().subscribe({\n        next: () => {\n          this.authUserService.clearAuthData();\n          this.authAdminService.clearAuthData();\n          setTimeout(() => {\n            this.router.navigate(['/admin/login'], {\n              queryParams: {\n                message: 'Déconnexion réussie'\n              },\n              replaceUrl: true\n            });\n          }, 100);\n        },\n        error: err => {\n          console.error('Logout error:', err);\n          this.authUserService.clearAuthData();\n          this.authAdminService.clearAuthData();\n          setTimeout(() => {\n            this.router.navigate(['/admin/login'], {});\n          }, 100);\n        }\n      });\n    }\n    formatDate(date) {\n      return new Date(date).toLocaleString();\n    }\n    getInitials(name) {\n      if (!name) return 'A';\n      return name.split(' ').map(n => n[0]).join('').toUpperCase();\n    }\n    static {\n      this.ɵfac = function ProfileComponent_Factory(t) {\n        return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.DataService), i0.ɵɵdirectiveInject(i3.AuthadminService), i0.ɵɵdirectiveInject(i4.AuthuserService), i0.ɵɵdirectiveInject(i5.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProfileComponent,\n        selectors: [[\"app-profile\"]],\n        decls: 8,\n        vars: 4,\n        consts: [[1, \"container-fluid\", \"p-4\", \"md:p-6\", \"bg-[#edf1f4]\", \"min-h-screen\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"md:items-center\", \"md:justify-between\", \"mb-6\"], [1, \"text-2xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-2\", \"md:mb-0\"], [\"class\", \"flex justify-center items-center py-20\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 border border-[#ff6b69] text-[#ff6b69] px-4 py-3 rounded-lg mb-6 animate-pulse\", 4, \"ngIf\"], [\"class\", \"bg-[#afcf75]/10 border border-[#afcf75] text-[#2a5a03] px-4 py-3 rounded-lg mb-6 animate-pulse\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"items-center\", \"py-20\"], [1, \"animate-spin\", \"rounded-full\", \"h-10\", \"w-10\", \"border-t-2\", \"border-b-2\", \"border-[#4f5fad]\"], [1, \"bg-[#ff6b69]/10\", \"border\", \"border-[#ff6b69]\", \"text-[#ff6b69]\", \"px-4\", \"py-3\", \"rounded-lg\", \"mb-6\", \"animate-pulse\"], [1, \"bg-[#afcf75]/10\", \"border\", \"border-[#afcf75]\", \"text-[#2a5a03]\", \"px-4\", \"py-3\", \"rounded-lg\", \"mb-6\", \"animate-pulse\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\", \"gap-6\", \"mb-8\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-md\", \"overflow-hidden\"], [1, \"relative\"], [\"src\", \"https://images.unsplash.com/photo-1579546929518-9e396f3cc809?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80\", \"alt\", \"Cover\", 1, \"w-full\", \"h-32\", \"object-cover\"], [1, \"absolute\", \"left-0\", \"right-0\", \"-bottom-12\", \"flex\", \"justify-center\"], [1, \"h-24\", \"w-24\", \"rounded-full\", \"border-4\", \"border-white\", \"overflow-hidden\", \"flex\", \"items-center\", \"justify-center\", 2, \"min-height\", \"96px\", \"min-width\", \"96px\"], [\"alt\", \"Profile\", \"class\", \"h-full w-full object-cover\", 3, \"src\", 4, \"ngIf\"], [\"alt\", \"Preview\", \"class\", \"h-full w-full object-cover\", 3, \"src\", 4, \"ngIf\"], [\"for\", \"profile-upload\", 1, \"absolute\", \"bottom-0\", \"right-0\", \"bg-[#7826b5]\", \"text-white\", \"p-1.5\", \"rounded-full\", \"cursor-pointer\", \"hover:bg-[#5f1d8f]\", \"transition-colors\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-4\", \"w-4\"], [\"fill-rule\", \"evenodd\", \"d\", \"M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z\", \"clip-rule\", \"evenodd\"], [\"type\", \"file\", \"id\", \"profile-upload\", \"accept\", \"image/*\", 1, \"hidden\", 3, \"change\"], [1, \"p-5\", \"pt-16\", \"text-center\"], [1, \"text-xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-1\"], [1, \"mb-4\"], [1, \"px-2\", \"py-1\", \"text-xs\", \"rounded-full\", \"bg-[#4f5fad]/10\", \"text-[#4f5fad]\", \"font-medium\"], [1, \"mt-6\", \"border-t\", \"border-[#edf1f4]\", \"pt-4\"], [1, \"flex\", \"items-center\", \"justify-center\", \"text-[#4f5fad]\", \"font-semibold\", \"mb-4\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\"], [\"fill-rule\", \"evenodd\", \"d\", \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\", \"clip-rule\", \"evenodd\"], [1, \"space-y-4\"], [1, \"text-left\", \"px-2\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\"], [1, \"text-[#4f5fad]\"], [1, \"flex\", \"space-x-4\"], [1, \"flex-1\", \"text-left\", \"px-2\"], [1, \"px-2\", \"py-1\", \"text-xs\", \"rounded-full\", 3, \"ngClass\"], [1, \"mt-6\", \"flex\", \"flex-wrap\", \"justify-center\", \"gap-3\"], [\"class\", \"inline-flex items-center bg-[#7826b5] hover:bg-[#5f1d8f] text-white px-4 py-2 rounded-lg shadow transition-all\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"inline-flex items-center bg-[#ff6b69] hover:bg-[#e05554] text-white px-4 py-2 rounded-lg shadow transition-all\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"inline-flex\", \"items-center\", \"bg-[#4f5fad]\", \"hover:bg-[#3d4a85]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"shadow\", \"transition-all\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-2\"], [\"fill-rule\", \"evenodd\", \"d\", \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\", \"clip-rule\", \"evenodd\"], [1, \"inline-flex\", \"items-center\", \"bg-[#ff6b69]\", \"hover:bg-[#e05554]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"shadow\", \"transition-all\", 3, \"click\"], [\"fill-rule\", \"evenodd\", \"d\", \"M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-4-4H3zm6 11a1 1 0 11-2 0 1 1 0 012 0zm2-5.5a.5.5 0 00-.5.5v3a.5.5 0 01-.5.5h-3a.5.5 0 010-1H9V9.5A1.5 1.5 0 0110.5 8h.5a.5.5 0 01.5.5z\", \"clip-rule\", \"evenodd\"], [1, \"lg:col-span-2\", \"space-y-6\"], [1, \"p-5\", \"border-b\", \"border-[#bdc6cc]\"], [1, \"flex\", \"items-center\", \"font-bold\", \"text-[#4f5fad]\"], [\"d\", \"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z\"], [1, \"p-5\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\", \"mb-6\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-2\"], [1, \"text-xl\", \"font-semibold\", \"text-[#4f5fad]\"], [1, \"w-full\", \"bg-[#edf1f4]\", \"rounded-full\", \"h-2.5\"], [1, \"bg-[#4f5fad]\", \"h-2.5\", \"rounded-full\", 2, \"width\", \"100%\"], [1, \"flex\", \"space-x-3\", \"text-sm\"], [1, \"text-[#afcf75]\", \"font-medium\"], [1, \"text-[#bdc6cc]\"], [1, \"text-[#ff6b69]\", \"font-medium\"], [1, \"w-full\", \"bg-[#edf1f4]\", \"rounded-full\", \"h-2.5\", \"overflow-hidden\", \"flex\"], [1, \"bg-[#afcf75]\", \"h-2.5\"], [1, \"bg-[#ff6b69]\", \"h-2.5\"], [1, \"text-[#4a89ce]\", \"font-medium\"], [1, \"text-[#7826b5]\", \"font-medium\"], [1, \"text-[#4f5fad]\", \"font-medium\"], [1, \"bg-[#4a89ce]\", \"h-2.5\"], [1, \"bg-[#7826b5]\", \"h-2.5\"], [1, \"bg-[#4f5fad]\", \"h-2.5\"], [1, \"flex\", \"justify-between\", \"mt-2\", \"text-xs\", \"text-[#6d6870]\"], [\"fill-rule\", \"evenodd\", \"d\", \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\", \"clip-rule\", \"evenodd\"], [1, \"divide-y\", \"divide-[#edf1f4]\"], [\"class\", \"py-3 flex justify-between items-center\", 4, \"ngFor\", \"ngForOf\"], [\"fill-rule\", \"evenodd\", \"d\", \"M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm0 2h10v7h-2l-1 2H8l-1-2H5V5z\", \"clip-rule\", \"evenodd\"], [1, \"flex\", \"flex-wrap\", \"gap-3\"], [1, \"inline-flex\", \"items-center\", \"bg-[#7826b5]\", \"hover:bg-[#5f1d8f]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"shadow\", \"transition-all\", 3, \"click\"], [\"d\", \"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z\"], [1, \"inline-flex\", \"items-center\", \"bg-[#4a89ce]\", \"hover:bg-[#3a6ca3]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"shadow\", \"transition-all\", 3, \"click\"], [\"d\", \"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z\"], [\"alt\", \"Profile\", 1, \"h-full\", \"w-full\", \"object-cover\", 3, \"src\"], [\"alt\", \"Preview\", 1, \"h-full\", \"w-full\", \"object-cover\", 3, \"src\"], [1, \"inline-flex\", \"items-center\", \"bg-[#7826b5]\", \"hover:bg-[#5f1d8f]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"shadow\", \"transition-all\", 3, \"disabled\", \"click\"], [\"class\", \"flex items-center\", 4, \"ngIf\"], [\"class\", \"flex items-center justify-center\", 4, \"ngIf\"], [1, \"flex\", \"items-center\"], [\"fill-rule\", \"evenodd\", \"d\", \"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z\", \"clip-rule\", \"evenodd\"], [1, \"flex\", \"items-center\", \"justify-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"animate-spin\", \"mr-2\", \"h-4\", \"w-4\", \"text-white\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"], [1, \"inline-flex\", \"items-center\", \"bg-[#ff6b69]\", \"hover:bg-[#e05554]\", \"text-white\", \"px-4\", \"py-2\", \"rounded-lg\", \"shadow\", \"transition-all\", 3, \"disabled\", \"click\"], [\"fill-rule\", \"evenodd\", \"d\", \"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\", \"clip-rule\", \"evenodd\"], [1, \"py-3\", \"flex\", \"justify-between\", \"items-center\"], [1, \"w-2\", \"h-2\", \"rounded-full\", \"bg-[#4f5fad]\", \"mr-3\"], [1, \"text-sm\", \"text-[#4f5fad]\"], [1, \"text-xs\", \"text-[#6d6870]\"], [1, \"text-xs\", \"text-[#bdc6cc]\"]],\n        template: function ProfileComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n            i0.ɵɵtext(3, \"My Profile\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(4, ProfileComponent_div_4_Template, 2, 0, \"div\", 3);\n            i0.ɵɵtemplate(5, ProfileComponent_div_5_Template, 2, 1, \"div\", 4);\n            i0.ɵɵtemplate(6, ProfileComponent_div_6_Template, 2, 1, \"div\", 5);\n            i0.ɵɵtemplate(7, ProfileComponent_div_7_Template, 142, 34, \"div\", 6);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.message);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.user);\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i6.TitleCasePipe, i6.DatePipe],\n        styles: [\".profile-card[_ngcontent-%COMP%]{overflow:hidden;border-radius:.5rem;border-width:1px;--tw-border-opacity: 1;border-color:rgb(243 244 246 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.profile-card[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(55 65 81 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1))}.profile-header[_ngcontent-%COMP%]{position:relative}.profile-header[_ngcontent-%COMP%]   .cover-photo[_ngcontent-%COMP%]{height:8rem;width:100%;object-fit:cover}.profile-header[_ngcontent-%COMP%]   .profile-photo-container[_ngcontent-%COMP%]{position:absolute;bottom:0;left:1.5rem;--tw-translate-y: 50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.profile-header[_ngcontent-%COMP%]   .profile-photo-container[_ngcontent-%COMP%]   .profile-photo[_ngcontent-%COMP%]{display:flex;height:5rem;width:5rem;align-items:center;justify-content:center;border-radius:9999px;border-width:2px;--tw-border-opacity: 1;border-color:rgb(255 255 255 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(218 196 234 / var(--tw-bg-opacity, 1));object-fit:cover;font-size:1.5rem;line-height:2rem;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.profile-header[_ngcontent-%COMP%]   .profile-photo-container[_ngcontent-%COMP%]   .profile-photo[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(31 41 55 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(120 38 181 / var(--tw-bg-opacity, 1))}.profile-header[_ngcontent-%COMP%]   .profile-photo-container[_ngcontent-%COMP%]   .upload-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;display:flex;cursor:pointer;align-items:center;justify-content:center;border-radius:9999px;background-color:rgb(0 0 0 / var(--tw-bg-opacity, 1));--tw-bg-opacity: .5;opacity:0;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.profile-header[_ngcontent-%COMP%]   .profile-photo-container[_ngcontent-%COMP%]   .upload-overlay[_ngcontent-%COMP%]:hover{opacity:1}.profile-header[_ngcontent-%COMP%]   .profile-photo-container[_ngcontent-%COMP%]   .upload-overlay[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{height:1.25rem;width:1.25rem;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.profile-content[_ngcontent-%COMP%]{padding:3.5rem 1.5rem 1.5rem}.profile-content[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%]{font-size:1.25rem;line-height:1.75rem;font-weight:600;--tw-text-opacity: 1;color:rgb(17 24 39 / var(--tw-text-opacity, 1))}.profile-content[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.profile-content[_ngcontent-%COMP%]   .profile-role[_ngcontent-%COMP%]{margin-bottom:1rem;font-size:.75rem;line-height:1rem;--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.profile-content[_ngcontent-%COMP%]   .profile-role[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.profile-section[_ngcontent-%COMP%]{margin-top:1.5rem}.profile-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{margin-bottom:.75rem;display:flex;align-items:center;font-size:.875rem;line-height:1.25rem;font-weight:500;--tw-text-opacity: 1;color:rgb(55 65 81 / var(--tw-text-opacity, 1))}.profile-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(209 213 219 / var(--tw-text-opacity, 1))}.profile-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{margin-right:.375rem;height:1rem;width:1rem;--tw-text-opacity: 1;color:rgb(218 196 234 / var(--tw-text-opacity, 1))}.profile-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(120 38 181 / var(--tw-text-opacity, 1))}.profile-section[_ngcontent-%COMP%]   .info-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(1,minmax(0,1fr));gap:.75rem}@media (min-width: 768px){.profile-section[_ngcontent-%COMP%]   .info-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,minmax(0,1fr))}}.profile-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]{border-radius:.375rem;border-width:1px;--tw-border-opacity: 1;border-color:rgb(243 244 246 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(249 250 251 / var(--tw-bg-opacity, 1));padding:.75rem}.profile-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(55 65 81 / var(--tw-border-opacity, 1));background-color:#37415180}.profile-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]{font-size:.75rem;line-height:1rem;--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.profile-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-label[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.profile-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]{font-size:.875rem;line-height:1.25rem;font-weight:500;--tw-text-opacity: 1;color:rgb(17 24 39 / var(--tw-text-opacity, 1))}.profile-section[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-value[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.action-buttons[_ngcontent-%COMP%]{margin-top:1.5rem;display:flex;flex-direction:column;gap:.5rem}@media (min-width: 640px){.action-buttons[_ngcontent-%COMP%]{flex-direction:row}}.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:.375rem;padding:.375rem .75rem;font-size:.875rem;line-height:1.25rem;font-weight:500;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:focus{outline:2px solid transparent;outline-offset:2px;--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000);--tw-ring-offset-width: 1px }.action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(218 196 234 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover{--tw-bg-opacity: .9 }.action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:focus{--tw-ring-opacity: 1;--tw-ring-color: rgb(218 196 234 / var(--tw-ring-opacity, 1)) }.action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(120 38 181 / var(--tw-bg-opacity, 1))}.action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:focus:is(.dark   *)[_ngcontent-%COMP%]{--tw-ring-opacity: 1;--tw-ring-color: rgb(120 38 181 / var(--tw-ring-opacity, 1)) }.action-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]{border-width:1px;--tw-border-opacity: 1;border-color:rgb(229 231 235 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(243 244 246 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(55 65 81 / var(--tw-text-opacity, 1))}.action-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover{--tw-bg-opacity: 1;background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))}.action-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:focus{--tw-ring-opacity: 1;--tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity, 1)) }.action-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(75 85 99 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(229 231 235 / var(--tw-text-opacity, 1))}.action-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover:is(.dark   *)[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(75 85 99 / var(--tw-bg-opacity, 1))}.action-buttons[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:focus:is(.dark   *)[_ngcontent-%COMP%]{--tw-ring-opacity: 1;--tw-ring-color: rgb(55 65 81 / var(--tw-ring-opacity, 1)) }.action-buttons[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]{border-width:1px;--tw-border-opacity: 1;border-color:rgb(254 202 202 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(254 242 242 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(220 38 38 / var(--tw-text-opacity, 1))}.action-buttons[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover{--tw-bg-opacity: 1;background-color:rgb(254 226 226 / var(--tw-bg-opacity, 1))}.action-buttons[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:focus{--tw-ring-opacity: 1;--tw-ring-color: rgb(254 202 202 / var(--tw-ring-opacity, 1)) }.action-buttons[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{--tw-border-opacity: 1;border-color:rgb(153 27 27 / var(--tw-border-opacity, 1));background-color:#7f1d1d33;--tw-text-opacity: 1;color:rgb(248 113 113 / var(--tw-text-opacity, 1))}.action-buttons[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover:is(.dark   *)[_ngcontent-%COMP%]{background-color:#7f1d1d4d}.action-buttons[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:focus:is(.dark   *)[_ngcontent-%COMP%]{--tw-ring-opacity: 1;--tw-ring-color: rgb(153 27 27 / var(--tw-ring-opacity, 1)) }.badge[_ngcontent-%COMP%]{display:inline-flex;align-items:center;border-radius:9999px;padding:.125rem .375rem;font-size:.75rem;line-height:1rem;font-weight:500}.badge.verified[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(220 252 231 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(22 101 52 / var(--tw-text-opacity, 1))}.badge.verified[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{background-color:#14532d33;--tw-text-opacity: 1;color:rgb(134 239 172 / var(--tw-text-opacity, 1))}.badge.not-verified[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(243 244 246 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(31 41 55 / var(--tw-text-opacity, 1))}.badge.not-verified[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(209 213 219 / var(--tw-text-opacity, 1))}.badge.active[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(219 234 254 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(30 64 175 / var(--tw-text-opacity, 1))}.badge.active[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{background-color:#1e3a8a33;--tw-text-opacity: 1;color:rgb(147 197 253 / var(--tw-text-opacity, 1))}.badge.inactive[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(254 226 226 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(153 27 27 / var(--tw-text-opacity, 1))}.badge.inactive[_ngcontent-%COMP%]:is(.dark   *)[_ngcontent-%COMP%]{background-color:#7f1d1d33;--tw-text-opacity: 1;color:rgb(252 165 165 / var(--tw-text-opacity, 1))}\"]\n      });\n    }\n  }\n  return ProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}