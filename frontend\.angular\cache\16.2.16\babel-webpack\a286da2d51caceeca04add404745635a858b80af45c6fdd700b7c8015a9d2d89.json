{"ast": null, "code": "import { Kind } from '../language/kinds.mjs';\nimport { GraphQLList, GraphQLNonNull } from '../type/definition.mjs';\nexport function typeFromAST(schema, typeNode) {\n  switch (typeNode.kind) {\n    case Kind.LIST_TYPE:\n      {\n        const innerType = typeFromAST(schema, typeNode.type);\n        return innerType && new GraphQLList(innerType);\n      }\n    case Kind.NON_NULL_TYPE:\n      {\n        const innerType = typeFromAST(schema, typeNode.type);\n        return innerType && new GraphQLNonNull(innerType);\n      }\n    case Kind.NAMED_TYPE:\n      return schema.getType(typeNode.name.value);\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}