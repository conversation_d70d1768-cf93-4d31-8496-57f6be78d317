{"ast": null, "code": "import { is<PERSON><PERSON><PERSON>nce, is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, resultKeyNameFromField, shouldInclude, isNonNullObject, compact, createFragmentMap, getFragmentDefinitions, isArray } from \"../../utilities/index.js\";\nexport var hasOwn = Object.prototype.hasOwnProperty;\nexport function isNullish(value) {\n  return value === null || value === void 0;\n}\nexport { isArray };\nexport function defaultDataIdFromObject(_a, context) {\n  var __typename = _a.__typename,\n    id = _a.id,\n    _id = _a._id;\n  if (typeof __typename === \"string\") {\n    if (context) {\n      context.keyObject = !isNullish(id) ? {\n        id: id\n      } : !isNullish(_id) ? {\n        _id: _id\n      } : void 0;\n    }\n    // If there is no object.id, fall back to object._id.\n    if (isNullish(id) && !isNullish(_id)) {\n      id = _id;\n    }\n    if (!isNullish(id)) {\n      return \"\".concat(__typename, \":\").concat(typeof id === \"number\" || typeof id === \"string\" ? id : JSON.stringify(id));\n    }\n  }\n}\nvar defaultConfig = {\n  dataIdFromObject: defaultDataIdFromObject,\n  addTypename: true,\n  resultCaching: true,\n  // Thanks to the shouldCanonizeResults helper, this should be the only line\n  // you have to change to reenable canonization by default in the future.\n  canonizeResults: false\n};\nexport function normalizeConfig(config) {\n  return compact(defaultConfig, config);\n}\nexport function shouldCanonizeResults(config) {\n  var value = config.canonizeResults;\n  return value === void 0 ? defaultConfig.canonizeResults : value;\n}\nexport function getTypenameFromStoreObject(store, objectOrReference) {\n  return isReference(objectOrReference) ? store.get(objectOrReference.__ref, \"__typename\") : objectOrReference && objectOrReference.__typename;\n}\nexport var TypeOrFieldNameRegExp = /^[_a-z][_0-9a-z]*/i;\nexport function fieldNameFromStoreName(storeFieldName) {\n  var match = storeFieldName.match(TypeOrFieldNameRegExp);\n  return match ? match[0] : storeFieldName;\n}\nexport function selectionSetMatchesResult(selectionSet, result, variables) {\n  if (isNonNullObject(result)) {\n    return isArray(result) ? result.every(function (item) {\n      return selectionSetMatchesResult(selectionSet, item, variables);\n    }) : selectionSet.selections.every(function (field) {\n      if (isField(field) && shouldInclude(field, variables)) {\n        var key = resultKeyNameFromField(field);\n        return hasOwn.call(result, key) && (!field.selectionSet || selectionSetMatchesResult(field.selectionSet, result[key], variables));\n      }\n      // If the selection has been skipped with @skip(true) or\n      // @include(false), it should not count against the matching. If\n      // the selection is not a field, it must be a fragment (inline or\n      // named). We will determine if selectionSetMatchesResult for that\n      // fragment when we get to it, so for now we return true.\n      return true;\n    });\n  }\n  return false;\n}\nexport function storeValueIsStoreObject(value) {\n  return isNonNullObject(value) && !isReference(value) && !isArray(value);\n}\nexport function makeProcessedFieldsMerger() {\n  return new DeepMerger();\n}\nexport function extractFragmentContext(document, fragments) {\n  // FragmentMap consisting only of fragments defined directly in document, not\n  // including other fragments registered in the FragmentRegistry.\n  var fragmentMap = createFragmentMap(getFragmentDefinitions(document));\n  return {\n    fragmentMap: fragmentMap,\n    lookupFragment: function (name) {\n      var def = fragmentMap[name];\n      if (!def && fragments) {\n        def = fragments.lookup(name);\n      }\n      return def || null;\n    }\n  };\n}\n//# sourceMappingURL=helpers.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}