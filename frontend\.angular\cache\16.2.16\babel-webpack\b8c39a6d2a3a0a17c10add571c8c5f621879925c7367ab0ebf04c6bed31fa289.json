{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nexport let DashboardComponent = class DashboardComponent {\n  constructor(authService, router, toastService) {\n    this.authService = authService;\n    this.router = router;\n    this.toastService = toastService;\n    this.users = [];\n    this.error = '';\n    this.message = '';\n    this.roles = ['student', 'teacher', 'admin'];\n    this.loading = true;\n    this.currentUser = null;\n    this.searchTerm = '';\n    this.filteredUsers = [];\n    // Create user modal properties\n    this.showCreateUserModal = false;\n    this.creatingUser = false;\n    this.newUser = {\n      fullName: '',\n      email: '',\n      role: 'student'\n    };\n    // Enhanced dashboard data\n    this.systemStats = {\n      totalUsers: 0,\n      activeUsers: 0,\n      newUsersToday: 0,\n      newUsersThisWeek: 0,\n      totalProjects: 0,\n      activeProjects: 0,\n      totalTeams: 0,\n      systemUptime: '99.9%'\n    };\n    // Activity logs\n    this.recentActivities = [];\n    // Filters and sorting\n    this.selectedRole = '';\n    this.selectedStatus = '';\n    this.sortBy = 'fullName';\n    this.sortOrder = 'asc';\n    // Bulk operations\n    this.selectedUsers = new Set();\n    this.showBulkActions = false;\n    // Advanced search\n    this.showAdvancedSearch = false;\n    this.advancedFilters = {\n      dateFrom: '',\n      dateTo: '',\n      verified: '',\n      hasGroup: ''\n    };\n  }\n  ngOnInit() {\n    this.loadUserData();\n  }\n  loadUserData() {\n    this.loading = true;\n    const token = localStorage.getItem('token');\n    const userStr = localStorage.getItem('user');\n    if (!token || !userStr) {\n      this.router.navigate(['/admin/login']);\n      return;\n    }\n    this.currentUser = JSON.parse(userStr);\n    // Check if user is admin\n    if (this.currentUser.role !== 'admin') {\n      this.router.navigate(['/']);\n      return;\n    }\n    this.authService.getAllUsers(token).subscribe({\n      next: res => {\n        this.users = res;\n        this.filteredUsers = [...this.users];\n        this.updateSystemStats();\n        this.loadRecentActivities();\n        this.loading = false;\n      },\n      error: err => {\n        this.error = err.error?.message || 'Failed to fetch users';\n        this.loading = false;\n      }\n    });\n  }\n  searchUsers() {\n    this.applyAdvancedFilters();\n  }\n  clearSearch() {\n    this.searchTerm = '';\n    this.applyAdvancedFilters();\n  }\n  onRoleChange(userId, newRole) {\n    const token = localStorage.getItem('token');\n    this.authService.updateUserRole(userId, newRole, token).subscribe({\n      next: res => {\n        this.message = res.message;\n        this.error = '';\n        // Update the user in the local arrays\n        const userIndex = this.users.findIndex(u => u._id === userId);\n        if (userIndex !== -1) {\n          this.users[userIndex].role = newRole;\n        }\n        const filteredIndex = this.filteredUsers.findIndex(u => u._id === userId);\n        if (filteredIndex !== -1) {\n          this.filteredUsers[filteredIndex].role = newRole;\n        }\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Failed to update role';\n        this.message = '';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  onDeleteUser(userId) {\n    const confirmDelete = confirm('Are you sure you want to delete this user?');\n    if (!confirmDelete) return;\n    const token = localStorage.getItem('token');\n    this.authService.deleteUser(userId, token).subscribe({\n      next: res => {\n        this.message = res.message;\n        this.error = '';\n        // Remove user from both arrays\n        this.users = this.users.filter(u => u._id !== userId);\n        this.filteredUsers = this.filteredUsers.filter(u => u._id !== userId);\n        // Auto-hide message after 3 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 3000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Failed to delete user';\n        this.message = '';\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  toggleUserActivation(userId, currentStatus) {\n    const newStatus = !currentStatus;\n    const action = newStatus ? 'activate' : 'deactivate';\n    // Find the user to get their name for better messaging\n    const user = this.users.find(u => u._id === userId);\n    const userName = user?.fullName || user?.firstName || 'User';\n    const confirmAction = confirm(`Are you sure you want to ${action} ${userName}?`);\n    if (!confirmAction) return;\n    const token = localStorage.getItem('token');\n    this.authService.toggleUserActivation(userId, newStatus, token).subscribe({\n      next: res => {\n        const statusText = newStatus ? 'activated' : 'deactivated';\n        const successMessage = `${userName} has been ${statusText} successfully`;\n        // Show success toast\n        this.toastService.showSuccess(successMessage);\n        // Clear any existing messages\n        this.message = '';\n        this.error = '';\n        // Update user in both arrays\n        const userIndex = this.users.findIndex(u => u._id === userId);\n        if (userIndex !== -1) {\n          this.users[userIndex].isActive = newStatus;\n        }\n        const filteredIndex = this.filteredUsers.findIndex(u => u._id === userId);\n        if (filteredIndex !== -1) {\n          this.filteredUsers[filteredIndex].isActive = newStatus;\n        }\n        // Apply filters to refresh the view\n        this.applyFilters();\n      },\n      error: err => {\n        const statusText = newStatus ? 'activate' : 'deactivate';\n        const errorMessage = err.error?.message || `Failed to ${statusText} ${userName}`;\n        // Show error toast\n        this.toastService.showError(errorMessage);\n        // Clear any existing messages\n        this.message = '';\n        this.error = '';\n      }\n    });\n  }\n  getStudentCount() {\n    return this.users.filter(u => u.role === 'student').length;\n  }\n  getTeacherCount() {\n    return this.users.filter(u => u.role === 'teacher').length;\n  }\n  getAdminCount() {\n    return this.users.filter(u => u.role === 'admin').length;\n  }\n  getActiveCount() {\n    return this.users.filter(u => u.isActive !== false).length;\n  }\n  getInactiveCount() {\n    return this.users.filter(u => u.isActive === false).length;\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/admin/login']);\n  }\n  showUserDetails(userId) {\n    this.router.navigate(['/admin/userdetails', userId]);\n  }\n  applyFilters() {\n    this.applyAdvancedFilters();\n  }\n  // Create user modal methods\n  openCreateUserModal() {\n    this.showCreateUserModal = true;\n    this.resetNewUserForm();\n  }\n  closeCreateUserModal() {\n    this.showCreateUserModal = false;\n    this.resetNewUserForm();\n  }\n  resetNewUserForm() {\n    this.newUser = {\n      fullName: '',\n      email: '',\n      role: 'student'\n    };\n    this.creatingUser = false;\n  }\n  onCreateUser(form) {\n    if (form.invalid || this.creatingUser) {\n      return;\n    }\n    this.creatingUser = true;\n    this.error = '';\n    this.message = '';\n    const token = localStorage.getItem('token');\n    this.authService.createUser(this.newUser, token).subscribe({\n      next: res => {\n        // Show success message\n        this.message = res.message;\n        this.error = '';\n        // Add new user to the lists\n        this.users.push(res.user);\n        this.filteredUsers = [...this.users];\n        this.applyFilters();\n        // Show success toast with credentials info\n        this.toastService.showSuccess(`User created successfully! Login credentials sent to ${res.user.email}`);\n        // Close modal and reset form\n        this.closeCreateUserModal();\n        // Auto-hide message after 5 seconds\n        setTimeout(() => {\n          this.message = '';\n        }, 5000);\n      },\n      error: err => {\n        this.error = err.error?.message || 'Failed to create user';\n        this.message = '';\n        this.creatingUser = false;\n        // Show error toast\n        this.toastService.showError(this.error);\n        // Auto-hide error after 3 seconds\n        setTimeout(() => {\n          this.error = '';\n        }, 3000);\n      }\n    });\n  }\n  // Enhanced dashboard methods\n  updateSystemStats() {\n    const now = new Date();\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);\n    this.systemStats = {\n      totalUsers: this.users.length,\n      activeUsers: this.users.filter(u => u.isActive !== false).length,\n      newUsersToday: this.users.filter(u => {\n        const userDate = new Date(u.createdAt);\n        return userDate >= today;\n      }).length,\n      newUsersThisWeek: this.users.filter(u => {\n        const userDate = new Date(u.createdAt);\n        return userDate >= weekAgo;\n      }).length,\n      totalProjects: 0,\n      activeProjects: 0,\n      totalTeams: 0,\n      systemUptime: '99.9%'\n    };\n  }\n  loadRecentActivities() {\n    // Mock recent activities - in real app, this would come from an API\n    this.recentActivities = [{\n      id: 1,\n      type: 'user_created',\n      message: 'New user registered',\n      user: 'John Doe',\n      timestamp: new Date(Date.now() - 1000 * 60 * 30),\n      icon: 'fas fa-user-plus',\n      color: 'text-green-600'\n    }, {\n      id: 2,\n      type: 'user_login',\n      message: 'User logged in',\n      user: 'Jane Smith',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60),\n      icon: 'fas fa-sign-in-alt',\n      color: 'text-blue-600'\n    }, {\n      id: 3,\n      type: 'role_changed',\n      message: 'User role updated to Teacher',\n      user: 'Mike Johnson',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),\n      icon: 'fas fa-user-tag',\n      color: 'text-purple-600'\n    }];\n  }\n  // Advanced filtering and sorting\n  applyAdvancedFilters() {\n    let filtered = [...this.users];\n    // Apply role filter\n    if (this.selectedRole) {\n      filtered = filtered.filter(user => user.role === this.selectedRole);\n    }\n    // Apply status filter\n    if (this.selectedStatus) {\n      if (this.selectedStatus === 'active') {\n        filtered = filtered.filter(user => user.isActive !== false);\n      } else if (this.selectedStatus === 'inactive') {\n        filtered = filtered.filter(user => user.isActive === false);\n      }\n    }\n    // Apply verification filter\n    if (this.advancedFilters.verified) {\n      const isVerified = this.advancedFilters.verified === 'true';\n      filtered = filtered.filter(user => user.verified === isVerified);\n    }\n    // Apply date range filter\n    if (this.advancedFilters.dateFrom) {\n      const fromDate = new Date(this.advancedFilters.dateFrom);\n      filtered = filtered.filter(user => new Date(user.createdAt) >= fromDate);\n    }\n    if (this.advancedFilters.dateTo) {\n      const toDate = new Date(this.advancedFilters.dateTo);\n      filtered = filtered.filter(user => new Date(user.createdAt) <= toDate);\n    }\n    // Apply search term\n    if (this.searchTerm.trim()) {\n      const term = this.searchTerm.toLowerCase().trim();\n      filtered = filtered.filter(user => user.fullName.toLowerCase().includes(term) || user.email.toLowerCase().includes(term) || user.role.toLowerCase().includes(term));\n    }\n    // Apply sorting\n    filtered.sort((a, b) => {\n      let aValue = a[this.sortBy];\n      let bValue = b[this.sortBy];\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (this.sortOrder === 'asc') {\n        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;\n      } else {\n        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;\n      }\n    });\n    this.filteredUsers = filtered;\n  }\n  // Bulk operations\n  toggleUserSelection(userId) {\n    if (this.selectedUsers.has(userId)) {\n      this.selectedUsers.delete(userId);\n    } else {\n      this.selectedUsers.add(userId);\n    }\n    this.showBulkActions = this.selectedUsers.size > 0;\n  }\n  selectAllUsers() {\n    if (this.selectedUsers.size === this.filteredUsers.length) {\n      this.selectedUsers.clear();\n    } else {\n      this.filteredUsers.forEach(user => this.selectedUsers.add(user._id));\n    }\n    this.showBulkActions = this.selectedUsers.size > 0;\n  }\n  bulkDeleteUsers() {\n    if (this.selectedUsers.size === 0) return;\n    const confirmDelete = confirm(`Are you sure you want to delete ${this.selectedUsers.size} users?`);\n    if (!confirmDelete) return;\n    const token = localStorage.getItem('token');\n    const userIds = Array.from(this.selectedUsers);\n    // For now, delete users one by one (in real app, implement bulk delete API)\n    let deletedCount = 0;\n    userIds.forEach(userId => {\n      this.authService.deleteUser(userId, token).subscribe({\n        next: () => {\n          deletedCount++;\n          this.users = this.users.filter(u => u._id !== userId);\n          if (deletedCount === userIds.length) {\n            this.selectedUsers.clear();\n            this.showBulkActions = false;\n            this.applyAdvancedFilters();\n            this.toastService.showSuccess(`${deletedCount} users deleted successfully`);\n          }\n        },\n        error: err => {\n          this.toastService.showError(`Failed to delete some users: ${err.error?.message}`);\n        }\n      });\n    });\n  }\n  bulkChangeRole(newRole) {\n    if (this.selectedUsers.size === 0) return;\n    const confirmChange = confirm(`Are you sure you want to change the role of ${this.selectedUsers.size} users to ${newRole}?`);\n    if (!confirmChange) return;\n    const token = localStorage.getItem('token');\n    const userIds = Array.from(this.selectedUsers);\n    let updatedCount = 0;\n    userIds.forEach(userId => {\n      this.authService.updateUserRole(userId, newRole, token).subscribe({\n        next: () => {\n          updatedCount++;\n          const userIndex = this.users.findIndex(u => u._id === userId);\n          if (userIndex !== -1) {\n            this.users[userIndex].role = newRole;\n          }\n          if (updatedCount === userIds.length) {\n            this.selectedUsers.clear();\n            this.showBulkActions = false;\n            this.applyAdvancedFilters();\n            this.toastService.showSuccess(`${updatedCount} users updated successfully`);\n          }\n        },\n        error: err => {\n          this.toastService.showError(`Failed to update some users: ${err.error?.message}`);\n        }\n      });\n    });\n  }\n  // Export functionality\n  exportUsers(format) {\n    const dataToExport = this.filteredUsers.map(user => ({\n      fullName: user.fullName,\n      email: user.email,\n      role: user.role,\n      isActive: user.isActive,\n      verified: user.verified,\n      createdAt: user.createdAt,\n      group: user.group?.name || 'No Group'\n    }));\n    if (format === 'csv') {\n      this.exportToCSV(dataToExport);\n    } else {\n      this.exportToJSON(dataToExport);\n    }\n  }\n  exportToCSV(data) {\n    const headers = Object.keys(data[0]);\n    const csvContent = [headers.join(','), ...data.map(row => headers.map(header => `\"${row[header]}\"`).join(','))].join('\\n');\n    this.downloadFile(csvContent, 'users.csv', 'text/csv');\n  }\n  exportToJSON(data) {\n    const jsonContent = JSON.stringify(data, null, 2);\n    this.downloadFile(jsonContent, 'users.json', 'application/json');\n  }\n  downloadFile(content, filename, contentType) {\n    const blob = new Blob([content], {\n      type: contentType\n    });\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    link.click();\n    window.URL.revokeObjectURL(url);\n  }\n  // Utility methods\n  toggleAdvancedSearch() {\n    this.showAdvancedSearch = !this.showAdvancedSearch;\n  }\n  clearAdvancedFilters() {\n    this.advancedFilters = {\n      dateFrom: '',\n      dateTo: '',\n      verified: '',\n      hasGroup: ''\n    };\n    this.selectedRole = '';\n    this.selectedStatus = '';\n    this.applyAdvancedFilters();\n  }\n  setSortBy(field) {\n    if (this.sortBy === field) {\n      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n    } else {\n      this.sortBy = field;\n      this.sortOrder = 'asc';\n    }\n    this.applyAdvancedFilters();\n  }\n  // Add missing methods that might be referenced in the template\n  updateUserRole(userId, newRole, token) {\n    // This method should be implemented in the auth service\n    // For now, return a mock observable\n    return new Promise(resolve => {\n      setTimeout(() => resolve({}), 1000);\n    });\n  }\n};\nDashboardComponent = __decorate([Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})], DashboardComponent);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}