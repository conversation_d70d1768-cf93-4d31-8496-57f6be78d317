{"ast": null, "code": "import { didYouMean } from '../../jsutils/didYouMean.mjs';\nimport { naturalCompare } from '../../jsutils/naturalCompare.mjs';\nimport { suggestionList } from '../../jsutils/suggestionList.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { isAbstractType, isInterfaceType, isObjectType } from '../../type/definition.mjs';\n\n/**\n * Fields on correct type\n *\n * A GraphQL document is only valid if all fields selected are defined by the\n * parent type, or are an allowed meta field such as __typename.\n *\n * See https://spec.graphql.org/draft/#sec-Field-Selections\n */\nexport function FieldsOnCorrectTypeRule(context) {\n  return {\n    Field(node) {\n      const type = context.getParentType();\n      if (type) {\n        const fieldDef = context.getFieldDef();\n        if (!fieldDef) {\n          // This field doesn't exist, lets look for suggestions.\n          const schema = context.getSchema();\n          const fieldName = node.name.value; // First determine if there are any suggested types to condition on.\n\n          let suggestion = didYouMean('to use an inline fragment on', getSuggestedTypeNames(schema, type, fieldName)); // If there are no suggested types, then perhaps this was a typo?\n\n          if (suggestion === '') {\n            suggestion = didYouMean(getSuggestedFieldNames(type, fieldName));\n          } // Report an error, including helpful suggestions.\n\n          context.reportError(new GraphQLError(`Cannot query field \"${fieldName}\" on type \"${type.name}\".` + suggestion, {\n            nodes: node\n          }));\n        }\n      }\n    }\n  };\n}\n/**\n * Go through all of the implementations of type, as well as the interfaces that\n * they implement. If any of those types include the provided field, suggest them,\n * sorted by how often the type is referenced.\n */\n\nfunction getSuggestedTypeNames(schema, type, fieldName) {\n  if (!isAbstractType(type)) {\n    // Must be an Object type, which does not have possible fields.\n    return [];\n  }\n  const suggestedTypes = new Set();\n  const usageCount = Object.create(null);\n  for (const possibleType of schema.getPossibleTypes(type)) {\n    if (!possibleType.getFields()[fieldName]) {\n      continue;\n    } // This object type defines this field.\n\n    suggestedTypes.add(possibleType);\n    usageCount[possibleType.name] = 1;\n    for (const possibleInterface of possibleType.getInterfaces()) {\n      var _usageCount$possibleI;\n      if (!possibleInterface.getFields()[fieldName]) {\n        continue;\n      } // This interface type defines this field.\n\n      suggestedTypes.add(possibleInterface);\n      usageCount[possibleInterface.name] = ((_usageCount$possibleI = usageCount[possibleInterface.name]) !== null && _usageCount$possibleI !== void 0 ? _usageCount$possibleI : 0) + 1;\n    }\n  }\n  return [...suggestedTypes].sort((typeA, typeB) => {\n    // Suggest both interface and object types based on how common they are.\n    const usageCountDiff = usageCount[typeB.name] - usageCount[typeA.name];\n    if (usageCountDiff !== 0) {\n      return usageCountDiff;\n    } // Suggest super types first followed by subtypes\n\n    if (isInterfaceType(typeA) && schema.isSubType(typeA, typeB)) {\n      return -1;\n    }\n    if (isInterfaceType(typeB) && schema.isSubType(typeB, typeA)) {\n      return 1;\n    }\n    return naturalCompare(typeA.name, typeB.name);\n  }).map(x => x.name);\n}\n/**\n * For the field name provided, determine if there are any similar field names\n * that may be the result of a typo.\n */\n\nfunction getSuggestedFieldNames(type, fieldName) {\n  if (isObjectType(type) || isInterfaceType(type)) {\n    const possibleFieldNames = Object.keys(type.getFields());\n    return suggestionList(fieldName, possibleFieldNames);\n  } // Otherwise, must be a Union type, which does not define fields.\n\n  return [];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}