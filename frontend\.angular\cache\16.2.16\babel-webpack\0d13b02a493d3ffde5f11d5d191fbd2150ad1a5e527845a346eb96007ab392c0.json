{"ast": null, "code": "import { __assign, __rest } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { Observable, hasDirectives } from \"../../utilities/index.js\";\nimport { serializeFetchParameter } from \"./serializeFetchParameter.js\";\nimport { selectURI } from \"./selectURI.js\";\nimport { handleError, readMultipartBody, parseAndCheckHttpResponse } from \"./parseAndCheckHttpResponse.js\";\nimport { checkFetcher } from \"./checkFetcher.js\";\nimport { selectHttpOptionsAndBodyInternal, defaultPrinter, fallbackHttpConfig } from \"./selectHttpOptionsAndBody.js\";\nimport { rewriteURIForGET } from \"./rewriteURIForGET.js\";\nimport { fromError, filterOperationVariables } from \"../utils/index.js\";\nimport { maybe, getMainDefinition, removeClientSetsFromDocument } from \"../../utilities/index.js\";\nvar backupFetch = maybe(function () {\n  return fetch;\n});\nexport var createHttpLink = function (linkOptions) {\n  if (linkOptions === void 0) {\n    linkOptions = {};\n  }\n  var _a = linkOptions.uri,\n    uri = _a === void 0 ? \"/graphql\" : _a,\n    // use default global fetch if nothing passed in\n    preferredFetch = linkOptions.fetch,\n    _b = linkOptions.print,\n    print = _b === void 0 ? defaultPrinter : _b,\n    includeExtensions = linkOptions.includeExtensions,\n    preserveHeaderCase = linkOptions.preserveHeaderCase,\n    useGETForQueries = linkOptions.useGETForQueries,\n    _c = linkOptions.includeUnusedVariables,\n    includeUnusedVariables = _c === void 0 ? false : _c,\n    requestOptions = __rest(linkOptions, [\"uri\", \"fetch\", \"print\", \"includeExtensions\", \"preserveHeaderCase\", \"useGETForQueries\", \"includeUnusedVariables\"]);\n  if (globalThis.__DEV__ !== false) {\n    // Make sure at least one of preferredFetch, window.fetch, or backupFetch is\n    // defined, so requests won't fail at runtime.\n    checkFetcher(preferredFetch || backupFetch);\n  }\n  var linkConfig = {\n    http: {\n      includeExtensions: includeExtensions,\n      preserveHeaderCase: preserveHeaderCase\n    },\n    options: requestOptions.fetchOptions,\n    credentials: requestOptions.credentials,\n    headers: requestOptions.headers\n  };\n  return new ApolloLink(function (operation) {\n    var chosenURI = selectURI(operation, uri);\n    var context = operation.getContext();\n    // `apollographql-client-*` headers are automatically set if a\n    // `clientAwareness` object is found in the context. These headers are\n    // set first, followed by the rest of the headers pulled from\n    // `context.headers`. If desired, `apollographql-client-*` headers set by\n    // the `clientAwareness` object can be overridden by\n    // `apollographql-client-*` headers set in `context.headers`.\n    var clientAwarenessHeaders = {};\n    if (context.clientAwareness) {\n      var _a = context.clientAwareness,\n        name_1 = _a.name,\n        version = _a.version;\n      if (name_1) {\n        clientAwarenessHeaders[\"apollographql-client-name\"] = name_1;\n      }\n      if (version) {\n        clientAwarenessHeaders[\"apollographql-client-version\"] = version;\n      }\n    }\n    var contextHeaders = __assign(__assign({}, clientAwarenessHeaders), context.headers);\n    var contextConfig = {\n      http: context.http,\n      options: context.fetchOptions,\n      credentials: context.credentials,\n      headers: contextHeaders\n    };\n    if (hasDirectives([\"client\"], operation.query)) {\n      var transformedQuery = removeClientSetsFromDocument(operation.query);\n      if (!transformedQuery) {\n        return fromError(new Error(\"HttpLink: Trying to send a client-only query to the server. To send to the server, ensure a non-client field is added to the query or set the `transformOptions.removeClientFields` option to `true`.\"));\n      }\n      operation.query = transformedQuery;\n    }\n    //uses fallback, link, and then context to build options\n    var _b = selectHttpOptionsAndBodyInternal(operation, print, fallbackHttpConfig, linkConfig, contextConfig),\n      options = _b.options,\n      body = _b.body;\n    if (body.variables && !includeUnusedVariables) {\n      body.variables = filterOperationVariables(body.variables, operation.query);\n    }\n    var controller;\n    if (!options.signal && typeof AbortController !== \"undefined\") {\n      controller = new AbortController();\n      options.signal = controller.signal;\n    }\n    // If requested, set method to GET if there are no mutations.\n    var definitionIsMutation = function (d) {\n      return d.kind === \"OperationDefinition\" && d.operation === \"mutation\";\n    };\n    var definitionIsSubscription = function (d) {\n      return d.kind === \"OperationDefinition\" && d.operation === \"subscription\";\n    };\n    var isSubscription = definitionIsSubscription(getMainDefinition(operation.query));\n    // does not match custom directives beginning with @defer\n    var hasDefer = hasDirectives([\"defer\"], operation.query);\n    if (useGETForQueries && !operation.query.definitions.some(definitionIsMutation)) {\n      options.method = \"GET\";\n    }\n    if (hasDefer || isSubscription) {\n      options.headers = options.headers || {};\n      var acceptHeader = \"multipart/mixed;\";\n      // Omit defer-specific headers if the user attempts to defer a selection\n      // set on a subscription and log a warning.\n      if (isSubscription && hasDefer) {\n        globalThis.__DEV__ !== false && invariant.warn(41);\n      }\n      if (isSubscription) {\n        acceptHeader += \"boundary=graphql;subscriptionSpec=1.0,application/json\";\n      } else if (hasDefer) {\n        acceptHeader += \"deferSpec=20220824,application/json\";\n      }\n      options.headers.accept = acceptHeader;\n    }\n    if (options.method === \"GET\") {\n      var _c = rewriteURIForGET(chosenURI, body),\n        newURI = _c.newURI,\n        parseError = _c.parseError;\n      if (parseError) {\n        return fromError(parseError);\n      }\n      chosenURI = newURI;\n    } else {\n      try {\n        options.body = serializeFetchParameter(body, \"Payload\");\n      } catch (parseError) {\n        return fromError(parseError);\n      }\n    }\n    return new Observable(function (observer) {\n      // Prefer linkOptions.fetch (preferredFetch) if provided, and otherwise\n      // fall back to the *current* global window.fetch function (see issue\n      // #7832), or (if all else fails) the backupFetch function we saved when\n      // this module was first evaluated. This last option protects against the\n      // removal of window.fetch, which is unlikely but not impossible.\n      var currentFetch = preferredFetch || maybe(function () {\n        return fetch;\n      }) || backupFetch;\n      var observerNext = observer.next.bind(observer);\n      currentFetch(chosenURI, options).then(function (response) {\n        var _a;\n        operation.setContext({\n          response: response\n        });\n        var ctype = (_a = response.headers) === null || _a === void 0 ? void 0 : _a.get(\"content-type\");\n        if (ctype !== null && /^multipart\\/mixed/i.test(ctype)) {\n          return readMultipartBody(response, observerNext);\n        } else {\n          return parseAndCheckHttpResponse(operation)(response).then(observerNext);\n        }\n      }).then(function () {\n        controller = undefined;\n        observer.complete();\n      }).catch(function (err) {\n        controller = undefined;\n        handleError(err, observer);\n      });\n      return function () {\n        // XXX support canceling this request\n        // https://developers.google.com/web/updates/2017/09/abortable-fetch\n        if (controller) controller.abort();\n      };\n    });\n  });\n};\n//# sourceMappingURL=createHttpLink.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}