{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { keyMap } from '../jsutils/keyMap.mjs';\nimport { printPathArray } from '../jsutils/printPathArray.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { print } from '../language/printer.mjs';\nimport { isInputType, isNonNullType } from '../type/definition.mjs';\nimport { coerceInputValue } from '../utilities/coerceInputValue.mjs';\nimport { typeFromAST } from '../utilities/typeFromAST.mjs';\nimport { valueFromAST } from '../utilities/valueFromAST.mjs';\n\n/**\n * Prepares an object map of variableValues of the correct type based on the\n * provided variable definitions and arbitrary input. If the input cannot be\n * parsed to match the variable definitions, a GraphQLError will be thrown.\n *\n * Note: The returned value is a plain Object with a prototype, since it is\n * exposed to user code. Care should be taken to not pull values from the\n * Object prototype.\n */\nexport function getVariableValues(schema, varDefNodes, inputs, options) {\n  const errors = [];\n  const maxErrors = options === null || options === void 0 ? void 0 : options.maxErrors;\n  try {\n    const coerced = coerceVariableValues(schema, varDefNodes, inputs, error => {\n      if (maxErrors != null && errors.length >= maxErrors) {\n        throw new GraphQLError('Too many errors processing variables, error limit reached. Execution aborted.');\n      }\n      errors.push(error);\n    });\n    if (errors.length === 0) {\n      return {\n        coerced\n      };\n    }\n  } catch (error) {\n    errors.push(error);\n  }\n  return {\n    errors\n  };\n}\nfunction coerceVariableValues(schema, varDefNodes, inputs, onError) {\n  const coercedValues = {};\n  for (const varDefNode of varDefNodes) {\n    const varName = varDefNode.variable.name.value;\n    const varType = typeFromAST(schema, varDefNode.type);\n    if (!isInputType(varType)) {\n      // Must use input types for variables. This should be caught during\n      // validation, however is checked again here for safety.\n      const varTypeStr = print(varDefNode.type);\n      onError(new GraphQLError(`Variable \"$${varName}\" expected value of type \"${varTypeStr}\" which cannot be used as an input type.`, {\n        nodes: varDefNode.type\n      }));\n      continue;\n    }\n    if (!hasOwnProperty(inputs, varName)) {\n      if (varDefNode.defaultValue) {\n        coercedValues[varName] = valueFromAST(varDefNode.defaultValue, varType);\n      } else if (isNonNullType(varType)) {\n        const varTypeStr = inspect(varType);\n        onError(new GraphQLError(`Variable \"$${varName}\" of required type \"${varTypeStr}\" was not provided.`, {\n          nodes: varDefNode\n        }));\n      }\n      continue;\n    }\n    const value = inputs[varName];\n    if (value === null && isNonNullType(varType)) {\n      const varTypeStr = inspect(varType);\n      onError(new GraphQLError(`Variable \"$${varName}\" of non-null type \"${varTypeStr}\" must not be null.`, {\n        nodes: varDefNode\n      }));\n      continue;\n    }\n    coercedValues[varName] = coerceInputValue(value, varType, (path, invalidValue, error) => {\n      let prefix = `Variable \"$${varName}\" got invalid value ` + inspect(invalidValue);\n      if (path.length > 0) {\n        prefix += ` at \"${varName}${printPathArray(path)}\"`;\n      }\n      onError(new GraphQLError(prefix + '; ' + error.message, {\n        nodes: varDefNode,\n        originalError: error\n      }));\n    });\n  }\n  return coercedValues;\n}\n/**\n * Prepares an object map of argument values given a list of argument\n * definitions and list of argument AST nodes.\n *\n * Note: The returned value is a plain Object with a prototype, since it is\n * exposed to user code. Care should be taken to not pull values from the\n * Object prototype.\n */\n\nexport function getArgumentValues(def, node, variableValues) {\n  var _node$arguments;\n  const coercedValues = {}; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  /* c8 ignore next */\n\n  const argumentNodes = (_node$arguments = node.arguments) !== null && _node$arguments !== void 0 ? _node$arguments : [];\n  const argNodeMap = keyMap(argumentNodes, arg => arg.name.value);\n  for (const argDef of def.args) {\n    const name = argDef.name;\n    const argType = argDef.type;\n    const argumentNode = argNodeMap[name];\n    if (!argumentNode) {\n      if (argDef.defaultValue !== undefined) {\n        coercedValues[name] = argDef.defaultValue;\n      } else if (isNonNullType(argType)) {\n        throw new GraphQLError(`Argument \"${name}\" of required type \"${inspect(argType)}\" ` + 'was not provided.', {\n          nodes: node\n        });\n      }\n      continue;\n    }\n    const valueNode = argumentNode.value;\n    let isNull = valueNode.kind === Kind.NULL;\n    if (valueNode.kind === Kind.VARIABLE) {\n      const variableName = valueNode.name.value;\n      if (variableValues == null || !hasOwnProperty(variableValues, variableName)) {\n        if (argDef.defaultValue !== undefined) {\n          coercedValues[name] = argDef.defaultValue;\n        } else if (isNonNullType(argType)) {\n          throw new GraphQLError(`Argument \"${name}\" of required type \"${inspect(argType)}\" ` + `was provided the variable \"$${variableName}\" which was not provided a runtime value.`, {\n            nodes: valueNode\n          });\n        }\n        continue;\n      }\n      isNull = variableValues[variableName] == null;\n    }\n    if (isNull && isNonNullType(argType)) {\n      throw new GraphQLError(`Argument \"${name}\" of non-null type \"${inspect(argType)}\" ` + 'must not be null.', {\n        nodes: valueNode\n      });\n    }\n    const coercedValue = valueFromAST(valueNode, argType, variableValues);\n    if (coercedValue === undefined) {\n      // Note: ValuesOfCorrectTypeRule validation should catch this before\n      // execution. This is a runtime check to ensure execution does not\n      // continue with an invalid argument value.\n      throw new GraphQLError(`Argument \"${name}\" has invalid value ${print(valueNode)}.`, {\n        nodes: valueNode\n      });\n    }\n    coercedValues[name] = coercedValue;\n  }\n  return coercedValues;\n}\n/**\n * Prepares an object map of argument values given a directive definition\n * and a AST node which may contain directives. Optionally also accepts a map\n * of variable values.\n *\n * If the directive does not exist on the node, returns undefined.\n *\n * Note: The returned value is a plain Object with a prototype, since it is\n * exposed to user code. Care should be taken to not pull values from the\n * Object prototype.\n */\n\nexport function getDirectiveValues(directiveDef, node, variableValues) {\n  var _node$directives;\n  const directiveNode = (_node$directives = node.directives) === null || _node$directives === void 0 ? void 0 : _node$directives.find(directive => directive.name.value === directiveDef.name);\n  if (directiveNode) {\n    return getArgumentValues(directiveDef, directiveNode, variableValues);\n  }\n}\nfunction hasOwnProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}