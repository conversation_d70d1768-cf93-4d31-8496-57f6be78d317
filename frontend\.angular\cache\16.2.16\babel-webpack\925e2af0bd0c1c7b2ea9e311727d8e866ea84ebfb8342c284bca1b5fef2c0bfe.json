{"ast": null, "code": "import { map, BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/message.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/services/toast.service\";\nimport * as i5 from \"src/app/services/logger.service\";\nimport * as i6 from \"@app/services/theme.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nfunction MessagesListComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\", 32);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const count_r6 = ctx.ngIf;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", count_r6, \" \");\n  }\n}\nfunction MessagesListComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"div\", 35);\n    i0.ɵɵelementStart(2, \"p\", 36);\n    i0.ɵɵtext(3, \"Chargement des conversations...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessagesListComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵelement(2, \"i\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 40)(4, \"h3\", 41);\n    i0.ɵɵtext(5, \" Erreur de chargement des conversations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 42);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function MessagesListComponent_div_39_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.loadConversations());\n    });\n    i0.ɵɵelement(9, \"i\", 44);\n    i0.ɵɵtext(10, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nfunction MessagesListComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46);\n    i0.ɵɵelement(2, \"i\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 48);\n    i0.ɵɵtext(4, \"Aucune conversation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 49);\n    i0.ɵɵtext(6, \" D\\u00E9marrez une nouvelle conversation pour communiquer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function MessagesListComponent_div_40_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.startNewConversation());\n    });\n    i0.ɵɵelement(8, \"i\", 51);\n    i0.ɵɵtext(9, \" Nouvelle Conversation \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessagesListComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 46);\n    i0.ɵɵelement(2, \"i\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 48);\n    i0.ɵɵtext(4, \"Aucun r\\u00E9sultat trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 49);\n    i0.ɵɵtext(6, \"Essayez un autre terme de recherche\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessagesListComponent_ul_42_li_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 69);\n  }\n}\nfunction MessagesListComponent_ul_42_li_1_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 70);\n    i0.ɵɵtext(1, \"Vous: \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessagesListComponent_ul_42_li_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const conv_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", conv_r12.unreadCount, \" \");\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"futuristic-conversation-selected\": a0\n  };\n};\nfunction MessagesListComponent_ul_42_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 56);\n    i0.ɵɵlistener(\"click\", function MessagesListComponent_ul_42_li_1_Template_li_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const conv_r12 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.openConversation(conv_r12.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 57)(2, \"div\", 58);\n    i0.ɵɵelement(3, \"img\", 59);\n    i0.ɵɵtemplate(4, MessagesListComponent_ul_42_li_1_div_4_Template, 1, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 61)(6, \"div\", 62)(7, \"h3\", 63);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 64);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 65)(13, \"p\", 66);\n    i0.ɵɵtemplate(14, MessagesListComponent_ul_42_li_1_span_14_Template, 2, 0, \"span\", 67);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, MessagesListComponent_ul_42_li_1_div_16_Template, 2, 1, \"div\", 68);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const conv_r12 = ctx.$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c0, ctx_r11.selectedConversationId === conv_r12.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", (conv_r12.participants ? (tmp_1_0 = ctx_r11.getOtherParticipant(conv_r12.participants)) == null ? null : tmp_1_0.image : null) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", conv_r12.participants && ((tmp_2_0 = ctx_r11.getOtherParticipant(conv_r12.participants)) == null ? null : tmp_2_0.isOnline));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (conv_r12.participants ? (tmp_3_0 = ctx_r11.getOtherParticipant(conv_r12.participants)) == null ? null : tmp_3_0.username : null) || \"Utilisateur inconnu\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(11, 8, conv_r12.lastMessage == null ? null : conv_r12.lastMessage.timestamp, \"shortTime\") || \"\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (conv_r12.lastMessage == null ? null : conv_r12.lastMessage.sender == null ? null : conv_r12.lastMessage.sender.id) === ctx_r11.currentUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", (conv_r12.lastMessage == null ? null : conv_r12.lastMessage.content) || \"Pas encore de messages\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", conv_r12.unreadCount && conv_r12.unreadCount > 0);\n  }\n}\nfunction MessagesListComponent_ul_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 54);\n    i0.ɵɵtemplate(1, MessagesListComponent_ul_42_li_1_Template, 17, 13, \"li\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.filteredConversations);\n  }\n}\nexport let MessagesListComponent = /*#__PURE__*/(() => {\n  class MessagesListComponent {\n    constructor(MessageService, authService, router, route, toastService, logger, themeService) {\n      this.MessageService = MessageService;\n      this.authService = authService;\n      this.router = router;\n      this.route = route;\n      this.toastService = toastService;\n      this.logger = logger;\n      this.themeService = themeService;\n      this.conversations = [];\n      this.filteredConversations = [];\n      this.loading = true;\n      this.currentUserId = null;\n      this.searchQuery = '';\n      this.selectedConversationId = null;\n      this.unreadCount = new BehaviorSubject(0);\n      this.unreadCount$ = this.unreadCount.asObservable();\n      this.subscriptions = [];\n      this.isDarkMode$ = this.themeService.darkMode$;\n    }\n    ngOnInit() {\n      this.currentUserId = this.authService.getCurrentUserId();\n      if (!this.currentUserId) {\n        this.handleError('User not authenticated');\n        return;\n      }\n      this.loadConversations();\n      this.subscribeToUserStatus();\n      this.subscribeToConversationUpdates();\n      // Check for active conversation from route\n      this.route.firstChild?.params.subscribe(params => {\n        this.selectedConversationId = params['conversationId'] || null;\n      });\n    }\n    loadConversations() {\n      this.logger.info('MessagesList', `Loading conversations`);\n      this.loading = true;\n      this.error = null;\n      const sub = this.MessageService.getConversations().subscribe({\n        next: conversations => {\n          this.logger.info('MessagesList', `Received ${conversations.length} conversations from service`);\n          this.conversations = Array.isArray(conversations) ? [...conversations] : [];\n          this.logger.debug('MessagesList', `Filtering conversations`);\n          this.filterConversations();\n          this.logger.debug('MessagesList', `Updating unread count`);\n          this.updateUnreadCount();\n          this.logger.debug('MessagesList', `Sorting conversations`);\n          this.sortConversations();\n          this.loading = false;\n          this.logger.info('MessagesList', `Conversations loaded successfully`);\n        },\n        error: error => {\n          this.logger.error('MessagesList', `Error loading conversations:`, error);\n          this.error = error;\n          this.loading = false;\n          this.toastService.showError('Failed to load conversations');\n        }\n      });\n      this.subscriptions.push(sub);\n      this.logger.debug('MessagesList', `Conversation subscription added`);\n    }\n    filterConversations() {\n      if (!this.searchQuery) {\n        this.filteredConversations = [...this.conversations];\n        return;\n      }\n      const query = this.searchQuery.toLowerCase();\n      this.filteredConversations = this.conversations.filter(conv => {\n        const otherParticipant = conv.participants ? this.getOtherParticipant(conv.participants) : undefined;\n        return otherParticipant?.username.toLowerCase().includes(query) || conv.lastMessage?.content?.toLowerCase().includes(query) || false;\n      });\n    }\n    updateUnreadCount() {\n      const count = this.conversations.reduce((sum, conv) => sum + (conv.unreadCount || 0), 0);\n      this.unreadCount.next(count);\n    }\n    sortConversations() {\n      this.conversations.sort((a, b) => {\n        const dateA = this.getConversationDate(a);\n        const dateB = this.getConversationDate(b);\n        return dateB.getTime() - dateA.getTime();\n      });\n      this.filterConversations();\n    }\n    getConversationDate(conv) {\n      // Utiliser une date par défaut si aucune date n'est disponible\n      const defaultDate = new Date(0); // 1970-01-01\n      if (conv.lastMessage?.timestamp) {\n        return typeof conv.lastMessage.timestamp === 'string' ? new Date(conv.lastMessage.timestamp) : conv.lastMessage.timestamp;\n      }\n      if (conv.updatedAt) {\n        return typeof conv.updatedAt === 'string' ? new Date(conv.updatedAt) : conv.updatedAt;\n      }\n      if (conv.createdAt) {\n        return typeof conv.createdAt === 'string' ? new Date(conv.createdAt) : conv.createdAt;\n      }\n      return defaultDate;\n    }\n    getOtherParticipant(participants) {\n      if (!participants || !Array.isArray(participants)) {\n        return undefined;\n      }\n      return participants.find(p => p._id !== this.currentUserId && p.id !== this.currentUserId);\n    }\n    subscribeToUserStatus() {\n      const sub = this.MessageService.subscribeToUserStatus().pipe(map(user => this.MessageService.normalizeUser(user))).subscribe({\n        next: user => {\n          if (user) {\n            this.updateUserStatus(user);\n          }\n        },\n        error: error => {\n          this.logger.error('MessagesList', 'Error in status subscription:', error);\n          this.toastService.showError('Connection to status updates lost');\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n    subscribeToConversationUpdates() {\n      const sub = this.MessageService.subscribeToConversationUpdates('global').subscribe({\n        next: updatedConv => {\n          const index = this.conversations.findIndex(c => c.id === updatedConv.id);\n          if (index >= 0) {\n            this.conversations[index] = updatedConv;\n          } else {\n            this.conversations.unshift(updatedConv);\n          }\n          this.sortConversations();\n        },\n        error: error => {\n          this.logger.error('MessagesList', 'Conversation update error:', error);\n        }\n      });\n      this.subscriptions.push(sub);\n    }\n    updateUserStatus(updatedUser) {\n      this.conversations = this.conversations.map(conv => {\n        if (!conv.participants) {\n          return conv;\n        }\n        const participants = conv.participants.map(p => {\n          const userIdMatches = p._id === updatedUser._id || p.id === updatedUser._id;\n          return userIdMatches ? {\n            ...p,\n            isOnline: updatedUser.isOnline,\n            lastActive: updatedUser.lastActive\n          } : p;\n        });\n        return {\n          ...conv,\n          participants\n        };\n      });\n      this.filterConversations();\n    }\n    openConversation(conversationId) {\n      if (!conversationId) {\n        this.logger.error('MessagesList', 'Cannot open conversation: conversationId is undefined');\n        return;\n      }\n      this.logger.info('MessagesList', `Opening conversation: ${conversationId}`);\n      this.selectedConversationId = conversationId;\n      // Trouver la conversation pour les logs\n      const conversation = this.conversations.find(c => c.id === conversationId);\n      if (conversation) {\n        const otherParticipant = conversation.participants ? this.getOtherParticipant(conversation.participants) : undefined;\n        this.logger.debug('MessagesList', `Conversation with: ${otherParticipant?.username || 'Unknown'}`);\n        this.logger.debug('MessagesList', `Unread messages: ${conversation.unreadCount || 0}`);\n      }\n      this.logger.debug('MessagesList', `Navigating to chat route with conversationId: ${conversationId}`);\n      this.router.navigate(['chat', conversationId], {\n        relativeTo: this.route\n      });\n    }\n    startNewConversation() {\n      this.logger.info('MessagesList', 'Starting new conversation, navigating to users list');\n      this.router.navigate(['/messages/users']);\n    }\n    formatLastActive(lastActive) {\n      if (!lastActive) return 'Offline';\n      const lastActiveDate = new Date(lastActive);\n      const now = new Date();\n      const diffHours = Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n      if (diffHours < 24) {\n        return `Active ${lastActiveDate.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        })}`;\n      } else {\n        return `Active ${lastActiveDate.toLocaleDateString()}`;\n      }\n    }\n    handleError(message, error) {\n      this.error = message;\n      this.loading = false;\n      this.logger.error('MessagesList', message, error);\n      this.toastService.showError(message);\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    static {\n      this.ɵfac = function MessagesListComponent_Factory(t) {\n        return new (t || MessagesListComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i5.LoggerService), i0.ɵɵdirectiveInject(i6.ThemeService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: MessagesListComponent,\n        selectors: [[\"app-messages-list\"]],\n        decls: 45,\n        vars: 13,\n        consts: [[1, \"flex\", \"h-screen\", \"futuristic-messages-page\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"md:w-80\", \"lg:w-96\", \"futuristic-sidebar\", \"flex\", \"flex-col\", \"relative\", \"z-10\", \"backdrop-blur-sm\"], [1, \"futuristic-header\", \"sticky\", \"top-0\", \"z-10\", \"backdrop-blur-sm\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-4\"], [1, \"futuristic-title\", \"text-xl\", \"font-bold\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"title\", \"Nouvelle conversation\", 1, \"p-2\", \"rounded-full\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"transition-all\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"rounded-full\", \"blur-md\"], [1, \"fas\", \"fa-edit\", \"relative\", \"z-10\", \"group-hover:scale-110\", \"transition-transform\"], [\"class\", \"relative\", 4, \"ngIf\"], [1, \"relative\", \"group\"], [\"type\", \"text\", \"placeholder\", \"Rechercher des conversations...\", 1, \"w-full\", \"pl-10\", \"pr-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\", 3, \"ngModel\", \"ngModelChange\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [1, \"fas\", \"fa-search\", \"text-[#bdc6cc]\", \"dark:text-[#6d6870]\", \"group-focus-within:text-[#4f5fad]\", \"dark:group-focus-within:text-[#6d78c9]\", \"transition-colors\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [1, \"flex-1\", \"overflow-y-auto\", \"futuristic-conversations-list\"], [\"class\", \"flex flex-col items-center justify-center h-full p-4\", 4, \"ngIf\"], [\"class\", \"futuristic-error-container\", 4, \"ngIf\"], [\"class\", \"futuristic-empty-state\", 4, \"ngIf\"], [\"class\", \"futuristic-no-results\", 4, \"ngIf\"], [\"class\", \"futuristic-conversations\", 4, \"ngIf\"], [1, \"flex-1\", \"hidden\", \"md:flex\", \"flex-col\", \"futuristic-main-area\"], [1, \"relative\"], [1, \"futuristic-badge\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/30\", \"dark:bg-[#6d78c9]/30\", \"rounded-full\", \"blur-md\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"h-full\", \"p-4\"], [1, \"futuristic-loading-circle\"], [1, \"futuristic-loading-text\"], [1, \"futuristic-error-container\"], [1, \"futuristic-error-icon\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"flex-1\"], [1, \"futuristic-error-title\"], [1, \"futuristic-error-message\"], [1, \"futuristic-retry-button\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\", \"mr-1.5\"], [1, \"futuristic-empty-state\"], [1, \"futuristic-empty-icon\"], [1, \"fas\", \"fa-comments\"], [1, \"futuristic-empty-title\"], [1, \"futuristic-empty-text\"], [1, \"futuristic-start-button\", 3, \"click\"], [1, \"fas\", \"fa-plus-circle\", \"mr-2\"], [1, \"futuristic-no-results\"], [1, \"fas\", \"fa-search\"], [1, \"futuristic-conversations\"], [\"class\", \"futuristic-conversation-item\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"futuristic-conversation-item\", 3, \"ngClass\", \"click\"], [1, \"flex\", \"items-center\"], [1, \"futuristic-avatar\"], [\"alt\", \"User avatar\", 3, \"src\"], [\"class\", \"futuristic-online-indicator\", 4, \"ngIf\"], [1, \"futuristic-conversation-details\"], [1, \"futuristic-conversation-header\"], [1, \"futuristic-conversation-name\"], [1, \"futuristic-conversation-time\"], [1, \"futuristic-conversation-preview\"], [1, \"futuristic-conversation-message\"], [\"class\", \"futuristic-you-prefix\", 4, \"ngIf\"], [\"class\", \"futuristic-unread-badge\", 4, \"ngIf\"], [1, \"futuristic-online-indicator\"], [1, \"futuristic-you-prefix\"], [1, \"futuristic-unread-badge\"]],\n        template: function MessagesListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵpipe(1, \"async\");\n            i0.ɵɵelementStart(2, \"div\", 1);\n            i0.ɵɵelement(3, \"div\", 2)(4, \"div\", 3);\n            i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5);\n            i0.ɵɵelement(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6)(17, \"div\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(18, \"div\", 7)(19, \"div\", 8);\n            i0.ɵɵelement(20, \"div\", 9)(21, \"div\", 10);\n            i0.ɵɵelementStart(22, \"div\", 11)(23, \"h1\", 12);\n            i0.ɵɵtext(24, \"Messages\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"div\", 13)(26, \"button\", 14);\n            i0.ɵɵlistener(\"click\", function MessagesListComponent_Template_button_click_26_listener() {\n              return ctx.startNewConversation();\n            });\n            i0.ɵɵelement(27, \"div\", 15)(28, \"i\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(29, MessagesListComponent_div_29_Template, 4, 1, \"div\", 17);\n            i0.ɵɵpipe(30, \"async\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 18)(32, \"input\", 19);\n            i0.ɵɵlistener(\"ngModelChange\", function MessagesListComponent_Template_input_ngModelChange_32_listener($event) {\n              return ctx.searchQuery = $event;\n            })(\"ngModelChange\", function MessagesListComponent_Template_input_ngModelChange_32_listener() {\n              return ctx.filterConversations();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"div\", 20);\n            i0.ɵɵelement(34, \"i\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"div\", 22);\n            i0.ɵɵelement(36, \"div\", 23);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"div\", 24);\n            i0.ɵɵtemplate(38, MessagesListComponent_div_38_Template, 4, 0, \"div\", 25);\n            i0.ɵɵtemplate(39, MessagesListComponent_div_39_Template, 11, 1, \"div\", 26);\n            i0.ɵɵtemplate(40, MessagesListComponent_div_40_Template, 10, 0, \"div\", 27);\n            i0.ɵɵtemplate(41, MessagesListComponent_div_41_Template, 7, 0, \"div\", 28);\n            i0.ɵɵtemplate(42, MessagesListComponent_ul_42_Template, 2, 1, \"ul\", 29);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(43, \"div\", 30);\n            i0.ɵɵelement(44, \"router-outlet\");\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 9, ctx.isDarkMode$));\n            i0.ɵɵadvance(29);\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(30, 11, ctx.unreadCount$));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredConversations.length === 0 && !ctx.searchQuery);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredConversations.length === 0 && ctx.searchQuery);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.filteredConversations.length > 0);\n          }\n        },\n        dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i3.RouterOutlet, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel, i7.AsyncPipe, i7.DatePipe],\n        styles: [\":not(.dark)[_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%]{background-color:#f0f4f8;color:#6d6870}.dark[_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-messages-page[_ngcontent-%COMP%]{background-color:var(--dark-bg);color:var(--text-light)}:not(.dark)[_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%]{background-color:#fff;border-color:#4f5fad33}.dark[_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-sidebar[_ngcontent-%COMP%]{background-color:var(--medium-bg);border-color:#00f7ff33}:not(.dark)[_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%]{background-color:#fff;border-bottom:1px solid rgba(79,95,173,.2);padding:15px}.dark[_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-header[_ngcontent-%COMP%]{background-color:var(--medium-bg);border-bottom:1px solid rgba(0,247,255,.2);padding:15px}:not(.dark)[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]{color:#4f5fad;font-weight:600;letter-spacing:.5px}.dark[_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-title[_ngcontent-%COMP%]{color:var(--text-light);font-weight:600;letter-spacing:.5px}:not(.dark)[_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad,#7826b5);color:#fff;font-size:.75rem;font-weight:500;border-radius:9999px;padding:2px 8px;min-width:1.5rem;text-align:center;box-shadow:0 0 15px #4f5fad66}.dark[_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-badge[_ngcontent-%COMP%]{background:var(--primary-gradient);color:#fff;font-size:.75rem;font-weight:500;border-radius:9999px;padding:2px 8px;min-width:1.5rem;text-align:center;box-shadow:var(--glow-effect)}:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]{scrollbar-width:thin;scrollbar-color:#4f5fad #f0f4f8}:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f0f4f8}:not(.dark)[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:#4f5fad;border-radius:10px}.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]{scrollbar-width:thin;scrollbar-color:var(--accent-color) var(--medium-bg)}.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar, .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px}.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track, .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:var(--medium-bg)}.dark[_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .dark   [_nghost-%COMP%]   .futuristic-conversations-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:var(--accent-color);border-radius:10px}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:3px solid rgba(79,95,173,.1);border-top-color:#4f5fad;animation:_ngcontent-%COMP%_spin-light 1.5s linear infinite}@keyframes _ngcontent-%COMP%_spin-light{to{transform:rotate(360deg)}}:not(.dark)[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%]{color:#4f5fad;font-size:.875rem;letter-spacing:.5px}.dark[_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-circle[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:3px solid rgba(0,247,255,.1);border-top-color:var(--accent-color);animation:_ngcontent-%COMP%_spin-dark 1.5s linear infinite}@keyframes _ngcontent-%COMP%_spin-dark{to{transform:rotate(360deg)}}.dark[_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-loading-text[_ngcontent-%COMP%]{color:var(--accent-color);font-size:.875rem;letter-spacing:.5px}:not(.dark)[_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%]{margin:15px;padding:15px;background:rgba(255,107,105,.1);border-left:3px solid #ff6b69;border-radius:5px;display:flex;align-items:flex-start}:not(.dark)[_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%]{color:#ff6b69;font-size:1.25rem;margin-right:15px}:not(.dark)[_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%]{color:#ff6b69;font-size:.875rem;font-weight:600;margin-bottom:5px}:not(.dark)[_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%]{color:#6d6870;font-size:.8125rem;margin-bottom:10px}:not(.dark)[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]{background:rgba(255,107,105,.2);color:#ff6b69;border:none;border-radius:5px;padding:5px 10px;font-size:.75rem;cursor:pointer;transition:all var(--transition-fast)}:not(.dark)[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover{background:rgba(255,107,105,.3)}.dark[_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-container[_ngcontent-%COMP%]{margin:15px;padding:15px;background:rgba(255,0,0,.1);border-left:3px solid #ff3b30;border-radius:5px;display:flex;align-items:flex-start}.dark[_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-icon[_ngcontent-%COMP%]{color:#ff3b30;font-size:1.25rem;margin-right:15px}.dark[_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-title[_ngcontent-%COMP%]{color:#ff3b30;font-size:.875rem;font-weight:600;margin-bottom:5px}.dark[_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-error-message[_ngcontent-%COMP%]{color:var(--text-dim);font-size:.8125rem;margin-bottom:10px}.dark[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]{background:rgba(255,0,0,.2);color:#ff3b30;border:none;border-radius:5px;padding:5px 10px;font-size:.75rem;cursor:pointer;transition:all var(--transition-fast)}.dark[_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-retry-button[_ngcontent-%COMP%]:hover{background:rgba(255,0,0,.3)}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], :not(.dark)[_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%]{color:#6d6870}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%]{font-size:2.5rem;color:#4f5fad;margin-bottom:20px;opacity:.7}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:600;color:#4f5fad;margin-bottom:5px}:not(.dark)[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%]{color:#6d6870;font-size:.875rem;margin-bottom:20px}:not(.dark)[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad,#7826b5);color:#fff;border:none;border-radius:20px;padding:8px 16px;font-size:.875rem;cursor:pointer;display:flex;align-items:center;transition:all var(--transition-fast);box-shadow:0 2px 10px #0003}:not(.dark)[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 0 15px #4f5fad66}.dark[_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-state[_ngcontent-%COMP%], .dark[_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-no-results[_ngcontent-%COMP%]{color:var(--text-dim)}.dark[_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-icon[_ngcontent-%COMP%]{font-size:2.5rem;color:var(--accent-color);margin-bottom:20px;opacity:.7}.dark[_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-title[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:600;color:var(--text-light);margin-bottom:5px}.dark[_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-empty-text[_ngcontent-%COMP%]{color:var(--text-dim);font-size:.875rem;margin-bottom:20px}.dark[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]{background:var(--primary-gradient);color:#fff;border:none;border-radius:20px;padding:8px 16px;font-size:.875rem;cursor:pointer;display:flex;align-items:center;transition:all var(--transition-fast);box-shadow:0 2px 10px #0003}.dark[_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-start-button[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:var(--glow-effect)}:not(.dark)[_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 15px;cursor:pointer;transition:background-color var(--transition-fast);border-bottom:1px solid rgba(79,95,173,.05)}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover{background-color:#4f5fad0d}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%]{background-color:#4f5fad1a!important;border-left:3px solid #4f5fad}:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]{position:relative;width:48px;height:48px;border-radius:50%;overflow:hidden;margin-right:12px;flex-shrink:0;border:2px solid rgba(79,95,173,.3);box-shadow:0 0 15px #4f5fad33}:not(.dark)[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}:not(.dark)[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:2px;right:2px;width:10px;height:10px;border-radius:50%;background:#4caf50;border:2px solid #ffffff;box-shadow:0 0 5px #4caf50cc}.dark[_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversations[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0}.dark[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:12px 15px;cursor:pointer;transition:background-color var(--transition-fast);border-bottom:1px solid rgba(0,247,255,.05)}.dark[_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-conversation-item[_ngcontent-%COMP%]:hover{background-color:#00f7ff0d}.dark[_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-selected[_ngcontent-%COMP%]{background-color:#00f7ff1a!important;border-left:3px solid var(--accent-color)}.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]{position:relative;width:48px;height:48px;border-radius:50%;overflow:hidden;margin-right:12px;flex-shrink:0;border:2px solid rgba(0,247,255,.3);box-shadow:var(--glow-effect)}.dark[_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.dark[_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:2px;right:2px;width:10px;height:10px;border-radius:50%;background:#00ff9d;border:2px solid var(--medium-bg);box-shadow:0 0 5px #00ff9dcc}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%]{flex:1;min-width:0}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:baseline;margin-bottom:4px}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%]{font-size:.9375rem;font-weight:600;color:#4f5fad;margin:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%]{font-size:.75rem;color:#6d6870;white-space:nowrap;margin-left:8px}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}:not(.dark)[_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%]{font-size:.8125rem;color:#6d6870;margin:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}:not(.dark)[_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%]{color:#4f5fad;font-weight:500}:not(.dark)[_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad,#7826b5);color:#fff;font-size:.75rem;font-weight:600;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;margin-left:8px;box-shadow:0 0 15px #4f5fad66}.dark[_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-details[_ngcontent-%COMP%]{flex:1;min-width:0}.dark[_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:baseline;margin-bottom:4px}.dark[_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-name[_ngcontent-%COMP%]{font-size:.9375rem;font-weight:600;color:var(--text-light);margin:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.dark[_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-time[_ngcontent-%COMP%]{font-size:.75rem;color:var(--text-dim);white-space:nowrap;margin-left:8px}.dark[_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-preview[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.dark[_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-conversation-message[_ngcontent-%COMP%]{font-size:.8125rem;color:var(--text-dim);margin:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.dark[_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-you-prefix[_ngcontent-%COMP%]{color:#00f7ffb3;font-weight:500}.dark[_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-unread-badge[_ngcontent-%COMP%]{background:var(--accent-color);color:var(--dark-bg);font-size:.75rem;font-weight:600;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;margin-left:8px;box-shadow:var(--glow-effect)}:not(.dark)[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]{background-color:#f0f4f8;position:relative}:not(.dark)[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]:before, :not(.dark)   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background-image:linear-gradient(rgba(79,95,173,.03) 1px,transparent 1px),linear-gradient(90deg,rgba(79,95,173,.03) 1px,transparent 1px);background-size:20px 20px;pointer-events:none;z-index:0}.dark[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]{background-color:var(--dark-bg);position:relative}.dark[_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]:before, .dark   [_nghost-%COMP%]   .futuristic-main-area[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background-image:linear-gradient(rgba(0,247,255,.03) 1px,transparent 1px),linear-gradient(90deg,rgba(0,247,255,.03) 1px,transparent 1px);background-size:20px 20px;pointer-events:none;z-index:0}\"]\n      });\n    }\n  }\n  return MessagesListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}