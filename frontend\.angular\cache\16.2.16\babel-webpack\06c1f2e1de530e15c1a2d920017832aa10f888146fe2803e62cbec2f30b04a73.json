{"ast": null, "code": "import { makeUniqueId } from \"./makeUniqueId.js\";\nexport function stringifyForDisplay(value, space) {\n  if (space === void 0) {\n    space = 0;\n  }\n  var undefId = makeUniqueId(\"stringifyForDisplay\");\n  return JSON.stringify(value, function (key, value) {\n    return value === void 0 ? undefId : value;\n  }, space).split(JSON.stringify(undefId)).join(\"<undefined>\");\n}\n//# sourceMappingURL=stringifyForDisplay.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}