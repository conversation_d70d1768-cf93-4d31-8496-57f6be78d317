{"ast": null, "code": "import { inspect } from '../../jsutils/inspect.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { print } from '../../language/printer.mjs';\nimport { getNamedType, isInterfaceType, isLeafType, isListType, isNonNullType, isObjectType } from '../../type/definition.mjs';\nimport { sortValueNode } from '../../utilities/sortValueNode.mjs';\nimport { typeFromAST } from '../../utilities/typeFromAST.mjs';\nfunction reasonMessage(reason) {\n  if (Array.isArray(reason)) {\n    return reason.map(([responseName, subReason]) => `subfields \"${responseName}\" conflict because ` + reasonMessage(subReason)).join(' and ');\n  }\n  return reason;\n}\n/**\n * Overlapping fields can be merged\n *\n * A selection set is only valid if all fields (including spreading any\n * fragments) either correspond to distinct response names or can be merged\n * without ambiguity.\n *\n * See https://spec.graphql.org/draft/#sec-Field-Selection-Merging\n */\n\nexport function OverlappingFieldsCanBeMergedRule(context) {\n  // A memoization for when two fragments are compared \"between\" each other for\n  // conflicts. Two fragments may be compared many times, so memoizing this can\n  // dramatically improve the performance of this validator.\n  const comparedFragmentPairs = new PairSet(); // A cache for the \"field map\" and list of fragment names found in any given\n  // selection set. Selection sets may be asked for this information multiple\n  // times, so this improves the performance of this validator.\n\n  const cachedFieldsAndFragmentNames = new Map();\n  return {\n    SelectionSet(selectionSet) {\n      const conflicts = findConflictsWithinSelectionSet(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, context.getParentType(), selectionSet);\n      for (const [[responseName, reason], fields1, fields2] of conflicts) {\n        const reasonMsg = reasonMessage(reason);\n        context.reportError(new GraphQLError(`Fields \"${responseName}\" conflict because ${reasonMsg}. Use different aliases on the fields to fetch both if this was intentional.`, {\n          nodes: fields1.concat(fields2)\n        }));\n      }\n    }\n  };\n}\n\n/**\n * Algorithm:\n *\n * Conflicts occur when two fields exist in a query which will produce the same\n * response name, but represent differing values, thus creating a conflict.\n * The algorithm below finds all conflicts via making a series of comparisons\n * between fields. In order to compare as few fields as possible, this makes\n * a series of comparisons \"within\" sets of fields and \"between\" sets of fields.\n *\n * Given any selection set, a collection produces both a set of fields by\n * also including all inline fragments, as well as a list of fragments\n * referenced by fragment spreads.\n *\n * A) Each selection set represented in the document first compares \"within\" its\n * collected set of fields, finding any conflicts between every pair of\n * overlapping fields.\n * Note: This is the *only time* that a the fields \"within\" a set are compared\n * to each other. After this only fields \"between\" sets are compared.\n *\n * B) Also, if any fragment is referenced in a selection set, then a\n * comparison is made \"between\" the original set of fields and the\n * referenced fragment.\n *\n * C) Also, if multiple fragments are referenced, then comparisons\n * are made \"between\" each referenced fragment.\n *\n * D) When comparing \"between\" a set of fields and a referenced fragment, first\n * a comparison is made between each field in the original set of fields and\n * each field in the the referenced set of fields.\n *\n * E) Also, if any fragment is referenced in the referenced selection set,\n * then a comparison is made \"between\" the original set of fields and the\n * referenced fragment (recursively referring to step D).\n *\n * F) When comparing \"between\" two fragments, first a comparison is made between\n * each field in the first referenced set of fields and each field in the the\n * second referenced set of fields.\n *\n * G) Also, any fragments referenced by the first must be compared to the\n * second, and any fragments referenced by the second must be compared to the\n * first (recursively referring to step F).\n *\n * H) When comparing two fields, if both have selection sets, then a comparison\n * is made \"between\" both selection sets, first comparing the set of fields in\n * the first selection set with the set of fields in the second.\n *\n * I) Also, if any fragment is referenced in either selection set, then a\n * comparison is made \"between\" the other set of fields and the\n * referenced fragment.\n *\n * J) Also, if two fragments are referenced in both selection sets, then a\n * comparison is made \"between\" the two fragments.\n *\n */\n// Find all conflicts found \"within\" a selection set, including those found\n// via spreading in fragments. Called when visiting each SelectionSet in the\n// GraphQL Document.\nfunction findConflictsWithinSelectionSet(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, parentType, selectionSet) {\n  const conflicts = [];\n  const [fieldMap, fragmentNames] = getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType, selectionSet); // (A) Find find all conflicts \"within\" the fields of this selection set.\n  // Note: this is the *only place* `collectConflictsWithin` is called.\n\n  collectConflictsWithin(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, fieldMap);\n  if (fragmentNames.length !== 0) {\n    // (B) Then collect conflicts between these fields and those represented by\n    // each spread fragment name found.\n    for (let i = 0; i < fragmentNames.length; i++) {\n      collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, false, fieldMap, fragmentNames[i]); // (C) Then compare this fragment with all other fragments found in this\n      // selection set to collect conflicts between fragments spread together.\n      // This compares each item in the list of fragment names to every other\n      // item in that same list (except for itself).\n\n      for (let j = i + 1; j < fragmentNames.length; j++) {\n        collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, false, fragmentNames[i], fragmentNames[j]);\n      }\n    }\n  }\n  return conflicts;\n} // Collect all conflicts found between a set of fields and a fragment reference\n// including via spreading in any nested fragments.\n\nfunction collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap, fragmentName) {\n  const fragment = context.getFragment(fragmentName);\n  if (!fragment) {\n    return;\n  }\n  const [fieldMap2, referencedFragmentNames] = getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment); // Do not compare a fragment's fieldMap to itself.\n\n  if (fieldMap === fieldMap2) {\n    return;\n  } // (D) First collect any conflicts between the provided collection of fields\n  // and the collection of fields represented by the given fragment.\n\n  collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap, fieldMap2); // (E) Then collect any conflicts between the provided collection of fields\n  // and any fragment names found in the given fragment.\n\n  for (const referencedFragmentName of referencedFragmentNames) {\n    // Memoize so two fragments are not compared for conflicts more than once.\n    if (comparedFragmentPairs.has(referencedFragmentName, fragmentName, areMutuallyExclusive)) {\n      continue;\n    }\n    comparedFragmentPairs.add(referencedFragmentName, fragmentName, areMutuallyExclusive);\n    collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap, referencedFragmentName);\n  }\n} // Collect all conflicts found between two fragments, including via spreading in\n// any nested fragments.\n\nfunction collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fragmentName1, fragmentName2) {\n  // No need to compare a fragment to itself.\n  if (fragmentName1 === fragmentName2) {\n    return;\n  } // Memoize so two fragments are not compared for conflicts more than once.\n\n  if (comparedFragmentPairs.has(fragmentName1, fragmentName2, areMutuallyExclusive)) {\n    return;\n  }\n  comparedFragmentPairs.add(fragmentName1, fragmentName2, areMutuallyExclusive);\n  const fragment1 = context.getFragment(fragmentName1);\n  const fragment2 = context.getFragment(fragmentName2);\n  if (!fragment1 || !fragment2) {\n    return;\n  }\n  const [fieldMap1, referencedFragmentNames1] = getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment1);\n  const [fieldMap2, referencedFragmentNames2] = getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment2); // (F) First, collect all conflicts between these two collections of fields\n  // (not including any nested fragments).\n\n  collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap1, fieldMap2); // (G) Then collect conflicts between the first fragment and any nested\n  // fragments spread in the second fragment.\n\n  for (const referencedFragmentName2 of referencedFragmentNames2) {\n    collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fragmentName1, referencedFragmentName2);\n  } // (G) Then collect conflicts between the second fragment and any nested\n  // fragments spread in the first fragment.\n\n  for (const referencedFragmentName1 of referencedFragmentNames1) {\n    collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, referencedFragmentName1, fragmentName2);\n  }\n} // Find all conflicts found between two selection sets, including those found\n// via spreading in fragments. Called when determining if conflicts exist\n// between the sub-fields of two overlapping fields.\n\nfunction findConflictsBetweenSubSelectionSets(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, parentType1, selectionSet1, parentType2, selectionSet2) {\n  const conflicts = [];\n  const [fieldMap1, fragmentNames1] = getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType1, selectionSet1);\n  const [fieldMap2, fragmentNames2] = getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType2, selectionSet2); // (H) First, collect all conflicts between these two collections of field.\n\n  collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap1, fieldMap2); // (I) Then collect conflicts between the first collection of fields and\n  // those referenced by each fragment name associated with the second.\n\n  for (const fragmentName2 of fragmentNames2) {\n    collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap1, fragmentName2);\n  } // (I) Then collect conflicts between the second collection of fields and\n  // those referenced by each fragment name associated with the first.\n\n  for (const fragmentName1 of fragmentNames1) {\n    collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap2, fragmentName1);\n  } // (J) Also collect conflicts between any fragment names by the first and\n  // fragment names by the second. This compares each item in the first set of\n  // names to each item in the second set of names.\n\n  for (const fragmentName1 of fragmentNames1) {\n    for (const fragmentName2 of fragmentNames2) {\n      collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fragmentName1, fragmentName2);\n    }\n  }\n  return conflicts;\n} // Collect all Conflicts \"within\" one collection of fields.\n\nfunction collectConflictsWithin(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, fieldMap) {\n  // A field map is a keyed collection, where each key represents a response\n  // name and the value at that key is a list of all fields which provide that\n  // response name. For every response name, if there are multiple fields, they\n  // must be compared to find a potential conflict.\n  for (const [responseName, fields] of Object.entries(fieldMap)) {\n    // This compares every field in the list to every other field in this list\n    // (except to itself). If the list only has one item, nothing needs to\n    // be compared.\n    if (fields.length > 1) {\n      for (let i = 0; i < fields.length; i++) {\n        for (let j = i + 1; j < fields.length; j++) {\n          const conflict = findConflict(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, false,\n          // within one collection is never mutually exclusive\n          responseName, fields[i], fields[j]);\n          if (conflict) {\n            conflicts.push(conflict);\n          }\n        }\n      }\n    }\n  }\n} // Collect all Conflicts between two collections of fields. This is similar to,\n// but different from the `collectConflictsWithin` function above. This check\n// assumes that `collectConflictsWithin` has already been called on each\n// provided collection of fields. This is true because this validator traverses\n// each individual selection set.\n\nfunction collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, parentFieldsAreMutuallyExclusive, fieldMap1, fieldMap2) {\n  // A field map is a keyed collection, where each key represents a response\n  // name and the value at that key is a list of all fields which provide that\n  // response name. For any response name which appears in both provided field\n  // maps, each field from the first field map must be compared to every field\n  // in the second field map to find potential conflicts.\n  for (const [responseName, fields1] of Object.entries(fieldMap1)) {\n    const fields2 = fieldMap2[responseName];\n    if (fields2) {\n      for (const field1 of fields1) {\n        for (const field2 of fields2) {\n          const conflict = findConflict(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, parentFieldsAreMutuallyExclusive, responseName, field1, field2);\n          if (conflict) {\n            conflicts.push(conflict);\n          }\n        }\n      }\n    }\n  }\n} // Determines if there is a conflict between two particular fields, including\n// comparing their sub-fields.\n\nfunction findConflict(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, parentFieldsAreMutuallyExclusive, responseName, field1, field2) {\n  const [parentType1, node1, def1] = field1;\n  const [parentType2, node2, def2] = field2; // If it is known that two fields could not possibly apply at the same\n  // time, due to the parent types, then it is safe to permit them to diverge\n  // in aliased field or arguments used as they will not present any ambiguity\n  // by differing.\n  // It is known that two parent types could never overlap if they are\n  // different Object types. Interface or Union types might overlap - if not\n  // in the current state of the schema, then perhaps in some future version,\n  // thus may not safely diverge.\n\n  const areMutuallyExclusive = parentFieldsAreMutuallyExclusive || parentType1 !== parentType2 && isObjectType(parentType1) && isObjectType(parentType2);\n  if (!areMutuallyExclusive) {\n    // Two aliases must refer to the same field.\n    const name1 = node1.name.value;\n    const name2 = node2.name.value;\n    if (name1 !== name2) {\n      return [[responseName, `\"${name1}\" and \"${name2}\" are different fields`], [node1], [node2]];\n    } // Two field calls must have the same arguments.\n\n    if (!sameArguments(node1, node2)) {\n      return [[responseName, 'they have differing arguments'], [node1], [node2]];\n    }\n  } // The return type for each field.\n\n  const type1 = def1 === null || def1 === void 0 ? void 0 : def1.type;\n  const type2 = def2 === null || def2 === void 0 ? void 0 : def2.type;\n  if (type1 && type2 && doTypesConflict(type1, type2)) {\n    return [[responseName, `they return conflicting types \"${inspect(type1)}\" and \"${inspect(type2)}\"`], [node1], [node2]];\n  } // Collect and compare sub-fields. Use the same \"visited fragment names\" list\n  // for both collections so fields in a fragment reference are never\n  // compared to themselves.\n\n  const selectionSet1 = node1.selectionSet;\n  const selectionSet2 = node2.selectionSet;\n  if (selectionSet1 && selectionSet2) {\n    const conflicts = findConflictsBetweenSubSelectionSets(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, getNamedType(type1), selectionSet1, getNamedType(type2), selectionSet2);\n    return subfieldConflicts(conflicts, responseName, node1, node2);\n  }\n}\nfunction sameArguments(node1, node2) {\n  const args1 = node1.arguments;\n  const args2 = node2.arguments;\n  if (args1 === undefined || args1.length === 0) {\n    return args2 === undefined || args2.length === 0;\n  }\n  if (args2 === undefined || args2.length === 0) {\n    return false;\n  }\n  /* c8 ignore next */\n\n  if (args1.length !== args2.length) {\n    /* c8 ignore next */\n    return false;\n    /* c8 ignore next */\n  }\n\n  const values2 = new Map(args2.map(({\n    name,\n    value\n  }) => [name.value, value]));\n  return args1.every(arg1 => {\n    const value1 = arg1.value;\n    const value2 = values2.get(arg1.name.value);\n    if (value2 === undefined) {\n      return false;\n    }\n    return stringifyValue(value1) === stringifyValue(value2);\n  });\n}\nfunction stringifyValue(value) {\n  return print(sortValueNode(value));\n} // Two types conflict if both types could not apply to a value simultaneously.\n// Composite types are ignored as their individual field types will be compared\n// later recursively. However List and Non-Null types must match.\n\nfunction doTypesConflict(type1, type2) {\n  if (isListType(type1)) {\n    return isListType(type2) ? doTypesConflict(type1.ofType, type2.ofType) : true;\n  }\n  if (isListType(type2)) {\n    return true;\n  }\n  if (isNonNullType(type1)) {\n    return isNonNullType(type2) ? doTypesConflict(type1.ofType, type2.ofType) : true;\n  }\n  if (isNonNullType(type2)) {\n    return true;\n  }\n  if (isLeafType(type1) || isLeafType(type2)) {\n    return type1 !== type2;\n  }\n  return false;\n} // Given a selection set, return the collection of fields (a mapping of response\n// name to field nodes and definitions) as well as a list of fragment names\n// referenced via fragment spreads.\n\nfunction getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType, selectionSet) {\n  const cached = cachedFieldsAndFragmentNames.get(selectionSet);\n  if (cached) {\n    return cached;\n  }\n  const nodeAndDefs = Object.create(null);\n  const fragmentNames = Object.create(null);\n  _collectFieldsAndFragmentNames(context, parentType, selectionSet, nodeAndDefs, fragmentNames);\n  const result = [nodeAndDefs, Object.keys(fragmentNames)];\n  cachedFieldsAndFragmentNames.set(selectionSet, result);\n  return result;\n} // Given a reference to a fragment, return the represented collection of fields\n// as well as a list of nested fragment names referenced via fragment spreads.\n\nfunction getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment) {\n  // Short-circuit building a type from the node if possible.\n  const cached = cachedFieldsAndFragmentNames.get(fragment.selectionSet);\n  if (cached) {\n    return cached;\n  }\n  const fragmentType = typeFromAST(context.getSchema(), fragment.typeCondition);\n  return getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragmentType, fragment.selectionSet);\n}\nfunction _collectFieldsAndFragmentNames(context, parentType, selectionSet, nodeAndDefs, fragmentNames) {\n  for (const selection of selectionSet.selections) {\n    switch (selection.kind) {\n      case Kind.FIELD:\n        {\n          const fieldName = selection.name.value;\n          let fieldDef;\n          if (isObjectType(parentType) || isInterfaceType(parentType)) {\n            fieldDef = parentType.getFields()[fieldName];\n          }\n          const responseName = selection.alias ? selection.alias.value : fieldName;\n          if (!nodeAndDefs[responseName]) {\n            nodeAndDefs[responseName] = [];\n          }\n          nodeAndDefs[responseName].push([parentType, selection, fieldDef]);\n          break;\n        }\n      case Kind.FRAGMENT_SPREAD:\n        fragmentNames[selection.name.value] = true;\n        break;\n      case Kind.INLINE_FRAGMENT:\n        {\n          const typeCondition = selection.typeCondition;\n          const inlineFragmentType = typeCondition ? typeFromAST(context.getSchema(), typeCondition) : parentType;\n          _collectFieldsAndFragmentNames(context, inlineFragmentType, selection.selectionSet, nodeAndDefs, fragmentNames);\n          break;\n        }\n    }\n  }\n} // Given a series of Conflicts which occurred between two sub-fields, generate\n// a single Conflict.\n\nfunction subfieldConflicts(conflicts, responseName, node1, node2) {\n  if (conflicts.length > 0) {\n    return [[responseName, conflicts.map(([reason]) => reason)], [node1, ...conflicts.map(([, fields1]) => fields1).flat()], [node2, ...conflicts.map(([,, fields2]) => fields2).flat()]];\n  }\n}\n/**\n * A way to keep track of pairs of things when the ordering of the pair does not matter.\n */\n\nclass PairSet {\n  constructor() {\n    this._data = new Map();\n  }\n  has(a, b, areMutuallyExclusive) {\n    var _this$_data$get;\n    const [key1, key2] = a < b ? [a, b] : [b, a];\n    const result = (_this$_data$get = this._data.get(key1)) === null || _this$_data$get === void 0 ? void 0 : _this$_data$get.get(key2);\n    if (result === undefined) {\n      return false;\n    } // areMutuallyExclusive being false is a superset of being true, hence if\n    // we want to know if this PairSet \"has\" these two with no exclusivity,\n    // we have to ensure it was added as such.\n\n    return areMutuallyExclusive ? true : areMutuallyExclusive === result;\n  }\n  add(a, b, areMutuallyExclusive) {\n    const [key1, key2] = a < b ? [a, b] : [b, a];\n    const map = this._data.get(key1);\n    if (map === undefined) {\n      this._data.set(key1, new Map([[key2, areMutuallyExclusive]]));\n    } else {\n      map.set(key2, areMutuallyExclusive);\n    }\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}