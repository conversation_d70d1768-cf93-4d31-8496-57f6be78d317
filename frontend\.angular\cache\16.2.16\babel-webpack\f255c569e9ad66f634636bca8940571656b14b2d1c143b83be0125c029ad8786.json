{"ast": null, "code": "\"use strict\";\n\n/**\n * The default implementation for [`createUploadLink`]{@link createUploadLink}\n * `options.formDataAppendFile` that uses the standard\n * [`FormData.append`](https://developer.mozilla.org/en-US/docs/Web/API/FormData/append)\n * method.\n * @kind function\n * @name formDataAppendFile\n * @type {FormDataFileAppender}\n * @param {FormData} formData [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData) instance to append the specified file to.\n * @param {string} fieldName Field name for the file.\n * @param {*} file File to append.\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { formDataAppendFile } from \"apollo-upload-client\";\n * ```\n *\n * ```js\n * import formDataAppendFile from \"apollo-upload-client/public/formDataAppendFile.js\";\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { formDataAppendFile } = require(\"apollo-upload-client\");\n * ```\n *\n * ```js\n * const formDataAppendFile = require(\"apollo-upload-client/public/formDataAppendFile.js\");\n * ```\n */\nmodule.exports = function formDataAppendFile(formData, fieldName, file) {\n  formData.append(fieldName, file, file.name);\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}