{"ast": null, "code": "import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link roundToNearestHours} function options.\n */\n\n/**\n * @name roundToNearestHours\n * @category Hour Helpers\n * @summary Rounds the given date to the nearest hour\n *\n * @description\n * Rounds the given date to the nearest hour (or number of hours).\n * Rounds up when the given date is exactly between the nearest round hours.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to round\n * @param options - An object with options.\n *\n * @returns The new date rounded to the closest hour\n *\n * @example\n * // Round 10 July 2014 12:34:56 to nearest hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56))\n * //=> Thu Jul 10 2014 13:00:00\n *\n * @example\n * // Round 10 July 2014 12:34:56 to nearest half hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56), { nearestTo: 6 })\n * //=> Thu Jul 10 2014 12:00:00\n *\n * @example\n * // Round 10 July 2014 12:34:56 to nearest half hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56), { nearestTo: 8 })\n * //=> Thu Jul 10 2014 16:00:00\n *\n * @example\n * // Floor (rounds down) 10 July 2014 12:34:56 to nearest hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 1, 23, 45), { roundingMethod: 'ceil' })\n * //=> Thu Jul 10 2014 02:00:00\n *\n * @example\n * // Ceil (rounds up) 10 July 2014 12:34:56 to nearest quarter hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56), { roundingMethod: 'floor', nearestTo: 8 })\n * //=> Thu Jul 10 2014 08:00:00\n */\nexport function roundToNearestHours(date, options) {\n  const nearestTo = options?.nearestTo ?? 1;\n  if (nearestTo < 1 || nearestTo > 12) return constructFrom(options?.in || date, NaN);\n  const date_ = toDate(date, options?.in);\n  const fractionalMinutes = date_.getMinutes() / 60;\n  const fractionalSeconds = date_.getSeconds() / 60 / 60;\n  const fractionalMilliseconds = date_.getMilliseconds() / 1000 / 60 / 60;\n  const hours = date_.getHours() + fractionalMinutes + fractionalSeconds + fractionalMilliseconds;\n  const method = options?.roundingMethod ?? \"round\";\n  const roundingMethod = getRoundingMethod(method);\n  const roundedHours = roundingMethod(hours / nearestTo) * nearestTo;\n  date_.setHours(roundedHours, 0, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default roundToNearestHours;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}