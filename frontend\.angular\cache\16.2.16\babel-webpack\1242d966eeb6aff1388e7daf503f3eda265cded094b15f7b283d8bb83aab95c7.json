{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { invariant } from '../jsutils/invariant.mjs';\nimport { DirectiveLocation } from '../language/directiveLocation.mjs';\nimport { print } from '../language/printer.mjs';\nimport { astFromValue } from '../utilities/astFromValue.mjs';\nimport { GraphQLEnumType, GraphQLList, GraphQLNonNull, GraphQLObjectType, isAbstractType, isEnumType, isInputObjectType, isInterfaceType, isListType, isNonNullType, isObjectType, isScalarType, isUnionType } from './definition.mjs';\nimport { GraphQLBoolean, GraphQLString } from './scalars.mjs';\nexport const __Schema = new GraphQLObjectType({\n  name: '__Schema',\n  description: 'A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.',\n  fields: () => ({\n    description: {\n      type: GraphQLString,\n      resolve: schema => schema.description\n    },\n    types: {\n      description: 'A list of all types supported by this server.',\n      type: new GraphQLNonNull(new GraphQLList(new GraphQLNonNull(__Type))),\n      resolve(schema) {\n        return Object.values(schema.getTypeMap());\n      }\n    },\n    queryType: {\n      description: 'The type that query operations will be rooted at.',\n      type: new GraphQLNonNull(__Type),\n      resolve: schema => schema.getQueryType()\n    },\n    mutationType: {\n      description: 'If this server supports mutation, the type that mutation operations will be rooted at.',\n      type: __Type,\n      resolve: schema => schema.getMutationType()\n    },\n    subscriptionType: {\n      description: 'If this server support subscription, the type that subscription operations will be rooted at.',\n      type: __Type,\n      resolve: schema => schema.getSubscriptionType()\n    },\n    directives: {\n      description: 'A list of all directives supported by this server.',\n      type: new GraphQLNonNull(new GraphQLList(new GraphQLNonNull(__Directive))),\n      resolve: schema => schema.getDirectives()\n    }\n  })\n});\nexport const __Directive = new GraphQLObjectType({\n  name: '__Directive',\n  description: \"A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.\\n\\nIn some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.\",\n  fields: () => ({\n    name: {\n      type: new GraphQLNonNull(GraphQLString),\n      resolve: directive => directive.name\n    },\n    description: {\n      type: GraphQLString,\n      resolve: directive => directive.description\n    },\n    isRepeatable: {\n      type: new GraphQLNonNull(GraphQLBoolean),\n      resolve: directive => directive.isRepeatable\n    },\n    locations: {\n      type: new GraphQLNonNull(new GraphQLList(new GraphQLNonNull(__DirectiveLocation))),\n      resolve: directive => directive.locations\n    },\n    args: {\n      type: new GraphQLNonNull(new GraphQLList(new GraphQLNonNull(__InputValue))),\n      args: {\n        includeDeprecated: {\n          type: GraphQLBoolean,\n          defaultValue: false\n        }\n      },\n      resolve(field, {\n        includeDeprecated\n      }) {\n        return includeDeprecated ? field.args : field.args.filter(arg => arg.deprecationReason == null);\n      }\n    }\n  })\n});\nexport const __DirectiveLocation = new GraphQLEnumType({\n  name: '__DirectiveLocation',\n  description: 'A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.',\n  values: {\n    QUERY: {\n      value: DirectiveLocation.QUERY,\n      description: 'Location adjacent to a query operation.'\n    },\n    MUTATION: {\n      value: DirectiveLocation.MUTATION,\n      description: 'Location adjacent to a mutation operation.'\n    },\n    SUBSCRIPTION: {\n      value: DirectiveLocation.SUBSCRIPTION,\n      description: 'Location adjacent to a subscription operation.'\n    },\n    FIELD: {\n      value: DirectiveLocation.FIELD,\n      description: 'Location adjacent to a field.'\n    },\n    FRAGMENT_DEFINITION: {\n      value: DirectiveLocation.FRAGMENT_DEFINITION,\n      description: 'Location adjacent to a fragment definition.'\n    },\n    FRAGMENT_SPREAD: {\n      value: DirectiveLocation.FRAGMENT_SPREAD,\n      description: 'Location adjacent to a fragment spread.'\n    },\n    INLINE_FRAGMENT: {\n      value: DirectiveLocation.INLINE_FRAGMENT,\n      description: 'Location adjacent to an inline fragment.'\n    },\n    VARIABLE_DEFINITION: {\n      value: DirectiveLocation.VARIABLE_DEFINITION,\n      description: 'Location adjacent to a variable definition.'\n    },\n    SCHEMA: {\n      value: DirectiveLocation.SCHEMA,\n      description: 'Location adjacent to a schema definition.'\n    },\n    SCALAR: {\n      value: DirectiveLocation.SCALAR,\n      description: 'Location adjacent to a scalar definition.'\n    },\n    OBJECT: {\n      value: DirectiveLocation.OBJECT,\n      description: 'Location adjacent to an object type definition.'\n    },\n    FIELD_DEFINITION: {\n      value: DirectiveLocation.FIELD_DEFINITION,\n      description: 'Location adjacent to a field definition.'\n    },\n    ARGUMENT_DEFINITION: {\n      value: DirectiveLocation.ARGUMENT_DEFINITION,\n      description: 'Location adjacent to an argument definition.'\n    },\n    INTERFACE: {\n      value: DirectiveLocation.INTERFACE,\n      description: 'Location adjacent to an interface definition.'\n    },\n    UNION: {\n      value: DirectiveLocation.UNION,\n      description: 'Location adjacent to a union definition.'\n    },\n    ENUM: {\n      value: DirectiveLocation.ENUM,\n      description: 'Location adjacent to an enum definition.'\n    },\n    ENUM_VALUE: {\n      value: DirectiveLocation.ENUM_VALUE,\n      description: 'Location adjacent to an enum value definition.'\n    },\n    INPUT_OBJECT: {\n      value: DirectiveLocation.INPUT_OBJECT,\n      description: 'Location adjacent to an input object type definition.'\n    },\n    INPUT_FIELD_DEFINITION: {\n      value: DirectiveLocation.INPUT_FIELD_DEFINITION,\n      description: 'Location adjacent to an input object field definition.'\n    }\n  }\n});\nexport const __Type = new GraphQLObjectType({\n  name: '__Type',\n  description: 'The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.\\n\\nDepending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByURL`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.',\n  fields: () => ({\n    kind: {\n      type: new GraphQLNonNull(__TypeKind),\n      resolve(type) {\n        if (isScalarType(type)) {\n          return TypeKind.SCALAR;\n        }\n        if (isObjectType(type)) {\n          return TypeKind.OBJECT;\n        }\n        if (isInterfaceType(type)) {\n          return TypeKind.INTERFACE;\n        }\n        if (isUnionType(type)) {\n          return TypeKind.UNION;\n        }\n        if (isEnumType(type)) {\n          return TypeKind.ENUM;\n        }\n        if (isInputObjectType(type)) {\n          return TypeKind.INPUT_OBJECT;\n        }\n        if (isListType(type)) {\n          return TypeKind.LIST;\n        }\n        if (isNonNullType(type)) {\n          return TypeKind.NON_NULL;\n        }\n        /* c8 ignore next 3 */\n        // Not reachable, all possible types have been considered)\n\n        false || invariant(false, `Unexpected type: \"${inspect(type)}\".`);\n      }\n    },\n    name: {\n      type: GraphQLString,\n      resolve: type => 'name' in type ? type.name : undefined\n    },\n    description: {\n      type: GraphQLString,\n      resolve: (type // FIXME: add test case\n      ) => /* c8 ignore next */\n      'description' in type ? type.description : undefined\n    },\n    specifiedByURL: {\n      type: GraphQLString,\n      resolve: obj => 'specifiedByURL' in obj ? obj.specifiedByURL : undefined\n    },\n    fields: {\n      type: new GraphQLList(new GraphQLNonNull(__Field)),\n      args: {\n        includeDeprecated: {\n          type: GraphQLBoolean,\n          defaultValue: false\n        }\n      },\n      resolve(type, {\n        includeDeprecated\n      }) {\n        if (isObjectType(type) || isInterfaceType(type)) {\n          const fields = Object.values(type.getFields());\n          return includeDeprecated ? fields : fields.filter(field => field.deprecationReason == null);\n        }\n      }\n    },\n    interfaces: {\n      type: new GraphQLList(new GraphQLNonNull(__Type)),\n      resolve(type) {\n        if (isObjectType(type) || isInterfaceType(type)) {\n          return type.getInterfaces();\n        }\n      }\n    },\n    possibleTypes: {\n      type: new GraphQLList(new GraphQLNonNull(__Type)),\n      resolve(type, _args, _context, {\n        schema\n      }) {\n        if (isAbstractType(type)) {\n          return schema.getPossibleTypes(type);\n        }\n      }\n    },\n    enumValues: {\n      type: new GraphQLList(new GraphQLNonNull(__EnumValue)),\n      args: {\n        includeDeprecated: {\n          type: GraphQLBoolean,\n          defaultValue: false\n        }\n      },\n      resolve(type, {\n        includeDeprecated\n      }) {\n        if (isEnumType(type)) {\n          const values = type.getValues();\n          return includeDeprecated ? values : values.filter(field => field.deprecationReason == null);\n        }\n      }\n    },\n    inputFields: {\n      type: new GraphQLList(new GraphQLNonNull(__InputValue)),\n      args: {\n        includeDeprecated: {\n          type: GraphQLBoolean,\n          defaultValue: false\n        }\n      },\n      resolve(type, {\n        includeDeprecated\n      }) {\n        if (isInputObjectType(type)) {\n          const values = Object.values(type.getFields());\n          return includeDeprecated ? values : values.filter(field => field.deprecationReason == null);\n        }\n      }\n    },\n    ofType: {\n      type: __Type,\n      resolve: type => 'ofType' in type ? type.ofType : undefined\n    }\n  })\n});\nexport const __Field = new GraphQLObjectType({\n  name: '__Field',\n  description: 'Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.',\n  fields: () => ({\n    name: {\n      type: new GraphQLNonNull(GraphQLString),\n      resolve: field => field.name\n    },\n    description: {\n      type: GraphQLString,\n      resolve: field => field.description\n    },\n    args: {\n      type: new GraphQLNonNull(new GraphQLList(new GraphQLNonNull(__InputValue))),\n      args: {\n        includeDeprecated: {\n          type: GraphQLBoolean,\n          defaultValue: false\n        }\n      },\n      resolve(field, {\n        includeDeprecated\n      }) {\n        return includeDeprecated ? field.args : field.args.filter(arg => arg.deprecationReason == null);\n      }\n    },\n    type: {\n      type: new GraphQLNonNull(__Type),\n      resolve: field => field.type\n    },\n    isDeprecated: {\n      type: new GraphQLNonNull(GraphQLBoolean),\n      resolve: field => field.deprecationReason != null\n    },\n    deprecationReason: {\n      type: GraphQLString,\n      resolve: field => field.deprecationReason\n    }\n  })\n});\nexport const __InputValue = new GraphQLObjectType({\n  name: '__InputValue',\n  description: 'Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.',\n  fields: () => ({\n    name: {\n      type: new GraphQLNonNull(GraphQLString),\n      resolve: inputValue => inputValue.name\n    },\n    description: {\n      type: GraphQLString,\n      resolve: inputValue => inputValue.description\n    },\n    type: {\n      type: new GraphQLNonNull(__Type),\n      resolve: inputValue => inputValue.type\n    },\n    defaultValue: {\n      type: GraphQLString,\n      description: 'A GraphQL-formatted string representing the default value for this input value.',\n      resolve(inputValue) {\n        const {\n          type,\n          defaultValue\n        } = inputValue;\n        const valueAST = astFromValue(defaultValue, type);\n        return valueAST ? print(valueAST) : null;\n      }\n    },\n    isDeprecated: {\n      type: new GraphQLNonNull(GraphQLBoolean),\n      resolve: field => field.deprecationReason != null\n    },\n    deprecationReason: {\n      type: GraphQLString,\n      resolve: obj => obj.deprecationReason\n    }\n  })\n});\nexport const __EnumValue = new GraphQLObjectType({\n  name: '__EnumValue',\n  description: 'One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.',\n  fields: () => ({\n    name: {\n      type: new GraphQLNonNull(GraphQLString),\n      resolve: enumValue => enumValue.name\n    },\n    description: {\n      type: GraphQLString,\n      resolve: enumValue => enumValue.description\n    },\n    isDeprecated: {\n      type: new GraphQLNonNull(GraphQLBoolean),\n      resolve: enumValue => enumValue.deprecationReason != null\n    },\n    deprecationReason: {\n      type: GraphQLString,\n      resolve: enumValue => enumValue.deprecationReason\n    }\n  })\n});\nvar TypeKind = /*#__PURE__*/function (TypeKind) {\n  TypeKind['SCALAR'] = 'SCALAR';\n  TypeKind['OBJECT'] = 'OBJECT';\n  TypeKind['INTERFACE'] = 'INTERFACE';\n  TypeKind['UNION'] = 'UNION';\n  TypeKind['ENUM'] = 'ENUM';\n  TypeKind['INPUT_OBJECT'] = 'INPUT_OBJECT';\n  TypeKind['LIST'] = 'LIST';\n  TypeKind['NON_NULL'] = 'NON_NULL';\n  return TypeKind;\n}(TypeKind || {});\nexport { TypeKind };\nexport const __TypeKind = new GraphQLEnumType({\n  name: '__TypeKind',\n  description: 'An enum describing what kind of type a given `__Type` is.',\n  values: {\n    SCALAR: {\n      value: TypeKind.SCALAR,\n      description: 'Indicates this type is a scalar.'\n    },\n    OBJECT: {\n      value: TypeKind.OBJECT,\n      description: 'Indicates this type is an object. `fields` and `interfaces` are valid fields.'\n    },\n    INTERFACE: {\n      value: TypeKind.INTERFACE,\n      description: 'Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields.'\n    },\n    UNION: {\n      value: TypeKind.UNION,\n      description: 'Indicates this type is a union. `possibleTypes` is a valid field.'\n    },\n    ENUM: {\n      value: TypeKind.ENUM,\n      description: 'Indicates this type is an enum. `enumValues` is a valid field.'\n    },\n    INPUT_OBJECT: {\n      value: TypeKind.INPUT_OBJECT,\n      description: 'Indicates this type is an input object. `inputFields` is a valid field.'\n    },\n    LIST: {\n      value: TypeKind.LIST,\n      description: 'Indicates this type is a list. `ofType` is a valid field.'\n    },\n    NON_NULL: {\n      value: TypeKind.NON_NULL,\n      description: 'Indicates this type is a non-null. `ofType` is a valid field.'\n    }\n  }\n});\n/**\n * Note that these are GraphQLField and not GraphQLFieldConfig,\n * so the format for args is different.\n */\n\nexport const SchemaMetaFieldDef = {\n  name: '__schema',\n  type: new GraphQLNonNull(__Schema),\n  description: 'Access the current type schema of this server.',\n  args: [],\n  resolve: (_source, _args, _context, {\n    schema\n  }) => schema,\n  deprecationReason: undefined,\n  extensions: Object.create(null),\n  astNode: undefined\n};\nexport const TypeMetaFieldDef = {\n  name: '__type',\n  type: __Type,\n  description: 'Request the type information of a single type.',\n  args: [{\n    name: 'name',\n    description: undefined,\n    type: new GraphQLNonNull(GraphQLString),\n    defaultValue: undefined,\n    deprecationReason: undefined,\n    extensions: Object.create(null),\n    astNode: undefined\n  }],\n  resolve: (_source, {\n    name\n  }, _context, {\n    schema\n  }) => schema.getType(name),\n  deprecationReason: undefined,\n  extensions: Object.create(null),\n  astNode: undefined\n};\nexport const TypeNameMetaFieldDef = {\n  name: '__typename',\n  type: new GraphQLNonNull(GraphQLString),\n  description: 'The name of the current Object type at runtime.',\n  args: [],\n  resolve: (_source, _args, _context, {\n    parentType\n  }) => parentType.name,\n  deprecationReason: undefined,\n  extensions: Object.create(null),\n  astNode: undefined\n};\nexport const introspectionTypes = Object.freeze([__Schema, __Directive, __DirectiveLocation, __Type, __Field, __InputValue, __EnumValue, __TypeKind]);\nexport function isIntrospectionType(type) {\n  return introspectionTypes.some(({\n    name\n  }) => type.name === name);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}