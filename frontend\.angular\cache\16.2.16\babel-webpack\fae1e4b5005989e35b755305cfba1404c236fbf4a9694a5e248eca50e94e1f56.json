{"ast": null, "code": "import { naturalCompare } from '../jsutils/naturalCompare.mjs';\nimport { Kind } from '../language/kinds.mjs';\n/**\n * Sort ValueNode.\n *\n * This function returns a sorted copy of the given ValueNode.\n *\n * @internal\n */\n\nexport function sortValueNode(valueNode) {\n  switch (valueNode.kind) {\n    case Kind.OBJECT:\n      return {\n        ...valueNode,\n        fields: sortFields(valueNode.fields)\n      };\n    case Kind.LIST:\n      return {\n        ...valueNode,\n        values: valueNode.values.map(sortValueNode)\n      };\n    case Kind.INT:\n    case Kind.FLOAT:\n    case Kind.STRING:\n    case Kind.BOOLEAN:\n    case Kind.NULL:\n    case Kind.ENUM:\n    case Kind.VARIABLE:\n      return valueNode;\n  }\n}\nfunction sortFields(fields) {\n  return fields.map(fieldNode => ({\n    ...fieldNode,\n    value: sortValueNode(fieldNode.value)\n  })).sort((fieldA, fieldB) => naturalCompare(fieldA.name.value, fieldB.name.value));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}