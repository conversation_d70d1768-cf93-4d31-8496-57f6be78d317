{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule, CSP_NONCE, Injectable, Optional, Inject } from '@angular/core';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\nlet LayoutModule = /*#__PURE__*/(() => {\n  class LayoutModule {\n    static {\n      this.ɵfac = function LayoutModule_Factory(t) {\n        return new (t || LayoutModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: LayoutModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n    }\n  }\n  return LayoutModule;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = /*#__PURE__*/new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nlet MediaMatcher = /*#__PURE__*/(() => {\n  class MediaMatcher {\n    constructor(_platform, _nonce) {\n      this._platform = _platform;\n      this._nonce = _nonce;\n      this._matchMedia = this._platform.isBrowser && window.matchMedia ?\n      // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n      // call it from a different scope.\n      window.matchMedia.bind(window) : noopMatchMedia;\n    }\n    /**\n     * Evaluates the given media query and returns the native MediaQueryList from which results\n     * can be retrieved.\n     * Confirms the layout engine will trigger for the selector query provided and returns the\n     * MediaQueryList for the query provided.\n     */\n    matchMedia(query) {\n      if (this._platform.WEBKIT || this._platform.BLINK) {\n        createEmptyStyleRule(query, this._nonce);\n      }\n      return this._matchMedia(query);\n    }\n    static {\n      this.ɵfac = function MediaMatcher_Factory(t) {\n        return new (t || MediaMatcher)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(CSP_NONCE, 8));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: MediaMatcher,\n        factory: MediaMatcher.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return MediaMatcher;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query, nonce) {\n  if (mediaQueriesForWebkitCompatibility.has(query)) {\n    return;\n  }\n  try {\n    if (!mediaQueryStyleNode) {\n      mediaQueryStyleNode = document.createElement('style');\n      if (nonce) {\n        mediaQueryStyleNode.nonce = nonce;\n      }\n      mediaQueryStyleNode.setAttribute('type', 'text/css');\n      document.head.appendChild(mediaQueryStyleNode);\n    }\n    if (mediaQueryStyleNode.sheet) {\n      mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n      mediaQueriesForWebkitCompatibility.add(query);\n    }\n  } catch (e) {\n    console.error(e);\n  }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n  // Use `as any` here to avoid adding additional necessary properties for\n  // the noop matcher.\n  return {\n    matches: query === 'all' || query === '',\n    media: query,\n    addListener: () => {},\n    removeListener: () => {}\n  };\n}\n\n/** Utility for checking the matching state of @media queries. */\nlet BreakpointObserver = /*#__PURE__*/(() => {\n  class BreakpointObserver {\n    constructor(_mediaMatcher, _zone) {\n      this._mediaMatcher = _mediaMatcher;\n      this._zone = _zone;\n      /**  A map of all media queries currently being listened for. */\n      this._queries = new Map();\n      /** A subject for all other observables to takeUntil based on. */\n      this._destroySubject = new Subject();\n    }\n    /** Completes the active subject, signalling to all other observables to complete. */\n    ngOnDestroy() {\n      this._destroySubject.next();\n      this._destroySubject.complete();\n    }\n    /**\n     * Whether one or more media queries match the current viewport size.\n     * @param value One or more media queries to check.\n     * @returns Whether any of the media queries match.\n     */\n    isMatched(value) {\n      const queries = splitQueries(coerceArray(value));\n      return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n    }\n    /**\n     * Gets an observable of results for the given queries that will emit new results for any changes\n     * in matching of the given queries.\n     * @param value One or more media queries to check.\n     * @returns A stream of matches for the given queries.\n     */\n    observe(value) {\n      const queries = splitQueries(coerceArray(value));\n      const observables = queries.map(query => this._registerQuery(query).observable);\n      let stateObservable = combineLatest(observables);\n      // Emit the first state immediately, and then debounce the subsequent emissions.\n      stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n      return stateObservable.pipe(map(breakpointStates => {\n        const response = {\n          matches: false,\n          breakpoints: {}\n        };\n        breakpointStates.forEach(({\n          matches,\n          query\n        }) => {\n          response.matches = response.matches || matches;\n          response.breakpoints[query] = matches;\n        });\n        return response;\n      }));\n    }\n    /** Registers a specific query to be listened for. */\n    _registerQuery(query) {\n      // Only set up a new MediaQueryList if it is not already being listened for.\n      if (this._queries.has(query)) {\n        return this._queries.get(query);\n      }\n      const mql = this._mediaMatcher.matchMedia(query);\n      // Create callback for match changes and add it is as a listener.\n      const queryObservable = new Observable(observer => {\n        // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n        // back into the zone because matchMedia is only included in Zone.js by loading the\n        // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n        // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n        // patches it.\n        const handler = e => this._zone.run(() => observer.next(e));\n        mql.addListener(handler);\n        return () => {\n          mql.removeListener(handler);\n        };\n      }).pipe(startWith(mql), map(({\n        matches\n      }) => ({\n        query,\n        matches\n      })), takeUntil(this._destroySubject));\n      // Add the MediaQueryList to the set of queries.\n      const output = {\n        observable: queryObservable,\n        mql\n      };\n      this._queries.set(query, output);\n      return output;\n    }\n    static {\n      this.ɵfac = function BreakpointObserver_Factory(t) {\n        return new (t || BreakpointObserver)(i0.ɵɵinject(MediaMatcher), i0.ɵɵinject(i0.NgZone));\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: BreakpointObserver,\n        factory: BreakpointObserver.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return BreakpointObserver;\n})();\n/*#__PURE__*/(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n  return queries.map(query => query.split(',')).reduce((a1, a2) => a1.concat(a2)).map(query => query.trim());\n}\n\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\nconst Breakpoints = {\n  XSmall: '(max-width: 599.98px)',\n  Small: '(min-width: 600px) and (max-width: 959.98px)',\n  Medium: '(min-width: 960px) and (max-width: 1279.98px)',\n  Large: '(min-width: 1280px) and (max-width: 1919.98px)',\n  XLarge: '(min-width: 1920px)',\n  Handset: '(max-width: 599.98px) and (orientation: portrait), ' + '(max-width: 959.98px) and (orientation: landscape)',\n  Tablet: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), ' + '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  Web: '(min-width: 840px) and (orientation: portrait), ' + '(min-width: 1280px) and (orientation: landscape)',\n  HandsetPortrait: '(max-width: 599.98px) and (orientation: portrait)',\n  TabletPortrait: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)',\n  WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n  HandsetLandscape: '(max-width: 959.98px) and (orientation: landscape)',\n  TabletLandscape: '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  WebLandscape: '(min-width: 1280px) and (orientation: landscape)'\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BreakpointObserver, Breakpoints, LayoutModule, MediaMatcher };\n//# sourceMappingURL=layout.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}