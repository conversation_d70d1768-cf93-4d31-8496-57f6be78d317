{"ast": null, "code": "import { syntaxError } from '../error/syntaxError.mjs';\nimport { Token } from './ast.mjs';\nimport { dedentBlockStringLines } from './blockString.mjs';\nimport { isDigit, isNameContinue, isNameStart } from './characterClasses.mjs';\nimport { TokenKind } from './tokenKind.mjs';\n/**\n * Given a Source object, creates a Lexer for that source.\n * A Lexer is a stateful stream generator in that every time\n * it is advanced, it returns the next token in the Source. Assuming the\n * source lexes, the final Token emitted by the lexer will be of kind\n * EOF, after which the lexer will repeatedly return the same EOF token\n * whenever called.\n */\n\nexport class Lexer {\n  /**\n   * The previously focused non-ignored token.\n   */\n\n  /**\n   * The currently focused non-ignored token.\n   */\n\n  /**\n   * The (1-indexed) line containing the current token.\n   */\n\n  /**\n   * The character offset at which the current line begins.\n   */\n  constructor(source) {\n    const startOfFileToken = new Token(TokenKind.SOF, 0, 0, 0, 0);\n    this.source = source;\n    this.lastToken = startOfFileToken;\n    this.token = startOfFileToken;\n    this.line = 1;\n    this.lineStart = 0;\n  }\n  get [Symbol.toStringTag]() {\n    return 'Lexer';\n  }\n  /**\n   * Advances the token stream to the next non-ignored token.\n   */\n\n  advance() {\n    this.lastToken = this.token;\n    const token = this.token = this.lookahead();\n    return token;\n  }\n  /**\n   * Looks ahead and returns the next non-ignored token, but does not change\n   * the state of Lexer.\n   */\n\n  lookahead() {\n    let token = this.token;\n    if (token.kind !== TokenKind.EOF) {\n      do {\n        if (token.next) {\n          token = token.next;\n        } else {\n          // Read the next token and form a link in the token linked-list.\n          const nextToken = readNextToken(this, token.end); // @ts-expect-error next is only mutable during parsing.\n\n          token.next = nextToken; // @ts-expect-error prev is only mutable during parsing.\n\n          nextToken.prev = token;\n          token = nextToken;\n        }\n      } while (token.kind === TokenKind.COMMENT);\n    }\n    return token;\n  }\n}\n/**\n * @internal\n */\n\nexport function isPunctuatorTokenKind(kind) {\n  return kind === TokenKind.BANG || kind === TokenKind.DOLLAR || kind === TokenKind.AMP || kind === TokenKind.PAREN_L || kind === TokenKind.PAREN_R || kind === TokenKind.SPREAD || kind === TokenKind.COLON || kind === TokenKind.EQUALS || kind === TokenKind.AT || kind === TokenKind.BRACKET_L || kind === TokenKind.BRACKET_R || kind === TokenKind.BRACE_L || kind === TokenKind.PIPE || kind === TokenKind.BRACE_R;\n}\n/**\n * A Unicode scalar value is any Unicode code point except surrogate code\n * points. In other words, the inclusive ranges of values 0x0000 to 0xD7FF and\n * 0xE000 to 0x10FFFF.\n *\n * SourceCharacter ::\n *   - \"Any Unicode scalar value\"\n */\n\nfunction isUnicodeScalarValue(code) {\n  return code >= 0x0000 && code <= 0xd7ff || code >= 0xe000 && code <= 0x10ffff;\n}\n/**\n * The GraphQL specification defines source text as a sequence of unicode scalar\n * values (which Unicode defines to exclude surrogate code points). However\n * JavaScript defines strings as a sequence of UTF-16 code units which may\n * include surrogates. A surrogate pair is a valid source character as it\n * encodes a supplementary code point (above U+FFFF), but unpaired surrogate\n * code points are not valid source characters.\n */\n\nfunction isSupplementaryCodePoint(body, location) {\n  return isLeadingSurrogate(body.charCodeAt(location)) && isTrailingSurrogate(body.charCodeAt(location + 1));\n}\nfunction isLeadingSurrogate(code) {\n  return code >= 0xd800 && code <= 0xdbff;\n}\nfunction isTrailingSurrogate(code) {\n  return code >= 0xdc00 && code <= 0xdfff;\n}\n/**\n * Prints the code point (or end of file reference) at a given location in a\n * source for use in error messages.\n *\n * Printable ASCII is printed quoted, while other points are printed in Unicode\n * code point form (ie. U+1234).\n */\n\nfunction printCodePointAt(lexer, location) {\n  const code = lexer.source.body.codePointAt(location);\n  if (code === undefined) {\n    return TokenKind.EOF;\n  } else if (code >= 0x0020 && code <= 0x007e) {\n    // Printable ASCII\n    const char = String.fromCodePoint(code);\n    return char === '\"' ? \"'\\\"'\" : `\"${char}\"`;\n  } // Unicode code point\n\n  return 'U+' + code.toString(16).toUpperCase().padStart(4, '0');\n}\n/**\n * Create a token with line and column location information.\n */\n\nfunction createToken(lexer, kind, start, end, value) {\n  const line = lexer.line;\n  const col = 1 + start - lexer.lineStart;\n  return new Token(kind, start, end, line, col, value);\n}\n/**\n * Gets the next token from the source starting at the given position.\n *\n * This skips over whitespace until it finds the next lexable token, then lexes\n * punctuators immediately or calls the appropriate helper function for more\n * complicated tokens.\n */\n\nfunction readNextToken(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start;\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // SourceCharacter\n\n    switch (code) {\n      // Ignored ::\n      //   - UnicodeBOM\n      //   - WhiteSpace\n      //   - LineTerminator\n      //   - Comment\n      //   - Comma\n      //\n      // UnicodeBOM :: \"Byte Order Mark (U+FEFF)\"\n      //\n      // WhiteSpace ::\n      //   - \"Horizontal Tab (U+0009)\"\n      //   - \"Space (U+0020)\"\n      //\n      // Comma :: ,\n      case 0xfeff: // <BOM>\n\n      case 0x0009: // \\t\n\n      case 0x0020: // <space>\n\n      case 0x002c:\n        // ,\n        ++position;\n        continue;\n      // LineTerminator ::\n      //   - \"New Line (U+000A)\"\n      //   - \"Carriage Return (U+000D)\" [lookahead != \"New Line (U+000A)\"]\n      //   - \"Carriage Return (U+000D)\" \"New Line (U+000A)\"\n\n      case 0x000a:\n        // \\n\n        ++position;\n        ++lexer.line;\n        lexer.lineStart = position;\n        continue;\n      case 0x000d:\n        // \\r\n        if (body.charCodeAt(position + 1) === 0x000a) {\n          position += 2;\n        } else {\n          ++position;\n        }\n        ++lexer.line;\n        lexer.lineStart = position;\n        continue;\n      // Comment\n\n      case 0x0023:\n        // #\n        return readComment(lexer, position);\n      // Token ::\n      //   - Punctuator\n      //   - Name\n      //   - IntValue\n      //   - FloatValue\n      //   - StringValue\n      //\n      // Punctuator :: one of ! $ & ( ) ... : = @ [ ] { | }\n\n      case 0x0021:\n        // !\n        return createToken(lexer, TokenKind.BANG, position, position + 1);\n      case 0x0024:\n        // $\n        return createToken(lexer, TokenKind.DOLLAR, position, position + 1);\n      case 0x0026:\n        // &\n        return createToken(lexer, TokenKind.AMP, position, position + 1);\n      case 0x0028:\n        // (\n        return createToken(lexer, TokenKind.PAREN_L, position, position + 1);\n      case 0x0029:\n        // )\n        return createToken(lexer, TokenKind.PAREN_R, position, position + 1);\n      case 0x002e:\n        // .\n        if (body.charCodeAt(position + 1) === 0x002e && body.charCodeAt(position + 2) === 0x002e) {\n          return createToken(lexer, TokenKind.SPREAD, position, position + 3);\n        }\n        break;\n      case 0x003a:\n        // :\n        return createToken(lexer, TokenKind.COLON, position, position + 1);\n      case 0x003d:\n        // =\n        return createToken(lexer, TokenKind.EQUALS, position, position + 1);\n      case 0x0040:\n        // @\n        return createToken(lexer, TokenKind.AT, position, position + 1);\n      case 0x005b:\n        // [\n        return createToken(lexer, TokenKind.BRACKET_L, position, position + 1);\n      case 0x005d:\n        // ]\n        return createToken(lexer, TokenKind.BRACKET_R, position, position + 1);\n      case 0x007b:\n        // {\n        return createToken(lexer, TokenKind.BRACE_L, position, position + 1);\n      case 0x007c:\n        // |\n        return createToken(lexer, TokenKind.PIPE, position, position + 1);\n      case 0x007d:\n        // }\n        return createToken(lexer, TokenKind.BRACE_R, position, position + 1);\n      // StringValue\n\n      case 0x0022:\n        // \"\n        if (body.charCodeAt(position + 1) === 0x0022 && body.charCodeAt(position + 2) === 0x0022) {\n          return readBlockString(lexer, position);\n        }\n        return readString(lexer, position);\n    } // IntValue | FloatValue (Digit | -)\n\n    if (isDigit(code) || code === 0x002d) {\n      return readNumber(lexer, position, code);\n    } // Name\n\n    if (isNameStart(code)) {\n      return readName(lexer, position);\n    }\n    throw syntaxError(lexer.source, position, code === 0x0027 ? 'Unexpected single quote character (\\'), did you mean to use a double quote (\")?' : isUnicodeScalarValue(code) || isSupplementaryCodePoint(body, position) ? `Unexpected character: ${printCodePointAt(lexer, position)}.` : `Invalid character: ${printCodePointAt(lexer, position)}.`);\n  }\n  return createToken(lexer, TokenKind.EOF, bodyLength, bodyLength);\n}\n/**\n * Reads a comment token from the source file.\n *\n * ```\n * Comment :: # CommentChar* [lookahead != CommentChar]\n *\n * CommentChar :: SourceCharacter but not LineTerminator\n * ```\n */\n\nfunction readComment(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // LineTerminator (\\n | \\r)\n\n    if (code === 0x000a || code === 0x000d) {\n      break;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      break;\n    }\n  }\n  return createToken(lexer, TokenKind.COMMENT, start, position, body.slice(start + 1, position));\n}\n/**\n * Reads a number token from the source file, either a FloatValue or an IntValue\n * depending on whether a FractionalPart or ExponentPart is encountered.\n *\n * ```\n * IntValue :: IntegerPart [lookahead != {Digit, `.`, NameStart}]\n *\n * IntegerPart ::\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit Digit*\n *\n * NegativeSign :: -\n *\n * NonZeroDigit :: Digit but not `0`\n *\n * FloatValue ::\n *   - IntegerPart FractionalPart ExponentPart [lookahead != {Digit, `.`, NameStart}]\n *   - IntegerPart FractionalPart [lookahead != {Digit, `.`, NameStart}]\n *   - IntegerPart ExponentPart [lookahead != {Digit, `.`, NameStart}]\n *\n * FractionalPart :: . Digit+\n *\n * ExponentPart :: ExponentIndicator Sign? Digit+\n *\n * ExponentIndicator :: one of `e` `E`\n *\n * Sign :: one of + -\n * ```\n */\n\nfunction readNumber(lexer, start, firstCode) {\n  const body = lexer.source.body;\n  let position = start;\n  let code = firstCode;\n  let isFloat = false; // NegativeSign (-)\n\n  if (code === 0x002d) {\n    code = body.charCodeAt(++position);\n  } // Zero (0)\n\n  if (code === 0x0030) {\n    code = body.charCodeAt(++position);\n    if (isDigit(code)) {\n      throw syntaxError(lexer.source, position, `Invalid number, unexpected digit after 0: ${printCodePointAt(lexer, position)}.`);\n    }\n  } else {\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // Full stop (.)\n\n  if (code === 0x002e) {\n    isFloat = true;\n    code = body.charCodeAt(++position);\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // E e\n\n  if (code === 0x0045 || code === 0x0065) {\n    isFloat = true;\n    code = body.charCodeAt(++position); // + -\n\n    if (code === 0x002b || code === 0x002d) {\n      code = body.charCodeAt(++position);\n    }\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // Numbers cannot be followed by . or NameStart\n\n  if (code === 0x002e || isNameStart(code)) {\n    throw syntaxError(lexer.source, position, `Invalid number, expected digit but got: ${printCodePointAt(lexer, position)}.`);\n  }\n  return createToken(lexer, isFloat ? TokenKind.FLOAT : TokenKind.INT, start, position, body.slice(start, position));\n}\n/**\n * Returns the new position in the source after reading one or more digits.\n */\n\nfunction readDigits(lexer, start, firstCode) {\n  if (!isDigit(firstCode)) {\n    throw syntaxError(lexer.source, start, `Invalid number, expected digit but got: ${printCodePointAt(lexer, start)}.`);\n  }\n  const body = lexer.source.body;\n  let position = start + 1; // +1 to skip first firstCode\n\n  while (isDigit(body.charCodeAt(position))) {\n    ++position;\n  }\n  return position;\n}\n/**\n * Reads a single-quote string token from the source file.\n *\n * ```\n * StringValue ::\n *   - `\"\"` [lookahead != `\"`]\n *   - `\"` StringCharacter+ `\"`\n *\n * StringCharacter ::\n *   - SourceCharacter but not `\"` or `\\` or LineTerminator\n *   - `\\u` EscapedUnicode\n *   - `\\` EscapedCharacter\n *\n * EscapedUnicode ::\n *   - `{` HexDigit+ `}`\n *   - HexDigit HexDigit HexDigit HexDigit\n *\n * EscapedCharacter :: one of `\"` `\\` `/` `b` `f` `n` `r` `t`\n * ```\n */\n\nfunction readString(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n  let chunkStart = position;\n  let value = '';\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // Closing Quote (\")\n\n    if (code === 0x0022) {\n      value += body.slice(chunkStart, position);\n      return createToken(lexer, TokenKind.STRING, start, position + 1, value);\n    } // Escape Sequence (\\)\n\n    if (code === 0x005c) {\n      value += body.slice(chunkStart, position);\n      const escape = body.charCodeAt(position + 1) === 0x0075 // u\n      ? body.charCodeAt(position + 2) === 0x007b // {\n      ? readEscapedUnicodeVariableWidth(lexer, position) : readEscapedUnicodeFixedWidth(lexer, position) : readEscapedCharacter(lexer, position);\n      value += escape.value;\n      position += escape.size;\n      chunkStart = position;\n      continue;\n    } // LineTerminator (\\n | \\r)\n\n    if (code === 0x000a || code === 0x000d) {\n      break;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      throw syntaxError(lexer.source, position, `Invalid character within String: ${printCodePointAt(lexer, position)}.`);\n    }\n  }\n  throw syntaxError(lexer.source, position, 'Unterminated string.');\n} // The string value and lexed size of an escape sequence.\n\nfunction readEscapedUnicodeVariableWidth(lexer, position) {\n  const body = lexer.source.body;\n  let point = 0;\n  let size = 3; // Cannot be larger than 12 chars (\\u{00000000}).\n\n  while (size < 12) {\n    const code = body.charCodeAt(position + size++); // Closing Brace (})\n\n    if (code === 0x007d) {\n      // Must be at least 5 chars (\\u{0}) and encode a Unicode scalar value.\n      if (size < 5 || !isUnicodeScalarValue(point)) {\n        break;\n      }\n      return {\n        value: String.fromCodePoint(point),\n        size\n      };\n    } // Append this hex digit to the code point.\n\n    point = point << 4 | readHexDigit(code);\n    if (point < 0) {\n      break;\n    }\n  }\n  throw syntaxError(lexer.source, position, `Invalid Unicode escape sequence: \"${body.slice(position, position + size)}\".`);\n}\nfunction readEscapedUnicodeFixedWidth(lexer, position) {\n  const body = lexer.source.body;\n  const code = read16BitHexCode(body, position + 2);\n  if (isUnicodeScalarValue(code)) {\n    return {\n      value: String.fromCodePoint(code),\n      size: 6\n    };\n  } // GraphQL allows JSON-style surrogate pair escape sequences, but only when\n  // a valid pair is formed.\n\n  if (isLeadingSurrogate(code)) {\n    // \\u\n    if (body.charCodeAt(position + 6) === 0x005c && body.charCodeAt(position + 7) === 0x0075) {\n      const trailingCode = read16BitHexCode(body, position + 8);\n      if (isTrailingSurrogate(trailingCode)) {\n        // JavaScript defines strings as a sequence of UTF-16 code units and\n        // encodes Unicode code points above U+FFFF using a surrogate pair of\n        // code units. Since this is a surrogate pair escape sequence, just\n        // include both codes into the JavaScript string value. Had JavaScript\n        // not been internally based on UTF-16, then this surrogate pair would\n        // be decoded to retrieve the supplementary code point.\n        return {\n          value: String.fromCodePoint(code, trailingCode),\n          size: 12\n        };\n      }\n    }\n  }\n  throw syntaxError(lexer.source, position, `Invalid Unicode escape sequence: \"${body.slice(position, position + 6)}\".`);\n}\n/**\n * Reads four hexadecimal characters and returns the positive integer that 16bit\n * hexadecimal string represents. For example, \"000f\" will return 15, and \"dead\"\n * will return 57005.\n *\n * Returns a negative number if any char was not a valid hexadecimal digit.\n */\n\nfunction read16BitHexCode(body, position) {\n  // readHexDigit() returns -1 on error. ORing a negative value with any other\n  // value always produces a negative value.\n  return readHexDigit(body.charCodeAt(position)) << 12 | readHexDigit(body.charCodeAt(position + 1)) << 8 | readHexDigit(body.charCodeAt(position + 2)) << 4 | readHexDigit(body.charCodeAt(position + 3));\n}\n/**\n * Reads a hexadecimal character and returns its positive integer value (0-15).\n *\n * '0' becomes 0, '9' becomes 9\n * 'A' becomes 10, 'F' becomes 15\n * 'a' becomes 10, 'f' becomes 15\n *\n * Returns -1 if the provided character code was not a valid hexadecimal digit.\n *\n * HexDigit :: one of\n *   - `0` `1` `2` `3` `4` `5` `6` `7` `8` `9`\n *   - `A` `B` `C` `D` `E` `F`\n *   - `a` `b` `c` `d` `e` `f`\n */\n\nfunction readHexDigit(code) {\n  return code >= 0x0030 && code <= 0x0039 // 0-9\n  ? code - 0x0030 : code >= 0x0041 && code <= 0x0046 // A-F\n  ? code - 0x0037 : code >= 0x0061 && code <= 0x0066 // a-f\n  ? code - 0x0057 : -1;\n}\n/**\n * | Escaped Character | Code Point | Character Name               |\n * | ----------------- | ---------- | ---------------------------- |\n * | `\"`               | U+0022     | double quote                 |\n * | `\\`               | U+005C     | reverse solidus (back slash) |\n * | `/`               | U+002F     | solidus (forward slash)      |\n * | `b`               | U+0008     | backspace                    |\n * | `f`               | U+000C     | form feed                    |\n * | `n`               | U+000A     | line feed (new line)         |\n * | `r`               | U+000D     | carriage return              |\n * | `t`               | U+0009     | horizontal tab               |\n */\n\nfunction readEscapedCharacter(lexer, position) {\n  const body = lexer.source.body;\n  const code = body.charCodeAt(position + 1);\n  switch (code) {\n    case 0x0022:\n      // \"\n      return {\n        value: '\\u0022',\n        size: 2\n      };\n    case 0x005c:\n      // \\\n      return {\n        value: '\\u005c',\n        size: 2\n      };\n    case 0x002f:\n      // /\n      return {\n        value: '\\u002f',\n        size: 2\n      };\n    case 0x0062:\n      // b\n      return {\n        value: '\\u0008',\n        size: 2\n      };\n    case 0x0066:\n      // f\n      return {\n        value: '\\u000c',\n        size: 2\n      };\n    case 0x006e:\n      // n\n      return {\n        value: '\\u000a',\n        size: 2\n      };\n    case 0x0072:\n      // r\n      return {\n        value: '\\u000d',\n        size: 2\n      };\n    case 0x0074:\n      // t\n      return {\n        value: '\\u0009',\n        size: 2\n      };\n  }\n  throw syntaxError(lexer.source, position, `Invalid character escape sequence: \"${body.slice(position, position + 2)}\".`);\n}\n/**\n * Reads a block string token from the source file.\n *\n * ```\n * StringValue ::\n *   - `\"\"\"` BlockStringCharacter* `\"\"\"`\n *\n * BlockStringCharacter ::\n *   - SourceCharacter but not `\"\"\"` or `\\\"\"\"`\n *   - `\\\"\"\"`\n * ```\n */\n\nfunction readBlockString(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let lineStart = lexer.lineStart;\n  let position = start + 3;\n  let chunkStart = position;\n  let currentLine = '';\n  const blockLines = [];\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // Closing Triple-Quote (\"\"\")\n\n    if (code === 0x0022 && body.charCodeAt(position + 1) === 0x0022 && body.charCodeAt(position + 2) === 0x0022) {\n      currentLine += body.slice(chunkStart, position);\n      blockLines.push(currentLine);\n      const token = createToken(lexer, TokenKind.BLOCK_STRING, start, position + 3,\n      // Return a string of the lines joined with U+000A.\n      dedentBlockStringLines(blockLines).join('\\n'));\n      lexer.line += blockLines.length - 1;\n      lexer.lineStart = lineStart;\n      return token;\n    } // Escaped Triple-Quote (\\\"\"\")\n\n    if (code === 0x005c && body.charCodeAt(position + 1) === 0x0022 && body.charCodeAt(position + 2) === 0x0022 && body.charCodeAt(position + 3) === 0x0022) {\n      currentLine += body.slice(chunkStart, position);\n      chunkStart = position + 1; // skip only slash\n\n      position += 4;\n      continue;\n    } // LineTerminator\n\n    if (code === 0x000a || code === 0x000d) {\n      currentLine += body.slice(chunkStart, position);\n      blockLines.push(currentLine);\n      if (code === 0x000d && body.charCodeAt(position + 1) === 0x000a) {\n        position += 2;\n      } else {\n        ++position;\n      }\n      currentLine = '';\n      chunkStart = position;\n      lineStart = position;\n      continue;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      throw syntaxError(lexer.source, position, `Invalid character within String: ${printCodePointAt(lexer, position)}.`);\n    }\n  }\n  throw syntaxError(lexer.source, position, 'Unterminated string.');\n}\n/**\n * Reads an alphanumeric + underscore name from the source.\n *\n * ```\n * Name ::\n *   - NameStart NameContinue* [lookahead != NameContinue]\n * ```\n */\n\nfunction readName(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position);\n    if (isNameContinue(code)) {\n      ++position;\n    } else {\n      break;\n    }\n  }\n  return createToken(lexer, TokenKind.NAME, start, position, body.slice(start, position));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}