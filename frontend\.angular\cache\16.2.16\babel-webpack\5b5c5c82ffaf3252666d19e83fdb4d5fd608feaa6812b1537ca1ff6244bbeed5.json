{"ast": null, "code": "/**\n * Merges the provided objects shallowly and removes\n * all properties with an `undefined` value\n */\nexport function compact() {\n  var objects = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    objects[_i] = arguments[_i];\n  }\n  var result = Object.create(null);\n  objects.forEach(function (obj) {\n    if (!obj) return;\n    Object.keys(obj).forEach(function (key) {\n      var value = obj[key];\n      if (value !== void 0) {\n        result[key] = value;\n      }\n    });\n  });\n  return result;\n}\n//# sourceMappingURL=compact.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}