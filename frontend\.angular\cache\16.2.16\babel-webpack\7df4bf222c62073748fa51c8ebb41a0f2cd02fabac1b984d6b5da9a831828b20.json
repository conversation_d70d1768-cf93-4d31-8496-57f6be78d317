{"ast": null, "code": "import { invariant, newInvariantError } from \"../globals/index.js\";\nimport { valueToObjectRepresentation } from \"./storeUtils.js\";\n// Checks the document for errors and throws an exception if there is an error.\nexport function checkDocument(doc) {\n  invariant(doc && doc.kind === \"Document\", 88);\n  var operations = doc.definitions.filter(function (d) {\n    return d.kind !== \"FragmentDefinition\";\n  }).map(function (definition) {\n    if (definition.kind !== \"OperationDefinition\") {\n      throw newInvariantError(89, definition.kind);\n    }\n    return definition;\n  });\n  invariant(operations.length <= 1, 90, operations.length);\n  return doc;\n}\nexport function getOperationDefinition(doc) {\n  checkDocument(doc);\n  return doc.definitions.filter(function (definition) {\n    return definition.kind === \"OperationDefinition\";\n  })[0];\n}\nexport function getOperationName(doc) {\n  return doc.definitions.filter(function (definition) {\n    return definition.kind === \"OperationDefinition\" && !!definition.name;\n  }).map(function (x) {\n    return x.name.value;\n  })[0] || null;\n}\n// Returns the FragmentDefinitions from a particular document as an array\nexport function getFragmentDefinitions(doc) {\n  return doc.definitions.filter(function (definition) {\n    return definition.kind === \"FragmentDefinition\";\n  });\n}\nexport function getQueryDefinition(doc) {\n  var queryDef = getOperationDefinition(doc);\n  invariant(queryDef && queryDef.operation === \"query\", 91);\n  return queryDef;\n}\nexport function getFragmentDefinition(doc) {\n  invariant(doc.kind === \"Document\", 92);\n  invariant(doc.definitions.length <= 1, 93);\n  var fragmentDef = doc.definitions[0];\n  invariant(fragmentDef.kind === \"FragmentDefinition\", 94);\n  return fragmentDef;\n}\n/**\n * Returns the first operation definition found in this document.\n * If no operation definition is found, the first fragment definition will be returned.\n * If no definitions are found, an error will be thrown.\n */\nexport function getMainDefinition(queryDoc) {\n  checkDocument(queryDoc);\n  var fragmentDefinition;\n  for (var _i = 0, _a = queryDoc.definitions; _i < _a.length; _i++) {\n    var definition = _a[_i];\n    if (definition.kind === \"OperationDefinition\") {\n      var operation = definition.operation;\n      if (operation === \"query\" || operation === \"mutation\" || operation === \"subscription\") {\n        return definition;\n      }\n    }\n    if (definition.kind === \"FragmentDefinition\" && !fragmentDefinition) {\n      // we do this because we want to allow multiple fragment definitions\n      // to precede an operation definition.\n      fragmentDefinition = definition;\n    }\n  }\n  if (fragmentDefinition) {\n    return fragmentDefinition;\n  }\n  throw newInvariantError(95);\n}\nexport function getDefaultValues(definition) {\n  var defaultValues = Object.create(null);\n  var defs = definition && definition.variableDefinitions;\n  if (defs && defs.length) {\n    defs.forEach(function (def) {\n      if (def.defaultValue) {\n        valueToObjectRepresentation(defaultValues, def.variable.name, def.defaultValue);\n      }\n    });\n  }\n  return defaultValues;\n}\n//# sourceMappingURL=getFromAST.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}