{"ast": null, "code": "import { finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/ai.service\";\nimport * as i2 from \"src/app/services/task.service\";\nimport * as i3 from \"src/app/services/notification.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"assistant-message\": a1\n  };\n};\nconst _c1 = function (a0) {\n  return {\n    \"flex-row-reverse\": a0\n  };\n};\nconst _c2 = function (a0, a1) {\n  return {\n    \"bg-primary\": a0,\n    \"bg-success\": a1\n  };\n};\nconst _c3 = function (a0, a1) {\n  return {\n    \"user-bubble\": a0,\n    \"assistant-bubble\": a1\n  };\n};\nfunction AiChatComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"div\", 21);\n    i0.ɵɵelement(3, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 22);\n    i0.ɵɵelement(5, \"p\", 23);\n    i0.ɵɵelementStart(6, \"div\", 24);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const message_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c0, message_r6.role === \"user\", message_r6.role === \"assistant\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c1, message_r6.role === \"user\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(13, _c2, message_r6.role === \"user\", message_r6.role === \"assistant\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", message_r6.role === \"user\" ? \"bi-person-fill\" : \"bi-robot\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(16, _c3, message_r6.role === \"user\", message_r6.role === \"assistant\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", message_r6.content, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", message_r6.role === \"user\" ? \"Vous\" : \"Assistant IA\", \" \\u2022 \", ctx_r1.getCurrentTime(), \" \");\n  }\n}\nfunction AiChatComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27);\n    i0.ɵɵelement(3, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 29)(5, \"div\", 30);\n    i0.ɵɵelement(6, \"span\")(7, \"span\")(8, \"span\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nconst _c4 = \"linear-gradient(45deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7))\";\nconst _c5 = function (a1) {\n  return {\n    \"background\": _c4,\n    \"border-left\": a1\n  };\n};\nconst _c6 = function (a0) {\n  return {\n    \"background\": a0\n  };\n};\nconst _c7 = function (a0) {\n  return {\n    \"color\": a0\n  };\n};\nfunction AiChatComponent_div_7_div_14_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"i\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"div\", 81);\n    i0.ɵɵtext(5, \"Responsable\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 82);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    const i_r11 = ctx_r14.index;\n    const entity_r10 = ctx_r14.$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c5, \"4px solid \" + ctx_r12.getColorForIndex(i_r11)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(6, _c6, ctx_r12.getGradientForIndex(i_r11)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(8, _c7, ctx_r12.getColorForIndex(i_r11)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(entity_r10.assignedTo);\n  }\n}\nconst _c8 = function (a0, a1, a2) {\n  return {\n    \"high-priority\": a0,\n    \"medium-priority\": a1,\n    \"low-priority\": a2\n  };\n};\nconst _c9 = function (a0, a1, a2) {\n  return {\n    \"bg-danger\": a0,\n    \"bg-warning text-dark\": a1,\n    \"bg-info text-dark\": a2\n  };\n};\nfunction AiChatComponent_div_7_div_14_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 84)(2, \"h6\", 85);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 86);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 87);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(5, _c8, task_r15.priority === \"high\", task_r15.priority === \"medium\", task_r15.priority === \"low\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(task_r15.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(9, _c9, task_r15.priority === \"high\", task_r15.priority === \"medium\", task_r15.priority === \"low\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", task_r15.priority === \"high\" ? \"Haute\" : task_r15.priority === \"medium\" ? \"Moyenne\" : \"Basse\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", task_r15.description, \" \");\n  }\n}\nfunction AiChatComponent_div_7_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"div\", 63)(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 64)(6, \"div\", 65);\n    i0.ɵɵelement(7, \"i\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"h5\", 67);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 68)(11, \"div\", 69)(12, \"p\", 70);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, AiChatComponent_div_7_div_14_div_14_Template, 8, 10, \"div\", 71);\n    i0.ɵɵelementStart(15, \"div\", 72)(16, \"h6\", 73);\n    i0.ɵɵelement(17, \"i\", 74);\n    i0.ɵɵtext(18, \" T\\u00E2ches \\u00E0 r\\u00E9aliser \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 75);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 76);\n    i0.ɵɵtemplate(22, AiChatComponent_div_7_div_14_div_22_Template, 8, 13, \"div\", 77);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const entity_r10 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(12, _c6, ctx_r8.getGradientForIndex(i_r11)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Module \", i_r11 + 1, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(14, _c6, ctx_r8.getGradientForIndex(i_r11)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r8.getIconForModule(entity_r10.name))(\"ngStyle\", i0.ɵɵpureFunction1(16, _c7, ctx_r8.getColorForIndex(i_r11)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(entity_r10.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(entity_r10.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", entity_r10.assignedTo);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(18, _c7, ctx_r8.getColorForIndex(i_r11)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(20, _c6, ctx_r8.getGradientForIndex(i_r11)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", entity_r10.tasks.length, \" t\\u00E2ches \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", entity_r10.tasks);\n  }\n}\nfunction AiChatComponent_div_7_div_16_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"div\", 98)(2, \"h6\", 53);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 99);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 100);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r20 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(task_r20.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(4, _c9, task_r20.priority === \"high\", task_r20.priority === \"medium\", task_r20.priority === \"low\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", task_r20.priority === \"high\" ? \"Haute\" : task_r20.priority === \"medium\" ? \"Moyenne\" : \"Basse\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(task_r20.description);\n  }\n}\nfunction AiChatComponent_div_7_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"h2\", 89)(2, \"button\", 90)(3, \"strong\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 91);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 92)(8, \"div\", 93)(9, \"p\", 94);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 95);\n    i0.ɵɵtemplate(12, AiChatComponent_div_7_div_16_div_12_Template, 8, 8, \"div\", 96);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const entity_r17 = ctx.$implicit;\n    const i_r18 = ctx.index;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"id\", \"heading\" + i_r18);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-bs-target\", \"#collapse\" + i_r18)(\"aria-expanded\", i_r18 === 0)(\"aria-controls\", \"collapse\" + i_r18);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(entity_r17.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", entity_r17.tasks.length, \" t\\u00E2ches\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"id\", \"collapse\" + i_r18);\n    i0.ɵɵattribute(\"aria-labelledby\", \"heading\" + i_r18);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(entity_r17.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", entity_r17.tasks);\n  }\n}\nfunction AiChatComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"div\", 33)(3, \"div\")(4, \"h5\", 34);\n    i0.ɵɵelement(5, \"i\", 35);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 36);\n    i0.ɵɵelement(8, \"i\", 37);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"span\", 38);\n    i0.ɵɵelement(11, \"i\", 39);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 40);\n    i0.ɵɵtemplate(14, AiChatComponent_div_7_div_14_Template, 23, 22, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 42);\n    i0.ɵɵtemplate(16, AiChatComponent_div_7_div_16_Template, 13, 10, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 44)(18, \"div\", 45)(19, \"div\", 46)(20, \"div\", 47)(21, \"div\", 48)(22, \"h5\", 49);\n    i0.ɵɵelement(23, \"i\", 50);\n    i0.ɵɵtext(24, \" Plan de projet pr\\u00EAt \\u00E0 \\u00EAtre impl\\u00E9ment\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 51)(26, \"div\", 52);\n    i0.ɵɵtext(27, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\")(29, \"h6\", 53);\n    i0.ɵɵtext(30, \"Cr\\u00E9ation des t\\u00E2ches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"p\", 54);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 51)(34, \"div\", 55);\n    i0.ɵɵtext(35, \"2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\")(37, \"h6\", 53);\n    i0.ɵɵtext(38, \"Assignation aux membres\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"p\", 54);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 26)(42, \"div\", 56);\n    i0.ɵɵtext(43, \"3\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\")(45, \"h6\", 53);\n    i0.ɵɵtext(46, \"Suivi du projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"p\", 54);\n    i0.ɵɵtext(48, \"Vous pourrez suivre l'avancement dans le tableau de bord des t\\u00E2ches\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(49, \"div\", 57)(50, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function AiChatComponent_div_7_Template_button_click_50_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.createTasks());\n    });\n    i0.ɵɵelement(51, \"i\", 59);\n    i0.ɵɵtext(52, \" Cr\\u00E9er les t\\u00E2ches \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 60);\n    i0.ɵɵelement(54, \"i\", 37);\n    i0.ɵɵtext(55, \" Cette action est irr\\u00E9versible \");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" Plan du projet \\\"\", ctx_r3.generatedContent.projectTitle, \"\\\" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.generatedContent.entities.length, \" modules g\\u00E9n\\u00E9r\\u00E9s avec \", ctx_r3.countTasks(ctx_r3.generatedContent), \" t\\u00E2ches au total \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.team && ctx_r3.team.members ? ctx_r3.team.members.length : 0, \" membres \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.generatedContent.entities);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.generatedContent.entities);\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.countTasks(ctx_r3.generatedContent), \" t\\u00E2ches seront cr\\u00E9\\u00E9es dans le syst\\u00E8me\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"Les t\\u00E2ches seront assign\\u00E9es aux \", ctx_r3.team && ctx_r3.team.members ? ctx_r3.team.members.length : 0, \" membres de l'\\u00E9quipe\");\n  }\n}\nfunction AiChatComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.error, \" \");\n  }\n}\nfunction AiChatComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"div\", 103)(2, \"div\", 104)(3, \"h6\", 105);\n    i0.ɵɵelement(4, \"i\", 106);\n    i0.ɵɵtext(5, \" G\\u00E9n\\u00E9rer des t\\u00E2ches avec l'IA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 107)(7, \"label\", 108);\n    i0.ɵɵtext(8, \"Entrez le titre de votre projet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 13)(10, \"span\", 14);\n    i0.ɵɵelement(11, \"i\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 110);\n    i0.ɵɵlistener(\"ngModelChange\", function AiChatComponent_div_10_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.projectTitle = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"small\", 111);\n    i0.ɵɵelement(14, \"i\", 37);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 112)(17, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function AiChatComponent_div_10_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.generateTasks());\n    });\n    i0.ɵɵelement(18, \"i\", 18);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.projectTitle)(\"disabled\", ctx_r5.isGenerating);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" L'IA g\\u00E9n\\u00E9rera \", ctx_r5.team && ctx_r5.team.members ? ctx_r5.team.members.length : 3, \" modules, un pour chaque membre de l'\\u00E9quipe. \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r5.isGenerating || !ctx_r5.projectTitle.trim());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r5.isGenerating ? \"bi-hourglass-split spin\" : \"bi-magic\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.isGenerating ? \"G\\u00E9n\\u00E9ration en cours...\" : \"G\\u00E9n\\u00E9rer des t\\u00E2ches\", \" \");\n  }\n}\nexport let AiChatComponent = /*#__PURE__*/(() => {\n  class AiChatComponent {\n    constructor(aiService, taskService, notificationService) {\n      this.aiService = aiService;\n      this.taskService = taskService;\n      this.notificationService = notificationService;\n      this.projectTitle = '';\n      this.isGenerating = false;\n      this.generatedContent = null;\n      this.error = null;\n      // Pour le chat\n      this.messages = [];\n      this.userQuestion = '';\n      this.isAskingQuestion = false;\n    }\n    ngOnInit() {\n      // Ajouter un message de bienvenue\n      this.messages.push({\n        role: 'assistant',\n        content: 'Bonjour ! Je suis votre assistant IA pour la gestion de projet. Entrez le titre de votre projet pour que je puisse vous aider à le diviser en tâches, ou posez-moi une question sur la gestion de projet.'\n      });\n    }\n    generateTasks() {\n      if (!this.projectTitle.trim()) {\n        this.notificationService.showError('Veuillez entrer un titre de projet');\n        return;\n      }\n      // Vérifier si l'équipe a des membres, sinon utiliser un nombre par défaut\n      let memberCount = this.team && this.team.members ? this.team.members.length : 3;\n      // S'assurer que nous avons au moins 3 entités pour un projet significatif\n      const effectiveMemberCount = Math.max(memberCount, 3);\n      if (memberCount === 0) {\n        this.notificationService.showWarning(\"L'équipe n'a pas de membres. Des tâches génériques seront créées.\");\n      }\n      this.isGenerating = true;\n      this.error = null;\n      console.log(`Génération de tâches pour ${effectiveMemberCount} entités (équipe de ${memberCount} membres)`);\n      // Ajouter la demande aux messages\n      this.messages.push({\n        role: 'user',\n        content: `Génère des tâches pour le projet \"${this.projectTitle}\" avec exactement ${effectiveMemberCount} entités, une pour chaque membre de l'équipe. Chaque entité doit représenter un module distinct du projet.`\n      });\n      // Ajouter un message de chargement\n      const loadingMessageIndex = this.messages.length;\n      this.messages.push({\n        role: 'assistant',\n        content: 'Je génère des tâches pour votre projet...'\n      });\n      // Récupérer les informations sur les membres de l'équipe\n      let teamMembers = [];\n      if (this.team && this.team.members) {\n        // Utiliser les IDs des membres\n        teamMembers = this.team.members.map((memberId, index) => {\n          return {\n            id: memberId,\n            name: `Membre ${index + 1}`,\n            role: 'membre'\n          };\n        });\n        console.log(\"Informations sur les membres passées à l'IA:\", teamMembers);\n      }\n      this.aiService.generateProjectTasks(this.projectTitle, memberCount, teamMembers).pipe(finalize(() => this.isGenerating = false)).subscribe({\n        next: result => {\n          if (!result || !result.entities || result.entities.length === 0) {\n            console.error(\"Résultat invalide reçu de l'API:\", result);\n            this.handleGenerationError(loadingMessageIndex, 'Format de réponse invalide');\n            return;\n          }\n          this.generatedContent = result;\n          // Remplacer le message de chargement par la réponse\n          this.messages[loadingMessageIndex] = {\n            role: 'assistant',\n            content: `J'ai généré ${result.entities.length} entités pour votre projet \"${result.projectTitle}\" avec un total de ${this.countTasks(result)} tâches.`\n          };\n          this.notificationService.showSuccess('Tâches générées avec succès');\n        },\n        error: error => {\n          console.error('Erreur lors de la génération des tâches:', error);\n          this.handleGenerationError(loadingMessageIndex, error.message || 'Erreur inconnue');\n        }\n      });\n    }\n    // Méthode pour gérer les erreurs de génération\n    handleGenerationError(messageIndex, errorDetails) {\n      this.error = 'Impossible de générer les tâches. Veuillez réessayer.';\n      // Remplacer le message de chargement par le message d'erreur\n      this.messages[messageIndex] = {\n        role: 'assistant',\n        content: \"Désolé, je n'ai pas pu générer les tâches. Veuillez réessayer avec un titre de projet différent.\"\n      };\n      this.notificationService.showError('Erreur lors de la génération des tâches: ' + errorDetails);\n    }\n    askQuestion() {\n      if (!this.userQuestion.trim()) {\n        return;\n      }\n      const question = this.userQuestion.trim();\n      this.userQuestion = '';\n      this.isAskingQuestion = true;\n      // Ajouter la question aux messages\n      this.messages.push({\n        role: 'user',\n        content: question\n      });\n      const projectContext = {\n        title: this.projectTitle || (this.generatedContent ? this.generatedContent.projectTitle : ''),\n        description: \"Projet géré par l'équipe \" + (this.team ? this.team.name : '')\n      };\n      this.aiService.askProjectQuestion(question, projectContext).pipe(finalize(() => this.isAskingQuestion = false)).subscribe({\n        next: response => {\n          // Ajouter la réponse aux messages\n          this.messages.push({\n            role: 'assistant',\n            content: response\n          });\n        },\n        error: error => {\n          console.error(\"Erreur lors de la demande à l'IA:\", error);\n          // Ajouter l'erreur aux messages\n          this.messages.push({\n            role: 'assistant',\n            content: \"Désolé, je n'ai pas pu répondre à votre question. Veuillez réessayer.\"\n          });\n          this.notificationService.showError(\"Erreur lors de la communication avec l'IA\");\n        }\n      });\n    }\n    createTasks() {\n      if (!this.generatedContent || !this.team || !this.team._id) {\n        this.notificationService.showError('Aucune tâche générée ou équipe invalide');\n        return;\n      }\n      let createdCount = 0;\n      const totalTasks = this.countTasks(this.generatedContent);\n      // Vérifier si l'équipe a des membres\n      if (!this.team.members || this.team.members.length === 0) {\n        this.notificationService.showError(\"L'équipe n'a pas de membres pour assigner les tâches\");\n        return;\n      }\n      // Préparer la liste des membres de l'équipe\n      const teamMembers = this.team.members.map(member => {\n        return typeof member === 'string' ? member : member.userId;\n      });\n      // Créer un mapping des noms de membres vers leurs IDs\n      const memberNameToIdMap = {};\n      teamMembers.forEach((memberId, index) => {\n        memberNameToIdMap[`Membre ${index + 1}`] = memberId;\n      });\n      console.log(\"Membres de l'équipe disponibles pour l'assignation:\", teamMembers);\n      console.log('Mapping des noms de membres vers leurs IDs:', memberNameToIdMap);\n      // Pour chaque entité\n      this.generatedContent.entities.forEach(entity => {\n        // Déterminer le membre assigné à cette entité\n        let assignedMemberId;\n        // Si l'IA a suggéré une assignation\n        if (entity.assignedTo) {\n          // Essayer de trouver l'ID du membre à partir du nom suggéré\n          const memberName = entity.assignedTo;\n          if (memberNameToIdMap[memberName]) {\n            assignedMemberId = memberNameToIdMap[memberName];\n            console.log(`Assignation suggérée par l'IA: Entité \"${entity.name}\" assignée à \"${memberName}\" (ID: ${assignedMemberId})`);\n          } else {\n            // Si le nom n'est pas trouvé, assigner aléatoirement\n            const randomMemberIndex = Math.floor(Math.random() * teamMembers.length);\n            assignedMemberId = teamMembers[randomMemberIndex];\n            console.log(`Nom de membre \"${memberName}\" non trouvé, assignation aléatoire à l'index ${randomMemberIndex}`);\n          }\n        } else {\n          // Si pas d'assignation suggérée, assigner aléatoirement\n          const randomMemberIndex = Math.floor(Math.random() * teamMembers.length);\n          assignedMemberId = teamMembers[randomMemberIndex];\n          console.log(`Pas d'assignation suggérée, assignation aléatoire à l'index ${randomMemberIndex}`);\n        }\n        // Pour chaque tâche dans l'entité\n        entity.tasks.forEach(taskData => {\n          const task = {\n            title: taskData.title,\n            description: `[${entity.name}] ${taskData.description}`,\n            status: taskData.status || 'todo',\n            priority: taskData.priority || 'medium',\n            teamId: this.team._id || '',\n            // Utiliser l'ID du membre assigné à l'entité\n            assignedTo: assignedMemberId\n          };\n          this.taskService.createTask(task).subscribe({\n            next: () => {\n              createdCount++;\n              if (createdCount === totalTasks) {\n                this.notificationService.showSuccess(`${createdCount} tâches créées avec succès et assignées aux membres de l'équipe`);\n                // Réinitialiser après création\n                this.generatedContent = null;\n                this.projectTitle = '';\n              }\n            },\n            error: error => {\n              console.error('Erreur lors de la création de la tâche:', error);\n              this.notificationService.showError('Erreur lors de la création des tâches');\n            }\n          });\n        });\n      });\n    }\n    countTasks(content) {\n      if (!content || !content.entities) return 0;\n      return content.entities.reduce((total, entity) => {\n        return total + (entity.tasks ? entity.tasks.length : 0);\n      }, 0);\n    }\n    // Méthode pour obtenir un dégradé de couleur basé sur l'index\n    getGradientForIndex(index) {\n      // Liste de dégradés prédéfinis\n      const gradients = ['linear-gradient(45deg, #007bff, #6610f2)', 'linear-gradient(45deg, #11998e, #38ef7d)', 'linear-gradient(45deg, #FC5C7D, #6A82FB)', 'linear-gradient(45deg, #FF8008, #FFC837)', 'linear-gradient(45deg, #8E2DE2, #4A00E0)', 'linear-gradient(45deg, #2193b0, #6dd5ed)', 'linear-gradient(45deg, #373B44, #4286f4)', 'linear-gradient(45deg, #834d9b, #d04ed6)', 'linear-gradient(45deg, #0cebeb, #20e3b2, #29ffc6)' // Turquoise\n      ];\n      // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\n      return gradients[index % gradients.length];\n    }\n    // Méthode pour obtenir une couleur unique basée sur l'index\n    getColorForIndex(index) {\n      // Liste de couleurs prédéfinies\n      const colors = ['#007bff', '#11998e', '#FC5C7D', '#FF8008', '#8E2DE2', '#2193b0', '#373B44', '#834d9b', '#0cebeb' // Turquoise\n      ];\n      // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\n      return colors[index % colors.length];\n    }\n    // Méthode pour obtenir une icône en fonction du nom du module\n    getIconForModule(moduleName) {\n      // Convertir le nom du module en minuscules pour faciliter la comparaison\n      const name = moduleName.toLowerCase();\n      // Mapper les noms de modules courants à des icônes Bootstrap\n      if (name.includes('crud') || name.includes('api') || name.includes('données') || name.includes('base')) {\n        return 'bi-database-fill';\n      } else if (name.includes('interface') || name.includes('ui') || name.includes('front') || name.includes('utilisateur')) {\n        return 'bi-window';\n      } else if (name.includes('déploiement') || name.includes('serveur') || name.includes('cloud')) {\n        return 'bi-cloud-arrow-up-fill';\n      } else if (name.includes('test') || name.includes('qualité') || name.includes('qa')) {\n        return 'bi-bug-fill';\n      } else if (name.includes('sécurité') || name.includes('auth')) {\n        return 'bi-shield-lock-fill';\n      } else if (name.includes('paiement') || name.includes('transaction')) {\n        return 'bi-credit-card-fill';\n      } else if (name.includes('utilisateur') || name.includes('user') || name.includes('profil')) {\n        return 'bi-person-fill';\n      } else if (name.includes('doc') || name.includes('documentation')) {\n        return 'bi-file-text-fill';\n      } else if (name.includes('mobile') || name.includes('app')) {\n        return 'bi-phone-fill';\n      } else if (name.includes('backend') || name.includes('serveur')) {\n        return 'bi-server';\n      } else if (name.includes('analytics') || name.includes('statistique') || name.includes('seo')) {\n        return 'bi-graph-up';\n      }\n      // Icône par défaut si aucune correspondance n'est trouvée\n      return 'bi-code-square';\n    }\n    // Méthode pour obtenir l'heure actuelle au format HH:MM\n    getCurrentTime() {\n      const now = new Date();\n      const hours = now.getHours().toString().padStart(2, '0');\n      const minutes = now.getMinutes().toString().padStart(2, '0');\n      return `${hours}:${minutes}`;\n    }\n    static {\n      this.ɵfac = function AiChatComponent_Factory(t) {\n        return new (t || AiChatComponent)(i0.ɵɵdirectiveInject(i1.AiService), i0.ɵɵdirectiveInject(i2.TaskService), i0.ɵɵdirectiveInject(i3.NotificationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AiChatComponent,\n        selectors: [[\"app-ai-chat\"]],\n        inputs: {\n          team: \"team\"\n        },\n        decls: 19,\n        vars: 9,\n        consts: [[1, \"ai-chat-container\", \"w-100\"], [1, \"card\", \"border-0\", \"shadow-sm\", \"w-100\"], [1, \"card-body\", \"p-0\"], [1, \"chat-messages\", \"p-3\"], [\"chatContainer\", \"\"], [\"class\", \"message mb-3\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"message assistant-message mb-3\", 4, \"ngIf\"], [\"class\", \"generated-content p-4 border-top\", 4, \"ngIf\"], [\"class\", \"alert alert-danger m-3\", 4, \"ngIf\"], [1, \"chat-input\", \"p-3\", \"border-top\"], [\"class\", \"mb-4\", 4, \"ngIf\"], [1, \"card\", \"border-0\", \"bg-light\", \"rounded-4\", \"shadow-sm\"], [1, \"card-body\", \"p-2\"], [1, \"input-group\"], [1, \"input-group-text\", \"bg-white\", \"border-0\"], [1, \"bi\", \"bi-chat-dots\", \"text-primary\"], [\"type\", \"text\", \"placeholder\", \"Posez une question sur la gestion de projet...\", 1, \"form-control\", \"border-0\", \"bg-white\", \"shadow-none\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keyup.enter\"], [1, \"btn\", \"btn-primary\", \"rounded-circle\", 2, \"width\", \"38px\", \"height\", \"38px\", 3, \"disabled\", \"click\"], [1, \"bi\", 3, \"ngClass\"], [1, \"message\", \"mb-3\", 3, \"ngClass\"], [1, \"d-flex\", 3, \"ngClass\"], [1, \"message-avatar\", \"rounded-circle\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"me-2\", 3, \"ngClass\"], [1, \"message-bubble\", \"p-3\", \"rounded-4\", \"shadow-sm\", 3, \"ngClass\"], [1, \"mb-0\", 3, \"innerHTML\"], [1, \"message-time\", \"small\", \"text-muted\", \"mt-1\", \"text-end\"], [1, \"message\", \"assistant-message\", \"mb-3\"], [1, \"d-flex\"], [1, \"message-avatar\", \"rounded-circle\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"me-2\", \"bg-success\"], [1, \"bi\", \"bi-robot\"], [1, \"message-bubble\", \"assistant-bubble\", \"p-3\", \"rounded-4\", \"shadow-sm\"], [1, \"typing-indicator\"], [1, \"generated-content\", \"p-4\", \"border-top\"], [1, \"generated-header\", \"mb-4\", \"p-3\", \"rounded-4\", \"shadow-sm\", 2, \"background\", \"linear-gradient(120deg, rgba(13, 110, 253, 0.1), rgba(102, 16, 242, 0.1))\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"text-primary\", \"mb-1\"], [1, \"bi\", \"bi-diagram-3-fill\", \"me-2\"], [1, \"text-muted\", \"mb-0\"], [1, \"bi\", \"bi-info-circle\", \"me-1\"], [1, \"badge\", \"bg-primary\", \"rounded-pill\", \"px-3\", \"py-2\"], [1, \"bi\", \"bi-people-fill\", \"me-1\"], [1, \"row\", \"mb-4\"], [\"class\", \"col-lg-3 col-md-4 col-sm-6 mb-4\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"generatedTasksAccordion\", 1, \"accordion\", \"d-none\"], [\"class\", \"accordion-item border-0 mb-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"mt-5\"], [1, \"card\", \"border-0\", \"rounded-4\", \"shadow-sm\", \"create-tasks-card\"], [1, \"card-body\", \"p-4\"], [1, \"row\", \"align-items-center\"], [1, \"col-lg-8\"], [1, \"mb-3\", \"text-success\"], [1, \"bi\", \"bi-check-circle-fill\", \"me-2\"], [1, \"d-flex\", \"mb-3\"], [1, \"step-circle\", \"bg-success\", \"text-white\", \"me-3\"], [1, \"mb-1\"], [1, \"text-muted\", \"mb-0\", \"small\"], [1, \"step-circle\", \"bg-primary\", \"text-white\", \"me-3\"], [1, \"step-circle\", \"bg-info\", \"text-white\", \"me-3\"], [1, \"col-lg-4\", \"text-center\", \"mt-4\", \"mt-lg-0\"], [1, \"btn\", \"btn-success\", \"btn-lg\", \"rounded-pill\", \"px-5\", \"py-3\", \"shadow\", \"create-button\", 3, \"click\"], [1, \"bi\", \"bi-plus-circle-fill\", \"me-2\"], [1, \"text-muted\", \"small\", \"mt-2\"], [1, \"col-lg-3\", \"col-md-4\", \"col-sm-6\", \"mb-4\"], [1, \"module-card\", \"card\", \"h-100\", \"border-0\", \"shadow-sm\"], [1, \"module-ribbon\", 3, \"ngStyle\"], [1, \"card-header\", \"text-white\", \"position-relative\", \"py-4\", 3, \"ngStyle\"], [1, \"module-icon-large\", \"rounded-circle\", \"bg-white\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"shadow\"], [1, \"bi\", 3, \"ngClass\", \"ngStyle\"], [1, \"mt-3\", \"mb-0\", \"text-center\"], [1, \"card-body\"], [1, \"description-box\", \"p-3\", \"rounded-3\", \"bg-light\", \"mb-3\"], [1, \"mb-0\"], [\"class\", \"assignation-badge mb-3 p-3 rounded-3 d-flex align-items-center\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"task-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\", \"pb-2\", \"border-bottom\"], [1, \"mb-0\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-list-check\", \"me-2\", 3, \"ngStyle\"], [1, \"badge\", \"rounded-pill\", 3, \"ngStyle\"], [1, \"task-list\"], [\"class\", \"task-item mb-3 p-3 rounded-3 shadow-sm\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"assignation-badge\", \"mb-3\", \"p-3\", \"rounded-3\", \"d-flex\", \"align-items-center\", 3, \"ngStyle\"], [1, \"member-avatar\", \"rounded-circle\", \"me-3\", \"d-flex\", \"align-items-center\", \"justify-content-center\", \"text-white\", 3, \"ngStyle\"], [1, \"bi\", \"bi-person-fill\"], [1, \"small\", \"text-muted\"], [1, \"fw-bold\", 3, \"ngStyle\"], [1, \"task-item\", \"mb-3\", \"p-3\", \"rounded-3\", \"shadow-sm\", 3, \"ngClass\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [1, \"mb-0\", \"task-title\"], [1, \"badge\", \"rounded-pill\", 3, \"ngClass\"], [1, \"task-description\", \"text-muted\", \"small\"], [1, \"accordion-item\", \"border-0\", \"mb-2\"], [1, \"accordion-header\", 3, \"id\"], [\"type\", \"button\", \"data-bs-toggle\", \"collapse\", 1, \"accordion-button\", \"collapsed\"], [1, \"badge\", \"bg-primary\", \"rounded-pill\", \"ms-2\"], [\"data-bs-parent\", \"#generatedTasksAccordion\", 1, \"accordion-collapse\", \"collapse\", 3, \"id\"], [1, \"accordion-body\"], [1, \"text-muted\", \"mb-3\"], [1, \"list-group\"], [\"class\", \"list-group-item list-group-item-action\", 4, \"ngFor\", \"ngForOf\"], [1, \"list-group-item\", \"list-group-item-action\"], [1, \"d-flex\", \"w-100\", \"justify-content-between\"], [1, \"badge\", 3, \"ngClass\"], [1, \"mb-1\", \"small\"], [1, \"alert\", \"alert-danger\", \"m-3\"], [1, \"mb-4\"], [1, \"card\", \"border-0\", \"bg-light\", \"rounded-4\", \"shadow-sm\", \"mb-3\"], [1, \"card-body\", \"p-3\"], [1, \"mb-3\", \"d-flex\", \"align-items-center\"], [1, \"bi\", \"bi-stars\", \"me-2\", \"text-primary\"], [1, \"mb-3\"], [\"for\", \"projectTitle\", 1, \"form-label\", \"small\", \"text-muted\"], [1, \"bi\", \"bi-lightbulb\", \"text-primary\"], [\"type\", \"text\", \"id\", \"projectTitle\", \"placeholder\", \"Ex: Site e-commerce, Application mobile, Syst\\u00E8me de gestion...\", 1, \"form-control\", \"border-0\", \"bg-white\", \"shadow-none\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [1, \"text-muted\", \"mt-1\", \"d-block\"], [1, \"d-grid\"], [1, \"btn\", \"btn-primary\", \"rounded-3\", 3, \"disabled\", \"click\"]],\n        template: function AiChatComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3, 4);\n            i0.ɵɵtemplate(5, AiChatComponent_div_5_Template, 8, 19, \"div\", 5);\n            i0.ɵɵtemplate(6, AiChatComponent_div_6_Template, 9, 0, \"div\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(7, AiChatComponent_div_7_Template, 56, 8, \"div\", 7);\n            i0.ɵɵtemplate(8, AiChatComponent_div_8_Template, 2, 1, \"div\", 8);\n            i0.ɵɵelementStart(9, \"div\", 9);\n            i0.ɵɵtemplate(10, AiChatComponent_div_10_Template, 20, 6, \"div\", 10);\n            i0.ɵɵelementStart(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"span\", 14);\n            i0.ɵɵelement(15, \"i\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"input\", 16);\n            i0.ɵɵlistener(\"ngModelChange\", function AiChatComponent_Template_input_ngModelChange_16_listener($event) {\n              return ctx.userQuestion = $event;\n            })(\"keyup.enter\", function AiChatComponent_Template_input_keyup_enter_16_listener() {\n              return ctx.askQuestion();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function AiChatComponent_Template_button_click_17_listener() {\n              return ctx.askQuestion();\n            });\n            i0.ɵɵelement(18, \"i\", 18);\n            i0.ɵɵelementEnd()()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.messages);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isGenerating || ctx.isAskingQuestion);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.generatedContent);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.generatedContent);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngModel\", ctx.userQuestion)(\"disabled\", ctx.isAskingQuestion);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", ctx.isAskingQuestion || !ctx.userQuestion.trim());\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngClass\", ctx.isAskingQuestion ? \"bi-hourglass-split spin\" : \"bi-send-fill\");\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgStyle, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel],\n        styles: [\".ai-chat-container[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;flex-direction:column}.card[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column}.card-body[_ngcontent-%COMP%]{display:flex;flex-direction:column;flex:1;overflow:hidden}.chat-messages[_ngcontent-%COMP%]{flex:1;overflow-y:auto;max-height:500px}.message[_ngcontent-%COMP%]{display:flex;margin-bottom:15px}.user-message[_ngcontent-%COMP%]{justify-content:flex-end}.assistant-message[_ngcontent-%COMP%]{justify-content:flex-start}.message-avatar[_ngcontent-%COMP%]{width:36px;height:36px;font-size:1rem;color:#fff;flex-shrink:0}.message-bubble[_ngcontent-%COMP%]{max-width:80%;word-wrap:break-word;position:relative;transition:all .3s ease}.message-bubble[_ngcontent-%COMP%]:hover{transform:translateY(-2px)}.user-bubble[_ngcontent-%COMP%]{background:linear-gradient(45deg,#007bff,#6610f2);color:#fff;border-top-right-radius:0!important}.assistant-bubble[_ngcontent-%COMP%]{background-color:#fff;color:#343a40;border-top-left-radius:0!important}.message-time[_ngcontent-%COMP%]{font-size:.7rem;opacity:.7}.user-bubble[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%]{color:#fffc!important}.typing-indicator[_ngcontent-%COMP%]{display:flex;align-items:center}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{height:8px;width:8px;background-color:#343a40;border-radius:50%;display:inline-block;margin-right:5px;animation:_ngcontent-%COMP%_typing 1s infinite ease-in-out}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){animation-delay:0s}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3){animation-delay:.4s;margin-right:0}@keyframes _ngcontent-%COMP%_typing{0%{transform:translateY(0);opacity:.4}50%{transform:translateY(-5px);opacity:.8}to{transform:translateY(0);opacity:.4}}.spin[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1.5s infinite linear}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.generated-content[_ngcontent-%COMP%]{background-color:#f8f9fa;border-radius:.25rem;max-height:800px;overflow-y:auto;width:100%}.generated-header[_ngcontent-%COMP%]{border-left:5px solid #007bff}.generated-content[_ngcontent-%COMP%]   .module-card[_ngcontent-%COMP%]{transition:all .3s ease;border:none;height:100%;border-radius:16px;overflow:hidden;position:relative}.generated-content[_ngcontent-%COMP%]   .module-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 20px 40px #00000026!important}.module-ribbon[_ngcontent-%COMP%]{position:absolute;top:15px;right:-35px;transform:rotate(45deg);width:150px;text-align:center;padding:5px;font-size:.8rem;font-weight:700;color:#fff;z-index:10;box-shadow:0 3px 10px #0000001a}.generated-content[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{border-radius:0;font-weight:600;padding:30px 15px;text-align:center}.module-icon-large[_ngcontent-%COMP%]{width:70px;height:70px;font-size:2rem;margin:0 auto;position:relative;z-index:5}.member-avatar[_ngcontent-%COMP%]{width:45px;height:45px;font-size:1.2rem;flex-shrink:0}.assignation-badge[_ngcontent-%COMP%]{box-shadow:0 3px 10px #0000000d;transition:all .3s ease}.assignation-badge[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 5px 15px #00000014}.description-box[_ngcontent-%COMP%]{border-left:4px solid #e9ecef;font-style:italic}.task-list[_ngcontent-%COMP%]{max-height:300px;overflow-y:auto;padding-right:5px;margin-bottom:10px}.task-header[_ngcontent-%COMP%]{position:sticky;top:0;background-color:#fff;z-index:5}.task-item[_ngcontent-%COMP%]{transition:all .3s ease;border-left:4px solid transparent;background-color:#fff}.task-item[_ngcontent-%COMP%]:hover{transform:translateY(-3px);box-shadow:0 10px 20px #0000001a!important}.high-priority[_ngcontent-%COMP%]{border-left-color:#dc3545}.medium-priority[_ngcontent-%COMP%]{border-left-color:#ffc107}.low-priority[_ngcontent-%COMP%]{border-left-color:#17a2b8}.task-title[_ngcontent-%COMP%]{font-weight:600;color:#343a40}.task-description[_ngcontent-%COMP%]{padding-top:8px;border-top:1px dashed #dee2e6;margin-top:5px}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.create-tasks-card[_ngcontent-%COMP%]{background:linear-gradient(120deg,rgba(255,255,255,1),rgba(248,249,250,1));border-left:5px solid #28a745}.step-circle[_ngcontent-%COMP%]{width:30px;height:30px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:700;flex-shrink:0}.create-button[_ngcontent-%COMP%]{transition:all .3s ease;box-shadow:0 5px 15px #28a7454d!important}.create-button[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 10px 25px #28a74566!important}.accordion-button[_ngcontent-%COMP%]:not(.collapsed){background-color:#e7f1ff;color:#0d6efd}.accordion-button[_ngcontent-%COMP%]:focus{box-shadow:none;border-color:#00000020}.chat-input[_ngcontent-%COMP%]{background-color:#fff;border-top:1px solid #dee2e6}\"]\n      });\n    }\n  }\n  return AiChatComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}