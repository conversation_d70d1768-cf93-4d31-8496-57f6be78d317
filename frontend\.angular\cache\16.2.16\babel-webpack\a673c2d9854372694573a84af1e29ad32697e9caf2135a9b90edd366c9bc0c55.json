{"ast": null, "code": "import { __extends } from \"tslib\";\nvar genericMessage = \"Invariant Violation\";\nvar _a = Object.setPrototypeOf,\n  setPrototypeOf = _a === void 0 ? function (obj, proto) {\n    obj.__proto__ = proto;\n    return obj;\n  } : _a;\nvar InvariantError = /** @class */function (_super) {\n  __extends(InvariantError, _super);\n  function InvariantError(message) {\n    if (message === void 0) {\n      message = genericMessage;\n    }\n    var _this = _super.call(this, typeof message === \"number\" ? genericMessage + \": \" + message + \" (see https://github.com/apollographql/invariant-packages)\" : message) || this;\n    _this.framesToPop = 1;\n    _this.name = genericMessage;\n    setPrototypeOf(_this, InvariantError.prototype);\n    return _this;\n  }\n  return InvariantError;\n}(Error);\nexport { InvariantError };\nexport function invariant(condition, message) {\n  if (!condition) {\n    throw new InvariantError(message);\n  }\n}\nvar verbosityLevels = [\"debug\", \"log\", \"warn\", \"error\", \"silent\"];\nvar verbosityLevel = verbosityLevels.indexOf(\"log\");\nfunction wrapConsoleMethod(name) {\n  return function () {\n    if (verbosityLevels.indexOf(name) >= verbosityLevel) {\n      // Default to console.log if this host environment happens not to provide\n      // all the console.* methods we need.\n      var method = console[name] || console.log;\n      return method.apply(console, arguments);\n    }\n  };\n}\n(function (invariant) {\n  invariant.debug = wrapConsoleMethod(\"debug\");\n  invariant.log = wrapConsoleMethod(\"log\");\n  invariant.warn = wrapConsoleMethod(\"warn\");\n  invariant.error = wrapConsoleMethod(\"error\");\n})(invariant || (invariant = {}));\nexport function setVerbosity(level) {\n  var old = verbosityLevels[verbosityLevel];\n  verbosityLevel = Math.max(0, verbosityLevels.indexOf(level));\n  return old;\n}\nexport default invariant;\n//# sourceMappingURL=invariant.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}