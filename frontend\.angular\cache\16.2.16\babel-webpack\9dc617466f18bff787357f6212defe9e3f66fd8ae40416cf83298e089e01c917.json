{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"src/app/services/file.service\";\nimport * as i4 from \"@app/services/rendus.service\";\nimport * as i5 from \"@angular/common\";\nfunction DetailProjectComponent_div_54_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65)(2, \"div\", 66)(3, \"div\", 67)(4, \"div\", 29);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 30);\n    i0.ɵɵelement(6, \"path\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"div\", 69)(8, \"p\", 70);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 71);\n    i0.ɵɵtext(11, \"Document\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"a\", 72)(13, \"div\", 16);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(14, \"svg\", 73);\n    i0.ɵɵelement(15, \"path\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"T\\u00E9l\\u00E9charger\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const file_r7 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getFileName(file_r7), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r6.getFileUrl(file_r7), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailProjectComponent_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtemplate(1, DetailProjectComponent_div_54_div_1_Template, 18, 2, \"div\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.projet.fichiers);\n  }\n}\nfunction DetailProjectComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 76)(2, \"div\", 77);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 78);\n    i0.ɵɵelement(4, \"path\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"p\", 22);\n    i0.ɵɵtext(6, \"Aucun fichier joint \\u00E0 ce projet\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DetailProjectComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"div\", 80)(3, \"div\", 81)(4, \"div\", 82);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailProjectComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 83)(2, \"div\", 23)(3, \"div\", 84);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 85);\n    i0.ɵɵelement(5, \"path\", 86);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"div\")(7, \"h3\", 87);\n    i0.ɵɵtext(8, \"Statistiques\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 88);\n    i0.ɵɵtext(10, \"Suivi des rendus\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 89)(12, \"div\")(13, \"div\", 90)(14, \"span\", 91);\n    i0.ɵɵtext(15, \"Progression globale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 92);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 93);\n    i0.ɵɵelement(19, \"div\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 95)(21, \"span\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 96)(26, \"div\", 97)(27, \"div\", 66)(28, \"div\")(29, \"p\", 98);\n    i0.ɵɵtext(30, \"Rendus\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"p\", 99);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 100);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(34, \"svg\", 101);\n    i0.ɵɵelement(35, \"path\", 102);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(36, \"div\", 103)(37, \"div\", 66)(38, \"div\")(39, \"p\", 104);\n    i0.ɵɵtext(40, \"En attente\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"p\", 105);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 106);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(44, \"svg\", 107);\n    i0.ɵɵelement(45, \"path\", 21);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(46, \"div\", 108)(47, \"div\", 66)(48, \"div\")(49, \"p\", 109);\n    i0.ɵɵtext(50, \"Taux\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\", 110);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 111);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(54, \"svg\", 42);\n    i0.ɵɵelement(55, \"path\", 112);\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(17);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r3.etudiantsRendus.length, \"/\", ctx_r3.totalEtudiants, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r3.getProgressPercentage(), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getProgressPercentage(), \"% compl\\u00E9t\\u00E9\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getRemainingDays(), \" jours restants\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r3.etudiantsRendus.length);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r3.totalEtudiants - ctx_r3.etudiantsRendus.length);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getProgressPercentage(), \"%\");\n  }\n}\nfunction DetailProjectComponent_div_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"div\", 114);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 115)(4, \"div\", 19);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 71);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 116);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 117);\n    i0.ɵɵelement(10, \"path\", 102);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const rendu_r8 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getStudentInitials(rendu_r8.etudiant), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getStudentName(rendu_r8.etudiant), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.formatDate(rendu_r8.dateRendu), \" \");\n  }\n}\nfunction DetailProjectComponent_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"div\", 76)(2, \"div\", 77);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 78);\n    i0.ɵɵelement(4, \"path\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"p\", 22);\n    i0.ɵɵtext(6, \"Aucun rendu pour le moment\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/admin/projects/editProjet\", a1];\n};\nconst _c1 = function () {\n  return [\"/admin/projects/rendus\"];\n};\nconst _c2 = function (a0) {\n  return {\n    projetId: a0\n  };\n};\nexport let DetailProjectComponent = /*#__PURE__*/(() => {\n  class DetailProjectComponent {\n    constructor(route, router, projectService, fileService, rendusService) {\n      this.route = route;\n      this.router = router;\n      this.projectService = projectService;\n      this.fileService = fileService;\n      this.rendusService = rendusService;\n      this.projet = null;\n      this.rendus = [];\n      this.totalEtudiants = 0;\n      this.etudiantsRendus = [];\n      this.derniersRendus = [];\n      this.isLoading = true;\n    }\n    ngOnInit() {\n      const id = this.route.snapshot.paramMap.get('id');\n      if (id) {\n        this.loadProjectData(id);\n      }\n    }\n    loadProjectData(id) {\n      this.isLoading = true;\n      // Charger les données du projet\n      this.projectService.getProjetById(id).subscribe({\n        next: data => {\n          this.projet = data;\n          this.loadProjectStatistics(id);\n        },\n        error: err => {\n          console.error('Erreur lors du chargement du projet:', err);\n          this.isLoading = false;\n        }\n      });\n    }\n    loadProjectStatistics(projetId) {\n      // Charger les rendus pour ce projet\n      this.rendusService.getRendusByProjet(projetId).subscribe({\n        next: rendus => {\n          this.rendus = rendus;\n          this.etudiantsRendus = rendus.filter(rendu => rendu.etudiant);\n          // Trier par date pour avoir les derniers rendus\n          this.derniersRendus = [...this.etudiantsRendus].sort((a, b) => new Date(b.dateRendu).getTime() - new Date(a.dateRendu).getTime()).slice(0, 5); // Prendre les 5 derniers\n          // Pour le total d'étudiants, on peut estimer ou récupérer depuis le backend\n          // Pour l'instant, on utilise le nombre d'étudiants uniques qui ont rendu + estimation\n          const etudiantsUniques = new Set(this.etudiantsRendus.map(r => r.etudiant._id || r.etudiant));\n          this.totalEtudiants = Math.max(etudiantsUniques.size, this.estimateStudentCount());\n          this.isLoading = false;\n        },\n        error: err => {\n          console.error('Erreur lors du chargement des statistiques:', err);\n          this.isLoading = false;\n        }\n      });\n    }\n    estimateStudentCount() {\n      // Estimation basée sur le groupe du projet\n      const groupe = this.projet?.groupe?.toLowerCase();\n      if (groupe?.includes('1c')) return 25; // Première année\n      if (groupe?.includes('2c')) return 20; // Deuxième année\n      if (groupe?.includes('3c')) return 15; // Troisième année\n      return 20; // Valeur par défaut\n    }\n\n    getFileUrl(filePath) {\n      return this.fileService.getDownloadUrl(filePath);\n    }\n    deleteProjet(id) {\n      if (!id) return;\n      if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {\n        this.projectService.deleteProjet(id).subscribe({\n          next: () => {\n            alert('Projet supprimé avec succès');\n            this.router.navigate(['/admin/projects']);\n          },\n          error: err => {\n            console.error('Erreur lors de la suppression du projet', err);\n            alert('Erreur lors de la suppression du projet');\n          }\n        });\n      }\n    }\n    formatDate(date) {\n      const d = new Date(date);\n      return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;\n    }\n    getFileName(filePath) {\n      if (!filePath) return 'Fichier';\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        return parts[parts.length - 1];\n      }\n      return filePath;\n    }\n    getProjectStatus() {\n      if (!this.projet?.dateLimite) return 'En cours';\n      const now = new Date();\n      const deadline = new Date(this.projet.dateLimite);\n      if (deadline < now) {\n        return 'Expiré';\n      } else if (deadline.getTime() - now.getTime() < 7 * 24 * 60 * 60 * 1000) {\n        return 'Urgent';\n      } else {\n        return 'Actif';\n      }\n    }\n    getStatusClass() {\n      const status = this.getProjectStatus();\n      switch (status) {\n        case 'Actif':\n          return 'bg-success/10 dark:bg-dark-accent-secondary/20 text-success dark:text-dark-accent-secondary border border-success/20 dark:border-dark-accent-secondary/30';\n        case 'Urgent':\n          return 'bg-warning/10 dark:bg-warning/20 text-warning dark:text-warning border border-warning/20 dark:border-warning/30';\n        case 'Expiré':\n          return 'bg-danger/10 dark:bg-danger-dark/20 text-danger dark:text-danger-dark border border-danger/20 dark:border-danger-dark/30';\n        default:\n          return 'bg-info/10 dark:bg-dark-accent-primary/20 text-info dark:text-dark-accent-primary border border-info/20 dark:border-dark-accent-primary/30';\n      }\n    }\n    getProgressPercentage() {\n      if (this.totalEtudiants === 0) return 0;\n      return Math.round(this.etudiantsRendus.length / this.totalEtudiants * 100);\n    }\n    getRemainingDays() {\n      if (!this.projet?.dateLimite) return 0;\n      const now = new Date();\n      const deadline = new Date(this.projet.dateLimite);\n      const diffTime = deadline.getTime() - now.getTime();\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n      return Math.max(0, diffDays);\n    }\n    getStudentInitials(etudiant) {\n      if (!etudiant) return '??';\n      // Priorité 1: firstName + lastName\n      const firstName = etudiant.firstName || etudiant.prenom || '';\n      const lastName = etudiant.lastName || etudiant.nom || '';\n      if (firstName && lastName) {\n        return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();\n      }\n      // Priorité 2: fullName\n      const fullName = etudiant.fullName || etudiant.name || '';\n      if (fullName) {\n        const parts = fullName.trim().split(' ');\n        if (parts.length >= 2) {\n          return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();\n        } else {\n          return fullName.substring(0, 2).toUpperCase();\n        }\n      }\n      // Priorité 3: firstName seul\n      if (firstName) {\n        return firstName.substring(0, 2).toUpperCase();\n      }\n      return '??';\n    }\n    getStudentName(etudiant) {\n      if (!etudiant) return 'Utilisateur inconnu';\n      // Priorité 1: firstName + lastName\n      const firstName = etudiant.firstName || etudiant.prenom || '';\n      const lastName = etudiant.lastName || etudiant.nom || '';\n      if (firstName && lastName) {\n        return `${firstName} ${lastName}`.trim();\n      }\n      // Priorité 2: fullName\n      const fullName = etudiant.fullName || etudiant.name || '';\n      if (fullName) {\n        return fullName.trim();\n      }\n      // Priorité 3: firstName seul\n      if (firstName) {\n        return firstName.trim();\n      }\n      // Priorité 4: email comme fallback\n      if (etudiant.email) {\n        return etudiant.email;\n      }\n      return 'Utilisateur inconnu';\n    }\n    static {\n      this.ɵfac = function DetailProjectComponent_Factory(t) {\n        return new (t || DetailProjectComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.FileService), i0.ɵɵdirectiveInject(i4.RendusService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DetailProjectComponent,\n        selectors: [[\"app-detail-project\"]],\n        decls: 98,\n        vars: 23,\n        consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [\"routerLink\", \"/admin/projects/list-project\", 1, \"hover:text-primary\", \"dark:hover:text-dark-accent-primary\", \"transition-colors\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"text-primary\", \"dark:text-dark-accent-primary\", \"font-medium\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mb-6\", \"lg:mb-0\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [1, \"text-3xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mt-2\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-warning\", \"dark:text-warning\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"px-4\", \"py-2\", \"rounded-xl\", \"text-sm\", \"font-medium\", 3, \"ngClass\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-3\", \"gap-8\"], [1, \"lg:col-span-2\", \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-4\"], [1, \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h7\"], [1, \"text-lg\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"prose\", \"prose-gray\", \"dark:prose-invert\", \"max-w-none\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"leading-relaxed\"], [1, \"bg-secondary/10\", \"dark:bg-dark-accent-secondary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-secondary\", \"dark:text-dark-accent-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-sm\", \"font-normal\", \"text-text\", \"dark:text-dark-text-secondary\", \"ml-2\"], [\"class\", \"grid grid-cols-1 sm:grid-cols-2 gap-4\", 4, \"ngIf\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [1, \"bg-info/10\", \"dark:bg-dark-accent-primary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-info\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-3\", \"gap-4\"], [1, \"group\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"routerLink\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"group\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"routerLink\", \"queryParams\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [1, \"group\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-danger\", \"to-danger-dark\", \"dark:from-danger-dark\", \"dark:to-danger\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"space-y-6\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"overflow-hidden\"], [\"class\", \"p-6\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"p-6\"], [1, \"space-y-3\"], [\"class\", \"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-dark-bg-tertiary/50 rounded-xl\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"gap-4\"], [\"class\", \"group\", 4, \"ngFor\", \"ngForOf\"], [1, \"group\"], [1, \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\", \"p-4\", \"border\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"hover:border-primary\", \"dark:hover:border-dark-accent-primary\", \"transition-all\", \"duration-200\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"flex-1\", \"min-w-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\"], [1, \"flex-1\", \"min-w-0\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"truncate\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\"], [\"download\", \"\", 1, \"ml-3\", \"px-3\", \"py-2\", \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"text-primary\", \"dark:text-dark-accent-primary\", \"hover:bg-primary\", \"hover:text-white\", \"dark:hover:bg-dark-accent-primary\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"text-xs\", \"font-medium\", 3, \"href\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4\"], [1, \"text-center\", \"py-8\"], [1, \"bg-gray-100\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\", \"p-6\"], [1, \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"p-3\", \"rounded-lg\", \"inline-flex\", \"items-center\", \"justify-center\", \"mb-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-gray-400\", \"dark:text-dark-text-secondary\"], [1, \"animate-pulse\", \"space-y-4\"], [1, \"h-4\", \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"rounded\", \"w-3/4\"], [1, \"h-4\", \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"rounded\", \"w-1/2\"], [1, \"h-8\", \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"rounded\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"p-6\", \"text-white\"], [1, \"bg-white/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"], [1, \"text-lg\", \"font-semibold\"], [1, \"text-sm\", \"text-white/80\"], [1, \"p-6\", \"space-y-6\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-3\"], [1, \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-sm\", \"font-bold\", \"text-primary\", \"dark:text-dark-accent-primary\"], [1, \"w-full\", \"bg-gray-200\", \"dark:bg-dark-bg-tertiary\", \"rounded-full\", \"h-3\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"h-3\", \"rounded-full\", \"transition-all\", \"duration-500\"], [1, \"flex\", \"justify-between\", \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\", \"mt-2\"], [1, \"grid\", \"grid-cols-1\", \"gap-4\"], [1, \"bg-gradient-to-br\", \"from-success/10\", \"to-success/5\", \"dark:from-dark-accent-secondary/20\", \"dark:to-dark-accent-secondary/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-success/20\", \"dark:border-dark-accent-secondary/30\"], [1, \"text-xs\", \"font-medium\", \"text-success\", \"dark:text-dark-accent-secondary\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-success\", \"dark:text-dark-accent-secondary\"], [1, \"bg-success/20\", \"dark:bg-dark-accent-secondary/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-success\", \"dark:text-dark-accent-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"bg-gradient-to-br\", \"from-warning/10\", \"to-warning/5\", \"dark:from-warning/20\", \"dark:to-warning/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-warning/20\", \"dark:border-warning/30\"], [1, \"text-xs\", \"font-medium\", \"text-warning\", \"dark:text-warning\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-warning\", \"dark:text-warning\"], [1, \"bg-warning/20\", \"dark:bg-warning/30\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-warning\", \"dark:text-warning\"], [1, \"bg-gradient-to-br\", \"from-info/10\", \"to-info/5\", \"dark:from-dark-accent-primary/20\", \"dark:to-dark-accent-primary/10\", \"rounded-xl\", \"p-4\", \"border\", \"border-info/20\", \"dark:border-dark-accent-primary/30\"], [1, \"text-xs\", \"font-medium\", \"text-info\", \"dark:text-dark-accent-primary\", \"uppercase\", \"tracking-wider\"], [1, \"text-2xl\", \"font-bold\", \"text-info\", \"dark:text-dark-accent-primary\"], [1, \"bg-info/20\", \"dark:bg-dark-accent-primary/30\", \"p-2\", \"rounded-lg\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"p-3\", \"bg-gray-50\", \"dark:bg-dark-bg-tertiary/50\", \"rounded-xl\"], [1, \"h-10\", \"w-10\", \"rounded-xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-sm\", \"font-bold\", \"shadow-lg\"], [1, \"flex-1\"], [1, \"bg-success/10\", \"dark:bg-dark-accent-secondary/20\", \"p-1.5\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-success\", \"dark:text-dark-accent-secondary\"]],\n        template: function DetailProjectComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"nav\", 3)(4, \"a\", 4);\n            i0.ɵɵtext(5, \"Projets\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(6, \"svg\", 5);\n            i0.ɵɵelement(7, \"path\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(8, \"span\", 7);\n            i0.ɵɵtext(9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(14, \"svg\", 12);\n            i0.ɵɵelement(15, \"path\", 13);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(16, \"div\")(17, \"h1\", 14);\n            i0.ɵɵtext(18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"div\", 15)(20, \"div\", 16);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(21, \"svg\", 17);\n            i0.ɵɵelement(22, \"path\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(23, \"span\", 19);\n            i0.ɵɵtext(24);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"div\", 16);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(26, \"svg\", 20);\n            i0.ɵɵelement(27, \"path\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(28, \"span\", 22);\n            i0.ɵɵtext(29);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(30, \"div\", 23)(31, \"span\", 24);\n            i0.ɵɵtext(32);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(33, \"div\", 25)(34, \"div\", 26)(35, \"div\", 27)(36, \"div\", 28)(37, \"div\", 29);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(38, \"svg\", 30);\n            i0.ɵɵelement(39, \"path\", 31);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(40, \"h3\", 32);\n            i0.ɵɵtext(41, \"Description du projet\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(42, \"div\", 33)(43, \"p\", 34);\n            i0.ɵɵtext(44);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(45, \"div\", 27)(46, \"div\", 28)(47, \"div\", 35);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(48, \"svg\", 36);\n            i0.ɵɵelement(49, \"path\", 37);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(50, \"h3\", 32);\n            i0.ɵɵtext(51, \" Fichiers joints \");\n            i0.ɵɵelementStart(52, \"span\", 38);\n            i0.ɵɵtext(53);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(54, DetailProjectComponent_div_54_Template, 2, 1, \"div\", 39);\n            i0.ɵɵtemplate(55, DetailProjectComponent_div_55_Template, 7, 0, \"div\", 40);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"div\", 27)(57, \"div\", 28)(58, \"div\", 41);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(59, \"svg\", 42);\n            i0.ɵɵelement(60, \"path\", 43)(61, \"path\", 44);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(62, \"h3\", 32);\n            i0.ɵɵtext(63, \"Actions disponibles\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(64, \"div\", 45)(65, \"a\", 46)(66, \"div\", 47);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(67, \"svg\", 48);\n            i0.ɵɵelement(68, \"path\", 49);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(69, \"span\");\n            i0.ɵɵtext(70, \"Modifier\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(71, \"a\", 50)(72, \"div\", 47);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(73, \"svg\", 48);\n            i0.ɵɵelement(74, \"path\", 51);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(75, \"span\");\n            i0.ɵɵtext(76, \"Voir rendus\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(77, \"button\", 52);\n            i0.ɵɵlistener(\"click\", function DetailProjectComponent_Template_button_click_77_listener() {\n              return ctx.deleteProjet(ctx.projet == null ? null : ctx.projet._id);\n            });\n            i0.ɵɵelementStart(78, \"div\", 47);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(79, \"svg\", 48);\n            i0.ɵɵelement(80, \"path\", 53);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(81, \"span\");\n            i0.ɵɵtext(82, \"Supprimer\");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(83, \"div\", 54)(84, \"div\", 55);\n            i0.ɵɵtemplate(85, DetailProjectComponent_div_85_Template, 5, 0, \"div\", 56);\n            i0.ɵɵtemplate(86, DetailProjectComponent_div_86_Template, 56, 9, \"div\", 57);\n            i0.ɵɵelementStart(87, \"div\", 58)(88, \"div\", 59)(89, \"div\", 28)(90, \"div\", 35);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(91, \"svg\", 36);\n            i0.ɵɵelement(92, \"path\", 21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(93, \"h3\", 32);\n            i0.ɵɵtext(94, \"Derniers rendus\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(95, \"div\", 60);\n            i0.ɵɵtemplate(96, DetailProjectComponent_div_96_Template, 11, 3, \"div\", 61);\n            i0.ɵɵtemplate(97, DetailProjectComponent_div_97_Template, 7, 0, \"div\", 40);\n            i0.ɵɵelementEnd()()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.titre) || \"D\\u00E9tails du projet\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.projet == null ? null : ctx.projet.titre) || \"Chargement...\", \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.groupe) || \"Tous les groupes\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate((ctx.projet == null ? null : ctx.projet.dateLimite) ? ctx.formatDate(ctx.projet == null ? null : ctx.projet.dateLimite) : \"Pas de date limite\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngClass\", ctx.getStatusClass());\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getProjectStatus(), \" \");\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate1(\" \", (ctx.projet == null ? null : ctx.projet.description) || \"Aucune description fournie pour ce projet.\", \" \");\n            i0.ɵɵadvance(9);\n            i0.ɵɵtextInterpolate2(\" (\", (ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) || 0, \" fichier\", ((ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) || 0) > 1 ? \"s\" : \"\", \") \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", (ctx.projet == null ? null : ctx.projet.fichiers == null ? null : ctx.projet.fichiers.length) > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !(ctx.projet == null ? null : ctx.projet.fichiers) || ctx.projet.fichiers.length === 0);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(18, _c0, ctx.projet == null ? null : ctx.projet._id));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(20, _c1))(\"queryParams\", i0.ɵɵpureFunction1(21, _c2, ctx.projet == null ? null : ctx.projet._id));\n            i0.ɵɵadvance(14);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngForOf\", ctx.derniersRendus);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.derniersRendus || ctx.derniersRendus.length === 0);\n          }\n        },\n        dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.RouterLink]\n      });\n    }\n  }\n  return DetailProjectComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}