{"ast": null, "code": "export function buildMatchPatternFn(args) {\n  return (string, options = {}) => {\n    const matchResult = string.match(args.matchPattern);\n    if (!matchResult) return null;\n    const matchedString = matchResult[0];\n    const parseResult = string.match(args.parsePattern);\n    if (!parseResult) return null;\n    let value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n\n    // [TODO] I challenge you to fix the type\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    const rest = string.slice(matchedString.length);\n    return {\n      value,\n      rest\n    };\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}