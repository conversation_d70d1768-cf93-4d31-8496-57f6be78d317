{"ast": null, "code": "'use strict';\n\nconst ReactNativeFile = require('./ReactNativeFile.js');\n\n/**\n * Checks if a value is an [extractable file]{@link ExtractableFile}.\n * @kind function\n * @name isExtractableFile\n * @type {ExtractableFileMatcher}\n * @param {*} value Value to check.\n * @returns {boolean} Is the value an [extractable file]{@link ExtractableFile}.\n * @example <caption>Ways to `import`.</caption>\n * ```js\n * import { isExtractableFile } from 'extract-files';\n * ```\n *\n * ```js\n * import isExtractableFile from 'extract-files/public/isExtractableFile.js';\n * ```\n * @example <caption>Ways to `require`.</caption>\n * ```js\n * const { isExtractableFile } = require('extract-files');\n * ```\n *\n * ```js\n * const isExtractableFile = require('extract-files/public/isExtractableFile.js');\n * ```\n */\nmodule.exports = function isExtractableFile(value) {\n  return typeof File !== 'undefined' && value instanceof File || typeof Blob !== 'undefined' && value instanceof Blob || value instanceof ReactNativeFile;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}