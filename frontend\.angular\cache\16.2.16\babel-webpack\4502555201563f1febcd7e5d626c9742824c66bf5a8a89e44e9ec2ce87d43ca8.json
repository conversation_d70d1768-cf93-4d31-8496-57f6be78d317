{"ast": null, "code": "import { DatePipe } from '@angular/common';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/rendus.service\";\nimport * as i2 from \"@app/services/projets.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction ListRendusComponent_option_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groupe_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", groupe_r6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(groupe_r6);\n  }\n}\nfunction ListRendusComponent_option_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const projet_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", projet_r7._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(projet_r7.titre);\n  }\n}\nfunction ListRendusComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵelement(2, \"div\", 42)(3, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 44);\n    i0.ɵɵtext(5, \"Chargement des rendus...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ListRendusComponent_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 47);\n    i0.ɵɵelement(3, \"path\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"p\", 49);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.error);\n  }\n}\nfunction ListRendusComponent_div_71_div_1__svg_path_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 31);\n  }\n}\nfunction ListRendusComponent_div_71_div_1__svg_path_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 78);\n  }\n}\nfunction ListRendusComponent_div_71_div_1_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 79);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 80);\n    i0.ɵɵelement(3, \"path\", 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"div\")(5, \"p\", 68);\n    i0.ɵɵtext(6, \"Score\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 82);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const rendu_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", ctx_r12.getScoreColorClass(ctx_r12.getScoreTotal(rendu_r9)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.getScoreTotal(rendu_r9), \"/20 \");\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/admin/projects/evaluation-details\", a1];\n};\nfunction ListRendusComponent_div_71_div_1_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"a\", 83)(2, \"div\", 84);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 85);\n    i0.ɵɵelement(4, \"path\", 86)(5, \"path\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"Voir l'\\u00E9valuation\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function ListRendusComponent_div_71_div_1_div_47_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const rendu_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.navigateToEditEvaluation(rendu_r9._id));\n    });\n    i0.ɵɵelementStart(9, \"div\", 84);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(10, \"svg\", 85);\n    i0.ɵɵelement(11, \"path\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Modifier\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const rendu_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, rendu_r9._id));\n  }\n}\nfunction ListRendusComponent_div_71_div_1_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function ListRendusComponent_div_71_div_1_div_48_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const rendu_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.evaluerRendu(rendu_r9._id, \"manual\"));\n    });\n    i0.ɵɵelementStart(2, \"div\", 84);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 85);\n    i0.ɵɵelement(4, \"path\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"\\u00C9valuer manuellement\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function ListRendusComponent_div_71_div_1_div_48_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const rendu_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.evaluerRendu(rendu_r9._id, \"ai\"));\n    });\n    i0.ɵɵelementStart(8, \"div\", 84);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 85);\n    i0.ɵɵelement(10, \"path\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"\\u00C9valuer par IA\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ListRendusComponent_div_71_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53)(2, \"div\", 4)(3, \"div\", 41)(4, \"div\", 54);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 55);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(7, \"svg\", 56);\n    i0.ɵɵelement(8, \"path\", 57);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"div\", 58)(10, \"h3\", 59);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 60);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 61)(15, \"div\", 62);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(16, \"svg\", 25);\n    i0.ɵɵelement(17, \"path\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(18, \"span\", 63);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 62);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(21, \"svg\", 25);\n    i0.ɵɵelement(22, \"path\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(23, \"span\", 63);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(25, \"div\", 65)(26, \"div\", 24)(27, \"div\", 66);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 67);\n    i0.ɵɵelement(29, \"path\", 64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(30, \"div\")(31, \"p\", 68);\n    i0.ɵɵtext(32, \"Soumis le\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 69);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 24)(36, \"div\", 70);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(37, \"svg\", 71);\n    i0.ɵɵtemplate(38, ListRendusComponent_div_71_div_1__svg_path_38_Template, 1, 0, \"path\", 72);\n    i0.ɵɵtemplate(39, ListRendusComponent_div_71_div_1__svg_path_39_Template, 1, 0, \"path\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(40, \"div\")(41, \"p\", 68);\n    i0.ɵɵtext(42, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\", 74);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(45, ListRendusComponent_div_71_div_1_div_45_Template, 9, 2, \"div\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 76);\n    i0.ɵɵtemplate(47, ListRendusComponent_div_71_div_1_div_47_Template, 14, 3, \"div\", 77);\n    i0.ɵɵtemplate(48, ListRendusComponent_div_71_div_1_div_48_Template, 13, 0, \"div\", 77);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const rendu_r9 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getInitials(rendu_r9.etudiant), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getStudentName(rendu_r9.etudiant), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((rendu_r9.etudiant == null ? null : rendu_r9.etudiant.email) || \"Email non disponible\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getGroupName(rendu_r9.etudiant), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(rendu_r9.projet == null ? null : rendu_r9.projet.titre);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r8.formatDate(rendu_r9.dateSoumission));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r8.getStatusIconClass(rendu_r9));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", rendu_r9.evaluation);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !rendu_r9.evaluation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r8.getStatusBadgeClass(rendu_r9));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getStatutEvaluation(rendu_r9), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", rendu_r9.evaluation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", rendu_r9.evaluation);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !rendu_r9.evaluation);\n  }\n}\nfunction ListRendusComponent_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtemplate(1, ListRendusComponent_div_71_div_1_Template, 49, 14, \"div\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.filteredRendus);\n  }\n}\nfunction ListRendusComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"div\", 94)(2, \"div\", 95);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 96);\n    i0.ɵɵelement(4, \"path\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"h3\", 98);\n    i0.ɵɵtext(6, \"Aucun rendu trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 99);\n    i0.ɵɵtext(8, \"Aucun rendu ne correspond \\u00E0 vos crit\\u00E8res de filtrage actuels.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function ListRendusComponent_div_72_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.resetFilters());\n    });\n    i0.ɵɵelementStart(10, \"div\", 84);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 71);\n    i0.ɵɵelement(12, \"path\", 101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"R\\u00E9initialiser les filtres\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nexport let ListRendusComponent = /*#__PURE__*/(() => {\n  class ListRendusComponent {\n    constructor(rendusService, projetService, router, datePipe) {\n      this.rendusService = rendusService;\n      this.projetService = projetService;\n      this.router = router;\n      this.datePipe = datePipe;\n      this.rendus = [];\n      this.filteredRendus = [];\n      this.isLoading = true;\n      this.error = '';\n      this.searchTerm = '';\n      this.filterStatus = 'all';\n      // Nouvelles propriétés pour les filtres\n      this.filtreGroupe = '';\n      this.filtreProjet = '';\n      this.groupes = [];\n      this.projets = [];\n    }\n    ngOnInit() {\n      this.loadRendus();\n      this.loadProjets();\n      this.extractGroupes();\n    }\n    loadRendus() {\n      this.isLoading = true;\n      this.rendusService.getAllRendus().subscribe({\n        next: data => {\n          this.rendus = data;\n          this.extractGroupes();\n          this.applyFilters();\n          this.isLoading = false;\n        },\n        error: err => {\n          console.error('Erreur lors du chargement des rendus', err);\n          this.error = 'Impossible de charger les rendus. Veuillez réessayer plus tard.';\n          this.isLoading = false;\n        }\n      });\n    }\n    loadProjets() {\n      this.projetService.getProjets().subscribe({\n        next: data => {\n          this.projets = data;\n        },\n        error: err => {\n          console.error('Erreur lors du chargement des projets', err);\n        }\n      });\n    }\n    extractGroupes() {\n      // Extraire les groupes uniques des rendus\n      if (this.rendus && this.rendus.length > 0) {\n        const groupesSet = new Set();\n        this.rendus.forEach(rendu => {\n          if (rendu.etudiant) {\n            const groupeName = this.getGroupName(rendu.etudiant);\n            if (groupeName && groupeName !== 'Non spécifié') {\n              groupesSet.add(groupeName);\n            }\n          }\n        });\n        this.groupes = Array.from(groupesSet);\n      }\n    }\n    applyFilters() {\n      let results = this.rendus;\n      // Filtre par statut d'évaluation\n      if (this.filterStatus === 'evaluated') {\n        results = results.filter(rendu => rendu.evaluation && rendu.evaluation.scores);\n      } else if (this.filterStatus === 'pending') {\n        results = results.filter(rendu => !rendu.evaluation || !rendu.evaluation.scores);\n      }\n      // Filtre par terme de recherche\n      if (this.searchTerm.trim() !== '') {\n        const term = this.searchTerm.toLowerCase().trim();\n        results = results.filter(rendu => {\n          const etudiant = rendu.etudiant;\n          if (!etudiant) return false;\n          const firstName = (etudiant.firstName || etudiant.prenom || '').toLowerCase();\n          const lastName = (etudiant.lastName || etudiant.nom || '').toLowerCase();\n          const fullName = (etudiant.fullName || etudiant.name || etudiant.username || '').toLowerCase();\n          const email = (etudiant.email || '').toLowerCase();\n          const projet = (rendu.projet?.titre || '').toLowerCase();\n          return firstName.includes(term) || lastName.includes(term) || fullName.includes(term) || email.includes(term) || projet.includes(term);\n        });\n      }\n      // Filtre par groupe\n      if (this.filtreGroupe) {\n        results = results.filter(rendu => {\n          const groupeName = this.getGroupName(rendu.etudiant);\n          return groupeName === this.filtreGroupe;\n        });\n      }\n      // Filtre par projet\n      if (this.filtreProjet) {\n        results = results.filter(rendu => rendu.projet?._id === this.filtreProjet);\n      }\n      this.filteredRendus = results;\n    }\n    // Méthode pour la compatibilité avec le template\n    filtrerRendus() {\n      return this.filteredRendus;\n    }\n    onSearchChange() {\n      this.applyFilters();\n    }\n    setFilterStatus(status) {\n      this.filterStatus = status;\n      this.applyFilters();\n    }\n    evaluateRendu(renduId) {\n      this.router.navigate(['/admin/projects/evaluate', renduId]);\n    }\n    // Méthode pour la compatibilité avec le template\n    evaluerRendu(renduId, mode) {\n      // Rediriger vers la page d'évaluation avec le mode approprié\n      this.router.navigate(['/admin/projects/evaluate', renduId], {\n        queryParams: {\n          mode: mode\n        }\n      });\n    }\n    viewEvaluationDetails(renduId) {\n      this.router.navigate(['/admin/projects/evaluation-details', renduId]);\n    }\n    getStatusClass(rendu) {\n      if (rendu.evaluation && rendu.evaluation.scores) {\n        return 'bg-green-100 text-green-800';\n      }\n      return 'bg-yellow-100 text-yellow-800';\n    }\n    // Méthode pour la compatibilité avec le template\n    getClasseStatut(rendu) {\n      return this.getStatusClass(rendu);\n    }\n    getStatusText(rendu) {\n      // Vérifier si l'évaluation existe de plusieurs façons\n      if (rendu.evaluation && rendu.evaluation._id) {\n        return 'Évalué';\n      }\n      if (rendu.statut === 'évalué') {\n        return 'Évalué';\n      }\n      return 'En attente';\n    }\n    // Méthode pour la compatibilité avec le template\n    getStatutEvaluation(rendu) {\n      return this.getStatusText(rendu);\n    }\n    getScoreTotal(rendu) {\n      if (!rendu.evaluation || !rendu.evaluation.scores) return 0;\n      const scores = rendu.evaluation.scores;\n      return scores.structure + scores.pratiques + scores.fonctionnalite + scores.originalite;\n    }\n    getScoreClass(score) {\n      if (score >= 16) return 'text-green-600';\n      if (score >= 12) return 'text-blue-600';\n      if (score >= 8) return 'text-yellow-600';\n      return 'text-red-600';\n    }\n    formatDate(date) {\n      if (!date) return '';\n      return this.datePipe.transform(date, 'dd/MM/yyyy') || '';\n    }\n    navigateToEditEvaluation(renduId) {\n      this.router.navigate(['/admin/projects/edit-evaluation', renduId]);\n    }\n    // Méthodes pour gérer les fichiers\n    getFileUrl(filePath) {\n      if (!filePath) return '';\n      // Extraire uniquement le nom du fichier\n      let fileName = filePath;\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        fileName = parts[parts.length - 1];\n      }\n      // Utiliser la route spécifique pour le téléchargement\n      return `${environment.urlBackend}projets/telecharger/${fileName}`;\n    }\n    getFileName(filePath) {\n      if (!filePath) return 'Fichier';\n      // Si le chemin contient des slashes ou backslashes, prendre la dernière partie\n      if (filePath.includes('/') || filePath.includes('\\\\')) {\n        const parts = filePath.split(/[\\/\\\\]/);\n        return parts[parts.length - 1];\n      }\n      return filePath;\n    }\n    // Nouvelles méthodes pour le design moderne\n    getInitials(etudiant) {\n      if (!etudiant) return '??';\n      // Priorité 1: firstName + lastName (si lastName existe et n'est pas vide)\n      const firstName = etudiant.firstName || '';\n      const lastName = etudiant.lastName || '';\n      if (firstName && lastName && lastName.trim()) {\n        return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();\n      }\n      // Priorité 2: fullName (diviser en mots)\n      const fullName = etudiant.fullName || etudiant.name || etudiant.username || '';\n      if (fullName && fullName.trim()) {\n        const parts = fullName.trim().split(' ');\n        if (parts.length >= 2) {\n          return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();\n        } else {\n          // Si un seul mot, prendre les 2 premières lettres\n          return fullName.substring(0, 2).toUpperCase();\n        }\n      }\n      // Priorité 3: firstName seul (prendre les 2 premières lettres)\n      if (firstName && firstName.trim()) {\n        return firstName.substring(0, 2).toUpperCase();\n      }\n      return '??';\n    }\n    getGroupName(etudiant) {\n      if (!etudiant) return 'Non spécifié';\n      // Si group est un objet (référence populée avec le modèle Group)\n      if (etudiant.group && typeof etudiant.group === 'object' && etudiant.group.name) {\n        return etudiant.group.name;\n      }\n      // Si group est une chaîne directe (valeur ajoutée manuellement)\n      if (etudiant.group && typeof etudiant.group === 'string' && etudiant.group.trim()) {\n        return etudiant.group.trim();\n      }\n      // Fallback vers d'autres champs possibles\n      if (etudiant.groupe && typeof etudiant.groupe === 'string' && etudiant.groupe.trim()) {\n        return etudiant.groupe.trim();\n      }\n      if (etudiant.groupName && typeof etudiant.groupName === 'string' && etudiant.groupName.trim()) {\n        return etudiant.groupName.trim();\n      }\n      if (etudiant.department && typeof etudiant.department === 'string' && etudiant.department.trim()) {\n        return etudiant.department.trim();\n      }\n      return 'Non spécifié';\n    }\n    getStudentName(etudiant) {\n      if (!etudiant) return 'Utilisateur inconnu';\n      // Priorité 1: firstName + lastName (si lastName existe et n'est pas vide)\n      const firstName = etudiant.firstName || '';\n      const lastName = etudiant.lastName || '';\n      if (firstName && lastName && lastName.trim()) {\n        return `${firstName} ${lastName}`.trim();\n      }\n      // Priorité 2: fullName\n      const fullName = etudiant.fullName || etudiant.name || etudiant.username || '';\n      if (fullName && fullName.trim()) {\n        return fullName.trim();\n      }\n      // Priorité 3: firstName seul\n      if (firstName && firstName.trim()) {\n        return firstName.trim();\n      }\n      // Priorité 4: email comme fallback\n      if (etudiant.email) {\n        return etudiant.email;\n      }\n      return 'Utilisateur inconnu';\n    }\n    getEvaluatedCount() {\n      return this.rendus.filter(rendu => rendu.evaluation && rendu.evaluation._id).length;\n    }\n    getStatusIconClass(rendu) {\n      if (rendu.evaluation && rendu.evaluation._id) {\n        return 'bg-success/10 dark:bg-dark-accent-secondary/10 text-success dark:text-dark-accent-secondary';\n      }\n      return 'bg-warning/10 dark:bg-warning/20 text-warning dark:text-warning';\n    }\n    getStatusBadgeClass(rendu) {\n      if (rendu.evaluation && rendu.evaluation._id) {\n        return 'bg-gradient-to-r from-success/20 to-success-dark/20 dark:from-dark-accent-secondary/30 dark:to-dark-accent-secondary/20 text-success-dark dark:text-dark-accent-secondary border border-success/30 dark:border-dark-accent-secondary/40';\n      }\n      return 'bg-gradient-to-r from-warning/20 to-warning/30 dark:from-warning/30 dark:to-warning/20 text-warning-dark dark:text-warning border border-warning/40 dark:border-warning/50';\n    }\n    getScoreColorClass(score) {\n      if (score >= 16) return 'text-success dark:text-dark-accent-secondary';\n      if (score >= 12) return 'text-info dark:text-dark-accent-primary';\n      if (score >= 8) return 'text-warning dark:text-warning';\n      return 'text-danger dark:text-danger-dark';\n    }\n    resetFilters() {\n      this.filtreGroupe = '';\n      this.filtreProjet = '';\n      this.filterStatus = 'all';\n      this.searchTerm = '';\n      this.applyFilters();\n    }\n    static {\n      this.ɵfac = function ListRendusComponent_Factory(t) {\n        return new (t || ListRendusComponent)(i0.ɵɵdirectiveInject(i1.RendusService), i0.ɵɵdirectiveInject(i2.ProjetService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.DatePipe));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ListRendusComponent,\n        selectors: [[\"app-list-rendus\"]],\n        features: [i0.ɵɵProvidersFeature([DatePipe])],\n        decls: 73,\n        vars: 11,\n        consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-dark-bg-primary\", \"transition-colors\", \"duration-300\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"rounded-2xl\", \"p-8\", \"mb-8\", \"shadow-xl\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"bg-white/20\", \"dark:bg-black/20\", \"p-3\", \"rounded-xl\", \"backdrop-blur-sm\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\"], [1, \"text-white/80\"], [1, \"hidden\", \"md:flex\", \"items-center\", \"space-x-4\", \"text-white/80\"], [1, \"text-center\"], [1, \"text-2xl\", \"font-bold\"], [1, \"text-sm\"], [1, \"w-px\", \"h-12\", \"bg-white/20\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"mb-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-6\"], [1, \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [1, \"space-y-2\"], [1, \"block\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 11H5m14-7H3a2 2 0 00-2 2v12a2 2 0 002 2h16a2 2 0 002-2V6a2 2 0 00-2-2z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"value\", \"all\"], [\"value\", \"evaluated\"], [\"value\", \"pending\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"bg-danger/10 dark:bg-danger-dark/20 border border-danger/30 dark:border-danger-dark/40 text-danger dark:text-danger-dark rounded-xl p-4 mb-6 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"space-y-6\", 4, \"ngIf\"], [\"class\", \"text-center py-16\", 4, \"ngIf\"], [3, \"value\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"relative\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-primary/30\", \"dark:border-dark-accent-primary/30\"], [1, \"animate-spin\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-transparent\", \"border-t-primary\", \"dark:border-t-dark-accent-primary\", \"absolute\", \"top-0\", \"left-0\"], [1, \"mt-4\", \"text-text\", \"dark:text-dark-text-secondary\", \"animate-pulse\"], [1, \"bg-danger/10\", \"dark:bg-danger-dark/20\", \"border\", \"border-danger/30\", \"dark:border-danger-dark/40\", \"text-danger\", \"dark:text-danger-dark\", \"rounded-xl\", \"p-4\", \"mb-6\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-danger\", \"dark:text-danger-dark\", \"flex-shrink-0\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"font-medium\"], [1, \"space-y-6\"], [\"class\", \"bg-white/80 dark:bg-dark-bg-secondary/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50 dark:border-dark-bg-tertiary/50 hover:shadow-xl transition-all duration-300 group\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-6\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"hover:shadow-xl\", \"transition-all\", \"duration-300\", \"group\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\", \"space-y-4\", \"lg:space-y-0\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-lg\", \"font-bold\", \"shadow-lg\"], [1, \"absolute\", \"-bottom-1\", \"-right-1\", \"w-6\", \"h-6\", \"bg-gradient-to-r\", \"from-success\", \"to-success-dark\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-3\", \"h-3\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"], [1, \"flex-1\"], [1, \"text-lg\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"flex\", \"items-center\", \"space-x-4\", \"mt-2\"], [1, \"flex\", \"items-center\", \"space-x-1\"], [1, \"text-sm\", \"font-medium\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 13a2 2 0 002 2h6a2 2 0 002-2L16 7\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"sm:items-center\", \"space-y-3\", \"sm:space-y-0\", \"sm:space-x-6\"], [1, \"bg-info/10\", \"dark:bg-dark-accent-primary/10\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-info\", \"dark:text-dark-accent-primary\"], [1, \"text-xs\", \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"p-2\", \"rounded-lg\", 3, \"ngClass\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\", 4, \"ngIf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\", 4, \"ngIf\"], [1, \"inline-flex\", \"items-center\", \"px-3\", \"py-1\", \"rounded-full\", \"text-xs\", \"font-semibold\", 3, \"ngClass\"], [\"class\", \"flex items-center space-x-2\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-2\"], [\"class\", \"flex flex-col sm:flex-row gap-2\", 4, \"ngIf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"bg-success/10\", \"dark:bg-dark-accent-secondary/10\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-success\", \"dark:text-dark-accent-secondary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"], [1, \"text-sm\", \"font-bold\", 3, \"ngClass\"], [1, \"group/btn\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-info\", \"to-primary\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", \"text-center\", 3, \"routerLink\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"group-hover/btn:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"], [1, \"group/btn\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary-dark\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"group/btn\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-success\", \"to-success-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [1, \"group/btn\", \"px-4\", \"py-2\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"], [1, \"text-center\", \"py-16\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-12\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"max-w-md\", \"mx-auto\"], [1, \"bg-gradient-to-br\", \"from-primary/10\", \"to-secondary/10\", \"dark:from-dark-accent-primary/20\", \"dark:to-dark-accent-secondary/20\", \"rounded-2xl\", \"p-6\", \"mb-6\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-16\", \"w-16\", \"mx-auto\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"mb-2\"], [1, \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [1, \"px-6\", \"py-2\", \"bg-gradient-to-r\", \"from-primary\", \"to-primary-dark\", \"dark:from-dark-accent-primary\", \"dark:to-dark-accent-secondary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"]],\n        template: function ListRendusComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(6, \"svg\", 6);\n            i0.ɵɵelement(7, \"path\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(8, \"div\")(9, \"h1\", 8);\n            i0.ɵɵtext(10, \"Liste des Rendus\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"p\", 9);\n            i0.ɵɵtext(12, \"Gestion et \\u00E9valuation des projets \\u00E9tudiants\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 10)(14, \"div\", 11)(15, \"div\", 12);\n            i0.ɵɵtext(16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 13);\n            i0.ɵɵtext(18, \"Total\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(19, \"div\", 14);\n            i0.ɵɵelementStart(20, \"div\", 11)(21, \"div\", 12);\n            i0.ɵɵtext(22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"div\", 13);\n            i0.ɵɵtext(24, \"\\u00C9valu\\u00E9s\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(25, \"div\", 15)(26, \"div\", 16)(27, \"div\", 17);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(28, \"svg\", 18);\n            i0.ɵɵelement(29, \"path\", 19);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(30, \"h2\", 20);\n            i0.ɵɵtext(31, \"Filtres et recherche\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"div\", 21)(33, \"div\", 22)(34, \"label\", 23)(35, \"div\", 24);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(36, \"svg\", 25);\n            i0.ɵɵelement(37, \"path\", 26);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(38, \"span\");\n            i0.ɵɵtext(39, \"Groupe\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(40, \"select\", 27);\n            i0.ɵɵlistener(\"ngModelChange\", function ListRendusComponent_Template_select_ngModelChange_40_listener($event) {\n              return ctx.filtreGroupe = $event;\n            })(\"ngModelChange\", function ListRendusComponent_Template_select_ngModelChange_40_listener() {\n              return ctx.applyFilters();\n            });\n            i0.ɵɵelementStart(41, \"option\", 28);\n            i0.ɵɵtext(42, \"Tous les groupes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(43, ListRendusComponent_option_43_Template, 2, 2, \"option\", 29);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(44, \"div\", 22)(45, \"label\", 23)(46, \"div\", 24);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(47, \"svg\", 25);\n            i0.ɵɵelement(48, \"path\", 30);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(49, \"span\");\n            i0.ɵɵtext(50, \"Projet\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(51, \"select\", 27);\n            i0.ɵɵlistener(\"ngModelChange\", function ListRendusComponent_Template_select_ngModelChange_51_listener($event) {\n              return ctx.filtreProjet = $event;\n            })(\"ngModelChange\", function ListRendusComponent_Template_select_ngModelChange_51_listener() {\n              return ctx.applyFilters();\n            });\n            i0.ɵɵelementStart(52, \"option\", 28);\n            i0.ɵɵtext(53, \"Tous les projets\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(54, ListRendusComponent_option_54_Template, 2, 2, \"option\", 29);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(55, \"div\", 22)(56, \"label\", 23)(57, \"div\", 24);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(58, \"svg\", 25);\n            i0.ɵɵelement(59, \"path\", 31);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(60, \"span\");\n            i0.ɵɵtext(61, \"Statut\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(62, \"select\", 27);\n            i0.ɵɵlistener(\"ngModelChange\", function ListRendusComponent_Template_select_ngModelChange_62_listener($event) {\n              return ctx.filterStatus = $event;\n            })(\"ngModelChange\", function ListRendusComponent_Template_select_ngModelChange_62_listener() {\n              return ctx.applyFilters();\n            });\n            i0.ɵɵelementStart(63, \"option\", 32);\n            i0.ɵɵtext(64, \"Tous les statuts\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"option\", 33);\n            i0.ɵɵtext(66, \"\\u00C9valu\\u00E9s\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"option\", 34);\n            i0.ɵɵtext(68, \"En attente\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(69, ListRendusComponent_div_69_Template, 6, 0, \"div\", 35);\n            i0.ɵɵtemplate(70, ListRendusComponent_div_70_Template, 6, 1, \"div\", 36);\n            i0.ɵɵtemplate(71, ListRendusComponent_div_71_Template, 2, 1, \"div\", 37);\n            i0.ɵɵtemplate(72, ListRendusComponent_div_72_Template, 15, 0, \"div\", 38);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(16);\n            i0.ɵɵtextInterpolate(ctx.rendus.length);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.getEvaluatedCount());\n            i0.ɵɵadvance(18);\n            i0.ɵɵproperty(\"ngModel\", ctx.filtreGroupe);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.groupes);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngModel\", ctx.filtreProjet);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.projets);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngModel\", ctx.filterStatus);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredRendus.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.filteredRendus.length === 0);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i3.RouterLink, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n        styles: [\".loading-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:2rem 0}.error-message[_ngcontent-%COMP%]{color:#dc3545;margin-top:.25rem}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_slideInRight{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@keyframes _ngcontent-%COMP%_scaleIn{0%{opacity:0;transform:scale(.9)}to{opacity:1;transform:scale(1)}}.glass-card[_ngcontent-%COMP%]{backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2)}.dark[_ngcontent-%COMP%]   .glass-card[_ngcontent-%COMP%]{border:1px solid rgba(255,255,255,.1)}.rendu-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out;transition:all .3s cubic-bezier(.4,0,.2,1)}.rendu-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 20px 40px #0000001a}.dark[_ngcontent-%COMP%]   .rendu-card[_ngcontent-%COMP%]:hover{box-shadow:0 20px 40px #0000004d}.btn-modern[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1)}.btn-modern[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.btn-modern[_ngcontent-%COMP%]:hover:before{left:100%}.avatar-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad 0%,#7826b5 100%);transition:all .3s ease}.dark[_ngcontent-%COMP%]   .avatar-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00f7ff 0%,#9d4edd 100%)}.avatar-gradient[_ngcontent-%COMP%]:hover{transform:scale(1.05);box-shadow:0 8px 25px #4f5fad4d}.dark[_ngcontent-%COMP%]   .avatar-gradient[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #00f7ff4d}.status-badge[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_scaleIn .4s ease-out;transition:all .2s ease}.status-badge[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.filter-select[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}.filter-select[_ngcontent-%COMP%]:focus{transform:translateY(-2px);box-shadow:0 8px 25px #4f5fad26}.dark[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]:focus{box-shadow:0 8px 25px #00f7ff26}.header-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f5fad 0%,#7826b5 100%);animation:_ngcontent-%COMP%_slideInRight .8s ease-out}.dark[_ngcontent-%COMP%]   .header-gradient[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00f7ff 0%,#9d4edd 100%)}@media (max-width: 768px){.rendu-card[_ngcontent-%COMP%]{margin-bottom:1rem}.btn-modern[_ngcontent-%COMP%]{width:100%;justify-content:center}.filter-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1rem}}.icon-hover[_ngcontent-%COMP%]{transition:transform .2s ease}.icon-hover[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(5deg)}.tooltip[_ngcontent-%COMP%]{position:relative}.tooltip[_ngcontent-%COMP%]:after{content:attr(data-tooltip);position:absolute;bottom:100%;left:50%;transform:translate(-50%);background:rgba(0,0,0,.8);color:#fff;padding:.5rem;border-radius:.375rem;font-size:.75rem;white-space:nowrap;opacity:0;pointer-events:none;transition:opacity .3s;z-index:1000}.tooltip[_ngcontent-%COMP%]:hover:after{opacity:1}.loading-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.5}}.empty-state[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .8s ease-out}.focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #4f5fad;outline-offset:2px}.dark[_ngcontent-%COMP%]   .focus-visible[_ngcontent-%COMP%]:focus{outline:2px solid #00f7ff}\"]\n      });\n    }\n  }\n  return ListRendusComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}