{"ast": null, "code": "var toString = Object.prototype.toString;\n/**\n * Deeply clones a value to create a new instance.\n */\nexport function cloneDeep(value) {\n  return cloneDeepHelper(value);\n}\nfunction cloneDeepHelper(val, seen) {\n  switch (toString.call(val)) {\n    case \"[object Array]\":\n      {\n        seen = seen || new Map();\n        if (seen.has(val)) return seen.get(val);\n        var copy_1 = val.slice(0);\n        seen.set(val, copy_1);\n        copy_1.forEach(function (child, i) {\n          copy_1[i] = cloneDeepHelper(child, seen);\n        });\n        return copy_1;\n      }\n    case \"[object Object]\":\n      {\n        seen = seen || new Map();\n        if (seen.has(val)) return seen.get(val);\n        // High fidelity polyfills of Object.create and Object.getPrototypeOf are\n        // possible in all JS environments, so we will assume they exist/work.\n        var copy_2 = Object.create(Object.getPrototypeOf(val));\n        seen.set(val, copy_2);\n        Object.keys(val).forEach(function (key) {\n          copy_2[key] = cloneDeepHelper(val[key], seen);\n        });\n        return copy_2;\n      }\n    default:\n      return val;\n  }\n}\n//# sourceMappingURL=cloneDeep.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}