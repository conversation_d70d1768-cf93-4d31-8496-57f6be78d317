{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { getISOWeekYear } from \"./getISOWeekYear.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\n\n/**\n * The {@link lastDayOfISOWeekYear} function options.\n */\n\n/**\n * @name lastDayOfISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Return the last day of an ISO week-numbering year for the given date.\n *\n * @description\n * Return the last day of an ISO week-numbering year,\n * which always starts 3 days before the year's first Thursday.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of an ISO week-numbering year\n *\n * @example\n * // The last day of an ISO week-numbering year for 2 July 2005:\n * const result = lastDayOfISOWeekYear(new Date(2005, 6, 2))\n * //=> Sun Jan 01 2006 00:00:00\n */\nexport function lastDayOfISOWeekYear(date, options) {\n  const year = getISOWeekYear(date, options);\n  const fourthOfJanuary = constructFrom(options?.in || date, 0);\n  fourthOfJanuary.setFullYear(year + 1, 0, 4);\n  fourthOfJanuary.setHours(0, 0, 0, 0);\n  const date_ = startOfISOWeek(fourthOfJanuary, options);\n  date_.setDate(date_.getDate() - 1);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfISOWeekYear;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}