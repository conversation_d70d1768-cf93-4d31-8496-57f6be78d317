{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\nimport { Kind } from '../../language/kinds.mjs';\nimport { isExecutableDefinitionNode } from '../../language/predicates.mjs';\n\n/**\n * Executable definitions\n *\n * A GraphQL document is only valid for execution if all definitions are either\n * operation or fragment definitions.\n *\n * See https://spec.graphql.org/draft/#sec-Executable-Definitions\n */\nexport function ExecutableDefinitionsRule(context) {\n  return {\n    Document(node) {\n      for (const definition of node.definitions) {\n        if (!isExecutableDefinitionNode(definition)) {\n          const defName = definition.kind === Kind.SCHEMA_DEFINITION || definition.kind === Kind.SCHEMA_EXTENSION ? 'schema' : '\"' + definition.name.value + '\"';\n          context.reportError(new GraphQLError(`The ${defName} definition is not executable.`, {\n            nodes: definition\n          }));\n        }\n      }\n      return false;\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}