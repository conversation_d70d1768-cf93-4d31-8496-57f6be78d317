{"ast": null, "code": "import { getLocation } from './location.mjs';\n\n/**\n * Ren<PERSON> a helpful description of the location in the GraphQL Source document.\n */\nexport function printLocation(location) {\n  return printSourceLocation(location.source, getLocation(location.source, location.start));\n}\n/**\n * Render a helpful description of the location in the GraphQL Source document.\n */\n\nexport function printSourceLocation(source, sourceLocation) {\n  const firstLineColumnOffset = source.locationOffset.column - 1;\n  const body = ''.padStart(firstLineColumnOffset) + source.body;\n  const lineIndex = sourceLocation.line - 1;\n  const lineOffset = source.locationOffset.line - 1;\n  const lineNum = sourceLocation.line + lineOffset;\n  const columnOffset = sourceLocation.line === 1 ? firstLineColumnOffset : 0;\n  const columnNum = sourceLocation.column + columnOffset;\n  const locationStr = `${source.name}:${lineNum}:${columnNum}\\n`;\n  const lines = body.split(/\\r\\n|[\\n\\r]/g);\n  const locationLine = lines[lineIndex]; // Special case for minified documents\n\n  if (locationLine.length > 120) {\n    const subLineIndex = Math.floor(columnNum / 80);\n    const subLineColumnNum = columnNum % 80;\n    const subLines = [];\n    for (let i = 0; i < locationLine.length; i += 80) {\n      subLines.push(locationLine.slice(i, i + 80));\n    }\n    return locationStr + printPrefixedLines([[`${lineNum} |`, subLines[0]], ...subLines.slice(1, subLineIndex + 1).map(subLine => ['|', subLine]), ['|', '^'.padStart(subLineColumnNum)], ['|', subLines[subLineIndex + 1]]]);\n  }\n  return locationStr + printPrefixedLines([\n  // Lines specified like this: [\"prefix\", \"string\"],\n  [`${lineNum - 1} |`, lines[lineIndex - 1]], [`${lineNum} |`, locationLine], ['|', '^'.padStart(columnNum)], [`${lineNum + 1} |`, lines[lineIndex + 1]]]);\n}\nfunction printPrefixedLines(lines) {\n  const existingLines = lines.filter(([_, line]) => line !== undefined);\n  const padLen = Math.max(...existingLines.map(([prefix]) => prefix.length));\n  return existingLines.map(([prefix, line]) => prefix.padStart(padLen) + (line ? ' ' + line : '')).join('\\n');\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}