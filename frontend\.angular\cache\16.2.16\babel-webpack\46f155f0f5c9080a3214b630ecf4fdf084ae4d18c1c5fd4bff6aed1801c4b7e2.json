{"ast": null, "code": "import { __assign, __spreadArray } from \"tslib\";\nimport { invariant, newInvariantError } from \"../globals/index.js\";\nimport { BREAK, visit } from \"graphql\";\n/**\n * Returns a query document which adds a single query operation that only\n * spreads the target fragment inside of it.\n *\n * So for example a document of:\n *\n * ```graphql\n * fragment foo on Foo { a b c }\n * ```\n *\n * Turns into:\n *\n * ```graphql\n * { ...foo }\n *\n * fragment foo on Foo { a b c }\n * ```\n *\n * The target fragment will either be the only fragment in the document, or a\n * fragment specified by the provided `fragmentName`. If there is more than one\n * fragment, but a `fragmentName` was not defined then an error will be thrown.\n */\nexport function getFragmentQueryDocument(document, fragmentName) {\n  var actualFragmentName = fragmentName;\n  // Build an array of all our fragment definitions that will be used for\n  // validations. We also do some validations on the other definitions in the\n  // document while building this list.\n  var fragments = [];\n  document.definitions.forEach(function (definition) {\n    // Throw an error if we encounter an operation definition because we will\n    // define our own operation definition later on.\n    if (definition.kind === \"OperationDefinition\") {\n      throw newInvariantError(85, definition.operation, definition.name ? \" named '\".concat(definition.name.value, \"'\") : \"\");\n    }\n    // Add our definition to the fragments array if it is a fragment\n    // definition.\n    if (definition.kind === \"FragmentDefinition\") {\n      fragments.push(definition);\n    }\n  });\n  // If the user did not give us a fragment name then let us try to get a\n  // name from a single fragment in the definition.\n  if (typeof actualFragmentName === \"undefined\") {\n    invariant(fragments.length === 1, 86, fragments.length);\n    actualFragmentName = fragments[0].name.value;\n  }\n  // Generate a query document with an operation that simply spreads the\n  // fragment inside of it.\n  var query = __assign(__assign({}, document), {\n    definitions: __spreadArray([{\n      kind: \"OperationDefinition\",\n      // OperationTypeNode is an enum\n      operation: \"query\",\n      selectionSet: {\n        kind: \"SelectionSet\",\n        selections: [{\n          kind: \"FragmentSpread\",\n          name: {\n            kind: \"Name\",\n            value: actualFragmentName\n          }\n        }]\n      }\n    }], document.definitions, true)\n  });\n  return query;\n}\n// Utility function that takes a list of fragment definitions and makes a hash out of them\n// that maps the name of the fragment to the fragment definition.\nexport function createFragmentMap(fragments) {\n  if (fragments === void 0) {\n    fragments = [];\n  }\n  var symTable = {};\n  fragments.forEach(function (fragment) {\n    symTable[fragment.name.value] = fragment;\n  });\n  return symTable;\n}\nexport function getFragmentFromSelection(selection, fragmentMap) {\n  switch (selection.kind) {\n    case \"InlineFragment\":\n      return selection;\n    case \"FragmentSpread\":\n      {\n        var fragmentName = selection.name.value;\n        if (typeof fragmentMap === \"function\") {\n          return fragmentMap(fragmentName);\n        }\n        var fragment = fragmentMap && fragmentMap[fragmentName];\n        invariant(fragment, 87, fragmentName);\n        return fragment || null;\n      }\n    default:\n      return null;\n  }\n}\nexport function isFullyUnmaskedOperation(document) {\n  var isUnmasked = true;\n  visit(document, {\n    FragmentSpread: function (node) {\n      isUnmasked = !!node.directives && node.directives.some(function (directive) {\n        return directive.name.value === \"unmask\";\n      });\n      if (!isUnmasked) {\n        return BREAK;\n      }\n    }\n  });\n  return isUnmasked;\n}\n//# sourceMappingURL=fragments.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}