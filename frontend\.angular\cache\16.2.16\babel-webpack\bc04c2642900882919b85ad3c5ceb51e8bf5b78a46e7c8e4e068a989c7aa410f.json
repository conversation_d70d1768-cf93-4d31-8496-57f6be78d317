{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/common\";\nfunction VerifyEmailComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39)(2, \"div\", 40);\n    i0.ɵɵelement(3, \"i\", 41)(4, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 43)(6, \"p\", 44);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction VerifyEmailComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 39)(2, \"div\", 46);\n    i0.ɵɵelement(3, \"i\", 47)(4, \"div\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 43)(6, \"p\", 49);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.message, \" \");\n  }\n}\nfunction VerifyEmailComponent_i_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 50);\n  }\n}\nfunction VerifyEmailComponent_i_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 51);\n  }\n}\nfunction VerifyEmailComponent_span_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r4.timer, \"s)\");\n  }\n}\nexport let VerifyEmailComponent = /*#__PURE__*/(() => {\n  class VerifyEmailComponent {\n    constructor(route, router, authService, fb) {\n      this.route = route;\n      this.router = router;\n      this.authService = authService;\n      this.fb = fb;\n      this.loading = false;\n      this.error = null;\n      this.message = null;\n      this.email = '';\n      this.timer = 60;\n      this.canResend = false;\n      this.verifyForm = this.fb.group({\n        code: ['', [Validators.required, Validators.minLength(6), Validators.maxLength(6)]]\n      });\n    }\n    ngOnInit() {\n      // Get email from URL query params\n      this.email = this.route.snapshot.queryParamMap.get('email') || '';\n      // Check if we have an email to verify\n      if (!this.email) {\n        this.error = 'No email address provided for verification.';\n        return;\n      }\n      // Start the countdown for resend\n      this.startCountdown();\n    }\n    startCountdown() {\n      this.canResend = false;\n      this.timer = 60;\n      this.intervalId = setInterval(() => {\n        this.timer--;\n        if (this.timer === 0) {\n          this.canResend = true;\n          clearInterval(this.intervalId);\n        }\n      }, 1000);\n    }\n    onVerifySubmit() {\n      if (this.verifyForm.invalid) return;\n      this.loading = true;\n      this.error = null;\n      this.message = null;\n      const verifyData = {\n        email: this.email,\n        code: this.verifyForm.value.code\n      };\n      this.authService.verifyEmail(verifyData).subscribe({\n        next: res => {\n          this.loading = false;\n          this.message = res.message + ' Redirection vers la page de connexion...';\n          // Reset form\n          this.verifyForm.reset();\n          // Redirect after 1.5 seconds\n          setTimeout(() => {\n            this.router.navigate(['/login'], {\n              queryParams: {\n                message: 'Votre compte a été vérifié avec succès. Vous pouvez maintenant vous connecter.'\n              }\n            });\n          }, 1500);\n        },\n        error: err => {\n          this.loading = false;\n          this.error = err.error?.message || 'Échec de la vérification';\n        }\n      });\n    }\n    resendCode() {\n      if (!this.canResend) return;\n      this.loading = true;\n      this.error = null;\n      this.message = null;\n      this.authService.resendCode(this.email).subscribe({\n        next: res => {\n          this.loading = false;\n          this.message = res.message || 'Code renvoyé avec succès.';\n          this.startCountdown();\n        },\n        error: err => {\n          this.loading = false;\n          this.error = err.error?.message || \"Erreur lors de l'envoi du code.\";\n        }\n      });\n    }\n    static {\n      this.ɵfac = function VerifyEmailComponent_Factory(t) {\n        return new (t || VerifyEmailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.FormBuilder));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: VerifyEmailComponent,\n        selectors: [[\"app-verify-email\"]],\n        decls: 56,\n        vars: 16,\n        consts: [[1, \"min-h-screen\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"flex\", \"items-center\", \"justify-center\", \"p-4\", \"relative\", \"futuristic-layout\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"max-w-md\", \"relative\", \"z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"blur-md\"], [1, \"p-6\", \"text-center\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"p-6\"], [1, \"space-y-5\", 3, \"formGroup\", \"ngSubmit\"], [1, \"group\"], [1, \"flex\", \"items-center\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mb-2\"], [1, \"fas\", \"fa-key\", \"mr-1.5\", \"text-xs\"], [1, \"relative\"], [\"formControlName\", \"code\", \"placeholder\", \"123456\", 1, \"w-full\", \"px-4\", \"py-2.5\", \"text-sm\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:outline-none\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"w-full\", \"relative\", \"overflow-hidden\", \"group\", \"mt-6\", 3, \"disabled\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"font-medium\", \"py-2.5\", \"px-4\", \"rounded-lg\", \"transition-all\", \"z-10\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-check mr-2\", 4, \"ngIf\"], [1, \"text-center\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"space-y-2\", \"pt-4\"], [1, \"flex\", \"items-center\", \"justify-center\"], [\"type\", \"button\", 1, \"ml-1.5\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"transition-colors\", \"font-medium\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"flex\", \"items-center\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-sync-alt\", \"mr-1\", \"text-xs\"], [\"class\", \"ml-1\", 4, \"ngIf\"], [1, \"text-center\", \"mt-4\"], [\"routerLink\", \"/login\", 1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:text-[#3d4a85]\", \"dark:hover:text-[#4f5fad]\", \"transition-colors\", \"text-sm\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"flex-1\"], [1, \"text-xs\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\"], [1, \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/5\", \"border\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]/30\", \"rounded-lg\", \"p-3\", \"backdrop-blur-sm\"], [1, \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-2\", \"text-base\", \"relative\"], [1, \"fas\", \"fa-check-circle\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-xs\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"], [1, \"fas\", \"fa-check\", \"mr-2\"], [1, \"ml-1\"]],\n        template: function VerifyEmailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n            i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n            i0.ɵɵelementStart(21, \"div\", 11)(22, \"h1\", 12);\n            i0.ɵɵtext(23, \" Email Verification \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"p\", 13);\n            i0.ɵɵtext(25);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 14)(27, \"form\", 15);\n            i0.ɵɵlistener(\"ngSubmit\", function VerifyEmailComponent_Template_form_ngSubmit_27_listener() {\n              return ctx.onVerifySubmit();\n            });\n            i0.ɵɵelementStart(28, \"div\", 16)(29, \"label\", 17);\n            i0.ɵɵelement(30, \"i\", 18);\n            i0.ɵɵtext(31, \" Verification Code \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"div\", 19);\n            i0.ɵɵelement(33, \"input\", 20);\n            i0.ɵɵelementStart(34, \"div\", 21);\n            i0.ɵɵelement(35, \"div\", 22);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(36, VerifyEmailComponent_div_36_Template, 8, 1, \"div\", 23);\n            i0.ɵɵtemplate(37, VerifyEmailComponent_div_37_Template, 8, 1, \"div\", 24);\n            i0.ɵɵelementStart(38, \"button\", 25);\n            i0.ɵɵelement(39, \"div\", 26)(40, \"div\", 27);\n            i0.ɵɵelementStart(41, \"span\", 28);\n            i0.ɵɵtemplate(42, VerifyEmailComponent_i_42_Template, 1, 0, \"i\", 29);\n            i0.ɵɵtemplate(43, VerifyEmailComponent_i_43_Template, 1, 0, \"i\", 30);\n            i0.ɵɵtext(44);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(45, \"div\", 31)(46, \"div\", 32)(47, \"span\");\n            i0.ɵɵtext(48, \"Didn't receive a code?\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"button\", 33);\n            i0.ɵɵlistener(\"click\", function VerifyEmailComponent_Template_button_click_49_listener() {\n              return ctx.resendCode();\n            });\n            i0.ɵɵelement(50, \"i\", 34);\n            i0.ɵɵtext(51, \" Resend \");\n            i0.ɵɵtemplate(52, VerifyEmailComponent_span_52_Template, 2, 1, \"span\", 35);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(53, \"div\", 36)(54, \"a\", 37);\n            i0.ɵɵtext(55, \" Back to Login \");\n            i0.ɵɵelementEnd()()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(25);\n            i0.ɵɵtextInterpolate1(\" A verification code has been sent to \", ctx.email, \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.verifyForm);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.message);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", ctx.loading || ctx.verifyForm.invalid);\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"opacity-70\", ctx.loading || ctx.verifyForm.invalid);\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"opacity-0\", ctx.loading || ctx.verifyForm.invalid);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Verifying...\" : \"Verify\", \" \");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"disabled\", !ctx.canResend || ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"animate-spin\", !ctx.canResend);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.canResend);\n          }\n        },\n        dependencies: [i4.NgIf, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName, i1.RouterLink]\n      });\n    }\n  }\n  return VerifyEmailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}