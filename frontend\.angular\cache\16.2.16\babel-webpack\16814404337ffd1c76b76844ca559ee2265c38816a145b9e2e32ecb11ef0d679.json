{"ast": null, "code": "import { __assign, __spreadArray } from \"tslib\";\nimport { invariant } from \"../globals/index.js\";\nimport { visit, Kind } from \"graphql\";\nimport { checkDocument, getOperationDefinition, getFragmentDefinition, getFragmentDefinitions, getMainDefinition } from \"./getFromAST.js\";\nimport { isField } from \"./storeUtils.js\";\nimport { createFragmentMap } from \"./fragments.js\";\nimport { isArray, isNonEmptyArray } from \"../common/arrays.js\";\nvar TYPENAME_FIELD = {\n  kind: Kind.FIELD,\n  name: {\n    kind: Kind.NAME,\n    value: \"__typename\"\n  }\n};\nfunction isEmpty(op, fragmentMap) {\n  return !op || op.selectionSet.selections.every(function (selection) {\n    return selection.kind === Kind.FRAGMENT_SPREAD && isEmpty(fragmentMap[selection.name.value], fragmentMap);\n  });\n}\nfunction nullIfDocIsEmpty(doc) {\n  return isEmpty(getOperationDefinition(doc) || getFragmentDefinition(doc), createFragmentMap(getFragmentDefinitions(doc))) ? null : doc;\n}\nfunction getDirectiveMatcher(configs) {\n  var names = new Map();\n  var tests = new Map();\n  configs.forEach(function (directive) {\n    if (directive) {\n      if (directive.name) {\n        names.set(directive.name, directive);\n      } else if (directive.test) {\n        tests.set(directive.test, directive);\n      }\n    }\n  });\n  return function (directive) {\n    var config = names.get(directive.name.value);\n    if (!config && tests.size) {\n      tests.forEach(function (testConfig, test) {\n        if (test(directive)) {\n          config = testConfig;\n        }\n      });\n    }\n    return config;\n  };\n}\nfunction makeInUseGetterFunction(defaultKey) {\n  var map = new Map();\n  return function inUseGetterFunction(key) {\n    if (key === void 0) {\n      key = defaultKey;\n    }\n    var inUse = map.get(key);\n    if (!inUse) {\n      map.set(key, inUse = {\n        // Variable and fragment spread names used directly within this\n        // operation or fragment definition, as identified by key. These sets\n        // will be populated during the first traversal of the document in\n        // removeDirectivesFromDocument below.\n        variables: new Set(),\n        fragmentSpreads: new Set()\n      });\n    }\n    return inUse;\n  };\n}\nexport function removeDirectivesFromDocument(directives, doc) {\n  checkDocument(doc);\n  // Passing empty strings to makeInUseGetterFunction means we handle anonymous\n  // operations as if their names were \"\". Anonymous fragment definitions are\n  // not supposed to be possible, but the same default naming strategy seems\n  // appropriate for that case as well.\n  var getInUseByOperationName = makeInUseGetterFunction(\"\");\n  var getInUseByFragmentName = makeInUseGetterFunction(\"\");\n  var getInUse = function (ancestors) {\n    for (var p = 0, ancestor = void 0; p < ancestors.length && (ancestor = ancestors[p]); ++p) {\n      if (isArray(ancestor)) continue;\n      if (ancestor.kind === Kind.OPERATION_DEFINITION) {\n        // If an operation is anonymous, we use the empty string as its key.\n        return getInUseByOperationName(ancestor.name && ancestor.name.value);\n      }\n      if (ancestor.kind === Kind.FRAGMENT_DEFINITION) {\n        return getInUseByFragmentName(ancestor.name.value);\n      }\n    }\n    globalThis.__DEV__ !== false && invariant.error(97);\n    return null;\n  };\n  var operationCount = 0;\n  for (var i = doc.definitions.length - 1; i >= 0; --i) {\n    if (doc.definitions[i].kind === Kind.OPERATION_DEFINITION) {\n      ++operationCount;\n    }\n  }\n  var directiveMatcher = getDirectiveMatcher(directives);\n  var shouldRemoveField = function (nodeDirectives) {\n    return isNonEmptyArray(nodeDirectives) && nodeDirectives.map(directiveMatcher).some(function (config) {\n      return config && config.remove;\n    });\n  };\n  var originalFragmentDefsByPath = new Map();\n  // Any time the first traversal of the document below makes a change like\n  // removing a fragment (by returning null), this variable should be set to\n  // true. Once it becomes true, it should never be set to false again. If this\n  // variable remains false throughout the traversal, then we can return the\n  // original doc immediately without any modifications.\n  var firstVisitMadeChanges = false;\n  var fieldOrInlineFragmentVisitor = {\n    enter: function (node) {\n      if (shouldRemoveField(node.directives)) {\n        firstVisitMadeChanges = true;\n        return null;\n      }\n    }\n  };\n  var docWithoutDirectiveSubtrees = visit(doc, {\n    // These two AST node types share the same implementation, defined above.\n    Field: fieldOrInlineFragmentVisitor,\n    InlineFragment: fieldOrInlineFragmentVisitor,\n    VariableDefinition: {\n      enter: function () {\n        // VariableDefinition nodes do not count as variables in use, though\n        // they do contain Variable nodes that might be visited below. To avoid\n        // counting variable declarations as usages, we skip visiting the\n        // contents of this VariableDefinition node by returning false.\n        return false;\n      }\n    },\n    Variable: {\n      enter: function (node, _key, _parent, _path, ancestors) {\n        var inUse = getInUse(ancestors);\n        if (inUse) {\n          inUse.variables.add(node.name.value);\n        }\n      }\n    },\n    FragmentSpread: {\n      enter: function (node, _key, _parent, _path, ancestors) {\n        if (shouldRemoveField(node.directives)) {\n          firstVisitMadeChanges = true;\n          return null;\n        }\n        var inUse = getInUse(ancestors);\n        if (inUse) {\n          inUse.fragmentSpreads.add(node.name.value);\n        }\n        // We might like to remove this FragmentSpread by returning null here if\n        // the corresponding FragmentDefinition node is also going to be removed\n        // by the logic below, but we can't control the relative order of those\n        // events, so we have to postpone the removal of dangling FragmentSpread\n        // nodes until after the current visit of the document has finished.\n      }\n    },\n\n    FragmentDefinition: {\n      enter: function (node, _key, _parent, path) {\n        originalFragmentDefsByPath.set(JSON.stringify(path), node);\n      },\n      leave: function (node, _key, _parent, path) {\n        var originalNode = originalFragmentDefsByPath.get(JSON.stringify(path));\n        if (node === originalNode) {\n          // If the FragmentNode received by this leave function is identical to\n          // the one received by the corresponding enter function (above), then\n          // the visitor must not have made any changes within this\n          // FragmentDefinition node. This fragment definition may still be\n          // removed if there are no ...spread references to it, but it won't be\n          // removed just because it has only a __typename field.\n          return node;\n        }\n        if (\n        // This logic applies only if the document contains one or more\n        // operations, since removing all fragments from a document containing\n        // only fragments makes the document useless.\n        operationCount > 0 && node.selectionSet.selections.every(function (selection) {\n          return selection.kind === Kind.FIELD && selection.name.value === \"__typename\";\n        })) {\n          // This is a somewhat opinionated choice: if a FragmentDefinition ends\n          // up having no fields other than __typename, we remove the whole\n          // fragment definition, and later prune ...spread references to it.\n          getInUseByFragmentName(node.name.value).removed = true;\n          firstVisitMadeChanges = true;\n          return null;\n        }\n      }\n    },\n    Directive: {\n      leave: function (node) {\n        // If a matching directive is found, remove the directive itself. Note\n        // that this does not remove the target (field, argument, etc) of the\n        // directive, but only the directive itself.\n        if (directiveMatcher(node)) {\n          firstVisitMadeChanges = true;\n          return null;\n        }\n      }\n    }\n  });\n  if (!firstVisitMadeChanges) {\n    // If our first pass did not change anything about the document, then there\n    // is no cleanup we need to do, and we can return the original doc.\n    return doc;\n  }\n  // Utility for making sure inUse.transitiveVars is recursively populated.\n  // Because this logic assumes inUse.fragmentSpreads has been completely\n  // populated and inUse.removed has been set if appropriate,\n  // populateTransitiveVars must be called after that information has been\n  // collected by the first traversal of the document.\n  var populateTransitiveVars = function (inUse) {\n    if (!inUse.transitiveVars) {\n      inUse.transitiveVars = new Set(inUse.variables);\n      if (!inUse.removed) {\n        inUse.fragmentSpreads.forEach(function (childFragmentName) {\n          populateTransitiveVars(getInUseByFragmentName(childFragmentName)).transitiveVars.forEach(function (varName) {\n            inUse.transitiveVars.add(varName);\n          });\n        });\n      }\n    }\n    return inUse;\n  };\n  // Since we've been keeping track of fragment spreads used by particular\n  // operations and fragment definitions, we now need to compute the set of all\n  // spreads used (transitively) by any operations in the document.\n  var allFragmentNamesUsed = new Set();\n  docWithoutDirectiveSubtrees.definitions.forEach(function (def) {\n    if (def.kind === Kind.OPERATION_DEFINITION) {\n      populateTransitiveVars(getInUseByOperationName(def.name && def.name.value)).fragmentSpreads.forEach(function (childFragmentName) {\n        allFragmentNamesUsed.add(childFragmentName);\n      });\n    } else if (def.kind === Kind.FRAGMENT_DEFINITION &&\n    // If there are no operations in the document, then all fragment\n    // definitions count as usages of their own fragment names. This heuristic\n    // prevents accidentally removing all fragment definitions from the\n    // document just because it contains no operations that use the fragments.\n    operationCount === 0 && !getInUseByFragmentName(def.name.value).removed) {\n      allFragmentNamesUsed.add(def.name.value);\n    }\n  });\n  // Now that we have added all fragment spreads used by operations to the\n  // allFragmentNamesUsed set, we can complete the set by transitively adding\n  // all fragment spreads used by those fragments, and so on.\n  allFragmentNamesUsed.forEach(function (fragmentName) {\n    // Once all the childFragmentName strings added here have been seen already,\n    // the top-level allFragmentNamesUsed.forEach loop will terminate.\n    populateTransitiveVars(getInUseByFragmentName(fragmentName)).fragmentSpreads.forEach(function (childFragmentName) {\n      allFragmentNamesUsed.add(childFragmentName);\n    });\n  });\n  var fragmentWillBeRemoved = function (fragmentName) {\n    return !!(\n    // A fragment definition will be removed if there are no spreads that refer\n    // to it, or the fragment was explicitly removed because it had no fields\n    // other than __typename.\n    !allFragmentNamesUsed.has(fragmentName) || getInUseByFragmentName(fragmentName).removed);\n  };\n  var enterVisitor = {\n    enter: function (node) {\n      if (fragmentWillBeRemoved(node.name.value)) {\n        return null;\n      }\n    }\n  };\n  return nullIfDocIsEmpty(visit(docWithoutDirectiveSubtrees, {\n    // If the fragment is going to be removed, then leaving any dangling\n    // FragmentSpread nodes with the same name would be a mistake.\n    FragmentSpread: enterVisitor,\n    // This is where the fragment definition is actually removed.\n    FragmentDefinition: enterVisitor,\n    OperationDefinition: {\n      leave: function (node) {\n        // Upon leaving each operation in the depth-first AST traversal, prune\n        // any variables that are declared by the operation but unused within.\n        if (node.variableDefinitions) {\n          var usedVariableNames_1 = populateTransitiveVars(\n          // If an operation is anonymous, we use the empty string as its key.\n          getInUseByOperationName(node.name && node.name.value)).transitiveVars;\n          // According to the GraphQL spec, all variables declared by an\n          // operation must either be used by that operation or used by some\n          // fragment included transitively into that operation:\n          // https://spec.graphql.org/draft/#sec-All-Variables-Used\n          //\n          // To stay on the right side of this validation rule, if/when we\n          // remove the last $var references from an operation or its fragments,\n          // we must also remove the corresponding $var declaration from the\n          // enclosing operation. This pruning applies only to operations and\n          // not fragment definitions, at the moment. Fragments may be able to\n          // declare variables eventually, but today they can only consume them.\n          if (usedVariableNames_1.size < node.variableDefinitions.length) {\n            return __assign(__assign({}, node), {\n              variableDefinitions: node.variableDefinitions.filter(function (varDef) {\n                return usedVariableNames_1.has(varDef.variable.name.value);\n              })\n            });\n          }\n        }\n      }\n    }\n  }));\n}\nexport var addTypenameToDocument = Object.assign(function (doc) {\n  return visit(doc, {\n    SelectionSet: {\n      enter: function (node, _key, parent) {\n        // Don't add __typename to OperationDefinitions.\n        if (parent && parent.kind === Kind.OPERATION_DEFINITION) {\n          return;\n        }\n        // No changes if no selections.\n        var selections = node.selections;\n        if (!selections) {\n          return;\n        }\n        // If selections already have a __typename, or are part of an\n        // introspection query, do nothing.\n        var skip = selections.some(function (selection) {\n          return isField(selection) && (selection.name.value === \"__typename\" || selection.name.value.lastIndexOf(\"__\", 0) === 0);\n        });\n        if (skip) {\n          return;\n        }\n        // If this SelectionSet is @export-ed as an input variable, it should\n        // not have a __typename field (see issue #4691).\n        var field = parent;\n        if (isField(field) && field.directives && field.directives.some(function (d) {\n          return d.name.value === \"export\";\n        })) {\n          return;\n        }\n        // Create and return a new SelectionSet with a __typename Field.\n        return __assign(__assign({}, node), {\n          selections: __spreadArray(__spreadArray([], selections, true), [TYPENAME_FIELD], false)\n        });\n      }\n    }\n  });\n}, {\n  added: function (field) {\n    return field === TYPENAME_FIELD;\n  }\n});\nvar connectionRemoveConfig = {\n  test: function (directive) {\n    var willRemove = directive.name.value === \"connection\";\n    if (willRemove) {\n      if (!directive.arguments || !directive.arguments.some(function (arg) {\n        return arg.name.value === \"key\";\n      })) {\n        globalThis.__DEV__ !== false && invariant.warn(98);\n      }\n    }\n    return willRemove;\n  }\n};\nexport function removeConnectionDirectiveFromDocument(doc) {\n  return removeDirectivesFromDocument([connectionRemoveConfig], checkDocument(doc));\n}\nfunction hasDirectivesInSelectionSet(directives, selectionSet, nestedCheck) {\n  if (nestedCheck === void 0) {\n    nestedCheck = true;\n  }\n  return !!selectionSet && selectionSet.selections && selectionSet.selections.some(function (selection) {\n    return hasDirectivesInSelection(directives, selection, nestedCheck);\n  });\n}\nfunction hasDirectivesInSelection(directives, selection, nestedCheck) {\n  if (nestedCheck === void 0) {\n    nestedCheck = true;\n  }\n  if (!isField(selection)) {\n    return true;\n  }\n  if (!selection.directives) {\n    return false;\n  }\n  return selection.directives.some(getDirectiveMatcher(directives)) || nestedCheck && hasDirectivesInSelectionSet(directives, selection.selectionSet, nestedCheck);\n}\nfunction getArgumentMatcher(config) {\n  return function argumentMatcher(argument) {\n    return config.some(function (aConfig) {\n      return argument.value && argument.value.kind === Kind.VARIABLE && argument.value.name && (aConfig.name === argument.value.name.value || aConfig.test && aConfig.test(argument));\n    });\n  };\n}\nexport function removeArgumentsFromDocument(config, doc) {\n  var argMatcher = getArgumentMatcher(config);\n  return nullIfDocIsEmpty(visit(doc, {\n    OperationDefinition: {\n      enter: function (node) {\n        return __assign(__assign({}, node), {\n          // Remove matching top level variables definitions.\n          variableDefinitions: node.variableDefinitions ? node.variableDefinitions.filter(function (varDef) {\n            return !config.some(function (arg) {\n              return arg.name === varDef.variable.name.value;\n            });\n          }) : []\n        });\n      }\n    },\n    Field: {\n      enter: function (node) {\n        // If `remove` is set to true for an argument, and an argument match\n        // is found for a field, remove the field as well.\n        var shouldRemoveField = config.some(function (argConfig) {\n          return argConfig.remove;\n        });\n        if (shouldRemoveField) {\n          var argMatchCount_1 = 0;\n          if (node.arguments) {\n            node.arguments.forEach(function (arg) {\n              if (argMatcher(arg)) {\n                argMatchCount_1 += 1;\n              }\n            });\n          }\n          if (argMatchCount_1 === 1) {\n            return null;\n          }\n        }\n      }\n    },\n    Argument: {\n      enter: function (node) {\n        // Remove all matching arguments.\n        if (argMatcher(node)) {\n          return null;\n        }\n      }\n    }\n  }));\n}\nexport function removeFragmentSpreadFromDocument(config, doc) {\n  function enter(node) {\n    if (config.some(function (def) {\n      return def.name === node.name.value;\n    })) {\n      return null;\n    }\n  }\n  return nullIfDocIsEmpty(visit(doc, {\n    FragmentSpread: {\n      enter: enter\n    },\n    FragmentDefinition: {\n      enter: enter\n    }\n  }));\n}\n// If the incoming document is a query, return it as is. Otherwise, build a\n// new document containing a query operation based on the selection set\n// of the previous main operation.\nexport function buildQueryFromSelectionSet(document) {\n  var definition = getMainDefinition(document);\n  var definitionOperation = definition.operation;\n  if (definitionOperation === \"query\") {\n    // Already a query, so return the existing document.\n    return document;\n  }\n  // Build a new query using the selection set of the main operation.\n  var modifiedDoc = visit(document, {\n    OperationDefinition: {\n      enter: function (node) {\n        return __assign(__assign({}, node), {\n          operation: \"query\"\n        });\n      }\n    }\n  });\n  return modifiedDoc;\n}\n// Remove fields / selection sets that include an @client directive.\nexport function removeClientSetsFromDocument(document) {\n  checkDocument(document);\n  var modifiedDoc = removeDirectivesFromDocument([{\n    test: function (directive) {\n      return directive.name.value === \"client\";\n    },\n    remove: true\n  }], document);\n  return modifiedDoc;\n}\nexport function addNonReactiveToNamedFragments(document) {\n  checkDocument(document);\n  return visit(document, {\n    FragmentSpread: function (node) {\n      var _a;\n      // Do not add `@nonreactive` if the fragment is marked with `@unmask`\n      // since we want to react to changes in this fragment.\n      if ((_a = node.directives) === null || _a === void 0 ? void 0 : _a.some(function (directive) {\n        return directive.name.value === \"unmask\";\n      })) {\n        return;\n      }\n      return __assign(__assign({}, node), {\n        directives: __spreadArray(__spreadArray([], node.directives || [], true), [{\n          kind: Kind.DIRECTIVE,\n          name: {\n            kind: Kind.NAME,\n            value: \"nonreactive\"\n          }\n        }], false)\n      });\n    }\n  });\n}\n//# sourceMappingURL=transform.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}