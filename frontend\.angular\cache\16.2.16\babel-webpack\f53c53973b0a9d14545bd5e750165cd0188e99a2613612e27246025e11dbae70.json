{"ast": null, "code": "import { isNode } from '../language/ast.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { getEnterLeaveForKind } from '../language/visitor.mjs';\nimport { getNamedType, getNullableType, isCompositeType, isEnumType, isInputObjectType, isInputType, isInterfaceType, isListType, isObjectType, isOutputType } from '../type/definition.mjs';\nimport { SchemaMetaFieldDef, TypeMetaFieldDef, TypeNameMetaFieldDef } from '../type/introspection.mjs';\nimport { typeFromAST } from './typeFromAST.mjs';\n/**\n * TypeInfo is a utility class which, given a GraphQL schema, can keep track\n * of the current field and type definitions at any point in a GraphQL document\n * AST during a recursive descent by calling `enter(node)` and `leave(node)`.\n */\n\nexport class TypeInfo {\n  constructor(schema,\n  /**\n   * Initial type may be provided in rare cases to facilitate traversals\n   *  beginning somewhere other than documents.\n   */\n  initialType, /** @deprecated will be removed in 17.0.0 */\n  getFieldDefFn) {\n    this._schema = schema;\n    this._typeStack = [];\n    this._parentTypeStack = [];\n    this._inputTypeStack = [];\n    this._fieldDefStack = [];\n    this._defaultValueStack = [];\n    this._directive = null;\n    this._argument = null;\n    this._enumValue = null;\n    this._getFieldDef = getFieldDefFn !== null && getFieldDefFn !== void 0 ? getFieldDefFn : getFieldDef;\n    if (initialType) {\n      if (isInputType(initialType)) {\n        this._inputTypeStack.push(initialType);\n      }\n      if (isCompositeType(initialType)) {\n        this._parentTypeStack.push(initialType);\n      }\n      if (isOutputType(initialType)) {\n        this._typeStack.push(initialType);\n      }\n    }\n  }\n  get [Symbol.toStringTag]() {\n    return 'TypeInfo';\n  }\n  getType() {\n    if (this._typeStack.length > 0) {\n      return this._typeStack[this._typeStack.length - 1];\n    }\n  }\n  getParentType() {\n    if (this._parentTypeStack.length > 0) {\n      return this._parentTypeStack[this._parentTypeStack.length - 1];\n    }\n  }\n  getInputType() {\n    if (this._inputTypeStack.length > 0) {\n      return this._inputTypeStack[this._inputTypeStack.length - 1];\n    }\n  }\n  getParentInputType() {\n    if (this._inputTypeStack.length > 1) {\n      return this._inputTypeStack[this._inputTypeStack.length - 2];\n    }\n  }\n  getFieldDef() {\n    if (this._fieldDefStack.length > 0) {\n      return this._fieldDefStack[this._fieldDefStack.length - 1];\n    }\n  }\n  getDefaultValue() {\n    if (this._defaultValueStack.length > 0) {\n      return this._defaultValueStack[this._defaultValueStack.length - 1];\n    }\n  }\n  getDirective() {\n    return this._directive;\n  }\n  getArgument() {\n    return this._argument;\n  }\n  getEnumValue() {\n    return this._enumValue;\n  }\n  enter(node) {\n    const schema = this._schema; // Note: many of the types below are explicitly typed as \"unknown\" to drop\n    // any assumptions of a valid schema to ensure runtime types are properly\n    // checked before continuing since TypeInfo is used as part of validation\n    // which occurs before guarantees of schema and document validity.\n\n    switch (node.kind) {\n      case Kind.SELECTION_SET:\n        {\n          const namedType = getNamedType(this.getType());\n          this._parentTypeStack.push(isCompositeType(namedType) ? namedType : undefined);\n          break;\n        }\n      case Kind.FIELD:\n        {\n          const parentType = this.getParentType();\n          let fieldDef;\n          let fieldType;\n          if (parentType) {\n            fieldDef = this._getFieldDef(schema, parentType, node);\n            if (fieldDef) {\n              fieldType = fieldDef.type;\n            }\n          }\n          this._fieldDefStack.push(fieldDef);\n          this._typeStack.push(isOutputType(fieldType) ? fieldType : undefined);\n          break;\n        }\n      case Kind.DIRECTIVE:\n        this._directive = schema.getDirective(node.name.value);\n        break;\n      case Kind.OPERATION_DEFINITION:\n        {\n          const rootType = schema.getRootType(node.operation);\n          this._typeStack.push(isObjectType(rootType) ? rootType : undefined);\n          break;\n        }\n      case Kind.INLINE_FRAGMENT:\n      case Kind.FRAGMENT_DEFINITION:\n        {\n          const typeConditionAST = node.typeCondition;\n          const outputType = typeConditionAST ? typeFromAST(schema, typeConditionAST) : getNamedType(this.getType());\n          this._typeStack.push(isOutputType(outputType) ? outputType : undefined);\n          break;\n        }\n      case Kind.VARIABLE_DEFINITION:\n        {\n          const inputType = typeFromAST(schema, node.type);\n          this._inputTypeStack.push(isInputType(inputType) ? inputType : undefined);\n          break;\n        }\n      case Kind.ARGUMENT:\n        {\n          var _this$getDirective;\n          let argDef;\n          let argType;\n          const fieldOrDirective = (_this$getDirective = this.getDirective()) !== null && _this$getDirective !== void 0 ? _this$getDirective : this.getFieldDef();\n          if (fieldOrDirective) {\n            argDef = fieldOrDirective.args.find(arg => arg.name === node.name.value);\n            if (argDef) {\n              argType = argDef.type;\n            }\n          }\n          this._argument = argDef;\n          this._defaultValueStack.push(argDef ? argDef.defaultValue : undefined);\n          this._inputTypeStack.push(isInputType(argType) ? argType : undefined);\n          break;\n        }\n      case Kind.LIST:\n        {\n          const listType = getNullableType(this.getInputType());\n          const itemType = isListType(listType) ? listType.ofType : listType; // List positions never have a default value.\n\n          this._defaultValueStack.push(undefined);\n          this._inputTypeStack.push(isInputType(itemType) ? itemType : undefined);\n          break;\n        }\n      case Kind.OBJECT_FIELD:\n        {\n          const objectType = getNamedType(this.getInputType());\n          let inputFieldType;\n          let inputField;\n          if (isInputObjectType(objectType)) {\n            inputField = objectType.getFields()[node.name.value];\n            if (inputField) {\n              inputFieldType = inputField.type;\n            }\n          }\n          this._defaultValueStack.push(inputField ? inputField.defaultValue : undefined);\n          this._inputTypeStack.push(isInputType(inputFieldType) ? inputFieldType : undefined);\n          break;\n        }\n      case Kind.ENUM:\n        {\n          const enumType = getNamedType(this.getInputType());\n          let enumValue;\n          if (isEnumType(enumType)) {\n            enumValue = enumType.getValue(node.value);\n          }\n          this._enumValue = enumValue;\n          break;\n        }\n      default: // Ignore other nodes\n    }\n  }\n\n  leave(node) {\n    switch (node.kind) {\n      case Kind.SELECTION_SET:\n        this._parentTypeStack.pop();\n        break;\n      case Kind.FIELD:\n        this._fieldDefStack.pop();\n        this._typeStack.pop();\n        break;\n      case Kind.DIRECTIVE:\n        this._directive = null;\n        break;\n      case Kind.OPERATION_DEFINITION:\n      case Kind.INLINE_FRAGMENT:\n      case Kind.FRAGMENT_DEFINITION:\n        this._typeStack.pop();\n        break;\n      case Kind.VARIABLE_DEFINITION:\n        this._inputTypeStack.pop();\n        break;\n      case Kind.ARGUMENT:\n        this._argument = null;\n        this._defaultValueStack.pop();\n        this._inputTypeStack.pop();\n        break;\n      case Kind.LIST:\n      case Kind.OBJECT_FIELD:\n        this._defaultValueStack.pop();\n        this._inputTypeStack.pop();\n        break;\n      case Kind.ENUM:\n        this._enumValue = null;\n        break;\n      default: // Ignore other nodes\n    }\n  }\n}\n\n/**\n * Not exactly the same as the executor's definition of getFieldDef, in this\n * statically evaluated environment we do not always have an Object type,\n * and need to handle Interface and Union types.\n */\nfunction getFieldDef(schema, parentType, fieldNode) {\n  const name = fieldNode.name.value;\n  if (name === SchemaMetaFieldDef.name && schema.getQueryType() === parentType) {\n    return SchemaMetaFieldDef;\n  }\n  if (name === TypeMetaFieldDef.name && schema.getQueryType() === parentType) {\n    return TypeMetaFieldDef;\n  }\n  if (name === TypeNameMetaFieldDef.name && isCompositeType(parentType)) {\n    return TypeNameMetaFieldDef;\n  }\n  if (isObjectType(parentType) || isInterfaceType(parentType)) {\n    return parentType.getFields()[name];\n  }\n}\n/**\n * Creates a new visitor instance which maintains a provided TypeInfo instance\n * along with visiting visitor.\n */\n\nexport function visitWithTypeInfo(typeInfo, visitor) {\n  return {\n    enter(...args) {\n      const node = args[0];\n      typeInfo.enter(node);\n      const fn = getEnterLeaveForKind(visitor, node.kind).enter;\n      if (fn) {\n        const result = fn.apply(visitor, args);\n        if (result !== undefined) {\n          typeInfo.leave(node);\n          if (isNode(result)) {\n            typeInfo.enter(result);\n          }\n        }\n        return result;\n      }\n    },\n    leave(...args) {\n      const node = args[0];\n      const fn = getEnterLeaveForKind(visitor, node.kind).leave;\n      let result;\n      if (fn) {\n        result = fn.apply(visitor, args);\n      }\n      typeInfo.leave(node);\n      return result;\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}