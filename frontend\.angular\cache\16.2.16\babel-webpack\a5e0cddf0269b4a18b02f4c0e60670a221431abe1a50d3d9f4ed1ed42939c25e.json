{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { compareAsc } from \"./compareAsc.js\";\nimport { differenceInCalendarISOWeekYears } from \"./differenceInCalendarISOWeekYears.js\";\nimport { subISOWeekYears } from \"./subISOWeekYears.js\";\n\n/**\n * The {@link differenceInISOWeekYears} function options.\n */\n\n/**\n * @name differenceInISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of full ISO week-numbering years between the given dates.\n *\n * @description\n * Get the number of full ISO week-numbering years between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - The options\n *\n * @returns The number of full ISO week-numbering years\n *\n * @example\n * // How many full ISO week-numbering years are between 1 January 2010 and 1 January 2012?\n * const result = differenceInISOWeekYears(\n *   new Date(2012, 0, 1),\n *   new Date(2010, 0, 1)\n * )\n * // => 1\n */\nexport function differenceInISOWeekYears(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  const sign = compareAsc(laterDate_, earlierDate_);\n  const diff = Math.abs(differenceInCalendarISOWeekYears(laterDate_, earlierDate_, options));\n  const adjustedDate = subISOWeekYears(laterDate_, sign * diff, options);\n  const isLastISOWeekYearNotFull = Number(compareAsc(adjustedDate, earlierDate_) === -sign);\n  const result = sign * (diff - isLastISOWeekYearNotFull);\n\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInISOWeekYears;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}