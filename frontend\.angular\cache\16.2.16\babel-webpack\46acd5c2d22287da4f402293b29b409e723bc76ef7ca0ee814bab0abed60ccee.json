{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isWithinInterval} function options.\n */\n\n/**\n * @name isWithinInterval\n * @category Interval Helpers\n * @summary Is the given date within the interval?\n *\n * @description\n * Is the given date within the interval? (Including start and end.)\n *\n * @param date - The date to check\n * @param interval - The interval to check\n * @param options - An object with options\n *\n * @returns The date is within the interval\n *\n * @example\n * // For the date within the interval:\n * isWithinInterval(new Date(2014, 0, 3), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * // => true\n *\n * @example\n * // For the date outside of the interval:\n * isWithinInterval(new Date(2014, 0, 10), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * // => false\n *\n * @example\n * // For date equal to the interval start:\n * isWithinInterval(date, { start, end: date })\n * // => true\n *\n * @example\n * // For date equal to the interval end:\n * isWithinInterval(date, { start: date, end })\n * // => true\n */\nexport function isWithinInterval(date, interval, options) {\n  const time = +toDate(date, options?.in);\n  const [startTime, endTime] = [+toDate(interval.start, options?.in), +toDate(interval.end, options?.in)].sort((a, b) => a - b);\n  return time >= startTime && time <= endTime;\n}\n\n// Fallback for modularized imports:\nexport default isWithinInterval;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}