{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { adapterFactory as baseAdapterFactory } from 'calendar-utils/date-adapters/date-fns';\nimport { addWeeks, addMonths, subDays, subWeeks, subMonths, getISOWeek, setDate, setMonth, setYear, getDate, getYear } from 'date-fns';\nexport function adapterFactory() {\n  return __assign(__assign({}, baseAdapterFactory()), {\n    addWeeks: addWeeks,\n    addMonths: addMonths,\n    subDays: subDays,\n    subWeeks: subWeeks,\n    subMonths: subMonths,\n    getISOWeek: getISOWeek,\n    setDate: setDate,\n    setMonth: setMonth,\n    setYear: setYear,\n    getDate: getDate,\n    getYear: getYear\n  });\n}\n//# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}