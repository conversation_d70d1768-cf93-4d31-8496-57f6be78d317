{"ast": null, "code": "import { Observable } from \"../../utilities/index.js\";\nexport function fromPromise(promise) {\n  return new Observable(function (observer) {\n    promise.then(function (value) {\n      observer.next(value);\n      observer.complete();\n    }).catch(observer.error.bind(observer));\n  });\n}\n//# sourceMappingURL=fromPromise.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}