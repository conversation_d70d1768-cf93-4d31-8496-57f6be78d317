{"ast": null, "code": "export function buildMatchFn(args) {\n  return (string, options = {}) => {\n    const width = options.width;\n    const matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    const matchResult = string.match(matchPattern);\n    if (!matchResult) {\n      return null;\n    }\n    const matchedString = matchResult[0];\n    const parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    const key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, pattern => pattern.test(matchedString)) :\n    // [TODO] -- I challenge you to fix the type\n    findKey(parsePatterns, pattern => pattern.test(matchedString));\n    let value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ?\n    // [TODO] -- I challenge you to fix the type\n    options.valueCallback(value) : value;\n    const rest = string.slice(matchedString.length);\n    return {\n      value,\n      rest\n    };\n  };\n}\nfunction findKey(object, predicate) {\n  for (const key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}\nfunction findIndex(array, predicate) {\n  for (let key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}