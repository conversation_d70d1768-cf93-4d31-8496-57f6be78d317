{"ast": null, "code": "import { filter } from 'rxjs/operators';\nimport { Observable } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./message.service\";\nimport * as i2 from \"./logger.service\";\nexport let UserStatusService = /*#__PURE__*/(() => {\n  class UserStatusService {\n    constructor(messageService, logger, ngZone) {\n      this.messageService = messageService;\n      this.logger = logger;\n      this.ngZone = ngZone;\n      this.onlineUsers = new Map();\n      this.reconnectionAttempts = 0;\n      this.maxReconnectionAttempts = 5;\n      this.reconnectionDelay = 1000;\n      this.initStatusSubscription();\n    }\n    //helper methode\n    initStatusSubscription() {\n      this.logger.debug('Initializing user status subscription');\n      this.ngZone.runOutsideAngular(() => {\n        try {\n          this.statusSub?.unsubscribe(); // Unsubscribe from any existing subscription\n          this.statusSub = this.messageService.subscribeToUserStatus().subscribe({\n            next: user => this.handleUserStatusUpdate(user),\n            error: error => this.handleSubscriptionError(error),\n            complete: () => this.logger.debug('User status subscription completed')\n          });\n          this.logger.debug('User status subscription initialized successfully');\n        } catch (error) {\n          this.logger.error('Error initializing user status subscription:', error);\n          // Schedule a retry after a delay\n          setTimeout(() => this.initStatusSubscription(), 5000);\n        }\n      });\n    }\n    handleUserStatusUpdate(user) {\n      this.ngZone.run(() => {\n        const isOnline = user.isOnline ?? false;\n        if (isOnline) {\n          this.onlineUsers.set(user._id, user);\n          this.logger.debug(`User ${user.username} is now online`, {\n            userId: user._id\n          });\n        } else {\n          this.onlineUsers.delete(user._id);\n          this.logger.debug(`User ${user.username} is now offline`, {\n            userId: user._id\n          });\n        }\n        this.reconnectionAttempts = 0;\n      });\n    }\n    handleSubscriptionError(error) {\n      this.logger.error('Status subscription error', error, {\n        attempt: this.reconnectionAttempts,\n        maxAttempts: this.maxReconnectionAttempts\n      });\n      if (this.reconnectionAttempts < this.maxReconnectionAttempts) {\n        this.reconnectionAttempts++;\n        const delay = this.reconnectionDelay * Math.pow(2, this.reconnectionAttempts - 1);\n        this.logger.debug(`Attempting reconnection in ${delay}ms`, {\n          attempt: this.reconnectionAttempts,\n          maxAttempts: this.maxReconnectionAttempts\n        });\n        setTimeout(() => {\n          this.initStatusSubscription();\n        }, delay);\n      } else {\n        this.logger.error('Max reconnection attempts reached', undefined, {\n          maxAttempts: this.maxReconnectionAttempts\n        });\n      }\n    }\n    //  méthodes\n    trackUserPresence(userId) {\n      return new Observable(observer => {\n        // État initial - avec vérification que isOnline est défini\n        const user = this.onlineUsers.get(userId);\n        observer.next(user?.isOnline ?? false);\n        // Abonnement aux changements\n        const sub = this.messageService.subscribeToUserStatus().pipe(filter(user => user._id === userId)).subscribe({\n          next: user => observer.next(user.isOnline ?? false),\n          error: err => observer.error(err)\n        });\n        return () => sub.unsubscribe();\n      });\n    }\n    isUserOnline(userId) {\n      const user = this.onlineUsers.get(userId);\n      return user?.isOnline ?? false;\n    }\n    getOnlineUsers() {\n      return Array.from(this.onlineUsers.values());\n    }\n    getUserStatus(userId) {\n      const user = this.onlineUsers.get(userId);\n      return {\n        isOnline: user?.isOnline ?? false,\n        lastSeen: user?.lastActive ? new Date(user.lastActive) : undefined\n      };\n    }\n    ngOnDestroy() {\n      this.statusSub?.unsubscribe();\n      this.onlineUsers.clear();\n      this.logger.debug('UserStatusService destroyed');\n    }\n    static {\n      this.ɵfac = function UserStatusService_Factory(t) {\n        return new (t || UserStatusService)(i0.ɵɵinject(i1.MessageService), i0.ɵɵinject(i2.LoggerService), i0.ɵɵinject(i0.NgZone));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: UserStatusService,\n        factory: UserStatusService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return UserStatusService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}