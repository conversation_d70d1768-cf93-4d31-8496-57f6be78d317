{"ast": null, "code": "var prefixCounts = new Map();\n// These IDs won't be globally unique, but they will be unique within this\n// process, thanks to the counter, and unguessable thanks to the random suffix.\nexport function makeUniqueId(prefix) {\n  var count = prefixCounts.get(prefix) || 1;\n  prefixCounts.set(prefix, count + 1);\n  return \"\".concat(prefix, \":\").concat(count, \":\").concat(Math.random().toString(36).slice(2));\n}\n//# sourceMappingURL=makeUniqueId.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}