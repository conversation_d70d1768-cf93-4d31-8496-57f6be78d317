{"ast": null, "code": "import { __assign, __extends, __rest } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { dep } from \"optimism\";\nimport { equal } from \"@wry/equality\";\nimport { Trie } from \"@wry/trie\";\nimport { isReference, makeReference, DeepMerger, maybeDeepFreeze, canUseWeakMap, isNonNullObject } from \"../../utilities/index.js\";\nimport { hasOwn, fieldNameFromStoreName } from \"./helpers.js\";\nvar DELETE = Object.create(null);\nvar delModifier = function () {\n  return DELETE;\n};\nvar INVALIDATE = Object.create(null);\nvar EntityStore = /** @class */function () {\n  function EntityStore(policies, group) {\n    var _this = this;\n    this.policies = policies;\n    this.group = group;\n    this.data = Object.create(null);\n    // Maps root entity IDs to the number of times they have been retained, minus\n    // the number of times they have been released. Retained entities keep other\n    // entities they reference (even indirectly) from being garbage collected.\n    this.rootIds = Object.create(null);\n    // Lazily tracks { __ref: <dataId> } strings contained by this.data[dataId].\n    this.refs = Object.create(null);\n    // Bound function that can be passed around to provide easy access to fields\n    // of Reference objects as well as ordinary objects.\n    this.getFieldValue = function (objectOrReference, storeFieldName) {\n      return maybeDeepFreeze(isReference(objectOrReference) ? _this.get(objectOrReference.__ref, storeFieldName) : objectOrReference && objectOrReference[storeFieldName]);\n    };\n    // Returns true for non-normalized StoreObjects and non-dangling\n    // References, indicating that readField(name, objOrRef) has a chance of\n    // working. Useful for filtering out dangling references from lists.\n    this.canRead = function (objOrRef) {\n      return isReference(objOrRef) ? _this.has(objOrRef.__ref) : typeof objOrRef === \"object\";\n    };\n    // Bound function that converts an id or an object with a __typename and\n    // primary key fields to a Reference object. If called with a Reference object,\n    // that same Reference object is returned. Pass true for mergeIntoStore to persist\n    // an object into the store.\n    this.toReference = function (objOrIdOrRef, mergeIntoStore) {\n      if (typeof objOrIdOrRef === \"string\") {\n        return makeReference(objOrIdOrRef);\n      }\n      if (isReference(objOrIdOrRef)) {\n        return objOrIdOrRef;\n      }\n      var id = _this.policies.identify(objOrIdOrRef)[0];\n      if (id) {\n        var ref = makeReference(id);\n        if (mergeIntoStore) {\n          _this.merge(id, objOrIdOrRef);\n        }\n        return ref;\n      }\n    };\n  }\n  // Although the EntityStore class is abstract, it contains concrete\n  // implementations of the various NormalizedCache interface methods that\n  // are inherited by the Root and Layer subclasses.\n  EntityStore.prototype.toObject = function () {\n    return __assign({}, this.data);\n  };\n  EntityStore.prototype.has = function (dataId) {\n    return this.lookup(dataId, true) !== void 0;\n  };\n  EntityStore.prototype.get = function (dataId, fieldName) {\n    this.group.depend(dataId, fieldName);\n    if (hasOwn.call(this.data, dataId)) {\n      var storeObject = this.data[dataId];\n      if (storeObject && hasOwn.call(storeObject, fieldName)) {\n        return storeObject[fieldName];\n      }\n    }\n    if (fieldName === \"__typename\" && hasOwn.call(this.policies.rootTypenamesById, dataId)) {\n      return this.policies.rootTypenamesById[dataId];\n    }\n    if (this instanceof Layer) {\n      return this.parent.get(dataId, fieldName);\n    }\n  };\n  EntityStore.prototype.lookup = function (dataId, dependOnExistence) {\n    // The has method (above) calls lookup with dependOnExistence = true, so\n    // that it can later be invalidated when we add or remove a StoreObject for\n    // this dataId. Any consumer who cares about the contents of the StoreObject\n    // should not rely on this dependency, since the contents could change\n    // without the object being added or removed.\n    if (dependOnExistence) this.group.depend(dataId, \"__exists\");\n    if (hasOwn.call(this.data, dataId)) {\n      return this.data[dataId];\n    }\n    if (this instanceof Layer) {\n      return this.parent.lookup(dataId, dependOnExistence);\n    }\n    if (this.policies.rootTypenamesById[dataId]) {\n      return Object.create(null);\n    }\n  };\n  EntityStore.prototype.merge = function (older, newer) {\n    var _this = this;\n    var dataId;\n    // Convert unexpected references to ID strings.\n    if (isReference(older)) older = older.__ref;\n    if (isReference(newer)) newer = newer.__ref;\n    var existing = typeof older === \"string\" ? this.lookup(dataId = older) : older;\n    var incoming = typeof newer === \"string\" ? this.lookup(dataId = newer) : newer;\n    // If newer was a string ID, but that ID was not defined in this store,\n    // then there are no fields to be merged, so we're done.\n    if (!incoming) return;\n    invariant(typeof dataId === \"string\", 2);\n    var merged = new DeepMerger(storeObjectReconciler).merge(existing, incoming);\n    // Even if merged === existing, existing may have come from a lower\n    // layer, so we always need to set this.data[dataId] on this level.\n    this.data[dataId] = merged;\n    if (merged !== existing) {\n      delete this.refs[dataId];\n      if (this.group.caching) {\n        var fieldsToDirty_1 = Object.create(null);\n        // If we added a new StoreObject where there was previously none, dirty\n        // anything that depended on the existence of this dataId, such as the\n        // EntityStore#has method.\n        if (!existing) fieldsToDirty_1.__exists = 1;\n        // Now invalidate dependents who called getFieldValue for any fields\n        // that are changing as a result of this merge.\n        Object.keys(incoming).forEach(function (storeFieldName) {\n          if (!existing || existing[storeFieldName] !== merged[storeFieldName]) {\n            // Always dirty the full storeFieldName, which may include\n            // serialized arguments following the fieldName prefix.\n            fieldsToDirty_1[storeFieldName] = 1;\n            // Also dirty fieldNameFromStoreName(storeFieldName) if it's\n            // different from storeFieldName and this field does not have\n            // keyArgs configured, because that means the cache can't make\n            // any assumptions about how field values with the same field\n            // name but different arguments might be interrelated, so it\n            // must err on the side of invalidating all field values that\n            // share the same short fieldName, regardless of arguments.\n            var fieldName = fieldNameFromStoreName(storeFieldName);\n            if (fieldName !== storeFieldName && !_this.policies.hasKeyArgs(merged.__typename, fieldName)) {\n              fieldsToDirty_1[fieldName] = 1;\n            }\n            // If merged[storeFieldName] has become undefined, and this is the\n            // Root layer, actually delete the property from the merged object,\n            // which is guaranteed to have been created fresh in this method.\n            if (merged[storeFieldName] === void 0 && !(_this instanceof Layer)) {\n              delete merged[storeFieldName];\n            }\n          }\n        });\n        if (fieldsToDirty_1.__typename && !(existing && existing.__typename) &&\n        // Since we return default root __typename strings\n        // automatically from store.get, we don't need to dirty the\n        // ROOT_QUERY.__typename field if merged.__typename is equal\n        // to the default string (usually \"Query\").\n        this.policies.rootTypenamesById[dataId] === merged.__typename) {\n          delete fieldsToDirty_1.__typename;\n        }\n        Object.keys(fieldsToDirty_1).forEach(function (fieldName) {\n          return _this.group.dirty(dataId, fieldName);\n        });\n      }\n    }\n  };\n  EntityStore.prototype.modify = function (dataId, fields) {\n    var _this = this;\n    var storeObject = this.lookup(dataId);\n    if (storeObject) {\n      var changedFields_1 = Object.create(null);\n      var needToMerge_1 = false;\n      var allDeleted_1 = true;\n      var sharedDetails_1 = {\n        DELETE: DELETE,\n        INVALIDATE: INVALIDATE,\n        isReference: isReference,\n        toReference: this.toReference,\n        canRead: this.canRead,\n        readField: function (fieldNameOrOptions, from) {\n          return _this.policies.readField(typeof fieldNameOrOptions === \"string\" ? {\n            fieldName: fieldNameOrOptions,\n            from: from || makeReference(dataId)\n          } : fieldNameOrOptions, {\n            store: _this\n          });\n        }\n      };\n      Object.keys(storeObject).forEach(function (storeFieldName) {\n        var fieldName = fieldNameFromStoreName(storeFieldName);\n        var fieldValue = storeObject[storeFieldName];\n        if (fieldValue === void 0) return;\n        var modify = typeof fields === \"function\" ? fields : fields[storeFieldName] || fields[fieldName];\n        if (modify) {\n          var newValue = modify === delModifier ? DELETE : modify(maybeDeepFreeze(fieldValue), __assign(__assign({}, sharedDetails_1), {\n            fieldName: fieldName,\n            storeFieldName: storeFieldName,\n            storage: _this.getStorage(dataId, storeFieldName)\n          }));\n          if (newValue === INVALIDATE) {\n            _this.group.dirty(dataId, storeFieldName);\n          } else {\n            if (newValue === DELETE) newValue = void 0;\n            if (newValue !== fieldValue) {\n              changedFields_1[storeFieldName] = newValue;\n              needToMerge_1 = true;\n              fieldValue = newValue;\n              if (globalThis.__DEV__ !== false) {\n                var checkReference = function (ref) {\n                  if (_this.lookup(ref.__ref) === undefined) {\n                    globalThis.__DEV__ !== false && invariant.warn(3, ref);\n                    return true;\n                  }\n                };\n                if (isReference(newValue)) {\n                  checkReference(newValue);\n                } else if (Array.isArray(newValue)) {\n                  // Warn about writing \"mixed\" arrays of Reference and non-Reference objects\n                  var seenReference = false;\n                  var someNonReference = void 0;\n                  for (var _i = 0, newValue_1 = newValue; _i < newValue_1.length; _i++) {\n                    var value = newValue_1[_i];\n                    if (isReference(value)) {\n                      seenReference = true;\n                      if (checkReference(value)) break;\n                    } else {\n                      // Do not warn on primitive values, since those could never be represented\n                      // by a reference. This is a valid (albeit uncommon) use case.\n                      if (typeof value === \"object\" && !!value) {\n                        var id = _this.policies.identify(value)[0];\n                        // check if object could even be referenced, otherwise we are not interested in it for this warning\n                        if (id) {\n                          someNonReference = value;\n                        }\n                      }\n                    }\n                    if (seenReference && someNonReference !== undefined) {\n                      globalThis.__DEV__ !== false && invariant.warn(4, someNonReference);\n                      break;\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n        if (fieldValue !== void 0) {\n          allDeleted_1 = false;\n        }\n      });\n      if (needToMerge_1) {\n        this.merge(dataId, changedFields_1);\n        if (allDeleted_1) {\n          if (this instanceof Layer) {\n            this.data[dataId] = void 0;\n          } else {\n            delete this.data[dataId];\n          }\n          this.group.dirty(dataId, \"__exists\");\n        }\n        return true;\n      }\n    }\n    return false;\n  };\n  // If called with only one argument, removes the entire entity\n  // identified by dataId. If called with a fieldName as well, removes all\n  // fields of that entity whose names match fieldName according to the\n  // fieldNameFromStoreName helper function. If called with a fieldName\n  // and variables, removes all fields of that entity whose names match fieldName\n  // and whose arguments when cached exactly match the variables passed.\n  EntityStore.prototype.delete = function (dataId, fieldName, args) {\n    var _a;\n    var storeObject = this.lookup(dataId);\n    if (storeObject) {\n      var typename = this.getFieldValue(storeObject, \"__typename\");\n      var storeFieldName = fieldName && args ? this.policies.getStoreFieldName({\n        typename: typename,\n        fieldName: fieldName,\n        args: args\n      }) : fieldName;\n      return this.modify(dataId, storeFieldName ? (_a = {}, _a[storeFieldName] = delModifier, _a) : delModifier);\n    }\n    return false;\n  };\n  EntityStore.prototype.evict = function (options, limit) {\n    var evicted = false;\n    if (options.id) {\n      if (hasOwn.call(this.data, options.id)) {\n        evicted = this.delete(options.id, options.fieldName, options.args);\n      }\n      if (this instanceof Layer && this !== limit) {\n        evicted = this.parent.evict(options, limit) || evicted;\n      }\n      // Always invalidate the field to trigger rereading of watched\n      // queries, even if no cache data was modified by the eviction,\n      // because queries may depend on computed fields with custom read\n      // functions, whose values are not stored in the EntityStore.\n      if (options.fieldName || evicted) {\n        this.group.dirty(options.id, options.fieldName || \"__exists\");\n      }\n    }\n    return evicted;\n  };\n  EntityStore.prototype.clear = function () {\n    this.replace(null);\n  };\n  EntityStore.prototype.extract = function () {\n    var _this = this;\n    var obj = this.toObject();\n    var extraRootIds = [];\n    this.getRootIdSet().forEach(function (id) {\n      if (!hasOwn.call(_this.policies.rootTypenamesById, id)) {\n        extraRootIds.push(id);\n      }\n    });\n    if (extraRootIds.length) {\n      obj.__META = {\n        extraRootIds: extraRootIds.sort()\n      };\n    }\n    return obj;\n  };\n  EntityStore.prototype.replace = function (newData) {\n    var _this = this;\n    Object.keys(this.data).forEach(function (dataId) {\n      if (!(newData && hasOwn.call(newData, dataId))) {\n        _this.delete(dataId);\n      }\n    });\n    if (newData) {\n      var __META = newData.__META,\n        rest_1 = __rest(newData, [\"__META\"]);\n      Object.keys(rest_1).forEach(function (dataId) {\n        _this.merge(dataId, rest_1[dataId]);\n      });\n      if (__META) {\n        __META.extraRootIds.forEach(this.retain, this);\n      }\n    }\n  };\n  EntityStore.prototype.retain = function (rootId) {\n    return this.rootIds[rootId] = (this.rootIds[rootId] || 0) + 1;\n  };\n  EntityStore.prototype.release = function (rootId) {\n    if (this.rootIds[rootId] > 0) {\n      var count = --this.rootIds[rootId];\n      if (!count) delete this.rootIds[rootId];\n      return count;\n    }\n    return 0;\n  };\n  // Return a Set<string> of all the ID strings that have been retained by\n  // this layer/root *and* any layers/roots beneath it.\n  EntityStore.prototype.getRootIdSet = function (ids) {\n    if (ids === void 0) {\n      ids = new Set();\n    }\n    Object.keys(this.rootIds).forEach(ids.add, ids);\n    if (this instanceof Layer) {\n      this.parent.getRootIdSet(ids);\n    } else {\n      // Official singleton IDs like ROOT_QUERY and ROOT_MUTATION are\n      // always considered roots for garbage collection, regardless of\n      // their retainment counts in this.rootIds.\n      Object.keys(this.policies.rootTypenamesById).forEach(ids.add, ids);\n    }\n    return ids;\n  };\n  // The goal of garbage collection is to remove IDs from the Root layer of the\n  // store that are no longer reachable starting from any IDs that have been\n  // explicitly retained (see retain and release, above). Returns an array of\n  // dataId strings that were removed from the store.\n  EntityStore.prototype.gc = function () {\n    var _this = this;\n    var ids = this.getRootIdSet();\n    var snapshot = this.toObject();\n    ids.forEach(function (id) {\n      if (hasOwn.call(snapshot, id)) {\n        // Because we are iterating over an ECMAScript Set, the IDs we add here\n        // will be visited in later iterations of the forEach loop only if they\n        // were not previously contained by the Set.\n        Object.keys(_this.findChildRefIds(id)).forEach(ids.add, ids);\n        // By removing IDs from the snapshot object here, we protect them from\n        // getting removed from the root store layer below.\n        delete snapshot[id];\n      }\n    });\n    var idsToRemove = Object.keys(snapshot);\n    if (idsToRemove.length) {\n      var root_1 = this;\n      while (root_1 instanceof Layer) root_1 = root_1.parent;\n      idsToRemove.forEach(function (id) {\n        return root_1.delete(id);\n      });\n    }\n    return idsToRemove;\n  };\n  EntityStore.prototype.findChildRefIds = function (dataId) {\n    if (!hasOwn.call(this.refs, dataId)) {\n      var found_1 = this.refs[dataId] = Object.create(null);\n      var root = this.data[dataId];\n      if (!root) return found_1;\n      var workSet_1 = new Set([root]);\n      // Within the store, only arrays and objects can contain child entity\n      // references, so we can prune the traversal using this predicate:\n      workSet_1.forEach(function (obj) {\n        if (isReference(obj)) {\n          found_1[obj.__ref] = true;\n          // In rare cases, a { __ref } Reference object may have other fields.\n          // This often indicates a mismerging of References with StoreObjects,\n          // but garbage collection should not be fooled by a stray __ref\n          // property in a StoreObject (ignoring all the other fields just\n          // because the StoreObject looks like a Reference). To avoid this\n          // premature termination of findChildRefIds recursion, we fall through\n          // to the code below, which will handle any other properties of obj.\n        }\n\n        if (isNonNullObject(obj)) {\n          Object.keys(obj).forEach(function (key) {\n            var child = obj[key];\n            // No need to add primitive values to the workSet, since they cannot\n            // contain reference objects.\n            if (isNonNullObject(child)) {\n              workSet_1.add(child);\n            }\n          });\n        }\n      });\n    }\n    return this.refs[dataId];\n  };\n  EntityStore.prototype.makeCacheKey = function () {\n    return this.group.keyMaker.lookupArray(arguments);\n  };\n  return EntityStore;\n}();\nexport { EntityStore };\n// A single CacheGroup represents a set of one or more EntityStore objects,\n// typically the Root store in a CacheGroup by itself, and all active Layer\n// stores in a group together. A single EntityStore object belongs to only\n// one CacheGroup, store.group. The CacheGroup is responsible for tracking\n// dependencies, so store.group is helpful for generating unique keys for\n// cached results that need to be invalidated when/if those dependencies\n// change. If we used the EntityStore objects themselves as cache keys (that\n// is, store rather than store.group), the cache would become unnecessarily\n// fragmented by all the different Layer objects. Instead, the CacheGroup\n// approach allows all optimistic Layer objects in the same linked list to\n// belong to one CacheGroup, with the non-optimistic Root object belonging\n// to another CacheGroup, allowing resultCaching dependencies to be tracked\n// separately for optimistic and non-optimistic entity data.\nvar CacheGroup = /** @class */function () {\n  function CacheGroup(caching, parent) {\n    if (parent === void 0) {\n      parent = null;\n    }\n    this.caching = caching;\n    this.parent = parent;\n    this.d = null;\n    this.resetCaching();\n  }\n  CacheGroup.prototype.resetCaching = function () {\n    this.d = this.caching ? dep() : null;\n    this.keyMaker = new Trie(canUseWeakMap);\n  };\n  CacheGroup.prototype.depend = function (dataId, storeFieldName) {\n    if (this.d) {\n      this.d(makeDepKey(dataId, storeFieldName));\n      var fieldName = fieldNameFromStoreName(storeFieldName);\n      if (fieldName !== storeFieldName) {\n        // Fields with arguments that contribute extra identifying\n        // information to the fieldName (thus forming the storeFieldName)\n        // depend not only on the full storeFieldName but also on the\n        // short fieldName, so the field can be invalidated using either\n        // level of specificity.\n        this.d(makeDepKey(dataId, fieldName));\n      }\n      if (this.parent) {\n        this.parent.depend(dataId, storeFieldName);\n      }\n    }\n  };\n  CacheGroup.prototype.dirty = function (dataId, storeFieldName) {\n    if (this.d) {\n      this.d.dirty(makeDepKey(dataId, storeFieldName),\n      // When storeFieldName === \"__exists\", that means the entity identified\n      // by dataId has either disappeared from the cache or was newly added,\n      // so the result caching system would do well to \"forget everything it\n      // knows\" about that object. To achieve that kind of invalidation, we\n      // not only dirty the associated result cache entry, but also remove it\n      // completely from the dependency graph. For the optimism implementation\n      // details, see https://github.com/benjamn/optimism/pull/195.\n      storeFieldName === \"__exists\" ? \"forget\" : \"setDirty\");\n    }\n  };\n  return CacheGroup;\n}();\nfunction makeDepKey(dataId, storeFieldName) {\n  // Since field names cannot have '#' characters in them, this method\n  // of joining the field name and the ID should be unambiguous, and much\n  // cheaper than JSON.stringify([dataId, fieldName]).\n  return storeFieldName + \"#\" + dataId;\n}\nexport function maybeDependOnExistenceOfEntity(store, entityId) {\n  if (supportsResultCaching(store)) {\n    // We use this pseudo-field __exists elsewhere in the EntityStore code to\n    // represent changes in the existence of the entity object identified by\n    // entityId. This dependency gets reliably dirtied whenever an object with\n    // this ID is deleted (or newly created) within this group, so any result\n    // cache entries (for example, StoreReader#executeSelectionSet results) that\n    // depend on __exists for this entityId will get dirtied as well, leading to\n    // the eventual recomputation (instead of reuse) of those result objects the\n    // next time someone reads them from the cache.\n    store.group.depend(entityId, \"__exists\");\n  }\n}\n(function (EntityStore) {\n  // Refer to this class as EntityStore.Root outside this namespace.\n  var Root = /** @class */function (_super) {\n    __extends(Root, _super);\n    function Root(_a) {\n      var policies = _a.policies,\n        _b = _a.resultCaching,\n        resultCaching = _b === void 0 ? true : _b,\n        seed = _a.seed;\n      var _this = _super.call(this, policies, new CacheGroup(resultCaching)) || this;\n      _this.stump = new Stump(_this);\n      _this.storageTrie = new Trie(canUseWeakMap);\n      if (seed) _this.replace(seed);\n      return _this;\n    }\n    Root.prototype.addLayer = function (layerId, replay) {\n      // Adding an optimistic Layer on top of the Root actually adds the Layer\n      // on top of the Stump, so the Stump always comes between the Root and\n      // any Layer objects that we've added.\n      return this.stump.addLayer(layerId, replay);\n    };\n    Root.prototype.removeLayer = function () {\n      // Never remove the root layer.\n      return this;\n    };\n    Root.prototype.getStorage = function () {\n      return this.storageTrie.lookupArray(arguments);\n    };\n    return Root;\n  }(EntityStore);\n  EntityStore.Root = Root;\n})(EntityStore || (EntityStore = {}));\n// Not exported, since all Layer instances are created by the addLayer method\n// of the EntityStore.Root class.\nvar Layer = /** @class */function (_super) {\n  __extends(Layer, _super);\n  function Layer(id, parent, replay, group) {\n    var _this = _super.call(this, parent.policies, group) || this;\n    _this.id = id;\n    _this.parent = parent;\n    _this.replay = replay;\n    _this.group = group;\n    replay(_this);\n    return _this;\n  }\n  Layer.prototype.addLayer = function (layerId, replay) {\n    return new Layer(layerId, this, replay, this.group);\n  };\n  Layer.prototype.removeLayer = function (layerId) {\n    var _this = this;\n    // Remove all instances of the given id, not just the first one.\n    var parent = this.parent.removeLayer(layerId);\n    if (layerId === this.id) {\n      if (this.group.caching) {\n        // Dirty every ID we're removing. Technically we might be able to avoid\n        // dirtying fields that have values in higher layers, but we don't have\n        // easy access to higher layers here, and we're about to recreate those\n        // layers anyway (see parent.addLayer below).\n        Object.keys(this.data).forEach(function (dataId) {\n          var ownStoreObject = _this.data[dataId];\n          var parentStoreObject = parent[\"lookup\"](dataId);\n          if (!parentStoreObject) {\n            // The StoreObject identified by dataId was defined in this layer\n            // but will be undefined in the parent layer, so we can delete the\n            // whole entity using this.delete(dataId). Since we're about to\n            // throw this layer away, the only goal of this deletion is to dirty\n            // the removed fields.\n            _this.delete(dataId);\n          } else if (!ownStoreObject) {\n            // This layer had an entry for dataId but it was undefined, which\n            // means the entity was deleted in this layer, and it's about to\n            // become undeleted when we remove this layer, so we need to dirty\n            // all fields that are about to be reexposed.\n            _this.group.dirty(dataId, \"__exists\");\n            Object.keys(parentStoreObject).forEach(function (storeFieldName) {\n              _this.group.dirty(dataId, storeFieldName);\n            });\n          } else if (ownStoreObject !== parentStoreObject) {\n            // If ownStoreObject is not exactly the same as parentStoreObject,\n            // dirty any fields whose values will change as a result of this\n            // removal.\n            Object.keys(ownStoreObject).forEach(function (storeFieldName) {\n              if (!equal(ownStoreObject[storeFieldName], parentStoreObject[storeFieldName])) {\n                _this.group.dirty(dataId, storeFieldName);\n              }\n            });\n          }\n        });\n      }\n      return parent;\n    }\n    // No changes are necessary if the parent chain remains identical.\n    if (parent === this.parent) return this;\n    // Recreate this layer on top of the new parent.\n    return parent.addLayer(this.id, this.replay);\n  };\n  Layer.prototype.toObject = function () {\n    return __assign(__assign({}, this.parent.toObject()), this.data);\n  };\n  Layer.prototype.findChildRefIds = function (dataId) {\n    var fromParent = this.parent.findChildRefIds(dataId);\n    return hasOwn.call(this.data, dataId) ? __assign(__assign({}, fromParent), _super.prototype.findChildRefIds.call(this, dataId)) : fromParent;\n  };\n  Layer.prototype.getStorage = function () {\n    var p = this.parent;\n    while (p.parent) p = p.parent;\n    return p.getStorage.apply(p,\n    // @ts-expect-error\n    arguments);\n  };\n  return Layer;\n}(EntityStore);\n// Represents a Layer permanently installed just above the Root, which allows\n// reading optimistically (and registering optimistic dependencies) even when\n// no optimistic layers are currently active. The stump.group CacheGroup object\n// is shared by any/all Layer objects added on top of the Stump.\nvar Stump = /** @class */function (_super) {\n  __extends(Stump, _super);\n  function Stump(root) {\n    return _super.call(this, \"EntityStore.Stump\", root, function () {}, new CacheGroup(root.group.caching, root.group)) || this;\n  }\n  Stump.prototype.removeLayer = function () {\n    // Never remove the Stump layer.\n    return this;\n  };\n  Stump.prototype.merge = function (older, newer) {\n    // We never want to write any data into the Stump, so we forward any merge\n    // calls to the Root instead. Another option here would be to throw an\n    // exception, but the toReference(object, true) function can sometimes\n    // trigger Stump writes (which used to be Root writes, before the Stump\n    // concept was introduced).\n    return this.parent.merge(older, newer);\n  };\n  return Stump;\n}(Layer);\nfunction storeObjectReconciler(existingObject, incomingObject, property) {\n  var existingValue = existingObject[property];\n  var incomingValue = incomingObject[property];\n  // Wherever there is a key collision, prefer the incoming value, unless\n  // it is deeply equal to the existing value. It's worth checking deep\n  // equality here (even though blindly returning incoming would be\n  // logically correct) because preserving the referential identity of\n  // existing data can prevent needless rereading and rerendering.\n  return equal(existingValue, incomingValue) ? existingValue : incomingValue;\n}\nexport function supportsResultCaching(store) {\n  // When result caching is disabled, store.depend will be null.\n  return !!(store instanceof EntityStore && store.group.caching);\n}\n//# sourceMappingURL=entityStore.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}