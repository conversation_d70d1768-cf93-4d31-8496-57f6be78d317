{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/equipe.service\";\nimport * as i2 from \"src/app/services/membre.service\";\nimport * as i3 from \"src/app/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"src/app/services/notification.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction EquipeFormComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"div\", 23)(3, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 25);\n    i0.ɵɵtext(5, \" Chargement des donn\\u00E9es... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EquipeFormComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28)(3, \"div\", 29);\n    i0.ɵɵelement(4, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31)(6, \"h3\", 32);\n    i0.ɵɵtext(7, \" Erreur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 33);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction EquipeFormComponent_div_33_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"i\", 65);\n    i0.ɵɵtext(2, \" Ce nom d'\\u00E9quipe existe d\\u00E9j\\u00E0. Veuillez en choisir un autre. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_33_button_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_33_button_45_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.deleteEquipe());\n    });\n    i0.ɵɵelement(1, \"i\", 67);\n    i0.ɵɵtext(2, \" Supprimer \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipeFormComponent_div_33_span_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 68);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"fa-save\": a0,\n    \"fa-plus-circle\": a1\n  };\n};\nfunction EquipeFormComponent_div_33_i_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 37);\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c0, ctx_r8.isEditMode, !ctx_r8.isEditMode));\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"fa-edit\": a0,\n    \"fa-plus-circle\": a1\n  };\n};\nfunction EquipeFormComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵelementStart(3, \"div\", 34)(4, \"div\", 35)(5, \"h3\", 36);\n    i0.ɵɵelement(6, \"i\", 37);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 38);\n    i0.ɵɵtext(9, \" Remplissez les informations de base de l'\\u00E9quipe \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 39)(11, \"form\", 40);\n    i0.ɵɵlistener(\"ngSubmit\", function EquipeFormComponent_div_33_Template_form_ngSubmit_11_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onSubmit());\n    });\n    i0.ɵɵelementStart(12, \"div\")(13, \"label\", 41);\n    i0.ɵɵtext(14, \" Nom de l'\\u00E9quipe \");\n    i0.ɵɵelementStart(15, \"span\", 42);\n    i0.ɵɵtext(16, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 22)(18, \"div\", 43);\n    i0.ɵɵelement(19, \"i\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"input\", 45, 46);\n    i0.ɵɵlistener(\"input\", function EquipeFormComponent_div_33_Template_input_input_20_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const _r3 = i0.ɵɵreference(21);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.updateName(_r3.value));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, EquipeFormComponent_div_33_div_22_Template, 3, 0, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\")(24, \"label\", 41);\n    i0.ɵɵtext(25, \" Description \");\n    i0.ɵɵelementStart(26, \"span\", 42);\n    i0.ɵɵtext(27, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 22)(29, \"div\", 48);\n    i0.ɵɵelement(30, \"i\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"textarea\", 50, 51);\n    i0.ɵɵlistener(\"input\", function EquipeFormComponent_div_33_Template_textarea_input_31_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const _r5 = i0.ɵɵreference(32);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.updateDescription(_r5.value));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(33, \"input\", 52);\n    i0.ɵɵelementStart(34, \"div\", 53)(35, \"div\", 54)(36, \"div\", 55);\n    i0.ɵɵelement(37, \"i\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 33);\n    i0.ɵɵtext(39, \" Un administrateur par d\\u00E9faut sera assign\\u00E9 \\u00E0 cette \\u00E9quipe. \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"div\", 57)(41, \"div\", 58)(42, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function EquipeFormComponent_div_33_Template_button_click_42_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.cancel());\n    });\n    i0.ɵɵelement(43, \"i\", 17);\n    i0.ɵɵtext(44, \" Retour \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(45, EquipeFormComponent_div_33_button_45_Template, 3, 0, \"button\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"button\", 61);\n    i0.ɵɵtemplate(47, EquipeFormComponent_div_33_span_47_Template, 1, 0, \"span\", 62);\n    i0.ɵɵtemplate(48, EquipeFormComponent_div_33_i_48_Template, 1, 4, \"i\", 63);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(11, _c1, ctx_r2.isEditMode, !ctx_r2.isEditMode));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditMode ? \"Informations de l'\\u00E9quipe\" : \"D\\u00E9tails de la nouvelle \\u00E9quipe\", \" \");\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.name || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.nameExists);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.description || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.equipe.admin);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditMode && ctx_r2.equipeId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.submitting || !ctx_r2.equipe.name || !ctx_r2.equipe.description || ctx_r2.nameExists || ctx_r2.nameError || ctx_r2.descriptionError);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.submitting);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditMode ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er l'\\u00E9quipe\", \" \");\n  }\n}\nexport let EquipeFormComponent = /*#__PURE__*/(() => {\n  class EquipeFormComponent {\n    constructor(equipeService, membreService, userService, route, router, notificationService) {\n      this.equipeService = equipeService;\n      this.membreService = membreService;\n      this.userService = userService;\n      this.route = route;\n      this.router = router;\n      this.notificationService = notificationService;\n      this.equipe = {\n        name: '',\n        description: '',\n        admin: '' // Sera défini avec l'ID de l'utilisateur connecté\n      };\n\n      this.isEditMode = false;\n      this.loading = false;\n      this.submitting = false;\n      this.error = null;\n      this.equipeId = null;\n      this.nameExists = false;\n      this.nameError = false;\n      this.descriptionError = false;\n      this.checkingName = false;\n      this.existingEquipes = [];\n      this.availableMembers = []; // Liste des membres disponibles\n      this.availableUsers = []; // Liste des utilisateurs disponibles\n      this.currentUserId = null; // ID de l'utilisateur connecté\n    }\n\n    ngOnInit() {\n      console.log('EquipeFormComponent initialized');\n      // Récupérer l'ID de l'utilisateur connecté\n      this.getCurrentUser();\n      // Charger toutes les équipes pour vérifier les noms existants\n      this.loadAllEquipes();\n      // Charger tous les membres disponibles\n      this.loadAllMembers();\n      // Charger tous les utilisateurs disponibles\n      this.loadAllUsers();\n      try {\n        // Vérifier si nous sommes en mode édition (si un ID est présent dans l'URL)\n        this.equipeId = this.route.snapshot.paramMap.get('id');\n        this.isEditMode = !!this.equipeId;\n        console.log('Edit mode:', this.isEditMode, 'ID:', this.equipeId);\n        if (this.isEditMode && this.equipeId) {\n          this.loadEquipe(this.equipeId);\n          // Ajouter un délai pour s'assurer que l'équipe est chargée\n          setTimeout(() => {\n            console.log('Après délai - this.equipeId:', this.equipeId);\n            console.log('Après délai - this.equipe:', this.equipe);\n          }, 1000);\n        }\n      } catch (error) {\n        console.error('Error in ngOnInit:', error);\n        this.error = \"Erreur d'initialisation\";\n      }\n      // Ajouter un gestionnaire d'événements pour le bouton d'ajout de membre\n      setTimeout(() => {\n        const addButton = document.getElementById('addMembreButton');\n        if (addButton) {\n          console.log(\"Bouton d'ajout de membre trouvé\");\n          addButton.addEventListener('click', () => {\n            console.log(\"Bouton d'ajout de membre cliqué\");\n          });\n        } else {\n          console.log(\"Bouton d'ajout de membre non trouvé\");\n        }\n      }, 2000);\n    }\n    getCurrentUser() {\n      const token = localStorage.getItem('token');\n      if (token) {\n        this.userService.getProfile(token).subscribe({\n          next: user => {\n            console.log('Utilisateur connecté:', user);\n            this.currentUserId = user._id || user.id;\n            // Définir l'admin de l'équipe avec l'ID de l'utilisateur connecté\n            if (!this.isEditMode && this.currentUserId) {\n              this.equipe.admin = this.currentUserId;\n              console.log('Admin défini pour nouvelle équipe:', this.equipe.admin);\n            }\n          },\n          error: error => {\n            console.error('Erreur lors de la récupération du profil utilisateur:', error);\n            this.error = \"Impossible de récupérer les informations de l'utilisateur connecté.\";\n          }\n        });\n      } else {\n        this.error = \"Aucun token d'authentification trouvé. Veuillez vous reconnecter.\";\n      }\n    }\n    loadAllMembers() {\n      this.membreService.getMembres().subscribe({\n        next: membres => {\n          this.availableMembers = membres;\n          console.log('Membres disponibles chargés:', membres);\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des membres:', error);\n          this.error = 'Impossible de charger la liste des membres. Veuillez réessayer plus tard.';\n        }\n      });\n    }\n    loadAllUsers() {\n      const token = localStorage.getItem('token');\n      if (token) {\n        this.userService.getAllUsers(token).subscribe({\n          next: users => {\n            this.availableUsers = users;\n            console.log('Utilisateurs disponibles chargés:', users);\n          },\n          error: error => {\n            console.error('Erreur lors du chargement des utilisateurs:', error);\n            this.error = 'Impossible de charger la liste des utilisateurs. Veuillez réessayer plus tard.';\n          }\n        });\n      }\n    }\n    loadAllEquipes() {\n      this.equipeService.getEquipes().subscribe({\n        next: equipes => {\n          this.existingEquipes = equipes;\n          console.log('Équipes existantes chargées:', equipes);\n        },\n        error: error => {\n          console.error('Erreur lors du chargement des équipes:', error);\n        }\n      });\n    }\n    loadEquipe(id) {\n      console.log('Loading equipe with ID:', id);\n      this.loading = true;\n      this.error = null;\n      this.equipeService.getEquipe(id).subscribe({\n        next: data => {\n          console.log('Équipe chargée:', data);\n          this.equipe = data;\n          // Vérifier que l'ID est correctement défini\n          console.log(\"ID de l'équipe après chargement:\", this.equipe._id);\n          console.log('this.equipeId:', this.equipeId);\n          // Si l'équipe a des membres, récupérer les informations de chaque membre\n          if (this.equipe.members && this.equipe.members.length > 0) {\n            this.loadMembersDetails();\n          }\n          this.loading = false;\n        },\n        error: error => {\n          console.error(\"Erreur lors du chargement de l'équipe:\", error);\n          this.error = \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n          this.loading = false;\n        }\n      });\n    }\n    // Méthode pour récupérer les détails des membres de l'équipe\n    loadMembersDetails() {\n      if (!this.equipe.members || this.equipe.members.length === 0) {\n        return;\n      }\n      console.log(\"Chargement des détails des membres de l'équipe...\");\n      // Pour chaque membre de l'équipe, essayer de trouver ses informations dans la liste des utilisateurs\n      this.equipe.members.forEach(membreId => {\n        // Chercher d'abord dans la liste des utilisateurs\n        const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n        if (user) {\n          console.log(`Membre ${membreId} trouvé dans la liste des utilisateurs:`, user);\n          // Vérifier si toutes les informations nécessaires sont présentes\n          if (!user.email || !user.profession && !user.role) {\n            // Si des informations manquent, essayer de les récupérer depuis l'API\n            const token = localStorage.getItem('token');\n            if (token) {\n              this.userService.getUserById(membreId, token).subscribe({\n                next: userData => {\n                  console.log(`Détails supplémentaires de l'utilisateur ${membreId} récupérés:`, userData);\n                  // Mettre à jour l'utilisateur dans la liste avec les nouvelles informations\n                  const index = this.availableUsers.findIndex(u => u._id === membreId || u.id === membreId);\n                  if (index !== -1) {\n                    this.availableUsers[index] = {\n                      ...this.availableUsers[index],\n                      ...userData\n                    };\n                  }\n                },\n                error: error => {\n                  console.error(`Erreur lors de la récupération des détails supplémentaires de l'utilisateur ${membreId}:`, error);\n                }\n              });\n            }\n          }\n        } else {\n          // Si non trouvé, essayer de récupérer l'utilisateur depuis l'API\n          const token = localStorage.getItem('token');\n          if (token) {\n            this.userService.getUserById(membreId, token).subscribe({\n              next: userData => {\n                console.log(`Détails de l'utilisateur ${membreId} récupérés:`, userData);\n                // Ajouter l'utilisateur à la liste des utilisateurs disponibles s'il n'y est pas déjà\n                if (!this.availableUsers.some(u => u._id === userData._id || u.id === userData.id)) {\n                  this.availableUsers.push(userData);\n                }\n              },\n              error: error => {\n                console.error(`Erreur lors de la récupération des détails de l'utilisateur ${membreId}:`, error);\n              }\n            });\n          }\n        }\n      });\n    }\n    checkNameExists(name) {\n      // En mode édition, ignorer l'équipe actuelle\n      if (this.isEditMode && this.equipeId) {\n        return this.existingEquipes.some(e => e.name === name && e._id !== this.equipeId);\n      }\n      // En mode création, vérifier tous les noms\n      return this.existingEquipes.some(e => e.name === name);\n    }\n    updateName(value) {\n      console.log('Name updated:', value);\n      this.equipe.name = value;\n      // Vérifier si le nom existe déjà\n      this.nameExists = this.checkNameExists(value);\n      if (this.nameExists) {\n        console.warn(\"Ce nom d'équipe existe déjà\");\n      }\n      // Vérifier si le nom a au moins 3 caractères\n      this.nameError = value.length > 0 && value.length < 3;\n      if (this.nameError) {\n        console.warn('Le nom doit contenir au moins 3 caractères');\n      }\n    }\n    updateDescription(value) {\n      console.log('Description updated:', value);\n      this.equipe.description = value;\n      // Vérifier si la description a au moins 10 caractères\n      this.descriptionError = value.length > 0 && value.length < 10;\n      if (this.descriptionError) {\n        console.warn('La description doit contenir au moins 10 caractères');\n      }\n    }\n    onSubmit() {\n      console.log('Form submitted with:', this.equipe);\n      // Vérifier si le nom est présent et valide\n      if (!this.equipe.name) {\n        this.error = \"Le nom de l'équipe est requis.\";\n        return;\n      }\n      if (this.equipe.name.length < 3) {\n        this.nameError = true;\n        this.error = \"Le nom de l'équipe doit contenir au moins 3 caractères.\";\n        return;\n      }\n      // Vérifier si la description est présente et valide\n      if (!this.equipe.description) {\n        this.error = \"La description de l'équipe est requise.\";\n        return;\n      }\n      if (this.equipe.description.length < 10) {\n        this.descriptionError = true;\n        this.error = \"La description de l'équipe doit contenir au moins 10 caractères.\";\n        return;\n      }\n      // Vérifier si le nom existe déjà avant de soumettre\n      if (this.checkNameExists(this.equipe.name)) {\n        this.nameExists = true;\n        this.error = 'Une équipe avec ce nom existe déjà. Veuillez choisir un autre nom.';\n        return;\n      }\n      this.submitting = true;\n      this.error = null;\n      // S'assurer que l'admin est défini\n      if (!this.equipe.admin && this.currentUserId) {\n        this.equipe.admin = this.currentUserId;\n      }\n      if (!this.equipe.admin) {\n        this.error = \"Impossible de déterminer l'administrateur de l'équipe. Veuillez vous reconnecter.\";\n        return;\n      }\n      // Créer une copie de l'objet équipe pour éviter les problèmes de référence\n      const equipeToSave = {\n        name: this.equipe.name,\n        description: this.equipe.description || '',\n        admin: this.equipe.admin\n      };\n      // Ajouter l'ID si nous sommes en mode édition\n      if (this.isEditMode && this.equipeId) {\n        equipeToSave._id = this.equipeId;\n      }\n      console.log('Données à envoyer:', equipeToSave);\n      if (this.isEditMode && this.equipeId) {\n        // Mode édition\n        this.equipeService.updateEquipe(this.equipeId, equipeToSave).subscribe({\n          next: response => {\n            console.log('Équipe mise à jour avec succès:', response);\n            this.submitting = false;\n            this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été mise à jour avec succès.`);\n            this.router.navigate(['/equipes/liste']);\n          },\n          error: error => {\n            console.error(\"Erreur lors de la mise à jour de l'équipe:\", error);\n            this.error = `Impossible de mettre à jour l'équipe: ${error.message}`;\n            this.submitting = false;\n            this.notificationService.showError(`Erreur: ${error.message}`);\n          }\n        });\n      } else {\n        // Mode ajout\n        this.equipeService.addEquipe(equipeToSave).subscribe({\n          next: response => {\n            console.log('Équipe ajoutée avec succès:', response);\n            this.submitting = false;\n            this.notificationService.showSuccess(`L'équipe \"${response.name}\" a été créée avec succès.`);\n            this.router.navigate(['/equipes/liste']);\n          },\n          error: error => {\n            console.error(\"Erreur lors de l'ajout de l'équipe:\", error);\n            this.error = `Impossible d'ajouter l'équipe: ${error.message}`;\n            this.submitting = false;\n            this.notificationService.showError(`Erreur: ${error.message}`);\n          }\n        });\n      }\n    }\n    cancel() {\n      console.log('Form cancelled');\n      this.router.navigate(['/admin/equipes']);\n    }\n    // Méthodes pour gérer les membres\n    addMembreToEquipe(membreId, role = 'membre') {\n      console.log('Début de addMembreToEquipe avec membreId:', membreId, 'et rôle:', role);\n      console.log('État actuel - this.equipeId:', this.equipeId);\n      console.log('État actuel - this.equipe:', this.equipe);\n      // Utiliser this.equipe._id si this.equipeId n'est pas défini\n      const equipeId = this.equipeId || this.equipe && this.equipe._id;\n      console.log('equipeId calculé:', equipeId);\n      if (!equipeId || !membreId) {\n        console.error(\"ID d'équipe ou ID de membre manquant\");\n        this.error = \"ID d'équipe ou ID de membre manquant\";\n        console.log('equipeId:', equipeId, 'membreId:', membreId);\n        // Afficher un message à l'utilisateur\n        this.notificationService.showError(\"Impossible d'ajouter le membre: ID d'équipe ou ID de membre manquant\");\n        return;\n      }\n      // Vérifier si le membre est déjà dans l'équipe\n      if (this.equipe.members && this.equipe.members.includes(membreId)) {\n        this.notificationService.showError(\"Ce membre fait déjà partie de l'équipe\");\n        return;\n      }\n      // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      const userName = user ? user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.name || membreId : membreId;\n      // Créer l'objet membre avec le rôle spécifié\n      const membre = {\n        id: membreId,\n        role: role\n      };\n      this.loading = true;\n      console.log(`Ajout de l'utilisateur \"${userName}\" comme ${role} à l'équipe ${equipeId}`);\n      this.equipeService.addMembreToEquipe(equipeId, membre).subscribe({\n        next: response => {\n          console.log('Membre ajouté avec succès:', response);\n          this.notificationService.showSuccess(`${userName} a été ajouté comme ${role === 'admin' ? 'administrateur' : 'membre'} à l'équipe`);\n          // Recharger l'équipe pour mettre à jour la liste des membres\n          this.loadEquipe(equipeId);\n          this.loading = false;\n        },\n        error: error => {\n          console.error(\"Erreur lors de l'ajout du membre:\", error);\n          this.error = \"Impossible d'ajouter le membre. Veuillez réessayer plus tard.\";\n          this.notificationService.showError(\"Erreur lors de l'ajout du membre: \" + error.message);\n          this.loading = false;\n        }\n      });\n    }\n    // Méthode pour obtenir le nom complet d'un membre à partir de son ID\n    getMembreName(membreId) {\n      // Chercher d'abord dans la liste des utilisateurs\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user) {\n        if (user.firstName && user.lastName) {\n          return `${user.firstName} ${user.lastName}`;\n        } else if (user.name) {\n          return user.name;\n        }\n      }\n      // Chercher ensuite dans la liste des membres\n      const membre = this.availableMembers.find(m => m._id === membreId || m.id === membreId);\n      if (membre && membre.name) {\n        return membre.name;\n      }\n      // Si aucun nom n'est trouvé, retourner l'ID\n      return membreId;\n    }\n    // Méthode pour obtenir l'email d'un membre\n    getMembreEmail(membreId) {\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user && user.email) {\n        return user.email;\n      }\n      return 'Non renseigné';\n    }\n    // Méthode pour obtenir la profession d'un membre\n    getMembreProfession(membreId) {\n      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);\n      if (user) {\n        if (user.profession) {\n          return user.profession === 'etudiant' ? 'Étudiant' : 'Professeur';\n        } else if (user.role) {\n          return user.role === 'etudiant' ? 'Étudiant' : 'Professeur';\n        }\n      }\n      return 'Non spécifié';\n    }\n    // Méthode pour obtenir le rôle d'un membre dans l'équipe\n    getMembreRole(_membreId) {\n      // Cette méthode nécessiterait d'avoir accès aux rôles des membres dans l'équipe\n      // Pour l'instant, nous retournons une valeur par défaut\n      return 'Membre';\n    }\n    removeMembreFromEquipe(membreId) {\n      console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n      console.log('État actuel - this.equipeId:', this.equipeId);\n      console.log('État actuel - this.equipe:', this.equipe);\n      // Utiliser this.equipe._id si this.equipeId n'est pas défini\n      const equipeId = this.equipeId || this.equipe && this.equipe._id;\n      if (!equipeId) {\n        console.error(\"ID d'équipe manquant\");\n        this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n        this.notificationService.showError(\"ID d'équipe manquant. Impossible de retirer le membre.\");\n        return;\n      }\n      if (!membreId) {\n        console.error('ID de membre manquant');\n        this.error = 'ID de membre manquant. Impossible de retirer le membre.';\n        this.notificationService.showError('ID de membre manquant. Impossible de retirer le membre.');\n        return;\n      }\n      // Obtenir le nom du membre pour l'afficher dans le message de confirmation\n      const membreName = this.getMembreName(membreId);\n      console.log(`Tentative de retrait de l'utilisateur ${membreId} (${membreName}) de l'équipe ${equipeId}`);\n      try {\n        if (confirm(`Êtes-vous sûr de vouloir retirer ${membreName} de l'équipe?`)) {\n          console.log('Confirmation acceptée, suppression en cours...');\n          this.loading = true;\n          this.error = null;\n          // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n          setTimeout(() => {\n            this.equipeService.removeMembreFromEquipe(equipeId, membreId).subscribe({\n              next: response => {\n                console.log(`Utilisateur \"${membreName}\" retiré avec succès de l'équipe:`, response);\n                this.loading = false;\n                this.notificationService.showSuccess(`${membreName} a été retiré avec succès de l'équipe`);\n                // Recharger l'équipe pour mettre à jour la liste des membres\n                this.loadEquipe(equipeId);\n              },\n              error: error => {\n                console.error(`Erreur lors du retrait de l'utilisateur \"${membreName}\":`, error);\n                console.error(\"Détails de l'erreur:\", {\n                  status: error.status,\n                  message: error.message,\n                  error: error\n                });\n                this.loading = false;\n                this.error = `Impossible de retirer l'utilisateur \"${membreName}\" de l'équipe: ${error.message || 'Erreur inconnue'}`;\n                this.notificationService.showError(`Erreur lors du retrait du membre: ${this.error}`);\n              }\n            });\n          }, 500);\n        } else {\n          console.log(\"Suppression annulée par l'utilisateur\");\n        }\n      } catch (error) {\n        console.error('Exception lors du retrait du membre:', error);\n        this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n        this.notificationService.showError(`Exception: ${this.error}`);\n      }\n    }\n    // Méthode pour supprimer l'équipe\n    deleteEquipe() {\n      console.log('Méthode deleteEquipe appelée dans equipe-form.component.ts');\n      console.log('État actuel - this.equipeId:', this.equipeId);\n      console.log('État actuel - this.equipe:', this.equipe);\n      // Utiliser this.equipe._id si this.equipeId n'est pas défini\n      const equipeId = this.equipeId || this.equipe && this.equipe._id;\n      if (!equipeId) {\n        console.error(\"ID d'équipe manquant\");\n        this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n        this.notificationService.showError(\"ID d'équipe manquant. Impossible de supprimer l'équipe.\");\n        return;\n      }\n      console.log(\"ID de l'équipe à supprimer (final):\", equipeId);\n      try {\n        if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\"? Cette action est irréversible.`)) {\n          console.log('Confirmation acceptée, suppression en cours...');\n          this.loading = true;\n          this.error = null;\n          // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n          setTimeout(() => {\n            this.equipeService.deleteEquipe(equipeId).subscribe({\n              next: response => {\n                console.log('Équipe supprimée avec succès, réponse:', response);\n                this.loading = false;\n                this.notificationService.showSuccess(`L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`);\n                // Ajouter un délai avant la redirection\n                setTimeout(() => {\n                  this.router.navigate(['/admin/equipes']);\n                }, 500);\n              },\n              error: error => {\n                console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n                console.error(\"Détails de l'erreur:\", {\n                  status: error.status,\n                  message: error.message,\n                  error: error\n                });\n                this.loading = false;\n                this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;\n                this.notificationService.showError(`Erreur lors de la suppression: ${this.error}`);\n              }\n            });\n          }, 500);\n        } else {\n          console.log(\"Suppression annulée par l'utilisateur\");\n        }\n      } catch (error) {\n        console.error('Exception lors de la suppression:', error);\n        this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n        this.notificationService.showError(`Exception: ${this.error}`);\n      }\n    }\n    static {\n      this.ɵfac = function EquipeFormComponent_Factory(t) {\n        return new (t || EquipeFormComponent)(i0.ɵɵdirectiveInject(i1.EquipeService), i0.ɵɵdirectiveInject(i2.MembreService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.NotificationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EquipeFormComponent,\n        selectors: [[\"app-equipe-form\"]],\n        decls: 34,\n        vars: 5,\n        consts: [[1, \"min-h-screen\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#dac4ea]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#dac4ea]\", \"dark:border-[#00f7ff]\"], [1, \"max-w-4xl\", \"mx-auto\", \"p-6\", \"relative\", \"z-10\"], [1, \"mb-8\", \"relative\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"h-1\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"blur-md\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"p-6\", \"backdrop-blur-sm\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"flex\", \"flex-col\", \"lg:flex-row\", \"lg:items-center\", \"lg:justify-between\"], [1, \"mb-4\", \"lg:mb-0\"], [1, \"text-3xl\", \"font-bold\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-2\", \"tracking-wide\"], [1, \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"text-sm\"], [1, \"bg-[#dac4ea]/20\", \"dark:bg-[#00f7ff]/20\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#dac4ea]/30\", \"dark:hover:bg-[#00f7ff]/30\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"mr-2\"], [\"class\", \"flex flex-col items-center justify-center py-16\", 4, \"ngIf\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"class\", \"mb-8 relative\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"py-16\"], [1, \"relative\"], [1, \"w-12\", \"h-12\", \"border-3\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", \"border-t-[#dac4ea]\", \"dark:border-t-[#00f7ff]\", \"rounded-full\", \"animate-spin\"], [1, \"absolute\", \"inset-0\", \"bg-[#dac4ea]/20\", \"dark:bg-[#00f7ff]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"mt-4\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"text-sm\", \"font-medium\", \"tracking-wide\"], [1, \"mb-6\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff3b30]/10\", \"border-l-4\", \"border-[#ff6b69]\", \"dark:border-[#ff3b30]\", \"rounded-lg\", \"p-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mr-3\", \"text-xl\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"flex-1\"], [1, \"font-semibold\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\"], [1, \"bg-white\", \"dark:bg-[#1a1a1a]\", \"rounded-xl\", \"shadow-lg\", \"dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\"], [1, \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"p-6\"], [1, \"text-xl\", \"font-bold\", \"text-white\", \"mb-1\", \"flex\", \"items-center\"], [1, \"fas\", \"mr-2\", 3, \"ngClass\"], [1, \"text-white/80\", \"text-sm\"], [1, \"p-6\"], [1, \"space-y-6\", 3, \"ngSubmit\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mb-2\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [1, \"fas\", \"fa-users\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\"], [\"type\", \"text\", \"placeholder\", \"Entrez le nom de l'\\u00E9quipe\", \"required\", \"\", \"minlength\", \"3\", 1, \"w-full\", \"pl-10\", \"pr-4\", \"py-3\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"placeholder-[#6d6870]/50\", \"dark:placeholder-[#a0a0a0]\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"transition-all\", 3, \"value\", \"input\"], [\"nameInput\", \"\"], [\"class\", \"mt-1 text-sm text-[#ff6b69] dark:text-[#ff3b30] flex items-center\", 4, \"ngIf\"], [1, \"absolute\", \"top-3\", \"left-3\", \"pointer-events-none\"], [1, \"fas\", \"fa-file-alt\", \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\"], [\"rows\", \"4\", \"placeholder\", \"D\\u00E9crivez l'objectif et les activit\\u00E9s de cette \\u00E9quipe\", \"required\", \"\", \"minlength\", \"10\", 1, \"w-full\", \"pl-10\", \"pr-4\", \"py-3\", \"bg-[#f0f4f8]\", \"dark:bg-[#0a0a0a]\", \"border\", \"border-[#dac4ea]/20\", \"dark:border-[#00f7ff]/20\", \"rounded-lg\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"placeholder-[#6d6870]/50\", \"dark:placeholder-[#a0a0a0]\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#dac4ea]\", \"dark:focus:ring-[#00f7ff]\", \"focus:border-transparent\", \"transition-all\", \"resize-none\", 3, \"value\", \"input\"], [\"descInput\", \"\"], [\"type\", \"hidden\", 3, \"value\"], [1, \"bg-[#dac4ea]/10\", \"dark:bg-[#00f7ff]/10\", \"border-l-4\", \"border-[#dac4ea]\", \"dark:border-[#00f7ff]\", \"rounded-lg\", \"p-4\"], [1, \"flex\", \"items-center\"], [1, \"text-[#dac4ea]\", \"dark:text-[#00f7ff]\", \"mr-3\", \"text-lg\"], [1, \"fas\", \"fa-info-circle\"], [1, \"flex\", \"items-center\", \"justify-between\", \"pt-4\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [\"type\", \"button\", 1, \"bg-[#6d6870]/20\", \"dark:bg-[#a0a0a0]/20\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#6d6870]/30\", \"dark:hover:bg-[#a0a0a0]/30\", 3, \"click\"], [\"type\", \"button\", \"class\", \"bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#ff6b69]/30 dark:hover:bg-[#ff3b30]/30\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"relative\", \"overflow-hidden\", \"group\", \"bg-gradient-to-r\", \"from-[#dac4ea]\", \"to-[#8b5a9f]\", \"dark:from-[#00f7ff]\", \"dark:to-[#dac4ea]\", \"text-white\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"shadow-lg\", \"hover:shadow-[0_0_25px_rgba(218,196,234,0.4)]\", \"dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:hover:scale-100\", 3, \"disabled\"], [\"class\", \"inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2\", 4, \"ngIf\"], [\"class\", \"fas mr-2\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"mt-1\", \"text-sm\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-1\"], [\"type\", \"button\", 1, \"bg-[#ff6b69]/20\", \"dark:bg-[#ff3b30]/20\", \"text-[#ff6b69]\", \"dark:text-[#ff3b30]\", \"px-6\", \"py-3\", \"rounded-xl\", \"font-medium\", \"transition-all\", \"duration-300\", \"hover:scale-105\", \"hover:bg-[#ff6b69]/30\", \"dark:hover:bg-[#ff3b30]/30\", 3, \"click\"], [1, \"fas\", \"fa-trash\", \"mr-2\"], [1, \"inline-block\", \"w-4\", \"h-4\", \"border-2\", \"border-white/30\", \"border-t-white\", \"rounded-full\", \"animate-spin\", \"mr-2\"]],\n        template: function EquipeFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n            i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n            i0.ɵɵelement(6, \"div\", 6)(7, \"div\", 6)(8, \"div\", 6)(9, \"div\", 6)(10, \"div\", 6)(11, \"div\", 6)(12, \"div\", 6)(13, \"div\", 6)(14, \"div\", 6)(15, \"div\", 6)(16, \"div\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n            i0.ɵɵelement(19, \"div\", 9)(20, \"div\", 10);\n            i0.ɵɵelementStart(21, \"div\", 11)(22, \"div\", 12)(23, \"div\", 13)(24, \"h1\", 14);\n            i0.ɵɵtext(25);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"p\", 15);\n            i0.ɵɵtext(27);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function EquipeFormComponent_Template_button_click_28_listener() {\n              return ctx.cancel();\n            });\n            i0.ɵɵelement(29, \"i\", 17);\n            i0.ɵɵtext(30, \" Retour \\u00E0 la liste \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(31, EquipeFormComponent_div_31_Template, 6, 0, \"div\", 18);\n            i0.ɵɵtemplate(32, EquipeFormComponent_div_32_Template, 10, 1, \"div\", 19);\n            i0.ɵɵtemplate(33, EquipeFormComponent_div_33_Template, 50, 14, \"div\", 20);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(25);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifier l'\\u00E9quipe\" : \"Nouvelle \\u00E9quipe\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifiez les informations et les membres de votre \\u00E9quipe\" : \"Cr\\u00E9ez une nouvelle \\u00E9quipe pour organiser vos projets et membres\", \" \");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          }\n        },\n        dependencies: [i6.NgClass, i6.NgIf, i7.ɵNgNoValidate, i7.NgControlStatusGroup, i7.NgForm],\n        styles: [\".cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}summary[_ngcontent-%COMP%]:hover{text-decoration:underline}\"]\n      });\n    }\n  }\n  return EquipeFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}