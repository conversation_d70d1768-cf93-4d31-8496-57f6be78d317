{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { Observable } from \"../../utilities/index.js\";\n// QueryBatcher doesn't fire requests immediately. Requests that were enqueued within\n// a certain amount of time (configurable through `batchInterval`) will be batched together\n// into one query.\nvar OperationBatcher = /** @class */function () {\n  function OperationBatcher(_a) {\n    var batchDebounce = _a.batchDebounce,\n      batchInterval = _a.batchInterval,\n      batchMax = _a.batchMax,\n      batchHandler = _a.batchHandler,\n      batchKey = _a.batchKey;\n    // Queue on which the QueryBatcher will operate on a per-tick basis.\n    this.batchesByKey = new Map();\n    this.scheduledBatchTimerByKey = new Map();\n    this.batchDebounce = batchDebounce;\n    this.batchInterval = batchInterval;\n    this.batchMax = batchMax || 0;\n    this.batchHandler = batchHandler;\n    this.batchKey = batchKey || function () {\n      return \"\";\n    };\n  }\n  OperationBatcher.prototype.enqueueRequest = function (request) {\n    var _this = this;\n    var requestCopy = __assign(__assign({}, request), {\n      next: [],\n      error: [],\n      complete: [],\n      subscribers: new Set()\n    });\n    var key = this.batchKey(request.operation);\n    if (!requestCopy.observable) {\n      requestCopy.observable = new Observable(function (observer) {\n        var batch = _this.batchesByKey.get(key);\n        if (!batch) _this.batchesByKey.set(key, batch = new Set());\n        // These booleans seem to me (@benjamn) like they might always be the\n        // same (and thus we could do with only one of them), but I'm not 100%\n        // sure about that.\n        var isFirstEnqueuedRequest = batch.size === 0;\n        var isFirstSubscriber = requestCopy.subscribers.size === 0;\n        requestCopy.subscribers.add(observer);\n        if (isFirstSubscriber) {\n          batch.add(requestCopy);\n        }\n        // called for each subscriber, so need to save all listeners (next, error, complete)\n        if (observer.next) {\n          requestCopy.next.push(observer.next.bind(observer));\n        }\n        if (observer.error) {\n          requestCopy.error.push(observer.error.bind(observer));\n        }\n        if (observer.complete) {\n          requestCopy.complete.push(observer.complete.bind(observer));\n        }\n        // The first enqueued request triggers the queue consumption after `batchInterval` milliseconds.\n        if (isFirstEnqueuedRequest || _this.batchDebounce) {\n          _this.scheduleQueueConsumption(key);\n        }\n        // When amount of requests reaches `batchMax`, trigger the queue consumption without waiting on the `batchInterval`.\n        if (batch.size === _this.batchMax) {\n          _this.consumeQueue(key);\n        }\n        return function () {\n          var _a;\n          // If this is last subscriber for this request, remove request from queue\n          if (requestCopy.subscribers.delete(observer) && requestCopy.subscribers.size < 1) {\n            // If this is last request from queue, remove queue entirely\n            if (batch.delete(requestCopy) && batch.size < 1) {\n              _this.consumeQueue(key);\n              // If queue was in flight, cancel it\n              (_a = batch.subscription) === null || _a === void 0 ? void 0 : _a.unsubscribe();\n            }\n          }\n        };\n      });\n    }\n    return requestCopy.observable;\n  };\n  // Consumes the queue.\n  // Returns a list of promises (one for each query).\n  OperationBatcher.prototype.consumeQueue = function (key) {\n    if (key === void 0) {\n      key = \"\";\n    }\n    var batch = this.batchesByKey.get(key);\n    // Delete this batch and process it below.\n    this.batchesByKey.delete(key);\n    if (!batch || !batch.size) {\n      // No requests to be processed.\n      return;\n    }\n    var operations = [];\n    var forwards = [];\n    var observables = [];\n    var nexts = [];\n    var errors = [];\n    var completes = [];\n    // Even though batch is a Set, it preserves the order of first insertion\n    // when iterating (per ECMAScript specification), so these requests will be\n    // handled in the order they were enqueued (minus any deleted ones).\n    batch.forEach(function (request) {\n      operations.push(request.operation);\n      forwards.push(request.forward);\n      observables.push(request.observable);\n      nexts.push(request.next);\n      errors.push(request.error);\n      completes.push(request.complete);\n    });\n    var batchedObservable = this.batchHandler(operations, forwards) || Observable.of();\n    var onError = function (error) {\n      //each callback list in batch\n      errors.forEach(function (rejecters) {\n        if (rejecters) {\n          //each subscriber to request\n          rejecters.forEach(function (e) {\n            return e(error);\n          });\n        }\n      });\n    };\n    batch.subscription = batchedObservable.subscribe({\n      next: function (results) {\n        if (!Array.isArray(results)) {\n          results = [results];\n        }\n        if (nexts.length !== results.length) {\n          var error = new Error(\"server returned results with length \".concat(results.length, \", expected length of \").concat(nexts.length));\n          error.result = results;\n          return onError(error);\n        }\n        results.forEach(function (result, index) {\n          if (nexts[index]) {\n            nexts[index].forEach(function (next) {\n              return next(result);\n            });\n          }\n        });\n      },\n      error: onError,\n      complete: function () {\n        completes.forEach(function (complete) {\n          if (complete) {\n            //each subscriber to request\n            complete.forEach(function (c) {\n              return c();\n            });\n          }\n        });\n      }\n    });\n    return observables;\n  };\n  OperationBatcher.prototype.scheduleQueueConsumption = function (key) {\n    var _this = this;\n    clearTimeout(this.scheduledBatchTimerByKey.get(key));\n    this.scheduledBatchTimerByKey.set(key, setTimeout(function () {\n      _this.consumeQueue(key);\n      _this.scheduledBatchTimerByKey.delete(key);\n    }, this.batchInterval));\n  };\n  return OperationBatcher;\n}();\nexport { OperationBatcher };\n//# sourceMappingURL=batching.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}