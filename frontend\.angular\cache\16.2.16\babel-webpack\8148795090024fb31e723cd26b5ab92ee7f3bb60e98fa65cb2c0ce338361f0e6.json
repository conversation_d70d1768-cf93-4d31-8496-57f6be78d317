{"ast": null, "code": "/**\n * GraphQL.js provides a reference implementation for the GraphQL specification\n * but is also a useful utility for operating on GraphQL files and building\n * sophisticated tools.\n *\n * This primary module exports a general purpose function for fulfilling all\n * steps of the GraphQL specification in a single operation, but also includes\n * utilities for every part of the GraphQL specification:\n *\n *   - Parsing the GraphQL language.\n *   - Building a GraphQL type schema.\n *   - Validating a GraphQL request against a type schema.\n *   - Executing a GraphQL request against a type schema.\n *\n * This also includes utility functions for operating on GraphQL types and\n * GraphQL documents to facilitate building tools.\n *\n * You may also import from each sub-directory directly. For example, the\n * following two import statements are equivalent:\n *\n * ```ts\n * import { parse } from 'graphql';\n * import { parse } from 'graphql/language';\n * ```\n *\n * @packageDocumentation\n */\n// The GraphQL.js version info.\nexport { version, versionInfo } from './version.mjs'; // The primary entry point into fulfilling a GraphQL request.\n\nexport { graphql, graphqlSync } from './graphql.mjs'; // Create and operate on GraphQL type definitions and schema.\n\nexport { resolveObjMapThunk, resolveReadonlyArrayThunk,\n// Definitions\nGraphQLSchema, GraphQLDirective, GraphQLScalarType, GraphQLObjectType, GraphQLInterfaceType, GraphQLUnionType, GraphQLEnumType, GraphQLInputObjectType, GraphQLList, GraphQLNonNull,\n// Standard GraphQL Scalars\nspecifiedScalarTypes, GraphQLInt, GraphQLFloat, GraphQLString, GraphQLBoolean, GraphQLID,\n// Int boundaries constants\nGRAPHQL_MAX_INT, GRAPHQL_MIN_INT,\n// Built-in Directives defined by the Spec\nspecifiedDirectives, GraphQLIncludeDirective, GraphQLSkipDirective, GraphQLDeprecatedDirective, GraphQLSpecifiedByDirective,\n// \"Enum\" of Type Kinds\nTypeKind,\n// Constant Deprecation Reason\nDEFAULT_DEPRECATION_REASON,\n// GraphQL Types for introspection.\nintrospectionTypes, __Schema, __Directive, __DirectiveLocation, __Type, __Field, __InputValue, __EnumValue, __TypeKind,\n// Meta-field definitions.\nSchemaMetaFieldDef, TypeMetaFieldDef, TypeNameMetaFieldDef,\n// Predicates\nisSchema, isDirective, isType, isScalarType, isObjectType, isInterfaceType, isUnionType, isEnumType, isInputObjectType, isListType, isNonNullType, isInputType, isOutputType, isLeafType, isCompositeType, isAbstractType, isWrappingType, isNullableType, isNamedType, isRequiredArgument, isRequiredInputField, isSpecifiedScalarType, isIntrospectionType, isSpecifiedDirective,\n// Assertions\nassertSchema, assertDirective, assertType, assertScalarType, assertObjectType, assertInterfaceType, assertUnionType, assertEnumType, assertInputObjectType, assertListType, assertNonNullType, assertInputType, assertOutputType, assertLeafType, assertCompositeType, assertAbstractType, assertWrappingType, assertNullableType, assertNamedType,\n// Un-modifiers\ngetNullableType, getNamedType,\n// Validate GraphQL schema.\nvalidateSchema, assertValidSchema,\n// Upholds the spec rules about naming.\nassertName, assertEnumValueName } from './type/index.mjs';\n// Parse and operate on GraphQL language source files.\nexport { Token, Source, Location, OperationTypeNode, getLocation,\n// Print source location.\nprintLocation, printSourceLocation,\n// Lex\nLexer, TokenKind,\n// Parse\nparse, parseValue, parseConstValue, parseType,\n// Print\nprint,\n// Visit\nvisit, visitInParallel, getVisitFn, getEnterLeaveForKind, BREAK, Kind, DirectiveLocation,\n// Predicates\nisDefinitionNode, isExecutableDefinitionNode, isSelectionNode, isValueNode, isConstValueNode, isTypeNode, isTypeSystemDefinitionNode, isTypeDefinitionNode, isTypeSystemExtensionNode, isTypeExtensionNode } from './language/index.mjs';\n// Execute GraphQL queries.\nexport { execute, executeSync, defaultFieldResolver, defaultTypeResolver, responsePathAsArray, getArgumentValues, getVariableValues, getDirectiveValues, subscribe, createSourceEventStream } from './execution/index.mjs';\n// Validate GraphQL documents.\nexport { validate, ValidationContext,\n// All validation rules in the GraphQL Specification.\nspecifiedRules,\n// Individual validation rules.\nExecutableDefinitionsRule, FieldsOnCorrectTypeRule, FragmentsOnCompositeTypesRule, KnownArgumentNamesRule, KnownDirectivesRule, KnownFragmentNamesRule, KnownTypeNamesRule, LoneAnonymousOperationRule, NoFragmentCyclesRule, NoUndefinedVariablesRule, NoUnusedFragmentsRule, NoUnusedVariablesRule, OverlappingFieldsCanBeMergedRule, PossibleFragmentSpreadsRule, ProvidedRequiredArgumentsRule, ScalarLeafsRule, SingleFieldSubscriptionsRule, UniqueArgumentNamesRule, UniqueDirectivesPerLocationRule, UniqueFragmentNamesRule, UniqueInputFieldNamesRule, UniqueOperationNamesRule, UniqueVariableNamesRule, ValuesOfCorrectTypeRule, VariablesAreInputTypesRule, VariablesInAllowedPositionRule,\n// SDL-specific validation rules\nLoneSchemaDefinitionRule, UniqueOperationTypesRule, UniqueTypeNamesRule, UniqueEnumValueNamesRule, UniqueFieldDefinitionNamesRule, UniqueArgumentDefinitionNamesRule, UniqueDirectiveNamesRule, PossibleTypeExtensionsRule,\n// Custom validation rules\nNoDeprecatedCustomRule, NoSchemaIntrospectionCustomRule } from './validation/index.mjs';\n// Create, format, and print GraphQL errors.\nexport { GraphQLError, syntaxError, locatedError, printError, formatError } from './error/index.mjs';\n// Utilities for operating on GraphQL type schema and parsed sources.\nexport {\n// Produce the GraphQL query recommended for a full schema introspection.\n// Accepts optional IntrospectionOptions.\ngetIntrospectionQuery,\n// Gets the target Operation from a Document.\ngetOperationAST,\n// Gets the Type for the target Operation AST.\ngetOperationRootType,\n// Convert a GraphQLSchema to an IntrospectionQuery.\nintrospectionFromSchema,\n// Build a GraphQLSchema from an introspection result.\nbuildClientSchema,\n// Build a GraphQLSchema from a parsed GraphQL Schema language AST.\nbuildASTSchema,\n// Build a GraphQLSchema from a GraphQL schema language document.\nbuildSchema,\n// Extends an existing GraphQLSchema from a parsed GraphQL Schema language AST.\nextendSchema,\n// Sort a GraphQLSchema.\nlexicographicSortSchema,\n// Print a GraphQLSchema to GraphQL Schema language.\nprintSchema,\n// Print a GraphQLType to GraphQL Schema language.\nprintType,\n// Prints the built-in introspection schema in the Schema Language format.\nprintIntrospectionSchema,\n// Create a GraphQLType from a GraphQL language AST.\ntypeFromAST,\n// Create a JavaScript value from a GraphQL language AST with a Type.\nvalueFromAST,\n// Create a JavaScript value from a GraphQL language AST without a Type.\nvalueFromASTUntyped,\n// Create a GraphQL language AST from a JavaScript value.\nastFromValue,\n// A helper to use within recursive-descent visitors which need to be aware of the GraphQL type system.\nTypeInfo, visitWithTypeInfo,\n// Coerces a JavaScript value to a GraphQL type, or produces errors.\ncoerceInputValue,\n// Concatenates multiple AST together.\nconcatAST,\n// Separates an AST into an AST per Operation.\nseparateOperations,\n// Strips characters that are not significant to the validity or execution of a GraphQL document.\nstripIgnoredCharacters,\n// Comparators for types\nisEqualType, isTypeSubTypeOf, doTypesOverlap,\n// Asserts a string is a valid GraphQL name.\nassertValidName,\n// Determine if a string is a valid GraphQL name.\nisValidNameError,\n// Compares two GraphQLSchemas and detects breaking changes.\nBreakingChangeType, DangerousChangeType, findBreakingChanges, findDangerousChanges } from './utilities/index.mjs';", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}