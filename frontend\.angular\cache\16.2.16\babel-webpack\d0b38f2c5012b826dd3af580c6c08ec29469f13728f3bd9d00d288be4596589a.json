{"ast": null, "code": "import { serializeFetchParameter } from \"./serializeFetchParameter.js\";\n// For GET operations, returns the given URI rewritten with parameters, or a\n// parse error.\nexport function rewriteURIForGET(chosenURI, body) {\n  // Implement the standard HTTP GET serialization, plus 'extensions'. Note\n  // the extra level of JSON serialization!\n  var queryParams = [];\n  var addQueryParam = function (key, value) {\n    queryParams.push(\"\".concat(key, \"=\").concat(encodeURIComponent(value)));\n  };\n  if (\"query\" in body) {\n    addQueryParam(\"query\", body.query);\n  }\n  if (body.operationName) {\n    addQueryParam(\"operationName\", body.operationName);\n  }\n  if (body.variables) {\n    var serializedVariables = void 0;\n    try {\n      serializedVariables = serializeFetchParameter(body.variables, \"Variables map\");\n    } catch (parseError) {\n      return {\n        parseError: parseError\n      };\n    }\n    addQueryParam(\"variables\", serializedVariables);\n  }\n  if (body.extensions) {\n    var serializedExtensions = void 0;\n    try {\n      serializedExtensions = serializeFetchParameter(body.extensions, \"Extensions map\");\n    } catch (parseError) {\n      return {\n        parseError: parseError\n      };\n    }\n    addQueryParam(\"extensions\", serializedExtensions);\n  }\n  // Reconstruct the URI with added query params.\n  // XXX This assumes that the URI is well-formed and that it doesn't\n  //     already contain any of these query params. We could instead use the\n  //     URL API and take a polyfill (whatwg-url@6) for older browsers that\n  //     don't support URLSearchParams. Note that some browsers (and\n  //     versions of whatwg-url) support URL but not URLSearchParams!\n  var fragment = \"\",\n    preFragment = chosenURI;\n  var fragmentStart = chosenURI.indexOf(\"#\");\n  if (fragmentStart !== -1) {\n    fragment = chosenURI.substr(fragmentStart);\n    preFragment = chosenURI.substr(0, fragmentStart);\n  }\n  var queryParamsPrefix = preFragment.indexOf(\"?\") === -1 ? \"?\" : \"&\";\n  var newURI = preFragment + queryParamsPrefix + queryParams.join(\"&\") + fragment;\n  return {\n    newURI: newURI\n  };\n}\n//# sourceMappingURL=rewriteURIForGET.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}