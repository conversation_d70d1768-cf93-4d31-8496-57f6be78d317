{"ast": null, "code": "/**\n * The current status of a query’s execution in our system.\n */\nexport var NetworkStatus = /*#__PURE__*/function (NetworkStatus) {\n  /**\n   * The query has never been run before and the query is now currently running. A query will still\n   * have this network status even if a partial data result was returned from the cache, but a\n   * query was dispatched anyway.\n   */\n  NetworkStatus[NetworkStatus[\"loading\"] = 1] = \"loading\";\n  /**\n   * If `setVariables` was called and a query was fired because of that then the network status\n   * will be `setVariables` until the result of that query comes back.\n   */\n  NetworkStatus[NetworkStatus[\"setVariables\"] = 2] = \"setVariables\";\n  /**\n   * Indicates that `fetchMore` was called on this query and that the query created is currently in\n   * flight.\n   */\n  NetworkStatus[NetworkStatus[\"fetchMore\"] = 3] = \"fetchMore\";\n  /**\n   * Similar to the `setVariables` network status. It means that `refetch` was called on a query\n   * and the refetch request is currently in flight.\n   */\n  NetworkStatus[NetworkStatus[\"refetch\"] = 4] = \"refetch\";\n  /**\n   * Indicates that a polling query is currently in flight. So for example if you are polling a\n   * query every 10 seconds then the network status will switch to `poll` every 10 seconds whenever\n   * a poll request has been sent but not resolved.\n   */\n  NetworkStatus[NetworkStatus[\"poll\"] = 6] = \"poll\";\n  /**\n   * No request is in flight for this query, and no errors happened. Everything is OK.\n   */\n  NetworkStatus[NetworkStatus[\"ready\"] = 7] = \"ready\";\n  /**\n   * No request is in flight for this query, but one or more errors were detected.\n   */\n  NetworkStatus[NetworkStatus[\"error\"] = 8] = \"error\";\n  return NetworkStatus;\n}(NetworkStatus || {});\n/**\n * Returns true if there is currently a network request in flight according to a given network\n * status.\n */\nexport function isNetworkRequestInFlight(networkStatus) {\n  return networkStatus ? networkStatus < 7 : false;\n}\n/**\n * Returns true if the network request is in ready or error state according to a given network\n * status.\n */\nexport function isNetworkRequestSettled(networkStatus) {\n  return networkStatus === 7 || networkStatus === 8;\n}\n//# sourceMappingURL=networkStatus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}