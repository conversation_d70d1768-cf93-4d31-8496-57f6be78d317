{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/authuser.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nfunction HomeComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Project Management \");\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵtext(3, \" Made Simple \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HomeComponent_ng_template_6_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 38);\n    i0.ɵɵtext(1, \"(Administrator)\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵtemplate(1, HomeComponent_ng_template_6_span_1_Template, 2, 0, \"span\", 37);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵtextInterpolate1(\" Welcome Back, \", (tmp_0_0 = ctx_r2.authService.getCurrentUser()) == null ? null : tmp_0_0.username, \"! \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isAdmin());\n  }\n}\nfunction HomeComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Streamline your workflow, collaborate with your team, and deliver projects on time with DevBridge. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HomeComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" You have \");\n    i0.ɵɵelementStart(1, \"strong\");\n    i0.ɵɵtext(2, \"3 active projects\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" with \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5, \"5 pending tasks\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \". \");\n  }\n}\nfunction HomeComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 39);\n    i0.ɵɵtext(2, \" Get Started \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 40);\n    i0.ɵɵtext(4, \" Learn More \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HomeComponent_ng_template_14_a_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 43);\n    i0.ɵɵtext(1, \" Go to Dashboard \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, HomeComponent_ng_template_14_a_0_Template, 2, 0, \"a\", 41);\n    i0.ɵɵelementStart(1, \"a\", 42);\n    i0.ɵɵtext(2, \" View Projects \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.isAdmin());\n  }\n}\nfunction HomeComponent_section_46_div_7_img_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 59);\n  }\n  if (rf & 2) {\n    const member_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"src\", member_r19.avatar, i0.ɵɵsanitizeUrl)(\"alt\", member_r19.name);\n  }\n}\nfunction HomeComponent_section_46_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51)(2, \"h3\", 52);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 53);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 54);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 55)(9, \"div\", 56);\n    i0.ɵɵtemplate(10, HomeComponent_section_46_div_7_img_10_Template, 1, 2, \"img\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 58);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r17 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(project_r17.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r17.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r17.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", project_r17.team);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r17.dueDate);\n  }\n}\nfunction HomeComponent_section_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 44)(1, \"div\", 45)(2, \"h2\", 46);\n    i0.ɵɵtext(3, \"Recent Activity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 47);\n    i0.ɵɵtext(5, \"Here's what's been happening with your projects.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 48);\n    i0.ɵɵtemplate(7, HomeComponent_section_46_div_7_Template, 13, 5, \"div\", 49);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.recentProjects);\n  }\n}\nfunction HomeComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 61)(2, \"div\", 62);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 63);\n    i0.ɵɵelement(4, \"path\", 64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h4\", 65);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 66);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"p\", 21);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const testimonial_r20 = ctx.$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(testimonial_r20.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(testimonial_r20.position);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\\"\", testimonial_r20.quote, \"\\\"\");\n  }\n}\nfunction HomeComponent_ng_container_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 67);\n    i0.ɵɵtext(2, \" Start Free Trial \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 68);\n    i0.ɵɵtext(4, \" Schedule Demo \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HomeComponent_ng_template_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 69);\n    i0.ɵɵtext(1, \" Contact Support \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"a\", 70);\n    i0.ɵɵtext(3, \" Upgrade Plan \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let HomeComponent = /*#__PURE__*/(() => {\n  class HomeComponent {\n    constructor(authService) {\n      this.authService = authService;\n      this.recentProjects = [{\n        name: 'E-Commerce Platform',\n        category: 'WEB DESIGN',\n        description: 'Create a user-friendly e-commerce platform with a sleek design and intuitive navigation.',\n        team: [{\n          name: 'Team member 1',\n          avatar: 'https://randomuser.me/api/portraits/women/44.jpg'\n        }, {\n          name: 'Team member 2',\n          avatar: 'https://randomuser.me/api/portraits/men/32.jpg'\n        }],\n        dueDate: 'Due in 3 days'\n      }\n      // ... autres projets\n      ];\n\n      this.testimonials = [{\n        name: 'Margot Henschke',\n        position: 'Project Manager',\n        quote: 'DevBridge has transformed how our team collaborates...'\n      }\n      // ... autres témoignages\n      ];\n    }\n    // Ajoutez cette méthode pour vérifier si l'utilisateur est admin\n    isAdmin() {\n      const user = this.authService.getCurrentUser();\n      return user && user.role === 'admin'; // Adaptez selon votre structure de données\n    }\n\n    static {\n      this.ɵfac = function HomeComponent_Factory(t) {\n        return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.AuthuserService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HomeComponent,\n        selectors: [[\"app-home\"]],\n        decls: 61,\n        vars: 16,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"hero\", \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-2xl\", \"p-8\", \"md:p-12\", \"mb-12\", \"shadow-lg\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"items-center\"], [1, \"md:w-1/2\", \"mb-8\", \"md:mb-0\"], [1, \"text-4xl\", \"md:text-5xl\", \"font-bold\", \"mb-4\"], [4, \"ngIf\", \"ngIfElse\"], [\"welcomeBack\", \"\"], [1, \"text-xl\", \"mb-6\", \"text-[#dac4ea]\"], [\"userStats\", \"\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\"], [\"dashboardLink\", \"\"], [1, \"md:w-1/2\"], [1, \"rounded-xl\", \"shadow-2xl\", \"border-4\", \"border-white\", 3, \"src\", \"alt\"], [\"id\", \"features\", 1, \"mb-16\"], [1, \"text-3xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-8\", \"text-center\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-3\", \"gap-8\"], [1, \"bg-white\", \"p-6\", \"rounded-xl\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"border-t-4\", \"border-[#7826b5]\"], [1, \"text-[#7826b5]\", \"mb-4\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-12\", \"w-12\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-xl\", \"font-bold\", \"mb-3\", \"text-[#6d6870]\"], [1, \"text-[#6d6870]\"], [1, \"bg-white\", \"p-6\", \"rounded-xl\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"border-t-4\", \"border-[#4a89ce]\"], [1, \"text-[#4a89ce]\", \"mb-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"bg-white\", \"p-6\", \"rounded-xl\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"border-t-4\", \"border-[#afcf75]\"], [1, \"text-[#afcf75]\", \"mb-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [\"class\", \"mb-16 bg-white rounded-2xl shadow-lg overflow-hidden\", 4, \"ngIf\"], [1, \"mb-16\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-8\"], [\"class\", \"bg-white p-6 rounded-xl shadow-md\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-gradient-to-r\", \"from-[#4f5fad]\", \"to-[#7826b5]\", \"text-white\", \"rounded-2xl\", \"p-8\", \"md:p-12\", \"text-center\"], [1, \"text-3xl\", \"md:text-4xl\", \"font-bold\", \"mb-4\"], [1, \"text-xl\", \"mb-8\", \"text-[#dac4ea]\", \"max-w-2xl\", \"mx-auto\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"justify-center\", \"gap-4\"], [\"supportCta\", \"\"], [\"class\", \"block text-sm text-[#dac4ea]\", 4, \"ngIf\"], [1, \"block\", \"text-sm\", \"text-[#dac4ea]\"], [\"routerLink\", \"/registeruser\", 1, \"bg-white\", \"text-[#4f5fad]\", \"hover:bg-[#edf1f4]\", \"font-bold\", \"py-3\", \"px-6\", \"rounded-lg\", \"text-center\", \"transition-all\"], [\"href\", \"#features\", 1, \"border-2\", \"border-white\", \"text-white\", \"hover:bg-white\", \"hover:text-[#4f5fad]\", \"font-bold\", \"py-3\", \"px-6\", \"rounded-lg\", \"text-center\", \"transition-all\"], [\"routerLink\", \"/admin/dashboard\", \"class\", \"bg-white text-[#4f5fad] hover:bg-[#edf1f4] font-bold py-3 px-6 rounded-lg text-center transition-all\", 4, \"ngIf\"], [\"routerLink\", \"/projects\", 1, \"border-2\", \"border-white\", \"text-white\", \"hover:bg-white\", \"hover:text-[#4f5fad]\", \"font-bold\", \"py-3\", \"px-6\", \"rounded-lg\", \"text-center\", \"transition-all\"], [\"routerLink\", \"/admin/dashboard\", 1, \"bg-white\", \"text-[#4f5fad]\", \"hover:bg-[#edf1f4]\", \"font-bold\", \"py-3\", \"px-6\", \"rounded-lg\", \"text-center\", \"transition-all\"], [1, \"mb-16\", \"bg-white\", \"rounded-2xl\", \"shadow-lg\", \"overflow-hidden\"], [1, \"p-8\"], [1, \"text-3xl\", \"font-bold\", \"text-[#4f5fad]\", \"mb-4\"], [1, \"text-[#6d6870]\", \"mb-6\", \"max-w-2xl\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"lg:grid-cols-3\", \"gap-6\"], [\"class\", \"border border-[#bdc6cc] rounded-lg p-4 hover:shadow-md transition-all\", 4, \"ngFor\", \"ngForOf\"], [1, \"border\", \"border-[#bdc6cc]\", \"rounded-lg\", \"p-4\", \"hover:shadow-md\", \"transition-all\"], [1, \"flex\", \"justify-between\", \"items-start\", \"mb-3\"], [1, \"font-bold\", \"text-lg\", \"text-[#4f5fad]\"], [1, \"bg-[#dac4ea]\", \"text-[#7826b5]\", \"text-xs\", \"px-2\", \"py-1\", \"rounded-full\"], [1, \"text-sm\", \"text-[#6d6870]\", \"mb-4\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"flex\", \"-space-x-2\"], [\"class\", \"w-8 h-8 rounded-full border-2 border-white\", 3, \"src\", \"alt\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-xs\", \"text-[#6d6870]\"], [1, \"w-8\", \"h-8\", \"rounded-full\", \"border-2\", \"border-white\", 3, \"src\", \"alt\"], [1, \"bg-white\", \"p-6\", \"rounded-xl\", \"shadow-md\"], [1, \"flex\", \"items-center\", \"mb-4\"], [1, \"text-[#7826b5]\", \"mr-3\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"h-8\", \"w-8\"], [\"d\", \"M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z\"], [1, \"font-bold\", \"text-[#4f5fad]\"], [1, \"text-sm\", \"text-[#6d6870]\"], [\"routerLink\", \"/registeruser\", 1, \"bg-white\", \"text-[#4f5fad]\", \"hover:bg-[#edf1f4]\", \"font-bold\", \"py-3\", \"px-8\", \"rounded-lg\", \"transition-all\"], [\"href\", \"#\", 1, \"border-2\", \"border-white\", \"text-white\", \"hover:bg-white\", \"hover:text-[#4f5fad]\", \"font-bold\", \"py-3\", \"px-8\", \"rounded-lg\", \"transition-all\"], [\"routerLink\", \"/support\", 1, \"bg-white\", \"text-[#4f5fad]\", \"hover:bg-[#edf1f4]\", \"font-bold\", \"py-3\", \"px-8\", \"rounded-lg\", \"transition-all\"], [\"routerLink\", \"/upgrade\", 1, \"border-2\", \"border-white\", \"text-white\", \"hover:bg-white\", \"hover:text-[#4f5fad]\", \"font-bold\", \"py-3\", \"px-8\", \"rounded-lg\", \"transition-all\"]],\n        template: function HomeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"section\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4);\n            i0.ɵɵtemplate(5, HomeComponent_ng_container_5_Template, 4, 0, \"ng-container\", 5);\n            i0.ɵɵtemplate(6, HomeComponent_ng_template_6_Template, 2, 2, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"p\", 7);\n            i0.ɵɵtemplate(9, HomeComponent_ng_container_9_Template, 2, 0, \"ng-container\", 5);\n            i0.ɵɵtemplate(10, HomeComponent_ng_template_10_Template, 7, 0, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 9);\n            i0.ɵɵtemplate(13, HomeComponent_ng_container_13_Template, 5, 0, \"ng-container\", 5);\n            i0.ɵɵtemplate(14, HomeComponent_ng_template_14_Template, 3, 1, \"ng-template\", null, 10, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 11);\n            i0.ɵɵelement(17, \"img\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(18, \"section\", 13)(19, \"h2\", 14);\n            i0.ɵɵtext(20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"div\", 15)(22, \"div\", 16)(23, \"div\", 17);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(24, \"svg\", 18);\n            i0.ɵɵelement(25, \"path\", 19);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(26, \"h3\", 20);\n            i0.ɵɵtext(27, \"Real-time Analytics\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"p\", 21);\n            i0.ɵɵtext(29, \"Get instant insights into your project's progress with our comprehensive analytics dashboard.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(30, \"div\", 22)(31, \"div\", 23);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(32, \"svg\", 18);\n            i0.ɵɵelement(33, \"path\", 24);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(34, \"h3\", 20);\n            i0.ɵɵtext(35, \"Team Collaboration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"p\", 21);\n            i0.ɵɵtext(37, \"Seamlessly collaborate with your team through integrated chat, file sharing, and task management.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"div\", 25)(39, \"div\", 26);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(40, \"svg\", 18);\n            i0.ɵɵelement(41, \"path\", 27);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(42, \"h3\", 20);\n            i0.ɵɵtext(43, \"Project Scheduling\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"p\", 21);\n            i0.ɵɵtext(45, \"Plan and track your projects with our intuitive calendar and milestone tracking system.\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(46, HomeComponent_section_46_Template, 8, 1, \"section\", 28);\n            i0.ɵɵelementStart(47, \"section\", 29)(48, \"h2\", 14);\n            i0.ɵɵtext(49);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"div\", 30);\n            i0.ɵɵtemplate(51, HomeComponent_div_51_Template, 12, 3, \"div\", 31);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(52, \"section\", 32)(53, \"h2\", 33);\n            i0.ɵɵtext(54);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"p\", 34);\n            i0.ɵɵtext(56);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"div\", 35);\n            i0.ɵɵtemplate(58, HomeComponent_ng_container_58_Template, 5, 0, \"ng-container\", 5);\n            i0.ɵɵtemplate(59, HomeComponent_ng_template_59_Template, 4, 0, \"ng-template\", null, 36, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            const _r1 = i0.ɵɵreference(7);\n            const _r4 = i0.ɵɵreference(11);\n            const _r7 = i0.ɵɵreference(15);\n            const _r12 = i0.ɵɵreference(60);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", !ctx.authService.userLoggedIn())(\"ngIfElse\", _r1);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", !ctx.authService.userLoggedIn())(\"ngIfElse\", _r4);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", !ctx.authService.userLoggedIn())(\"ngIfElse\", _r7);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"src\", ctx.authService.userLoggedIn() ? \"assets/images/dashboard-preview.png\" : \"assets/images/project-management.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.authService.userLoggedIn() ? \"Dashboard Preview\" : \"Project Management Illustration\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.authService.userLoggedIn() ? \"Your Productivity Tools\" : \"Powerful Features\", \" \");\n            i0.ɵɵadvance(26);\n            i0.ɵɵproperty(\"ngIf\", ctx.authService.userLoggedIn());\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.authService.userLoggedIn() ? \"What Our Users Say\" : \"Trusted by Teams Worldwide\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.testimonials);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\" \", ctx.authService.userLoggedIn() ? \"Need Help With Your Projects?\" : \"Ready to Transform Your Workflow?\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.authService.userLoggedIn() ? \"Our support team is available 24/7 to help you get the most out of DevBridge.\" : \"Join thousands of teams who are already managing their projects more efficiently with DevBridge.\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.authService.userLoggedIn())(\"ngIfElse\", _r12);\n          }\n        },\n        dependencies: [i2.NgForOf, i2.NgIf, i3.RouterLink]\n      });\n    }\n  }\n  return HomeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}