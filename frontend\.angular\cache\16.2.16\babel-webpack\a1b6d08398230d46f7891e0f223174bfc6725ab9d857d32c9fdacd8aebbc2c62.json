{"ast": null, "code": "import { printBlockString } from '../language/blockString.mjs';\nimport { isPunctuatorToken<PERSON><PERSON>, <PERSON><PERSON> } from '../language/lexer.mjs';\nimport { isSource, Source } from '../language/source.mjs';\nimport { TokenKind } from '../language/tokenKind.mjs';\n/**\n * Strips characters that are not significant to the validity or execution\n * of a GraphQL document:\n *   - UnicodeBOM\n *   - WhiteSpace\n *   - LineTerminator\n *   - Comment\n *   - Comma\n *   - BlockString indentation\n *\n * Note: It is required to have a delimiter character between neighboring\n * non-punctuator tokens and this function always uses single space as delimiter.\n *\n * It is guaranteed that both input and output documents if parsed would result\n * in the exact same AST except for nodes location.\n *\n * Warning: It is guaranteed that this function will always produce stable results.\n * However, it's not guaranteed that it will stay the same between different\n * releases due to bugfixes or changes in the GraphQL specification.\n *\n * Query example:\n *\n * ```graphql\n * query SomeQuery($foo: String!, $bar: String) {\n *   someField(foo: $foo, bar: $bar) {\n *     a\n *     b {\n *       c\n *       d\n *     }\n *   }\n * }\n * ```\n *\n * Becomes:\n *\n * ```graphql\n * query SomeQuery($foo:String!$bar:String){someField(foo:$foo bar:$bar){a b{c d}}}\n * ```\n *\n * SDL example:\n *\n * ```graphql\n * \"\"\"\n * Type description\n * \"\"\"\n * type Foo {\n *   \"\"\"\n *   Field description\n *   \"\"\"\n *   bar: String\n * }\n * ```\n *\n * Becomes:\n *\n * ```graphql\n * \"\"\"Type description\"\"\" type Foo{\"\"\"Field description\"\"\" bar:String}\n * ```\n */\n\nexport function stripIgnoredCharacters(source) {\n  const sourceObj = isSource(source) ? source : new Source(source);\n  const body = sourceObj.body;\n  const lexer = new Lexer(sourceObj);\n  let strippedBody = '';\n  let wasLastAddedTokenNonPunctuator = false;\n  while (lexer.advance().kind !== TokenKind.EOF) {\n    const currentToken = lexer.token;\n    const tokenKind = currentToken.kind;\n    /**\n     * Every two non-punctuator tokens should have space between them.\n     * Also prevent case of non-punctuator token following by spread resulting\n     * in invalid token (e.g. `1...` is invalid Float token).\n     */\n\n    const isNonPunctuator = !isPunctuatorTokenKind(currentToken.kind);\n    if (wasLastAddedTokenNonPunctuator) {\n      if (isNonPunctuator || currentToken.kind === TokenKind.SPREAD) {\n        strippedBody += ' ';\n      }\n    }\n    const tokenBody = body.slice(currentToken.start, currentToken.end);\n    if (tokenKind === TokenKind.BLOCK_STRING) {\n      strippedBody += printBlockString(currentToken.value, {\n        minimize: true\n      });\n    } else {\n      strippedBody += tokenBody;\n    }\n    wasLastAddedTokenNonPunctuator = isNonPunctuator;\n  }\n  return strippedBody;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}