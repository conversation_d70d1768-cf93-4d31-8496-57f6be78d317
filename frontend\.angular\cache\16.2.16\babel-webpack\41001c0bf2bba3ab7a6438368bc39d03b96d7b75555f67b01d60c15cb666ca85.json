{"ast": null, "code": "import { groupBy } from '../../jsutils/groupBy.mjs';\nimport { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * Unique argument definition names\n *\n * A GraphQL Object or Interface type is only valid if all its fields have uniquely named arguments.\n * A GraphQL Directive is only valid if all its arguments are uniquely named.\n */\nexport function UniqueArgumentDefinitionNamesRule(context) {\n  return {\n    DirectiveDefinition(directiveNode) {\n      var _directiveNode$argume;\n\n      // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n      const argumentNodes = (_directiveNode$argume = directiveNode.arguments) !== null && _directiveNode$argume !== void 0 ? _directiveNode$argume : [];\n      return checkArgUniqueness(`@${directiveNode.name.value}`, argumentNodes);\n    },\n    InterfaceTypeDefinition: checkArgUniquenessPerField,\n    InterfaceTypeExtension: checkArgUniquenessPerField,\n    ObjectTypeDefinition: checkArgUniquenessPerField,\n    ObjectTypeExtension: checkArgUniquenessPerField\n  };\n  function checkArgUniquenessPerField(typeNode) {\n    var _typeNode$fields;\n    const typeName = typeNode.name.value; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n    /* c8 ignore next */\n\n    const fieldNodes = (_typeNode$fields = typeNode.fields) !== null && _typeNode$fields !== void 0 ? _typeNode$fields : [];\n    for (const fieldDef of fieldNodes) {\n      var _fieldDef$arguments;\n      const fieldName = fieldDef.name.value; // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n      /* c8 ignore next */\n\n      const argumentNodes = (_fieldDef$arguments = fieldDef.arguments) !== null && _fieldDef$arguments !== void 0 ? _fieldDef$arguments : [];\n      checkArgUniqueness(`${typeName}.${fieldName}`, argumentNodes);\n    }\n    return false;\n  }\n  function checkArgUniqueness(parentName, argumentNodes) {\n    const seenArgs = groupBy(argumentNodes, arg => arg.name.value);\n    for (const [argName, argNodes] of seenArgs) {\n      if (argNodes.length > 1) {\n        context.reportError(new GraphQLError(`Argument \"${parentName}(${argName}:)\" can only be defined once.`, {\n          nodes: argNodes.map(node => node.name)\n        }));\n      }\n    }\n    return false;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}