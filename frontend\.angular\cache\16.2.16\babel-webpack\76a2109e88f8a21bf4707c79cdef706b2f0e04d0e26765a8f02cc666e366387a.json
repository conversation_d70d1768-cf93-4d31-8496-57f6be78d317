{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@app/services/projets.service\";\nimport * as i4 from \"@angular/common\";\nfunction UpdateProjectComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 5);\n    i0.ɵɵelement(2, \"path\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Le titre est requis\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UpdateProjectComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 5);\n    i0.ɵɵelement(2, \"path\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"La description est requise\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UpdateProjectComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 5);\n    i0.ɵɵelement(2, \"path\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"La date limite est requise\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UpdateProjectComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 5);\n    i0.ɵɵelement(2, \"path\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Le groupe est requis\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/admin/projects/details\", a1];\n};\nexport let UpdateProjectComponent = /*#__PURE__*/(() => {\n  class UpdateProjectComponent {\n    constructor(route, fb, projetService, router) {\n      this.route = route;\n      this.fb = fb;\n      this.projetService = projetService;\n      this.router = router;\n    }\n    ngOnInit() {\n      this.projectId = this.route.snapshot.paramMap.get('id') || '';\n      this.projetId = this.projectId; // Pour le template\n      this.updateForm = this.fb.group({\n        titre: ['', Validators.required],\n        description: ['', Validators.required],\n        groupe: ['', Validators.required],\n        dateLimite: ['', Validators.required]\n      });\n      this.projetService.getProjetById(this.projectId).subscribe({\n        next: projet => {\n          // Formater la date pour l'input date\n          let formattedDate = '';\n          if (projet.dateLimite) {\n            const date = new Date(projet.dateLimite);\n            formattedDate = date.toISOString().split('T')[0];\n          }\n          this.updateForm.patchValue({\n            titre: projet.titre,\n            description: projet.description,\n            groupe: projet.groupe,\n            dateLimite: formattedDate\n          });\n        },\n        error: err => {\n          console.error('Erreur lors du chargement du projet:', err);\n          alert('Erreur lors du chargement du projet');\n        }\n      });\n    }\n    onSubmit() {\n      if (this.updateForm.valid) {\n        this.projetService.updateProjet(this.projectId, this.updateForm.value).subscribe({\n          next: () => {\n            alert('Projet mis à jour avec succès');\n            this.router.navigate(['/admin/projects/details', this.projectId]);\n          },\n          error: err => {\n            console.error('Erreur lors de la mise à jour:', err);\n            alert('Erreur lors de la mise à jour du projet');\n          }\n        });\n      }\n    }\n    static {\n      this.ɵfac = function UpdateProjectComponent_Factory(t) {\n        return new (t || UpdateProjectComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.ProjetService), i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: UpdateProjectComponent,\n        selectors: [[\"app-update-project\"]],\n        decls: 93,\n        vars: 12,\n        consts: [[1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"via-blue-50\", \"to-indigo-100\", \"dark:from-dark-bg-primary\", \"dark:via-dark-bg-secondary\", \"dark:to-dark-bg-tertiary\"], [1, \"container\", \"mx-auto\", \"px-4\", \"py-8\"], [1, \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-text\", \"dark:text-dark-text-secondary\", \"mb-4\"], [\"routerLink\", \"/admin/projects/list-project\", 1, \"hover:text-primary\", \"dark:hover:text-dark-accent-primary\", \"transition-colors\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"hover:text-primary\", \"dark:hover:text-dark-accent-primary\", \"transition-colors\", 3, \"routerLink\"], [1, \"text-primary\", \"dark:text-dark-accent-primary\", \"font-medium\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"p-8\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"h-16\", \"w-16\", \"rounded-2xl\", \"bg-gradient-to-br\", \"from-secondary\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"text-3xl\", \"font-bold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"text-text\", \"dark:text-dark-text-secondary\"], [1, \"max-w-4xl\", \"mx-auto\"], [1, \"bg-white/80\", \"dark:bg-dark-bg-secondary/80\", \"backdrop-blur-sm\", \"rounded-2xl\", \"shadow-lg\", \"border\", \"border-gray-200/50\", \"dark:border-dark-bg-tertiary/50\", \"overflow-hidden\"], [1, \"p-8\"], [1, \"space-y-8\", 3, \"formGroup\", \"ngSubmit\"], [1, \"space-y-6\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"mb-6\"], [1, \"bg-primary/10\", \"dark:bg-dark-accent-primary/20\", \"p-2\", \"rounded-lg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-lg\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [1, \"space-y-2\"], [\"for\", \"titre\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-primary\", \"dark:text-dark-accent-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a1.994 1.994 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"], [1, \"text-danger\", \"dark:text-danger-dark\"], [1, \"relative\"], [\"type\", \"text\", \"id\", \"titre\", \"formControlName\", \"titre\", \"placeholder\", \"Ex: D\\u00E9veloppement d'une application web\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-gray-400\", \"dark:placeholder-dark-text-secondary\"], [\"class\", \"flex items-center space-x-2 text-danger dark:text-danger-dark text-sm\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h7\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"4\", \"placeholder\", \"D\\u00E9crivez les objectifs, les livrables attendus et les crit\\u00E8res d'\\u00E9valuation...\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-gray-400\", \"dark:placeholder-dark-text-secondary\", \"resize-none\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"for\", \"dateLimite\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [\"type\", \"date\", \"id\", \"dateLimite\", \"formControlName\", \"dateLimite\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"for\", \"groupe\", 1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"font-semibold\", \"text-text-dark\", \"dark:text-dark-text-primary\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [\"type\", \"text\", \"id\", \"groupe\", \"formControlName\", \"groupe\", \"placeholder\", \"Ex: 2cinfo1\", 1, \"w-full\", \"px-4\", \"py-3\", \"bg-white\", \"dark:bg-dark-bg-secondary\", \"border-2\", \"border-gray-200\", \"dark:border-dark-bg-tertiary\", \"rounded-xl\", \"focus:outline-none\", \"focus:border-primary\", \"dark:focus:border-dark-accent-primary\", \"focus:ring-4\", \"focus:ring-primary/10\", \"dark:focus:ring-dark-accent-primary/20\", \"transition-all\", \"duration-200\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"placeholder-gray-400\", \"dark:placeholder-dark-text-secondary\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\", \"pt-6\", \"border-t\", \"border-gray-200\", \"dark:border-dark-bg-tertiary/50\"], [\"type\", \"button\", 1, \"flex-1\", \"px-6\", \"py-3\", \"bg-gray-100\", \"dark:bg-dark-bg-tertiary\", \"text-text-dark\", \"dark:text-dark-text-primary\", \"hover:bg-gray-200\", \"dark:hover:bg-dark-bg-tertiary/80\", \"rounded-xl\", \"transition-all\", \"duration-200\", \"font-medium\", 3, \"routerLink\"], [1, \"flex\", \"items-center\", \"justify-center\", \"space-x-2\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [\"type\", \"submit\", 1, \"flex-1\", \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-secondary\", \"to-primary\", \"dark:from-dark-accent-secondary\", \"dark:to-dark-accent-primary\", \"text-white\", \"rounded-xl\", \"hover:shadow-lg\", \"hover:scale-105\", \"transition-all\", \"duration-200\", \"font-medium\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"disabled:hover:scale-100\", \"disabled:hover:shadow-none\", 3, \"disabled\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M5 13l4 4L19 7\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-danger\", \"dark:text-danger-dark\", \"text-sm\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"]],\n        template: function UpdateProjectComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"nav\", 3)(4, \"a\", 4);\n            i0.ɵɵtext(5, \"Projets\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(6, \"svg\", 5);\n            i0.ɵɵelement(7, \"path\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(8, \"a\", 7);\n            i0.ɵɵtext(9, \"D\\u00E9tails\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(10, \"svg\", 5);\n            i0.ɵɵelement(11, \"path\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(12, \"span\", 8);\n            i0.ɵɵtext(13, \"Modifier\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 9)(15, \"div\", 10)(16, \"div\", 11);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(17, \"svg\", 12);\n            i0.ɵɵelement(18, \"path\", 13);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(19, \"div\")(20, \"h1\", 14);\n            i0.ɵɵtext(21, \" Modifier le projet \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"p\", 15);\n            i0.ɵɵtext(23, \" Mettez \\u00E0 jour les informations du projet \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(24, \"div\", 16)(25, \"div\", 17)(26, \"div\", 18)(27, \"form\", 19);\n            i0.ɵɵlistener(\"ngSubmit\", function UpdateProjectComponent_Template_form_ngSubmit_27_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(28, \"div\", 20)(29, \"div\", 21)(30, \"div\", 22);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(31, \"svg\", 23);\n            i0.ɵɵelement(32, \"path\", 24);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(33, \"h3\", 25);\n            i0.ɵɵtext(34, \"Informations du projet\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"div\", 26)(36, \"label\", 27);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(37, \"svg\", 28);\n            i0.ɵɵelement(38, \"path\", 29);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(39, \"span\");\n            i0.ɵɵtext(40, \"Titre du projet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"span\", 30);\n            i0.ɵɵtext(42, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(43, \"div\", 31);\n            i0.ɵɵelement(44, \"input\", 32);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(45, UpdateProjectComponent_div_45_Template, 5, 0, \"div\", 33);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"div\", 26)(47, \"label\", 34);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(48, \"svg\", 28);\n            i0.ɵɵelement(49, \"path\", 35);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(50, \"span\");\n            i0.ɵɵtext(51, \"Description\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"span\", 30);\n            i0.ɵɵtext(53, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(54, \"div\", 31);\n            i0.ɵɵelement(55, \"textarea\", 36);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(56, UpdateProjectComponent_div_56_Template, 5, 0, \"div\", 33);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"div\", 37)(58, \"div\", 26)(59, \"label\", 38);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(60, \"svg\", 28);\n            i0.ɵɵelement(61, \"path\", 39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(62, \"span\");\n            i0.ɵɵtext(63, \"Date limite\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(64, \"span\", 30);\n            i0.ɵɵtext(65, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(66, \"div\", 31);\n            i0.ɵɵelement(67, \"input\", 40);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(68, UpdateProjectComponent_div_68_Template, 5, 0, \"div\", 33);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"div\", 26)(70, \"label\", 41);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(71, \"svg\", 28);\n            i0.ɵɵelement(72, \"path\", 42);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(73, \"span\");\n            i0.ɵɵtext(74, \"Groupe cible\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(75, \"span\", 30);\n            i0.ɵɵtext(76, \"*\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(77, \"div\", 31);\n            i0.ɵɵelement(78, \"input\", 43);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(79, UpdateProjectComponent_div_79_Template, 5, 0, \"div\", 33);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(80, \"div\", 44)(81, \"button\", 45)(82, \"div\", 46);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(83, \"svg\", 47);\n            i0.ɵɵelement(84, \"path\", 48);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(85, \"span\");\n            i0.ɵɵtext(86, \"Annuler\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(87, \"button\", 49)(88, \"div\", 46);\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(89, \"svg\", 47);\n            i0.ɵɵelement(90, \"path\", 50);\n            i0.ɵɵelementEnd();\n            i0.ɵɵnamespaceHTML();\n            i0.ɵɵelementStart(91, \"span\");\n            i0.ɵɵtext(92, \"Mettre \\u00E0 jour le projet\");\n            i0.ɵɵelementEnd()()()()()()()()()();\n          }\n          if (rf & 2) {\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_4_0;\n            let tmp_5_0;\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(8, _c0, ctx.projetId));\n            i0.ɵɵadvance(19);\n            i0.ɵɵproperty(\"formGroup\", ctx.updateForm);\n            i0.ɵɵadvance(18);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.updateForm.get(\"titre\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.updateForm.get(\"titre\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.updateForm.get(\"description\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.updateForm.get(\"description\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.updateForm.get(\"dateLimite\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.updateForm.get(\"dateLimite\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.updateForm.get(\"groupe\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.updateForm.get(\"groupe\")) == null ? null : tmp_5_0.touched));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(10, _c0, ctx.projetId));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"disabled\", !ctx.updateForm.valid);\n          }\n        },\n        dependencies: [i4.NgIf, i1.RouterLink, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName]\n      });\n    }\n  }\n  return UpdateProjectComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}