{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ReunionListComponent } from './reunion-list/reunion-list.component';\nimport { ReunionFormComponent } from './reunion-form/reunion-form.component';\nimport { ReunionDetailComponent } from './reunion-detail/reunion-detail.component';\nimport { ReunionSchedulerComponent } from './reunion-scheduler/reunion-scheduler.component';\nimport { ReunionEditComponent } from \"./reunion-edit/reunion-edit.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ReunionListComponent\n}, {\n  path: 'nouvelleReunion',\n  component: ReunionFormComponent\n}, {\n  path: 'planifierReunion',\n  component: ReunionSchedulerComponent\n}, {\n  path: 'reunionDetails/:id',\n  component: ReunionDetailComponent\n}, {\n  path: 'modifier/:id',\n  component: ReunionEditComponent\n}];\nexport let ReunionsRoutingModule = /*#__PURE__*/(() => {\n  class ReunionsRoutingModule {\n    static {\n      this.ɵfac = function ReunionsRoutingModule_Factory(t) {\n        return new (t || ReunionsRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ReunionsRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return ReunionsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}