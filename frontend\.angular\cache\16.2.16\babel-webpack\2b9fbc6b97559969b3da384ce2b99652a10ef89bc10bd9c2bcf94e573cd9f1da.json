{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { BehaviorSubject, catchError, of, tap, throwError } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@auth0/angular-jwt\";\nimport * as i3 from \"./authuser.service\";\nexport let DataService = /*#__PURE__*/(() => {\n  class DataService {\n    constructor(http, jwtHelper, authService) {\n      this.http = http;\n      this.jwtHelper = jwtHelper;\n      this.authService = authService;\n      this.usersCache$ = new BehaviorSubject([]);\n      this.lastFetchTime = 0;\n      this.CACHE_DURATION = 300000;\n      this.currentUserSubject = new BehaviorSubject(null);\n      this.userAddedSubject = new BehaviorSubject(null);\n      this.userAdded$ = this.userAddedSubject.asObservable();\n      this.currentUser$ = this.currentUserSubject.asObservable();\n      this.initializeCurrentUser();\n    }\n    fetchCurrentUser() {\n      return this.getProfile().pipe(tap(user => this.currentUserSubject.next(user)));\n    }\n    getAdminHeaders() {\n      const token = localStorage.getItem('token');\n      if (!token || this.jwtHelper.isTokenExpired(token)) {\n        throw new Error('Token invalide ou expiré');\n      }\n      return new HttpHeaders({\n        Authorization: `Bearer ${token}`,\n        role: 'admin',\n        'Content-Type': 'application/json'\n      });\n    }\n    getUserHeaders() {\n      const token = localStorage.getItem('token');\n      if (!token || this.jwtHelper.isTokenExpired(token)) {\n        throw new Error('Token invalide ou expiré');\n      }\n      return new HttpHeaders({\n        Authorization: `Bearer ${token || ''}`,\n        'Content-Type': 'application/json'\n      });\n    }\n    getCommonParams() {\n      return new HttpParams().set('secret', environment.secret).set('client', environment.client);\n    }\n    syncCurrentUser() {\n      return this.getProfile().pipe(tap(user => {\n        this.currentUserSubject.next(user);\n        this.authService.setCurrentUser(user);\n      }), catchError(error => {\n        // If fetch fails, try to get from auth service\n        const authUser = this.authService.getCurrentUser();\n        if (authUser) {\n          this.currentUserSubject.next(authUser);\n          return of(authUser);\n        }\n        return throwError(() => error);\n      }));\n    }\n    getProfile() {\n      return this.http.get(`${environment.urlBackend}users/profile`, {\n        headers: this.getUserHeaders(),\n        params: this.getCommonParams()\n      }).pipe(catchError(this.handleError));\n    }\n    initializeCurrentUser() {\n      const token = localStorage.getItem('token');\n      if (token && !this.jwtHelper.isTokenExpired(token)) {\n        this.syncCurrentUser().subscribe({\n          error: () => {\n            const decodedToken = this.jwtHelper.decodeToken(token);\n            // Déterminer l'image de profil à utiliser\n            let profileImage = 'assets/images/default-profile.png';\n            // Vérifier d'abord profileImage\n            if (decodedToken.profileImage && decodedToken.profileImage !== 'null' && decodedToken.profileImage !== 'undefined' && decodedToken.profileImage.trim() !== '') {\n              profileImage = decodedToken.profileImage;\n            }\n            // Ensuite vérifier image si profileImage n'est pas valide\n            else if (decodedToken.image && decodedToken.image !== 'null' && decodedToken.image !== 'undefined' && decodedToken.image.trim() !== '') {\n              profileImage = decodedToken.image;\n            }\n            console.log('DataService - Using profile image:', profileImage);\n            const fallbackUser = {\n              _id: decodedToken.id,\n              username: decodedToken.username,\n              email: decodedToken.email,\n              role: decodedToken.role,\n              image: profileImage,\n              profileImage: profileImage,\n              isActive: true\n            };\n            this.currentUserSubject.next(fallbackUser);\n            this.authService.setCurrentUser(fallbackUser);\n          }\n        });\n      }\n    }\n    updateCurrentUser(userData) {\n      const currentUser = this.currentUserSubject.value;\n      if (currentUser) {\n        this.currentUserSubject.next({\n          ...currentUser,\n          ...userData\n        });\n      }\n    }\n    updateSelf(userId, updateData) {\n      return this.http.put(`${environment.urlBackend}users/updateself/${userId}`, updateData, {\n        headers: this.getUserHeaders(),\n        params: this.getCommonParams()\n      }).pipe(tap(updatedUser => {\n        this.updateUserInCache(updatedUser);\n        this.updateCurrentUser(updatedUser);\n      }), catchError(this.handleError));\n    }\n    changePassword(currentPassword, newPassword) {\n      const userId = this.currentUserValue?._id;\n      if (!userId) {\n        return throwError(() => new Error('User not logged in'));\n      }\n      const passwordData = {\n        currentPassword,\n        newPassword\n      };\n      return this.http.put(`${environment.urlBackend}users/updateself/${userId}`, passwordData, {\n        headers: this.getUserHeaders(),\n        params: this.getCommonParams()\n      }).pipe(tap(response => {\n        if (response.token) {\n          localStorage.setItem('token', response.token);\n          const decodedToken = this.jwtHelper.decodeToken(response.token);\n          this.updateCurrentUser(decodedToken);\n        }\n      }), catchError(error => {\n        if (error.status === 400) {\n          if (error.error?.errors?.general) {\n            return throwError(() => new Error(error.error.errors.general));\n          }\n          return throwError(() => new Error(error.error?.message || 'Validation failed'));\n        }\n        return throwError(() => new Error('Failed to change password'));\n      }));\n    }\n    uploadProfileImage(file) {\n      const formData = new FormData();\n      formData.append('image', file);\n      return this.http.post(`${environment.urlBackend}users/upload-profile-image`, formData, {\n        headers: new HttpHeaders({\n          Authorization: `Bearer ${localStorage.getItem('token')}`\n        }),\n        params: this.getCommonParams()\n      }).pipe(tap(response => {\n        if (response.token) {\n          localStorage.setItem('token', response.token);\n          const decodedToken = this.jwtHelper.decodeToken(response.token);\n          this.updateCurrentUser({\n            image: response.imageUrl,\n            ...decodedToken\n          });\n        }\n      }), catchError(this.handleError));\n    }\n    removeProfileImage() {\n      return this.http.delete(`${environment.urlBackend}users/remove-profile-image`, {\n        headers: this.getUserHeaders(),\n        params: this.getCommonParams()\n      }).pipe(tap(response => {\n        if (response.token) {\n          localStorage.setItem('token', response.token);\n          let decodeToken = this.jwtHelper.decodeToken(response.token);\n          this.updateCurrentUser(decodeToken);\n        }\n      }), catchError(this.handleError));\n    }\n    completeProfile(formData) {\n      // Add common params to FormData\n      formData.append('secret', environment.secret);\n      formData.append('client', environment.client);\n      return this.http.put(`${environment.urlBackend}auth/complete-profile`, formData, {\n        headers: new HttpHeaders({\n          Authorization: `Bearer ${localStorage.getItem('token')}`\n        })\n      }).pipe(tap(response => {\n        if (response.user) {\n          this.updateCurrentUser(response.user);\n          this.authService.setCurrentUser(response.user);\n        }\n      }), catchError(this.handleError));\n    }\n    get currentUserValue() {\n      return this.currentUserSubject.value;\n    }\n    isAdmin() {\n      return this.currentUserValue?.role === 'admin';\n    }\n    isCurrentUser(userId) {\n      return this.currentUserValue?._id === userId;\n    }\n    updateUserInCache(updatedUser) {\n      const updatedUsers = this.usersCache$.value.map(u => u._id === updatedUser._id ? {\n        ...u,\n        ...updatedUser\n      } : u);\n      this.usersCache$.next(updatedUsers);\n    }\n    refreshUserCache() {\n      this.lastFetchTime = 0;\n      this.getAllUsers(true).subscribe();\n    }\n    getAllUsers(forceRefresh = false) {\n      const now = Date.now();\n      const cacheValid = !forceRefresh && this.usersCache$.value.length > 0 && now - this.lastFetchTime <= this.CACHE_DURATION;\n      if (cacheValid) {\n        return this.usersCache$.asObservable();\n      }\n      this.lastFetchTime = now;\n      return this.http.get(`${environment.urlBackend}users/getall`, {\n        headers: this.getAdminHeaders(),\n        params: this.getCommonParams()\n      }).pipe(tap(users => this.usersCache$.next([...users])), catchError(this.handleError));\n    }\n    getOneUser(id) {\n      return this.http.get(`${environment.urlBackend}users/getone/${id}`, {\n        headers: this.getAdminHeaders(),\n        params: this.getCommonParams()\n      }).pipe(catchError(this.handleError));\n    }\n    addUser(userData) {\n      return this.http.post(`${environment.urlBackend}users/add`, userData, {\n        headers: this.getAdminHeaders(),\n        params: this.getCommonParams()\n      }).pipe(tap(newUser => {\n        const currentUsers = this.usersCache$.value;\n        this.usersCache$.next([...currentUsers, newUser]);\n        this.userAddedSubject.next();\n      }), catchError(this.handleError));\n    }\n    deleteUser(id) {\n      return this.http.delete(`${environment.urlBackend}users/delete/${id}`, {\n        headers: this.getAdminHeaders(),\n        params: this.getCommonParams()\n      }).pipe(tap(() => {\n        const updatedUsers = this.usersCache$.value.filter(u => u._id !== id);\n        this.usersCache$.next(updatedUsers);\n      }), catchError(this.handleError));\n    }\n    updateUserByAdmin(id, data) {\n      return this.http.put(`${environment.urlBackend}users/update/${id}`, data, {\n        headers: this.getAdminHeaders(),\n        params: this.getCommonParams()\n      }).pipe(tap(updatedUser => this.updateUserInCache(updatedUser)), catchError(this.handleError));\n    }\n    deactivateUser(id) {\n      return this.http.put(`${environment.urlBackend}users/update/${id}/deactivate`, {}, {\n        headers: this.getAdminHeaders(),\n        params: this.getCommonParams()\n      }).pipe(tap(updatedUser => this.updateUserInCache(updatedUser)), catchError(this.handleError));\n    }\n    reactivateUser(id) {\n      return this.http.put(`${environment.urlBackend}users/update/${id}/reactivate`, {}, {\n        headers: this.getAdminHeaders(),\n        params: this.getCommonParams()\n      }).pipe(tap(updatedUser => this.updateUserInCache(updatedUser)), catchError(this.handleError));\n    }\n    updateUserRole(id, role) {\n      return this.http.put(`${environment.urlBackend}admin/users/${id}/role`, {\n        role\n      }, {\n        headers: this.getAdminHeaders(),\n        params: this.getCommonParams()\n      }).pipe(tap(updatedUser => this.updateUserInCache(updatedUser)), catchError(this.handleError));\n    }\n    toggleUserActivation(id, isActive) {\n      return this.http.put(`${environment.urlBackend}admin/users/${id}/activation`, {\n        isActive\n      }, {\n        headers: this.getAdminHeaders(),\n        params: this.getCommonParams()\n      }).pipe(tap(updatedUser => this.updateUserInCache(updatedUser)), catchError(this.handleError));\n    }\n    handleError(error) {\n      let errorMessage = 'Une erreur est survenue';\n      if (error.status === 0) {\n        errorMessage = 'Erreur réseau - impossible de contacter le serveur';\n      } else if (error.status >= 400 && error.status < 500) {\n        errorMessage = error.error?.message || error.message;\n      } else if (error.status >= 500) {\n        errorMessage = 'Erreur serveur - veuillez réessayer plus tard';\n      }\n      console.error(`Erreur ${error.status}:`, error.error);\n      return throwError(() => new Error(errorMessage));\n    }\n    static {\n      this.ɵfac = function DataService_Factory(t) {\n        return new (t || DataService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.JwtHelperService), i0.ɵɵinject(i3.AuthuserService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: DataService,\n        factory: DataService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return DataService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}