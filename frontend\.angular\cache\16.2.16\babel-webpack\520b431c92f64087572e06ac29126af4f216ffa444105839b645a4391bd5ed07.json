{"ast": null, "code": "/**\n * @deprecated\n * This is not used internally any more and will be removed in\n * the next major version of Apollo Client.\n */\nexport var createSignalIfSupported = function () {\n  if (typeof AbortController === \"undefined\") return {\n    controller: false,\n    signal: false\n  };\n  var controller = new AbortController();\n  var signal = controller.signal;\n  return {\n    controller: controller,\n    signal: signal\n  };\n};\n//# sourceMappingURL=createSignalIfSupported.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}