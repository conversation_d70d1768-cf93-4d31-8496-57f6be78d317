{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { UserdetailsComponent } from './userdetails.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: UserdetailsComponent\n}];\nexport let UserdetailsRoutingModule = /*#__PURE__*/(() => {\n  class UserdetailsRoutingModule {\n    static {\n      this.ɵfac = function UserdetailsRoutingModule_Factory(t) {\n        return new (t || UserdetailsRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: UserdetailsRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return UserdetailsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}