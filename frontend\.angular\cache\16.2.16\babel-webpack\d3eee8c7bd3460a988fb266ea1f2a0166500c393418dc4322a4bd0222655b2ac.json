{"ast": null, "code": "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\nexport class MinuteParser extends Parser {\n  priority = 60;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"m\":\n        return parseNumericPattern(numericPatterns.minute, dateString);\n      case \"mo\":\n        return match.ordinalNumber(dateString, {\n          unit: \"minute\"\n        });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n  validate(_date, value) {\n    return value >= 0 && value <= 59;\n  }\n  set(date, _flags, value) {\n    date.setMinutes(value, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"t\", \"T\"];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}