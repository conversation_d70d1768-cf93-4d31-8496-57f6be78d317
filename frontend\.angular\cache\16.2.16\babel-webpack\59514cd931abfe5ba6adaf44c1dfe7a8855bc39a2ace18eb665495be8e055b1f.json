{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AdminSettingsComponent } from './settings.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: AdminSettingsComponent\n}];\nexport let SettingsRoutingModule = /*#__PURE__*/(() => {\n  class SettingsRoutingModule {\n    static {\n      this.ɵfac = function SettingsRoutingModule_Factory(t) {\n        return new (t || SettingsRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: SettingsRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return SettingsRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}