{"ast": null, "code": "import { WeakCache, StrongCache } from \"@wry/caches\";\nvar scheduledCleanup = new WeakSet();\nfunction schedule(cache) {\n  if (cache.size <= (cache.max || -1)) {\n    return;\n  }\n  if (!scheduledCleanup.has(cache)) {\n    scheduledCleanup.add(cache);\n    setTimeout(function () {\n      cache.clean();\n      scheduledCleanup.delete(cache);\n    }, 100);\n  }\n}\n/**\n * @internal\n * A version of WeakCache that will auto-schedule a cleanup of the cache when\n * a new item is added and the cache reached maximum size.\n * Throttled to once per 100ms.\n *\n * @privateRemarks\n * Should be used throughout the rest of the codebase instead of WeakCache,\n * with the notable exception of usage in `wrap` from `optimism` - that one\n * already handles cleanup and should remain a `WeakCache`.\n */\nexport var AutoCleanedWeakCache = function (max, dispose) {\n  /*\n  Some builds of `WeakCache` are function prototypes, some are classes.\n  This library still builds with an ES5 target, so we can't extend the\n  real classes.\n  Instead, we have to use this workaround until we switch to a newer build\n  target.\n  */\n  var cache = new WeakCache(max, dispose);\n  cache.set = function (key, value) {\n    var ret = WeakCache.prototype.set.call(this, key, value);\n    schedule(this);\n    return ret;\n  };\n  return cache;\n};\n/**\n * @internal\n * A version of StrongCache that will auto-schedule a cleanup of the cache when\n * a new item is added and the cache reached maximum size.\n * Throttled to once per 100ms.\n *\n * @privateRemarks\n * Should be used throughout the rest of the codebase instead of StrongCache,\n * with the notable exception of usage in `wrap` from `optimism` - that one\n * already handles cleanup and should remain a `StrongCache`.\n */\nexport var AutoCleanedStrongCache = function (max, dispose) {\n  /*\n  Some builds of `StrongCache` are function prototypes, some are classes.\n  This library still builds with an ES5 target, so we can't extend the\n  real classes.\n  Instead, we have to use this workaround until we switch to a newer build\n  target.\n  */\n  var cache = new StrongCache(max, dispose);\n  cache.set = function (key, value) {\n    var ret = StrongCache.prototype.set.call(this, key, value);\n    schedule(this);\n    return ret;\n  };\n  return cache;\n};\n//# sourceMappingURL=caches.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}