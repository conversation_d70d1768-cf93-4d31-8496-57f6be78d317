{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { of } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let RendusService = /*#__PURE__*/(() => {\n  class RendusService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.urlBackend}rendus`;\n    }\n    getHeaders() {\n      const token = localStorage.getItem('token');\n      return new HttpHeaders({\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      });\n    }\n    // Soumettre un nouveau rendu\n    submitRendu(renduData) {\n      return this.http.post(`${this.apiUrl}/submit`, renduData);\n    }\n    // Vérifier si un étudiant a déjà soumis un rendu pour un projet\n    checkRenduExists(projetId, etudiantId) {\n      return this.http.get(`${this.apiUrl}/check/${projetId}/${etudiantId}`, {\n        headers: this.getHeaders()\n      });\n    }\n    // Récupérer tous les rendus\n    getAllRendus() {\n      return this.http.get(this.apiUrl, {\n        headers: this.getHeaders()\n      }).pipe(catchError(error => {\n        console.error('Erreur lors de la récupération des rendus:', error);\n        return of([]);\n      }));\n    }\n    // Récupérer un rendu par son ID\n    getRenduById(id) {\n      return this.http.get(`${this.apiUrl}/${id}`, {\n        headers: this.getHeaders()\n      });\n    }\n    // Récupérer les rendus par projet\n    getRendusByProjet(projetId) {\n      return this.http.get(`${this.apiUrl}/projet/${projetId}`, {\n        headers: this.getHeaders()\n      }).pipe(catchError(error => {\n        console.error('Erreur lors de la récupération des rendus par projet:', error);\n        return of([]);\n      }));\n    }\n    // Évaluer un rendu (manuellement ou via IA)\n    evaluateRendu(renduId, evaluationData) {\n      return this.http.post(`${this.apiUrl}/evaluations/${renduId}`, evaluationData, {\n        headers: this.getHeaders()\n      });\n    }\n    // Mettre à jour une évaluation existante\n    updateEvaluation(renduId, evaluationData) {\n      return this.http.put(`${this.apiUrl}/evaluations/${renduId}`, evaluationData, {\n        headers: this.getHeaders()\n      });\n    }\n    static {\n      this.ɵfac = function RendusService_Factory(t) {\n        return new (t || RendusService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: RendusService,\n        factory: RendusService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return RendusService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}