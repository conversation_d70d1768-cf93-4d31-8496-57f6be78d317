{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let EvaluationService = /*#__PURE__*/(() => {\n  class EvaluationService {\n    constructor(http) {\n      this.http = http;\n    }\n    getAllEvaluations() {\n      const url = `${environment.urlBackend}evaluations/getev`;\n      return this.http.get(url).pipe(catchError(error => {\n        console.error('Erreur HTTP lors de la récupération des évaluations:', error);\n        return throwError(() => new Error('Erreur lors de la récupération des évaluations'));\n      }));\n    }\n    getEvaluationById(id) {\n      const url = `${environment.urlBackend}evaluations/${id}`;\n      return this.http.get(url).pipe(catchError(error => {\n        console.error('Erreur HTTP lors de la récupération de l\\'évaluation:', error);\n        return throwError(() => new Error('Erreur lors de la récupération de l\\'évaluation'));\n      }));\n    }\n    // Autres méthodes du service...\n    // Ajouter cette méthode pour mettre à jour les groupes manquants\n    updateMissingGroups() {\n      const url = `${environment.urlBackend}evaluations/update-missing-groups`;\n      return this.http.post(url, {}).pipe(catchError(error => {\n        console.error('Erreur HTTP lors de la mise à jour des groupes:', error);\n        return throwError(() => new Error('Erreur lors de la mise à jour des groupes'));\n      }));\n    }\n    // Méthode pour supprimer une évaluation\n    deleteEvaluation(evaluationId) {\n      const url = `${environment.urlBackend}evaluations/${evaluationId}`;\n      return this.http.delete(url).pipe(catchError(error => {\n        console.error('Erreur HTTP lors de la suppression de l\\'évaluation:', error);\n        return throwError(() => new Error('Erreur lors de la suppression de l\\'évaluation'));\n      }));\n    }\n    static {\n      this.ɵfac = function EvaluationService_Factory(t) {\n        return new (t || EvaluationService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: EvaluationService,\n        factory: EvaluationService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return EvaluationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}