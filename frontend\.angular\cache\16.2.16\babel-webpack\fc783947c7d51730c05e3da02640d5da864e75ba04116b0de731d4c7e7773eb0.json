{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { parse } from '../language/parser.mjs';\nimport { specifiedDirectives } from '../type/directives.mjs';\nimport { GraphQLSchema } from '../type/schema.mjs';\nimport { assertValidSDL } from '../validation/validate.mjs';\nimport { extendSchemaImpl } from './extendSchema.mjs';\n\n/**\n * This takes the ast of a schema document produced by the parse function in\n * src/language/parser.js.\n *\n * If no schema definition is provided, then it will look for types named Query,\n * Mutation and Subscription.\n *\n * Given that AST it constructs a GraphQLSchema. The resulting schema\n * has no resolve methods, so execution will use default resolvers.\n */\nexport function buildASTSchema(documentAST, options) {\n  documentAST != null && documentAST.kind === Kind.DOCUMENT || devAssert(false, 'Must provide valid Document AST.');\n  if ((options === null || options === void 0 ? void 0 : options.assumeValid) !== true && (options === null || options === void 0 ? void 0 : options.assumeValidSDL) !== true) {\n    assertValidSDL(documentAST);\n  }\n  const emptySchemaConfig = {\n    description: undefined,\n    types: [],\n    directives: [],\n    extensions: Object.create(null),\n    extensionASTNodes: [],\n    assumeValid: false\n  };\n  const config = extendSchemaImpl(emptySchemaConfig, documentAST, options);\n  if (config.astNode == null) {\n    for (const type of config.types) {\n      switch (type.name) {\n        // Note: While this could make early assertions to get the correctly\n        // typed values below, that would throw immediately while type system\n        // validation with validateSchema() will produce more actionable results.\n        case 'Query':\n          // @ts-expect-error validated in `validateSchema`\n          config.query = type;\n          break;\n        case 'Mutation':\n          // @ts-expect-error validated in `validateSchema`\n          config.mutation = type;\n          break;\n        case 'Subscription':\n          // @ts-expect-error validated in `validateSchema`\n          config.subscription = type;\n          break;\n      }\n    }\n  }\n  const directives = [...config.directives,\n  // If specified directives were not explicitly declared, add them.\n  ...specifiedDirectives.filter(stdDirective => config.directives.every(directive => directive.name !== stdDirective.name))];\n  return new GraphQLSchema({\n    ...config,\n    directives\n  });\n}\n/**\n * A helper function to build a GraphQLSchema directly from a source\n * document.\n */\n\nexport function buildSchema(source, options) {\n  const document = parse(source, {\n    noLocation: options === null || options === void 0 ? void 0 : options.noLocation,\n    allowLegacyFragmentVariables: options === null || options === void 0 ? void 0 : options.allowLegacyFragmentVariables\n  });\n  return buildASTSchema(document, {\n    assumeValidSDL: options === null || options === void 0 ? void 0 : options.assumeValidSDL,\n    assumeValid: options === null || options === void 0 ? void 0 : options.assumeValid\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}