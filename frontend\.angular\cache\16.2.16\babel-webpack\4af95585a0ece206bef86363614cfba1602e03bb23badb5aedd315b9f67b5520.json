{"ast": null, "code": "import { isNonNullObject } from \"./objects.js\";\nimport { isNonEmptyArray } from \"./arrays.js\";\nimport { DeepMerger } from \"./mergeDeep.js\";\nexport function isExecutionPatchIncrementalResult(value) {\n  return \"incremental\" in value;\n}\nexport function isExecutionPatchInitialResult(value) {\n  return \"hasNext\" in value && \"data\" in value;\n}\nexport function isExecutionPatchResult(value) {\n  return isExecutionPatchIncrementalResult(value) || isExecutionPatchInitialResult(value);\n}\n// This function detects an Apollo payload result before it is transformed\n// into a FetchResult via HttpLink; it cannot detect an ApolloPayloadResult\n// once it leaves the link chain.\nexport function isApolloPayloadResult(value) {\n  return isNonNullObject(value) && \"payload\" in value;\n}\nexport function mergeIncrementalData(prevResult, result) {\n  var mergedData = prevResult;\n  var merger = new DeepMerger();\n  if (isExecutionPatchIncrementalResult(result) && isNonEmptyArray(result.incremental)) {\n    result.incremental.forEach(function (_a) {\n      var data = _a.data,\n        path = _a.path;\n      for (var i = path.length - 1; i >= 0; --i) {\n        var key = path[i];\n        var isNumericKey = !isNaN(+key);\n        var parent_1 = isNumericKey ? [] : {};\n        parent_1[key] = data;\n        data = parent_1;\n      }\n      mergedData = merger.merge(mergedData, data);\n    });\n  }\n  return mergedData;\n}\n//# sourceMappingURL=incrementalResult.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}