{"ast": null, "code": "export const {\n  hasOwnProperty\n} = Object.prototype;\nexport const arrayFromSet = Array.from || function (set) {\n  const array = [];\n  set.forEach(item => array.push(item));\n  return array;\n};\nexport function maybeUnsubscribe(entryOrDep) {\n  const {\n    unsubscribe\n  } = entryOrDep;\n  if (typeof unsubscribe === \"function\") {\n    entryOrDep.unsubscribe = void 0;\n    unsubscribe();\n  }\n}\n//# sourceMappingURL=helpers.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}