{"ast": null, "code": "import { invariant } from \"../globals/index.js\";\nimport { visit, BREAK, Kind } from \"graphql\";\nexport function shouldInclude(_a, variables) {\n  var directives = _a.directives;\n  if (!directives || !directives.length) {\n    return true;\n  }\n  return getInclusionDirectives(directives).every(function (_a) {\n    var directive = _a.directive,\n      ifArgument = _a.ifArgument;\n    var evaledValue = false;\n    if (ifArgument.value.kind === \"Variable\") {\n      evaledValue = variables && variables[ifArgument.value.name.value];\n      invariant(evaledValue !== void 0, 78, directive.name.value);\n    } else {\n      evaledValue = ifArgument.value.value;\n    }\n    return directive.name.value === \"skip\" ? !evaledValue : evaledValue;\n  });\n}\nexport function getDirectiveNames(root) {\n  var names = [];\n  visit(root, {\n    Directive: function (node) {\n      names.push(node.name.value);\n    }\n  });\n  return names;\n}\nexport var hasAnyDirectives = function (names, root) {\n  return hasDirectives(names, root, false);\n};\nexport var hasAllDirectives = function (names, root) {\n  return hasDirectives(names, root, true);\n};\nexport function hasDirectives(names, root, all) {\n  var nameSet = new Set(names);\n  var uniqueCount = nameSet.size;\n  visit(root, {\n    Directive: function (node) {\n      if (nameSet.delete(node.name.value) && (!all || !nameSet.size)) {\n        return BREAK;\n      }\n    }\n  });\n  // If we found all the names, nameSet will be empty. If we only care about\n  // finding some of them, the < condition is sufficient.\n  return all ? !nameSet.size : nameSet.size < uniqueCount;\n}\nexport function hasClientExports(document) {\n  return document && hasDirectives([\"client\", \"export\"], document, true);\n}\nfunction isInclusionDirective(_a) {\n  var value = _a.name.value;\n  return value === \"skip\" || value === \"include\";\n}\nexport function getInclusionDirectives(directives) {\n  var result = [];\n  if (directives && directives.length) {\n    directives.forEach(function (directive) {\n      if (!isInclusionDirective(directive)) return;\n      var directiveArguments = directive.arguments;\n      var directiveName = directive.name.value;\n      invariant(directiveArguments && directiveArguments.length === 1, 79, directiveName);\n      var ifArgument = directiveArguments[0];\n      invariant(ifArgument.name && ifArgument.name.value === \"if\", 80, directiveName);\n      var ifValue = ifArgument.value;\n      // means it has to be a variable value if this is a valid @skip or @include directive\n      invariant(ifValue && (ifValue.kind === \"Variable\" || ifValue.kind === \"BooleanValue\"), 81, directiveName);\n      result.push({\n        directive: directive,\n        ifArgument: ifArgument\n      });\n    });\n  }\n  return result;\n}\n/** @internal */\nexport function getFragmentMaskMode(fragment) {\n  var _a, _b;\n  var directive = (_a = fragment.directives) === null || _a === void 0 ? void 0 : _a.find(function (_a) {\n    var name = _a.name;\n    return name.value === \"unmask\";\n  });\n  if (!directive) {\n    return \"mask\";\n  }\n  var modeArg = (_b = directive.arguments) === null || _b === void 0 ? void 0 : _b.find(function (_a) {\n    var name = _a.name;\n    return name.value === \"mode\";\n  });\n  if (globalThis.__DEV__ !== false) {\n    if (modeArg) {\n      if (modeArg.value.kind === Kind.VARIABLE) {\n        globalThis.__DEV__ !== false && invariant.warn(82);\n      } else if (modeArg.value.kind !== Kind.STRING) {\n        globalThis.__DEV__ !== false && invariant.warn(83);\n      } else if (modeArg.value.value !== \"migrate\") {\n        globalThis.__DEV__ !== false && invariant.warn(84, modeArg.value.value);\n      }\n    }\n  }\n  if (modeArg && \"value\" in modeArg.value && modeArg.value.value === \"migrate\") {\n    return \"migrate\";\n  }\n  return \"unmask\";\n}\n//# sourceMappingURL=directives.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}