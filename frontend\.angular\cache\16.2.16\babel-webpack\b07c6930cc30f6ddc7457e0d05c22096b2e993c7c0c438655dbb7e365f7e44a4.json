{"ast": null, "code": "/**\n * Produce the GraphQL query recommended for a full schema introspection.\n * Accepts optional IntrospectionOptions.\n */\nexport function getIntrospectionQuery(options) {\n  const optionsWithDefault = {\n    descriptions: true,\n    specifiedByUrl: false,\n    directiveIsRepeatable: false,\n    schemaDescription: false,\n    inputValueDeprecation: false,\n    ...options\n  };\n  const descriptions = optionsWithDefault.descriptions ? 'description' : '';\n  const specifiedByUrl = optionsWithDefault.specifiedByUrl ? 'specifiedByURL' : '';\n  const directiveIsRepeatable = optionsWithDefault.directiveIsRepeatable ? 'isRepeatable' : '';\n  const schemaDescription = optionsWithDefault.schemaDescription ? descriptions : '';\n  function inputDeprecation(str) {\n    return optionsWithDefault.inputValueDeprecation ? str : '';\n  }\n  return `\n    query IntrospectionQuery {\n      __schema {\n        ${schemaDescription}\n        queryType { name }\n        mutationType { name }\n        subscriptionType { name }\n        types {\n          ...FullType\n        }\n        directives {\n          name\n          ${descriptions}\n          ${directiveIsRepeatable}\n          locations\n          args${inputDeprecation('(includeDeprecated: true)')} {\n            ...InputValue\n          }\n        }\n      }\n    }\n\n    fragment FullType on __Type {\n      kind\n      name\n      ${descriptions}\n      ${specifiedByUrl}\n      fields(includeDeprecated: true) {\n        name\n        ${descriptions}\n        args${inputDeprecation('(includeDeprecated: true)')} {\n          ...InputValue\n        }\n        type {\n          ...TypeRef\n        }\n        isDeprecated\n        deprecationReason\n      }\n      inputFields${inputDeprecation('(includeDeprecated: true)')} {\n        ...InputValue\n      }\n      interfaces {\n        ...TypeRef\n      }\n      enumValues(includeDeprecated: true) {\n        name\n        ${descriptions}\n        isDeprecated\n        deprecationReason\n      }\n      possibleTypes {\n        ...TypeRef\n      }\n    }\n\n    fragment InputValue on __InputValue {\n      name\n      ${descriptions}\n      type { ...TypeRef }\n      defaultValue\n      ${inputDeprecation('isDeprecated')}\n      ${inputDeprecation('deprecationReason')}\n    }\n\n    fragment TypeRef on __Type {\n      kind\n      name\n      ofType {\n        kind\n        name\n        ofType {\n          kind\n          name\n          ofType {\n            kind\n            name\n            ofType {\n              kind\n              name\n              ofType {\n                kind\n                name\n                ofType {\n                  kind\n                  name\n                  ofType {\n                    kind\n                    name\n                    ofType {\n                      kind\n                      name\n                      ofType {\n                        kind\n                        name\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  `;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}