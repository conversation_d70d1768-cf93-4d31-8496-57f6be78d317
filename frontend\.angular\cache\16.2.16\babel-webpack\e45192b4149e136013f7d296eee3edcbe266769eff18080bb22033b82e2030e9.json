{"ast": null, "code": "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { add } from \"./add.js\";\nimport { differenceInDays } from \"./differenceInDays.js\";\nimport { differenceInHours } from \"./differenceInHours.js\";\nimport { differenceInMinutes } from \"./differenceInMinutes.js\";\nimport { differenceInMonths } from \"./differenceInMonths.js\";\nimport { differenceInSeconds } from \"./differenceInSeconds.js\";\nimport { differenceInYears } from \"./differenceInYears.js\";\n\n/**\n * The {@link intervalToDuration} function options.\n */\n\n/**\n * @name intervalToDuration\n * @category Common Helpers\n * @summary Convert interval to duration\n *\n * @description\n * Convert an interval object to a duration object.\n *\n * @param interval - The interval to convert to duration\n * @param options - The context options\n *\n * @returns The duration object\n *\n * @example\n * // Get the duration between January 15, 1929 and April 4, 1968.\n * intervalToDuration({\n *   start: new Date(1929, 0, 15, 12, 0, 0),\n *   end: new Date(1968, 3, 4, 19, 5, 0)\n * });\n * //=> { years: 39, months: 2, days: 20, hours: 7, minutes: 5, seconds: 0 }\n */\nexport function intervalToDuration(interval, options) {\n  const {\n    start,\n    end\n  } = normalizeInterval(options?.in, interval);\n  const duration = {};\n  const years = differenceInYears(end, start);\n  if (years) duration.years = years;\n  const remainingMonths = add(start, {\n    years: duration.years\n  });\n  const months = differenceInMonths(end, remainingMonths);\n  if (months) duration.months = months;\n  const remainingDays = add(remainingMonths, {\n    months: duration.months\n  });\n  const days = differenceInDays(end, remainingDays);\n  if (days) duration.days = days;\n  const remainingHours = add(remainingDays, {\n    days: duration.days\n  });\n  const hours = differenceInHours(end, remainingHours);\n  if (hours) duration.hours = hours;\n  const remainingMinutes = add(remainingHours, {\n    hours: duration.hours\n  });\n  const minutes = differenceInMinutes(end, remainingMinutes);\n  if (minutes) duration.minutes = minutes;\n  const remainingSeconds = add(remainingMinutes, {\n    minutes: duration.minutes\n  });\n  const seconds = differenceInSeconds(end, remainingSeconds);\n  if (seconds) duration.seconds = seconds;\n  return duration;\n}\n\n// Fallback for modularized imports:\nexport default intervalToDuration;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}