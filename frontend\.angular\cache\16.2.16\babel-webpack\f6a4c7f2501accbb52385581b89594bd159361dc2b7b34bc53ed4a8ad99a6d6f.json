{"ast": null, "code": "import { GraphQLError } from '../../error/GraphQLError.mjs';\n\n/**\n * No unused fragments\n *\n * A GraphQL document is only valid if all fragment definitions are spread\n * within operations, or spread within other fragments spread within operations.\n *\n * See https://spec.graphql.org/draft/#sec-Fragments-Must-Be-Used\n */\nexport function NoUnusedFragmentsRule(context) {\n  const operationDefs = [];\n  const fragmentDefs = [];\n  return {\n    OperationDefinition(node) {\n      operationDefs.push(node);\n      return false;\n    },\n    FragmentDefinition(node) {\n      fragmentDefs.push(node);\n      return false;\n    },\n    Document: {\n      leave() {\n        const fragmentNameUsed = Object.create(null);\n        for (const operation of operationDefs) {\n          for (const fragment of context.getRecursivelyReferencedFragments(operation)) {\n            fragmentNameUsed[fragment.name.value] = true;\n          }\n        }\n        for (const fragmentDef of fragmentDefs) {\n          const fragName = fragmentDef.name.value;\n          if (fragmentNameUsed[fragName] !== true) {\n            context.reportError(new GraphQLError(`Fragment \"${fragName}\" is never used.`, {\n              nodes: fragmentDef\n            }));\n          }\n        }\n      }\n    }\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}