{"ast": null, "code": "/**\n * Creates a keyed JS object from an array, given a function to produce the keys\n * and a function to produce the values from each item in the array.\n * ```ts\n * const phoneBook = [\n *   { name: '<PERSON>', num: '555-1234' },\n *   { name: '<PERSON>', num: '867-5309' }\n * ]\n *\n * // { Jon: '555-1234', <PERSON>: '867-5309' }\n * const phonesByName = keyValMap(\n *   phoneBook,\n *   entry => entry.name,\n *   entry => entry.num\n * )\n * ```\n */\nexport function keyValMap(list, keyFn, valFn) {\n  const result = Object.create(null);\n  for (const item of list) {\n    result[keyFn(item)] = valFn(item);\n  }\n  return result;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}