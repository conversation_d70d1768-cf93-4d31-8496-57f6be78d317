{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { createHttpLink } from \"./createHttpLink.js\";\nvar HttpLink = /** @class */function (_super) {\n  __extends(HttpLink, _super);\n  function HttpLink(options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var _this = _super.call(this, createHttpLink(options).request) || this;\n    _this.options = options;\n    return _this;\n  }\n  return HttpLink;\n}(ApolloLink);\nexport { HttpLink };\n//# sourceMappingURL=HttpLink.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}