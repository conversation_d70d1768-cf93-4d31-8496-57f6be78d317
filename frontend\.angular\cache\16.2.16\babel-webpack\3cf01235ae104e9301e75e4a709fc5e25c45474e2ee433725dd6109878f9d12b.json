{"ast": null, "code": "import { devAssert } from '../jsutils/devAssert.mjs';\nimport { inspect } from '../jsutils/inspect.mjs';\nimport { instanceOf } from '../jsutils/instanceOf.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { toObjMap } from '../jsutils/toObjMap.mjs';\nimport { OperationTypeNode } from '../language/ast.mjs';\nimport { getNamedType, isInputObjectType, isInterfaceType, isObjectType, isUnionType } from './definition.mjs';\nimport { isDirective, specifiedDirectives } from './directives.mjs';\nimport { __Schema } from './introspection.mjs';\n/**\n * Test if the given value is a GraphQL schema.\n */\n\nexport function isSchema(schema) {\n  return instanceOf(schema, GraphQLSchema);\n}\nexport function assertSchema(schema) {\n  if (!isSchema(schema)) {\n    throw new Error(`Expected ${inspect(schema)} to be a GraphQL schema.`);\n  }\n  return schema;\n}\n/**\n * Custom extensions\n *\n * @remarks\n * Use a unique identifier name for your extension, for example the name of\n * your library or project. Do not use a shortened identifier as this increases\n * the risk of conflicts. We recommend you add at most one extension field,\n * an object which can contain all the values you need.\n */\n\n/**\n * Schema Definition\n *\n * A Schema is created by supplying the root types of each type of operation,\n * query and mutation (optional). A schema definition is then supplied to the\n * validator and executor.\n *\n * Example:\n *\n * ```ts\n * const MyAppSchema = new GraphQLSchema({\n *   query: MyAppQueryRootType,\n *   mutation: MyAppMutationRootType,\n * })\n * ```\n *\n * Note: When the schema is constructed, by default only the types that are\n * reachable by traversing the root types are included, other types must be\n * explicitly referenced.\n *\n * Example:\n *\n * ```ts\n * const characterInterface = new GraphQLInterfaceType({\n *   name: 'Character',\n *   ...\n * });\n *\n * const humanType = new GraphQLObjectType({\n *   name: 'Human',\n *   interfaces: [characterInterface],\n *   ...\n * });\n *\n * const droidType = new GraphQLObjectType({\n *   name: 'Droid',\n *   interfaces: [characterInterface],\n *   ...\n * });\n *\n * const schema = new GraphQLSchema({\n *   query: new GraphQLObjectType({\n *     name: 'Query',\n *     fields: {\n *       hero: { type: characterInterface, ... },\n *     }\n *   }),\n *   ...\n *   // Since this schema references only the `Character` interface it's\n *   // necessary to explicitly list the types that implement it if\n *   // you want them to be included in the final schema.\n *   types: [humanType, droidType],\n * })\n * ```\n *\n * Note: If an array of `directives` are provided to GraphQLSchema, that will be\n * the exact list of directives represented and allowed. If `directives` is not\n * provided then a default set of the specified directives (e.g. `@include` and\n * `@skip`) will be used. If you wish to provide *additional* directives to these\n * specified directives, you must explicitly declare them. Example:\n *\n * ```ts\n * const MyAppSchema = new GraphQLSchema({\n *   ...\n *   directives: specifiedDirectives.concat([ myCustomDirective ]),\n * })\n * ```\n */\nexport class GraphQLSchema {\n  // Used as a cache for validateSchema().\n  constructor(config) {\n    var _config$extensionASTN, _config$directives;\n\n    // If this schema was built from a source known to be valid, then it may be\n    // marked with assumeValid to avoid an additional type system validation.\n    this.__validationErrors = config.assumeValid === true ? [] : undefined; // Check for common mistakes during construction to produce early errors.\n\n    isObjectLike(config) || devAssert(false, 'Must provide configuration object.');\n    !config.types || Array.isArray(config.types) || devAssert(false, `\"types\" must be Array if provided but got: ${inspect(config.types)}.`);\n    !config.directives || Array.isArray(config.directives) || devAssert(false, '\"directives\" must be Array if provided but got: ' + `${inspect(config.directives)}.`);\n    this.description = config.description;\n    this.extensions = toObjMap(config.extensions);\n    this.astNode = config.astNode;\n    this.extensionASTNodes = (_config$extensionASTN = config.extensionASTNodes) !== null && _config$extensionASTN !== void 0 ? _config$extensionASTN : [];\n    this._queryType = config.query;\n    this._mutationType = config.mutation;\n    this._subscriptionType = config.subscription; // Provide specified directives (e.g. @include and @skip) by default.\n\n    this._directives = (_config$directives = config.directives) !== null && _config$directives !== void 0 ? _config$directives : specifiedDirectives; // To preserve order of user-provided types, we add first to add them to\n    // the set of \"collected\" types, so `collectReferencedTypes` ignore them.\n\n    const allReferencedTypes = new Set(config.types);\n    if (config.types != null) {\n      for (const type of config.types) {\n        // When we ready to process this type, we remove it from \"collected\" types\n        // and then add it together with all dependent types in the correct position.\n        allReferencedTypes.delete(type);\n        collectReferencedTypes(type, allReferencedTypes);\n      }\n    }\n    if (this._queryType != null) {\n      collectReferencedTypes(this._queryType, allReferencedTypes);\n    }\n    if (this._mutationType != null) {\n      collectReferencedTypes(this._mutationType, allReferencedTypes);\n    }\n    if (this._subscriptionType != null) {\n      collectReferencedTypes(this._subscriptionType, allReferencedTypes);\n    }\n    for (const directive of this._directives) {\n      // Directives are not validated until validateSchema() is called.\n      if (isDirective(directive)) {\n        for (const arg of directive.args) {\n          collectReferencedTypes(arg.type, allReferencedTypes);\n        }\n      }\n    }\n    collectReferencedTypes(__Schema, allReferencedTypes); // Storing the resulting map for reference by the schema.\n\n    this._typeMap = Object.create(null);\n    this._subTypeMap = Object.create(null); // Keep track of all implementations by interface name.\n\n    this._implementationsMap = Object.create(null);\n    for (const namedType of allReferencedTypes) {\n      if (namedType == null) {\n        continue;\n      }\n      const typeName = namedType.name;\n      typeName || devAssert(false, 'One of the provided types for building the Schema is missing a name.');\n      if (this._typeMap[typeName] !== undefined) {\n        throw new Error(`Schema must contain uniquely named types but contains multiple types named \"${typeName}\".`);\n      }\n      this._typeMap[typeName] = namedType;\n      if (isInterfaceType(namedType)) {\n        // Store implementations by interface.\n        for (const iface of namedType.getInterfaces()) {\n          if (isInterfaceType(iface)) {\n            let implementations = this._implementationsMap[iface.name];\n            if (implementations === undefined) {\n              implementations = this._implementationsMap[iface.name] = {\n                objects: [],\n                interfaces: []\n              };\n            }\n            implementations.interfaces.push(namedType);\n          }\n        }\n      } else if (isObjectType(namedType)) {\n        // Store implementations by objects.\n        for (const iface of namedType.getInterfaces()) {\n          if (isInterfaceType(iface)) {\n            let implementations = this._implementationsMap[iface.name];\n            if (implementations === undefined) {\n              implementations = this._implementationsMap[iface.name] = {\n                objects: [],\n                interfaces: []\n              };\n            }\n            implementations.objects.push(namedType);\n          }\n        }\n      }\n    }\n  }\n  get [Symbol.toStringTag]() {\n    return 'GraphQLSchema';\n  }\n  getQueryType() {\n    return this._queryType;\n  }\n  getMutationType() {\n    return this._mutationType;\n  }\n  getSubscriptionType() {\n    return this._subscriptionType;\n  }\n  getRootType(operation) {\n    switch (operation) {\n      case OperationTypeNode.QUERY:\n        return this.getQueryType();\n      case OperationTypeNode.MUTATION:\n        return this.getMutationType();\n      case OperationTypeNode.SUBSCRIPTION:\n        return this.getSubscriptionType();\n    }\n  }\n  getTypeMap() {\n    return this._typeMap;\n  }\n  getType(name) {\n    return this.getTypeMap()[name];\n  }\n  getPossibleTypes(abstractType) {\n    return isUnionType(abstractType) ? abstractType.getTypes() : this.getImplementations(abstractType).objects;\n  }\n  getImplementations(interfaceType) {\n    const implementations = this._implementationsMap[interfaceType.name];\n    return implementations !== null && implementations !== void 0 ? implementations : {\n      objects: [],\n      interfaces: []\n    };\n  }\n  isSubType(abstractType, maybeSubType) {\n    let map = this._subTypeMap[abstractType.name];\n    if (map === undefined) {\n      map = Object.create(null);\n      if (isUnionType(abstractType)) {\n        for (const type of abstractType.getTypes()) {\n          map[type.name] = true;\n        }\n      } else {\n        const implementations = this.getImplementations(abstractType);\n        for (const type of implementations.objects) {\n          map[type.name] = true;\n        }\n        for (const type of implementations.interfaces) {\n          map[type.name] = true;\n        }\n      }\n      this._subTypeMap[abstractType.name] = map;\n    }\n    return map[maybeSubType.name] !== undefined;\n  }\n  getDirectives() {\n    return this._directives;\n  }\n  getDirective(name) {\n    return this.getDirectives().find(directive => directive.name === name);\n  }\n  toConfig() {\n    return {\n      description: this.description,\n      query: this.getQueryType(),\n      mutation: this.getMutationType(),\n      subscription: this.getSubscriptionType(),\n      types: Object.values(this.getTypeMap()),\n      directives: this.getDirectives(),\n      extensions: this.extensions,\n      astNode: this.astNode,\n      extensionASTNodes: this.extensionASTNodes,\n      assumeValid: this.__validationErrors !== undefined\n    };\n  }\n}\nfunction collectReferencedTypes(type, typeSet) {\n  const namedType = getNamedType(type);\n  if (!typeSet.has(namedType)) {\n    typeSet.add(namedType);\n    if (isUnionType(namedType)) {\n      for (const memberType of namedType.getTypes()) {\n        collectReferencedTypes(memberType, typeSet);\n      }\n    } else if (isObjectType(namedType) || isInterfaceType(namedType)) {\n      for (const interfaceType of namedType.getInterfaces()) {\n        collectReferencedTypes(interfaceType, typeSet);\n      }\n      for (const field of Object.values(namedType.getFields())) {\n        collectReferencedTypes(field.type, typeSet);\n        for (const arg of field.args) {\n          collectReferencedTypes(arg.type, typeSet);\n        }\n      }\n    } else if (isInputObjectType(namedType)) {\n      for (const field of Object.values(namedType.getFields())) {\n        collectReferencedTypes(field.type, typeSet);\n      }\n    }\n  }\n  return typeSet;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}