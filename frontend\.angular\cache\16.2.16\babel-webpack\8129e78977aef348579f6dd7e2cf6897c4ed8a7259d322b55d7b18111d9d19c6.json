{"ast": null, "code": "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/index.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../utilities/index.js\";\nimport asyncIterator from \"./iterators/async.js\";\nimport nodeStreamIterator from \"./iterators/nodeStream.js\";\nimport promiseIterator from \"./iterators/promise.js\";\nimport readerIterator from \"./iterators/reader.js\";\nfunction isNodeResponse(value) {\n  return !!value.body;\n}\nfunction isReadableStream(value) {\n  return !!value.getReader;\n}\nfunction isAsyncIterableIterator(value) {\n  return !!(canUseAsyncIteratorSymbol && value[Symbol.asyncIterator]);\n}\nfunction isStreamableBlob(value) {\n  return !!value.stream;\n}\nfunction isBlob(value) {\n  return !!value.arrayBuffer;\n}\nfunction isNodeReadableStream(value) {\n  return !!value.pipe;\n}\nexport function responseIterator(response) {\n  var body = response;\n  if (isNodeResponse(response)) body = response.body;\n  if (isAsyncIterableIterator(body)) return asyncIterator(body);\n  if (isReadableStream(body)) return readerIterator(body.getReader());\n  // this errors without casting to ReadableStream<T>\n  // because Blob.stream() returns a NodeJS ReadableStream\n  if (isStreamableBlob(body)) {\n    return readerIterator(body.stream().getReader());\n  }\n  if (isBlob(body)) return promiseIterator(body.arrayBuffer());\n  if (isNodeReadableStream(body)) return nodeStreamIterator(body);\n  throw new Error(\"Unknown body type for responseIterator. Please pass a streamable response.\");\n}\n//# sourceMappingURL=responseIterator.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}