{"ast": null, "code": "import { inspect } from '../jsutils/inspect.mjs';\nimport { isObjectLike } from '../jsutils/isObjectLike.mjs';\nimport { GraphQLError } from '../error/GraphQLError.mjs';\nimport { Kind } from '../language/kinds.mjs';\nimport { print } from '../language/printer.mjs';\nimport { GraphQLScalarType } from './definition.mjs';\n/**\n * Maximum possible Int value as per GraphQL Spec (32-bit signed integer).\n * n.b. This differs from JavaScript's numbers that are IEEE 754 doubles safe up-to 2^53 - 1\n * */\n\nexport const GRAPHQL_MAX_INT = 2147483647;\n/**\n * Minimum possible Int value as per GraphQL Spec (32-bit signed integer).\n * n.b. This differs from JavaScript's numbers that are IEEE 754 doubles safe starting at -(2^53 - 1)\n * */\n\nexport const GRAPHQL_MIN_INT = -2147483648;\nexport const GraphQLInt = new GraphQLScalarType({\n  name: 'Int',\n  description: 'The `Int` scalar type represents non-fractional signed whole numeric values. Int can represent values between -(2^31) and 2^31 - 1.',\n  serialize(outputValue) {\n    const coercedValue = serializeObject(outputValue);\n    if (typeof coercedValue === 'boolean') {\n      return coercedValue ? 1 : 0;\n    }\n    let num = coercedValue;\n    if (typeof coercedValue === 'string' && coercedValue !== '') {\n      num = Number(coercedValue);\n    }\n    if (typeof num !== 'number' || !Number.isInteger(num)) {\n      throw new GraphQLError(`Int cannot represent non-integer value: ${inspect(coercedValue)}`);\n    }\n    if (num > GRAPHQL_MAX_INT || num < GRAPHQL_MIN_INT) {\n      throw new GraphQLError('Int cannot represent non 32-bit signed integer value: ' + inspect(coercedValue));\n    }\n    return num;\n  },\n  parseValue(inputValue) {\n    if (typeof inputValue !== 'number' || !Number.isInteger(inputValue)) {\n      throw new GraphQLError(`Int cannot represent non-integer value: ${inspect(inputValue)}`);\n    }\n    if (inputValue > GRAPHQL_MAX_INT || inputValue < GRAPHQL_MIN_INT) {\n      throw new GraphQLError(`Int cannot represent non 32-bit signed integer value: ${inputValue}`);\n    }\n    return inputValue;\n  },\n  parseLiteral(valueNode) {\n    if (valueNode.kind !== Kind.INT) {\n      throw new GraphQLError(`Int cannot represent non-integer value: ${print(valueNode)}`, {\n        nodes: valueNode\n      });\n    }\n    const num = parseInt(valueNode.value, 10);\n    if (num > GRAPHQL_MAX_INT || num < GRAPHQL_MIN_INT) {\n      throw new GraphQLError(`Int cannot represent non 32-bit signed integer value: ${valueNode.value}`, {\n        nodes: valueNode\n      });\n    }\n    return num;\n  }\n});\nexport const GraphQLFloat = new GraphQLScalarType({\n  name: 'Float',\n  description: 'The `Float` scalar type represents signed double-precision fractional values as specified by [IEEE 754](https://en.wikipedia.org/wiki/IEEE_floating_point).',\n  serialize(outputValue) {\n    const coercedValue = serializeObject(outputValue);\n    if (typeof coercedValue === 'boolean') {\n      return coercedValue ? 1 : 0;\n    }\n    let num = coercedValue;\n    if (typeof coercedValue === 'string' && coercedValue !== '') {\n      num = Number(coercedValue);\n    }\n    if (typeof num !== 'number' || !Number.isFinite(num)) {\n      throw new GraphQLError(`Float cannot represent non numeric value: ${inspect(coercedValue)}`);\n    }\n    return num;\n  },\n  parseValue(inputValue) {\n    if (typeof inputValue !== 'number' || !Number.isFinite(inputValue)) {\n      throw new GraphQLError(`Float cannot represent non numeric value: ${inspect(inputValue)}`);\n    }\n    return inputValue;\n  },\n  parseLiteral(valueNode) {\n    if (valueNode.kind !== Kind.FLOAT && valueNode.kind !== Kind.INT) {\n      throw new GraphQLError(`Float cannot represent non numeric value: ${print(valueNode)}`, valueNode);\n    }\n    return parseFloat(valueNode.value);\n  }\n});\nexport const GraphQLString = new GraphQLScalarType({\n  name: 'String',\n  description: 'The `String` scalar type represents textual data, represented as UTF-8 character sequences. The String type is most often used by GraphQL to represent free-form human-readable text.',\n  serialize(outputValue) {\n    const coercedValue = serializeObject(outputValue); // Serialize string, boolean and number values to a string, but do not\n    // attempt to coerce object, function, symbol, or other types as strings.\n\n    if (typeof coercedValue === 'string') {\n      return coercedValue;\n    }\n    if (typeof coercedValue === 'boolean') {\n      return coercedValue ? 'true' : 'false';\n    }\n    if (typeof coercedValue === 'number' && Number.isFinite(coercedValue)) {\n      return coercedValue.toString();\n    }\n    throw new GraphQLError(`String cannot represent value: ${inspect(outputValue)}`);\n  },\n  parseValue(inputValue) {\n    if (typeof inputValue !== 'string') {\n      throw new GraphQLError(`String cannot represent a non string value: ${inspect(inputValue)}`);\n    }\n    return inputValue;\n  },\n  parseLiteral(valueNode) {\n    if (valueNode.kind !== Kind.STRING) {\n      throw new GraphQLError(`String cannot represent a non string value: ${print(valueNode)}`, {\n        nodes: valueNode\n      });\n    }\n    return valueNode.value;\n  }\n});\nexport const GraphQLBoolean = new GraphQLScalarType({\n  name: 'Boolean',\n  description: 'The `Boolean` scalar type represents `true` or `false`.',\n  serialize(outputValue) {\n    const coercedValue = serializeObject(outputValue);\n    if (typeof coercedValue === 'boolean') {\n      return coercedValue;\n    }\n    if (Number.isFinite(coercedValue)) {\n      return coercedValue !== 0;\n    }\n    throw new GraphQLError(`Boolean cannot represent a non boolean value: ${inspect(coercedValue)}`);\n  },\n  parseValue(inputValue) {\n    if (typeof inputValue !== 'boolean') {\n      throw new GraphQLError(`Boolean cannot represent a non boolean value: ${inspect(inputValue)}`);\n    }\n    return inputValue;\n  },\n  parseLiteral(valueNode) {\n    if (valueNode.kind !== Kind.BOOLEAN) {\n      throw new GraphQLError(`Boolean cannot represent a non boolean value: ${print(valueNode)}`, {\n        nodes: valueNode\n      });\n    }\n    return valueNode.value;\n  }\n});\nexport const GraphQLID = new GraphQLScalarType({\n  name: 'ID',\n  description: 'The `ID` scalar type represents a unique identifier, often used to refetch an object or as key for a cache. The ID type appears in a JSON response as a String; however, it is not intended to be human-readable. When expected as an input type, any string (such as `\"4\"`) or integer (such as `4`) input value will be accepted as an ID.',\n  serialize(outputValue) {\n    const coercedValue = serializeObject(outputValue);\n    if (typeof coercedValue === 'string') {\n      return coercedValue;\n    }\n    if (Number.isInteger(coercedValue)) {\n      return String(coercedValue);\n    }\n    throw new GraphQLError(`ID cannot represent value: ${inspect(outputValue)}`);\n  },\n  parseValue(inputValue) {\n    if (typeof inputValue === 'string') {\n      return inputValue;\n    }\n    if (typeof inputValue === 'number' && Number.isInteger(inputValue)) {\n      return inputValue.toString();\n    }\n    throw new GraphQLError(`ID cannot represent value: ${inspect(inputValue)}`);\n  },\n  parseLiteral(valueNode) {\n    if (valueNode.kind !== Kind.STRING && valueNode.kind !== Kind.INT) {\n      throw new GraphQLError('ID cannot represent a non-string and non-integer value: ' + print(valueNode), {\n        nodes: valueNode\n      });\n    }\n    return valueNode.value;\n  }\n});\nexport const specifiedScalarTypes = Object.freeze([GraphQLString, GraphQLInt, GraphQLFloat, GraphQLBoolean, GraphQLID]);\nexport function isSpecifiedScalarType(type) {\n  return specifiedScalarTypes.some(({\n    name\n  }) => type.name === name);\n} // Support serializing objects with custom valueOf() or toJSON() functions -\n// a common way to represent a complex value which can be represented as\n// a string (ex: MongoDB id objects).\n\nfunction serializeObject(outputValue) {\n  if (isObjectLike(outputValue)) {\n    if (typeof outputValue.valueOf === 'function') {\n      const valueOfResult = outputValue.valueOf();\n      if (!isObjectLike(valueOfResult)) {\n        return valueOfResult;\n      }\n    }\n    if (typeof outputValue.toJSON === 'function') {\n      return outputValue.toJSON();\n    }\n  }\n  return outputValue;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}