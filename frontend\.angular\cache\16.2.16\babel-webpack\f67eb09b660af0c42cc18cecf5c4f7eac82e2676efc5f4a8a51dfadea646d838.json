{"ast": null, "code": "/* global window */\nimport ponyfill from './ponyfill.js';\nvar root;\nif (typeof self !== 'undefined') {\n  root = self;\n} else if (typeof window !== 'undefined') {\n  root = window;\n} else if (typeof global !== 'undefined') {\n  root = global;\n} else if (typeof module !== 'undefined') {\n  root = module;\n} else {\n  root = Function('return this')();\n}\nvar result = ponyfill(root);\nexport default result;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}