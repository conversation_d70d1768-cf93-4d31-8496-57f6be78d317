{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let ToastService = /*#__PURE__*/(() => {\n  class ToastService {\n    constructor() {\n      this.toastsSubject = new BehaviorSubject([]);\n      this.toasts$ = this.toastsSubject.asObservable();\n      this.currentId = 0;\n    }\n    show(message, type = 'info', duration = 5000) {\n      const id = this.currentId++;\n      const toast = {\n        id,\n        type,\n        message,\n        duration\n      };\n      const currentToasts = this.toastsSubject.value;\n      this.toastsSubject.next([...currentToasts, toast]);\n      if (duration > 0) {\n        setTimeout(() => this.dismiss(id), duration);\n      }\n    }\n    showSuccess(message, duration = 3000) {\n      this.show(message, 'success', duration);\n    }\n    showError(message, duration = 5000) {\n      this.show(message, 'error', duration);\n    }\n    showWarning(message, duration = 4000) {\n      this.show(message, 'warning', duration);\n    }\n    showInfo(message, duration = 3000) {\n      this.show(message, 'info', duration);\n    }\n    dismiss(id) {\n      const currentToasts = this.toastsSubject.value.filter(t => t.id !== id);\n      this.toastsSubject.next(currentToasts);\n    }\n    clear() {\n      this.toastsSubject.next([]);\n    }\n    static {\n      this.ɵfac = function ToastService_Factory(t) {\n        return new (t || ToastService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ToastService,\n        factory: ToastService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ToastService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}